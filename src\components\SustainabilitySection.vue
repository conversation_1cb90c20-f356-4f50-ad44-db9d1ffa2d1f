<template>
  <section class="py-16 bg-gradient-to-br from-green-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-2xl md:text-3xl font-YesevaOne text-gray-900 mb-4">
          Gemeinsam für eine <span class="text-green-600">bessere Welt</span>
        </h2>
        <p class="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
          Jede Ordy-Familie reduziert ihren CO2-Fußabdruck um 15% und hilft dabei, Lebensmittelverschwendung zu bekämpfen
        </p>
      </div>

      <!-- Environmental Impact Stats -->
      <div class="grid md:grid-cols-3 gap-8 mb-16">
        <div class="bg-white rounded-2xl p-8 shadow-lg text-center">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="text-3xl font-bold text-green-600 mb-2">15%</div>
          <div class="text-gray-900 font-medium mb-2">CO2-Reduktion</div>
          <div class="text-gray-600 text-sm">pro Familie durch weniger Verschwendung</div>
        </div>

        <div class="bg-white rounded-2xl p-8 shadow-lg text-center">
          <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="text-3xl font-bold text-blue-600 mb-2">{{ animatedSavedTons }}</div>
          <div class="text-gray-900 font-medium mb-2">Tonnen gerettet</div>
          <div class="text-gray-600 text-sm">Lebensmittel vor der Verschwendung bewahrt</div>
        </div>

        <div class="bg-white rounded-2xl p-8 shadow-lg text-center">
          <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div class="text-3xl font-bold text-purple-600 mb-2">10.000+</div>
          <div class="text-gray-900 font-medium mb-2">Aktive Familien</div>
          <div class="text-gray-600 text-sm">Teil der Nachhaltigkeits-Bewegung</div>
        </div>
      </div>

      <!-- Real-time Impact Counter -->
      <div class="bg-white rounded-3xl p-8 shadow-lg mb-16">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-YesevaOne text-gray-900 mb-2">
            Unsere Community-Impact in Echtzeit
          </h3>
          <p class="text-gray-600">Sieh zu, wie unsere Gemeinschaft jeden Tag einen Unterschied macht</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="text-4xl font-bold text-green-600 mb-2" ref="liveCounter1">{{ Math.round(liveSavedTons * 10) / 10 }}</div>
            <div class="text-gray-600">Tonnen heute gerettet</div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div class="bg-green-600 h-2 rounded-full transition-all duration-1000" :style="`width: ${(liveSavedTons / 5) * 100}%`"></div>
            </div>
          </div>

          <div class="text-center">
            <div class="text-4xl font-bold text-blue-600 mb-2">{{ Math.round(liveCO2Saved) }}</div>
            <div class="text-gray-600">kg CO2 eingespart</div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-1000" :style="`width: ${(liveCO2Saved / 200) * 100}%`"></div>
            </div>
          </div>

          <div class="text-center">
            <div class="text-4xl font-bold text-purple-600 mb-2">{{ Math.round(liveActiveFamilies) }}</div>
            <div class="text-gray-600">Familien heute aktiv</div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div class="bg-purple-600 h-2 rounded-full transition-all duration-1000" :style="`width: ${(liveActiveFamilies / 500) * 100}%`"></div>
            </div>
          </div>

          <div class="text-center">
            <div class="text-4xl font-bold text-red-600 mb-2">{{ Math.round(liveRecipesCreated) }}</div>
            <div class="text-gray-600">Rezepte heute erstellt</div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div class="bg-red-600 h-2 rounded-full transition-all duration-1000" :style="`width: ${(liveRecipesCreated / 100) * 100}%`"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Personal Impact Calculator -->
      <div class="bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-3xl p-8 text-white">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-YesevaOne mb-4">
            Wie viel könntest DU sparen?
          </h3>
          <p class="text-white/90">Berechne deinen persönlichen Impact mit Ordy</p>
        </div>
        
        <div class="max-w-2xl mx-auto">
          <div class="grid md:grid-cols-2 gap-6 mb-8">
            <div>
              <label class="block text-white/90 text-sm font-medium mb-2">Familiengröße</label>
              <div class="relative">
                <input v-model="familySize" 
                       type="range" 
                       min="1" 
                       max="8" 
                       class="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider">
                <div class="flex justify-between text-xs text-white/70 mt-1">
                  <span>1</span>
                  <span>8 Personen</span>
                </div>
              </div>
              <div class="text-center mt-2">
                <span class="text-2xl font-bold">{{ familySize }}</span>
                <span class="text-white/90 ml-1">{{ familySize === 1 ? 'Person' : 'Personen' }}</span>
              </div>
            </div>
            
            <div>
              <label class="block text-white/90 text-sm font-medium mb-2">Kochfrequenz pro Woche</label>
              <div class="relative">
                <input v-model="cookingFrequency" 
                       type="range" 
                       min="3" 
                       max="21" 
                       class="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider">
                <div class="flex justify-between text-xs text-white/70 mt-1">
                  <span>3</span>
                  <span>21 Mahlzeiten</span>
                </div>
              </div>
              <div class="text-center mt-2">
                <span class="text-2xl font-bold">{{ cookingFrequency }}</span>
                <span class="text-white/90 ml-1">Mahlzeiten</span>
              </div>
            </div>
          </div>
          
          <!-- Results -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
            <div class="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div class="text-3xl font-bold text-white mb-2">{{ calculatedSavings.money }}€</div>
                <div class="text-white/90 text-sm">Ersparnis pro Jahr</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-white mb-2">{{ calculatedSavings.co2 }}kg</div>
                <div class="text-white/90 text-sm">CO2 eingespart</div>
              </div>
              <div>
                <div class="text-3xl font-bold text-white mb-2">{{ calculatedSavings.food }}kg</div>
                <div class="text-white/90 text-sm">Lebensmittel gerettet</div>
              </div>
            </div>
            
            <div class="text-center mt-6">
              <button @click="$emit('start-saving')" 
                      class="bg-white text-ordypurple-100 px-8 py-3 rounded-full font-medium hover:bg-gray-50 transition-all transform hover:scale-105">
                Jetzt starten und sparen
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sustainability Tips -->
      <div class="mt-16">
        <div class="text-center mb-12">
          <h3 class="text-2xl font-YesevaOne text-gray-900 mb-4">
            Wie Ordy dir beim nachhaltigen Kochen hilft
          </h3>
        </div>
        
        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h4 class="text-lg font-YesevaOne text-gray-900 mb-2">Intelligente Resteverwertung</h4>
            <p class="text-gray-600">
              Unsere KI erkennt, was du zu Hause hast und schlägt passende Rezepte vor - nichts wird verschwendet.
            </p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <h4 class="text-lg font-YesevaOne text-gray-900 mb-2">Präzise Mengenplanung</h4>
            <p class="text-gray-600">
              Automatische Portionsberechnung sorgt dafür, dass du genau die richtige Menge einkaufst.
            </p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
              </svg>
            </div>
            <h4 class="text-lg font-YesevaOne text-gray-900 mb-2">Community-Lernen</h4>
            <p class="text-gray-600">
              Lerne von anderen Familien und teile deine eigenen Nachhaltigkeits-Erfolge.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props
const animatedSavedTons = ref(50)

// Calculator state
const familySize = ref(4)
const cookingFrequency = ref(14)

// Live counters
const liveSavedTons = ref(0)
const liveCO2Saved = ref(0)
const liveActiveFamilies = ref(0)
const liveRecipesCreated = ref(0)

// Intervals for live updates
let liveUpdateInterval = null

// Computed values for calculator
const calculatedSavings = computed(() => {
  const baseMoneyPerPerson = 45
  const baseCO2PerMeal = 2.5
  const baseFoodPerMeal = 0.3
  
  const money = Math.round(familySize.value * baseMoneyPerPerson * (cookingFrequency.value / 14))
  const co2 = Math.round(cookingFrequency.value * baseCO2PerMeal * 52) // per year
  const food = Math.round(cookingFrequency.value * baseFoodPerMeal * 52) // per year
  
  return { money, co2, food }
})

// Emit events
defineEmits(['start-saving'])

// Live counter simulation
const updateLiveCounters = () => {
  // Simulate real-time updates with cleaner increments
  liveSavedTons.value = Math.min(liveSavedTons.value + Math.random() * 0.05, 5)
  liveCO2Saved.value = Math.min(liveCO2Saved.value + Math.random() * 1.5, 200)
  liveActiveFamilies.value = Math.min(liveActiveFamilies.value + Math.random() * 3, 500)
  liveRecipesCreated.value = Math.min(liveRecipesCreated.value + Math.random() * 0.8, 100)
}

onMounted(() => {
  // Initialize live counters with clean values
  liveSavedTons.value = 2.3
  liveCO2Saved.value = 87
  liveActiveFamilies.value = 234
  liveRecipesCreated.value = 45

  // Start live updates every 5 seconds for smoother experience
  liveUpdateInterval = setInterval(updateLiveCounters, 5000)
})

onUnmounted(() => {
  if (liveUpdateInterval) {
    clearInterval(liveUpdateInterval)
  }
})
</script>

<style scoped>
/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
</style>
