console.log('--- Loading shoppingListController.js ---');

const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const categoryHelper = require('../utils/categoryHelper');
const ShoppingList = require('../models/shoppingListModel');
const ShoppingListItem = require('../models/shoppingListItemModel');
const ShoppingListItemContribution = require('../models/shoppingListItemContributionModel');
const Kitchentable = require('../models/kitchentableModel');
const Menu = require('../models/menuModel'); // Represents Recipe
const MenuChild = require('../models/menuchildModel'); // Contains ingredients
// import Grocery from '../models/groceryModel.js'; // Unused import with Mongoose population
// import Unit from '../models/unitModel.js'; // Unused import with Mongoose population
// No sequelize import needed anymore
const mongoose = require('mongoose');

// --- Utility function for quantity aggregation (simple addition for numbers) ---
const addQuantities = (q1, q2) => {
    const num1 = parseFloat(q1);
    const num2 = parseFloat(q2);
    if (!isNaN(num1) && !isNaN(num2)) {
        // Simple addition for numbers, format to 2 decimal places
        return (num1 + num2).toFixed(2);
    }
    // If either is not a number, return the new quantity (q2) or handle differently?
    // Current strategy: Keep the latest non-numeric quantity or the sum if numeric.
    // Consider a more robust approach if complex string quantities need merging.
    return q2; // Or potentially concatenate: `${q1} + ${q2}`?
};

const subtractQuantities = (totalQ, subtractQ) => {
    const numTotal = parseFloat(totalQ);
    const numSubtract = parseFloat(subtractQ);
    if (!isNaN(numTotal) && !isNaN(numSubtract)) {
        // Simple subtraction
        return (numTotal - numSubtract).toFixed(2);
    }
    // Cannot subtract non-numeric quantities reliably
    // Return the original total or null/error?
    return totalQ; // Keep original if subtraction isn't possible
};

// --- Helper function to get updated list and broadcast (Mongoose version) ---
// Signature is already correct: accepts broadcastToRoomFunc
const getListAndBroadcast = async (listId, kitchentableId, broadcastToRoomFunc) => {
    const functionName = 'getListAndBroadcast'; // For logging
    helper.devConsole(`[${functionName}] START for List: ${listId}, KT: ${kitchentableId}`);

    // Use the passed function directly
    const broadcastToRoom = broadcastToRoomFunc;
    if (!broadcastToRoom || typeof broadcastToRoom !== 'function') {
        helper.devConsole(`[${functionName}] WS Error: Invalid broadcastToRoom function received!`);
        // Cannot proceed without broadcast function, maybe throw or return error indicator
        throw new AppError('Internal server error: Broadcast function missing.', 500);
    }

    try {
        // Fetch list and items separately for clarity
        const updatedList = await ShoppingList.findById(listId);
        const items = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ createdAt: 1 });

        helper.devConsole(`[${functionName}] Found List: ${updatedList ? updatedList._id : 'null'}, Found ${items.length} items.`);
        // Optional: Log item names to verify the new one is included
        // helper.devConsole(`[${functionName}] Item names: ${items.map(i => i.name).join(', ')}`);

        let listData = null;
        if (updatedList) {
            listData = { ...updatedList.toObject(), items: items }; // Combine list data and items
        } else {
             // List might have been deleted concurrently?
             if (items.length > 0) {
                 // If items exist without list, maybe just broadcast items?
                 helper.devConsole(`[${functionName}] WARN: List ${listId} not found, but ${items.length} items still exist! Broadcasting items only.`);
                 listData = { items: items }; // Or handle differently
             } else {
                 helper.devConsole(`[${functionName}] List ${listId} not found and no items found.`);
                 listData = null; // List and items are gone
             }
        }

        const roomId = `kitchentable_${kitchentableId}`;
        helper.devConsole(`[${functionName}] Preparing to broadcast to room: ${roomId}`);
        // --- PRE-BROADCAST CHECK ---
        helper.devConsole(`[${functionName}] PRE-BROADCAST CHECK for room ${roomId}, event 'zettel_updated'. Type of listData: ${typeof listData}`);
        if (listData === null) {
            helper.devConsole(`[${functionName}] PRE-BROADCAST CHECK: listData is null.`);
        } else if (typeof listData === 'object' && listData !== null) {
            helper.devConsole(`[${functionName}] PRE-BROADCAST CHECK: listData is an object. Keys: ${Object.keys(listData).join(', ')}. Has items array? ${Array.isArray(listData.items)}`);
            if (Array.isArray(listData.items)) {
                helper.devConsole(`[${functionName}] PRE-BROADCAST CHECK: listData.items length: ${listData.items.length}`);
            }
        } else {
            // This case should not happen based on the logic, but log it defensively
            helper.devConsole(`[${functionName}] PRE-BROADCAST CHECK WARNING: listData is NEITHER null NOR an object! Value:`, listData);
        }
        // --- END PRE-BROADCAST CHECK ---
        helper.devConsole(`[${functionName}] Broadcasting data (first 200 chars): ${JSON.stringify(listData)?.substring(0, 200) || 'null'}...`); // Add null check for stringify

        broadcastToRoom(roomId, 'zettel_updated', listData); // Send combined data or null

        helper.devConsole(`[${functionName}] Broadcast call finished for room: ${roomId}`);
        return listData; // Return combined data or null

    } catch (error) {
         helper.devConsole(`[${functionName}] WS Error: Could not fetch/prepare list ${listId} for broadcast: ${error}`);
         const roomId = `kitchentable_${kitchentableId}`;
         // Attempt broadcast error even if fetching failed?
         try { broadcastToRoom(roomId, 'zettel_error', { message: 'Error fetching list update.'}); } catch (bcErr) { helper.devConsole("Failed to broadcast fetch error"); }
         throw error; // Re-throw the error so the caller knows something went wrong
    }
};

// --- Controller Functions (CommonJS Export Style) ---

// Kleine Hilfsfunktion für ISO 8601 Wochennummer
function getWeekNumber(d) {
    d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
    // Set to nearest Thursday: current date + 4 - current day number
    // Make Sunday's day number 7
    d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay()||7));
    // Get first day of year
    var yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
    // Calculate full weeks to nearest Thursday
    var weekNo = Math.ceil((((d - yearStart) / 86400000) + 1)/7);
    // Return week number
    return weekNo;
}

/**
 * @openapi
 * /api/kitchentables/{kitchentableId}/shopping-list/active:
 *   get:
 *     tags: [Shopping List]
 *     summary: Holt die aktive Einkaufsliste für einen Küchentisch.
 *     description: >
 *       Ruft die Details der aktuell aktiven Einkaufsliste für den angegebenen Küchentisch ab,
 *       einschließlich aller zugehörigen Einkaufslisten-Einträge.
 *       Wenn für den Küchentisch noch keine aktive Liste existiert, wird automatisch
 *       eine neue, leere Liste mit dem Namen "Einkaufszettel KW[AktuelleWoche]" erstellt und zurückgegeben.
 *     parameters:
 *       - in: path
 *         name: kitchentableId
 *         required: true
 *         schema:
 *           type: string
 *           description: Die ID des Küchentisches.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Aktive Einkaufsliste erfolgreich abgerufen oder erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ShoppingListWithItems'
 *       401:
 *         description: Nicht autorisiert (Token fehlt, ist ungültig oder User nicht Mitglied).
 *       404:
 *         description: Küchentisch nicht gefunden.
 *       500:
 *         description: Interner Serverfehler.
 */
exports.getActiveShoppingList = catchAsync(async (req, res, next) => {
    const kitchentableId = req.params.kitchentableId;
    const userId = req.user._id; // User ID from auth middleware

    helper.devConsole(`[getActiveShoppingList] Start for KT: ${kitchentableId}, User: ${userId}`);

    // 1. Verify User is part of the Kitchentable
    // Use try-catch for robustness, although catchAsync helps
    try {
        const kitchentable = await Kitchentable.findOne({ _id: kitchentableId, 'members.userId': userId });
        if (!kitchentable) {
            helper.devConsole(`[getActiveShoppingList] Kitchentable ${kitchentableId} not found or user ${userId} not member.`);
            return next(new AppError('Kitchentable not found or user not authorized', 404)); // Use 404 or 403 based on policy
        }
         helper.devConsole(`[getActiveShoppingList] Kitchentable ${kitchentableId} found and user ${userId} is member.`);
    } catch (err) {
         helper.devConsole(`[getActiveShoppingList] Error finding Kitchentable ${kitchentableId}: ${err}`);
         return next(new AppError('Error verifying kitchentable access', 500));
    }

    // 2. Find or Create Active Shopping List
    let shoppingList = null;
    let statusCode = 200; // Default status
    try {
        helper.devConsole(`[getActiveShoppingList] Finding active ShoppingList for KT: ${kitchentableId}`);
        shoppingList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true });
        helper.devConsole(`[getActiveShoppingList] Found ShoppingList: ${shoppingList ? shoppingList._id : 'null'}`);

        if (!shoppingList) {
            helper.devConsole(`[getActiveShoppingList] No active list found. Creating new one for KT: ${kitchentableId}`);
            try {
                const currentWeek = getWeekNumber(new Date()); // Get current week number
                const listName = `Einkaufszettel KW${currentWeek}`; // Construct the name
                helper.devConsole(`[getActiveShoppingList] New list name: ${listName}`);

                shoppingList = await ShoppingList.create({
                    kitchentable_id: kitchentableId,
                    name: listName, // Use the dynamic name here
                    is_active: true,
                    // created_at/updated_at handled by Mongoose timestamps if schema allows
                });
                statusCode = 201; // Indicate resource creation
                helper.devConsole(`[getActiveShoppingList] Created new ShoppingList: ${shoppingList ? shoppingList._id : 'null'}`);
            } catch (creationError) {
                helper.devConsole(`[getActiveShoppingList] Error creating ShoppingList for KT ${kitchentableId}: ${creationError}`);
                return next(new AppError('Could not create shopping list', 500));
            }
        }
    } catch (err) {
        helper.devConsole(`[getActiveShoppingList] Error finding/creating ShoppingList for KT ${kitchentableId}: ${err}`);
        return next(new AppError('Error accessing shopping list', 500));
    }

    // Safety check: Should have a shoppingList object here
    if (!shoppingList) {
        helper.devConsole(`[getActiveShoppingList] CRITICAL: shoppingList is null after find/create attempt! KT: ${kitchentableId}`);
        return next(new AppError('Failed to retrieve or create shopping list', 500));
    }
    helper.devConsole(`[getActiveShoppingList] Proceeding with ShoppingList ID: ${shoppingList._id}`);

    // 3. Fetch Items for the Shopping List
    let items = [];
    try {
        helper.devConsole(`[getActiveShoppingList] Finding items for list: ${shoppingList._id}`);
        items = await ShoppingListItem.find({ shopping_list_id: shoppingList._id })
            .populate('added_by_user_id', 'firstName lastName') // Populate user name if needed
            .sort({ is_purchased: 1, name: 1 }); // Sort by purchased status, then name
        helper.devConsole(`[getActiveShoppingList] Found ${items.length} items for list: ${shoppingList._id}`);
    } catch (itemError) {
        helper.devConsole(`[getActiveShoppingList] Error fetching items for list ${shoppingList._id}: ${itemError}`);
        // Decide if we should return the list without items or throw an error
        return next(new AppError('Error fetching shopping list items', 500));
    }

    // 4. Prepare Response Object
    let listData = {};
    try {
        helper.devConsole(`[getActiveShoppingList] Converting list ${shoppingList._id} toObject`);
        listData = shoppingList.toObject(); // Convert Mongoose doc to plain object
        listData.items = items; // Add items array to the response object
        helper.devConsole(`[getActiveShoppingList] Successfully prepared listData for list ${shoppingList._id}`);
    } catch (toObjectError) {
        helper.devConsole(`[getActiveShoppingList] Error converting shoppingList ${shoppingList._id} to object: ${toObjectError}`);
        return next(new AppError('Error preparing shopping list data', 500));
    }

    // 5. Send Response
    // helper.devConsole(`[getActiveShoppingList] Sending response with status ${statusCode} for list ${shoppingList._id}`);

    // ---> REMOVE/COMMENT LOGGING FOR CREATION CASE <---
    // if (statusCode === 201) {
    //     helper.devConsole(`[getActiveShoppingList] CREATED List - Response Data Check:`, JSON.stringify(listData));
    // }
    // ---> END LOGGING <---

    res.status(statusCode).json({
        success: true,
        data: listData
    });
});

// --- Core logic for adding a recipe (without catchAsync wrapper) ---
// Accepts broadcastToRoomFunc as the last argument
const addRecipeToListImpl = async (req, res, next, broadcastToRoomFunc) => {
    // ---> ADD ENTRY LOG < ---
    helper.devConsole(`[addRecipeToListImpl] FUNCTION ENTERED. listId=${req.params?.listId}, recipeId=${req.body?.recipeId}, userId=${req.user?.id}`);

    const { listId } = req.params; // Keep original extraction
    const { recipeId } = req.body; // Keep original extraction
    const userId = req.user.id;    // Keep original extraction
    // Removed fallback logic - Rely ONLY on passed function

    helper.devConsole(`[addRecipeToListImpl] POST /api/shopping-lists/${listId}/recipes with Recipe ${recipeId} by User ${userId}`); // Log context

    if (!recipeId) {
        // For direct WS calls, we might throw instead of calling next
        throw new AppError('Recipe ID is required', 400);
    }

    // --- Authorization and Validation ---
    // Find list and verify access via kitchentable membership
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id', // Populate the referenced Kitchentable
        select: 'members _id' // Select members and _id (for broadcasting)
    });

    if (!shoppingList) {
        throw new AppError('Shopping list not found', 404);
    }

    // Check if the list is completed
    if (shoppingList.is_completed) {
        helper.devConsole(`[addRecipeToListImpl] Shopping list ${listId} is completed and cannot be modified.`);
        throw new AppError('Abgeschlossene Listen können nicht bearbeitet werden', 400);
    }

    const kitchentable = shoppingList.kitchentable_id; // Populated kitchentable
    // Ensure kitchentable and its _id are available before proceeding
    if (!kitchentable || !kitchentable._id) {
         helper.devConsole(`[addRecipeToListImpl] CRITICAL: Kitchentable or Kitchentable._id missing after population for List ${listId}.`);
         throw new AppError('Internal Server Error: Could not verify kitchentable association.', 500);
    }
    if (!kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        throw new AppError('User not authorized for this shopping list', 403);
    }
     helper.devConsole(`[addRecipeToListImpl] Authorization OK for User ${userId} on KT ${kitchentable._id}.`); // Log success


    // Find the recipe (Menu) and its standard ingredients (MenuChild)
    const recipe = await Menu.findById(recipeId);
    if (!recipe) {
        throw new AppError('Recipe not found', 404);
    }
     helper.devConsole(`[addRecipeToListImpl] Recipe ${recipeId} found.`); // Log success

    // Find associated standard MenuChild
    const menuChild = await MenuChild.findOne({ parentId: recipeId, isStandard: true })
                                      .populate('ingredients.name') // Populate Grocery name
                                      .populate('ingredients.unit');  // Populate Unit name

    if (!menuChild || !menuChild.ingredients || menuChild.ingredients.length === 0) {
        helper.devConsole(`[addRecipeToListImpl] No standard ingredients found for recipe ${recipeId} (MenuChild: ${menuChild?._id}).`); // Log info
        // Use the passed broadcast function directly
        const listToBroadcast = await getListAndBroadcast(listId, kitchentable._id, broadcastToRoomFunc);
        // If called via HTTP, send response
        if (res && typeof res.status === 'function') {
             helper.devConsole(`[addRecipeToListImpl] Responding via HTTP.`);
             return res.status(200).json({ status: 'success', message: 'Recipe has no standard ingredients to add.', data: listToBroadcast });
        } else {
             helper.devConsole(`[addRecipeToListImpl] Returning list data (called via WS or similar).`);
             return listToBroadcast; // If called internally/WS, return the data
        }
    }
     helper.devConsole(`[addRecipeToListImpl] Found MenuChild ${menuChild._id} with ${menuChild.ingredients.length} ingredients.`); // Log success

    // --- Aggregation Logic ---
    // No transaction needed for simple creates/updates unless essential
    try {
        helper.devConsole(`[addRecipeToListImpl] Processing ingredients for recipe ${recipeId}, MenuChild ${menuChild._id}`); // Log start
        for (const ingredient of menuChild.ingredients) {
             helper.devConsole(`[addRecipeToListImpl] --- Iterating ingredient: ${JSON.stringify(ingredient)}`); // Log raw ingredient

            // Validate populated fields before accessing nested properties
            if (!ingredient.name || typeof ingredient.name !== 'object' || !ingredient.name.name) {
                 helper.devConsole(`[addRecipeToListImpl] Skipping ingredient: Missing or invalid populated ingredient.name. ID was: ${ingredient.name}`);
                 continue;
            }
             if (ingredient.amount === undefined || ingredient.amount === null) {
                 helper.devConsole(`[addRecipeToListImpl] Skipping ingredient: Missing amount. Name: ${ingredient.name?.name}`);
                 continue;
             }
             // Unit is optional, but log if populate failed unexpectedly
             if (ingredient.unit && typeof ingredient.unit !== 'object') {
                  helper.devConsole(`[addRecipeToListImpl] Warning: ingredient.unit exists but is not a populated object. Value: ${ingredient.unit}`);
             }

            const unitName = ingredient.unit && ingredient.unit.name ? ingredient.unit.name : null;
            const ingredientName = ingredient.name.name; // Safe now after checks
            const ingredientAmount = ingredient.amount;
            helper.devConsole(`[addRecipeToListImpl]   -> Extracted: Name='${ingredientName}', Amount='${ingredientAmount}', Unit='${unitName}'`);

            // Find existing non-custom item
            let existingItem = null;
            try {
                 existingItem = await ShoppingListItem.findOne({
                    shopping_list_id: listId,
                    name: ingredientName,
                    unit: unitName,
                    is_custom: false
                });
                 helper.devConsole(`[addRecipeToListImpl]   -> Found existing item? ${existingItem ? existingItem._id : 'No'}`);
            } catch (findError) {
                 helper.devConsole(`[addRecipeToListImpl]   -> ERROR finding existing item: ${findError}`);
                 continue; // Skip this ingredient if find fails
            }

            if (existingItem) {
                // Update existing item's quantity using utility function
                 helper.devConsole(`[addRecipeToListImpl]   -> Updating existing item ${existingItem._id}. Current Qty: ${existingItem.quantity}`);
                existingItem.quantity = addQuantities(existingItem.quantity, ingredientAmount);
                // --- FIX: Ensure recipeId is also set on update ---
                existingItem.recipeId = recipeId;
                // --- END FIX ---
                 helper.devConsole(`[addRecipeToListImpl]   -> New Qty: ${existingItem.quantity}`);
                 helper.devConsole(`[addRecipeToListImpl]   -> Ensuring recipeId: ${existingItem.recipeId}`); // Log added
                try {
                    await existingItem.save();
                     helper.devConsole(`[addRecipeToListImpl]   -> Successfully saved updated item ${existingItem._id}`);
                    // Create contribution record
                    await ShoppingListItemContribution.create({
                        shopping_list_item_id: existingItem._id,
                        recipe_id: recipeId,
                        contributed_quantity: ingredientAmount
                    });
                     helper.devConsole(`[addRecipeToListImpl]   -> Created contribution for updated item.`); // Log success
                } catch(saveError) {
                     helper.devConsole(`[addRecipeToListImpl]   -> ERROR saving updated item ${existingItem._id}: ${saveError}`);
                     // Decide whether to continue with next ingredient or throw
                     // For robustness in loops, maybe just log and continue? Or collect errors?
                     // Re-throwing here would stop the loop.
                     throw new AppError(`Failed to save updated item: ${ingredientName}`, 500);
                }

            } else {
                // Create new item
                 helper.devConsole(`[addRecipeToListImpl]   -> Creating new item.`);

                 // Automatische Kategorisierung des Artikels
                 const category = categoryHelper.categorizeItem(ingredientName);
                 helper.devConsole(`[addRecipeToListImpl]   -> Categorized item "${ingredientName}" as "${category}"`);

                 const newItemData = {
                    shopping_list_id: listId,
                    name: ingredientName,
                    quantity: ingredientAmount,
                    unit: unitName,
                    is_custom: false,
                    is_purchased: false,
                    recipeId: recipeId,
                    category: category
                };
                 helper.devConsole(`[addRecipeToListImpl]   -> New item data: ${JSON.stringify(newItemData)}`);
                try {
                    const newItem = await ShoppingListItem.create(newItemData);
                     helper.devConsole(`[addRecipeToListImpl]   -> Successfully created new item ${newItem._id}`);
                    // Create contribution record for the new item
                    await ShoppingListItemContribution.create({
                        shopping_list_item_id: newItem._id,
                        recipe_id: recipeId,
                        contributed_quantity: ingredientAmount
                    });
                     helper.devConsole(`[addRecipeToListImpl]   -> Created contribution for new item.`); // Log success
                } catch (createError) {
                     helper.devConsole(`[addRecipeToListImpl]   -> ERROR creating new item: ${createError}`);
                     // Decide whether to continue or throw
                     throw new AppError(`Failed to create new item: ${ingredientName}`, 500);
                }
            }
        }

        helper.devConsole(`[addRecipeToListImpl] Successfully added/updated items from recipe ${recipeId} to list ${listId}`);

        // Fetch updated list and broadcast using the passed function
         helper.devConsole(`[addRecipeToListImpl] Fetching final list state for broadcast (KT: ${kitchentable._id}).`);
        const listToBroadcast = await getListAndBroadcast(listId, kitchentable._id, broadcastToRoomFunc);
         helper.devConsole(`[addRecipeToListImpl] List fetched, returning/sending response.`);

        // If called via HTTP, send response
        if (res && typeof res.status === 'function') {
             return res.status(200).json({
                status: 'success',
                data: listToBroadcast
            });
        } else {
             return listToBroadcast; // If called internally/WS, return the data
        }

    } catch (error) {
        helper.devConsole(`[addRecipeToListImpl] Error processing ingredients for recipe ${recipeId} to list ${listId}: ${error}`);
        // If called via HTTP, use next()
        if (next && typeof next === 'function') {
             return next(new AppError(`Failed to add recipe ingredients: ${error.message}`, 500));
        } else {
            // If called internally/WS, re-throw the original error or a new AppError
            // Ensure the error is propagated back to the WS handler's catch block
            throw error instanceof AppError ? error : new AppError(`Failed to add recipe ingredients: ${error.message}`, 500);
        }
    }
};

// Export the implementation function for direct use (e.g., WebSocket)
exports.addRecipeToListImpl = addRecipeToListImpl;

// Export the catchAsync-wrapped version for use in Express routes
exports.addRecipeToList = catchAsync(addRecipeToListImpl);


// --- Core logic for removing a recipe (without catchAsync wrapper) ---
// Accepts broadcastToRoomFunc as the last argument
const removeRecipeFromListImpl = async (req, res, next, broadcastToRoomFunc) => {
    // ---> ADD ENTRY LOG < ---
    helper.devConsole(`[removeRecipeFromListImpl] FUNCTION ENTERED. listId=${req.params?.listId}, recipeId=${req.params?.recipeId}, userId=${req.user?.id}`);

    const { listId, recipeId } = req.params; // Keep original extraction
    const userId = req.user.id;           // Keep original extraction
    // Removed fallback logic

    helper.devConsole(`[removeRecipeFromListImpl] DELETE /api/shopping-lists/${listId}/recipes/${recipeId} by User ${userId}`);

    // --- Authorization and Validation ---
     const shoppingList = await ShoppingList.findById(listId).populate({ path: 'kitchentable_id', select: 'members _id' }); // Need _id
     if (!shoppingList) {
         throw new AppError('Shopping list not found', 404);
     }
     const kitchentable = shoppingList.kitchentable_id;
     // Ensure kitchentable and its _id are available before proceeding
     if (!kitchentable || !kitchentable._id) {
          helper.devConsole(`[removeRecipeFromListImpl] CRITICAL: Kitchentable or Kitchentable._id missing after population for List ${listId}.`);
          throw new AppError('Internal Server Error: Could not verify kitchentable association.', 500);
     }
     if (!kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
         throw new AppError('User not authorized for this shopping list', 403);
     }
      helper.devConsole(`[removeRecipeFromListImpl] Authorization OK for User ${userId} on KT ${kitchentable._id}.`);

    // --- De-aggregation Logic ---
    // Using a session for transaction-like behavior if needed, but might be overkill.
    // For now, proceed operation by operation.
    try {
        // 1. Find all contributions for this recipe on this list
         helper.devConsole(`[removeRecipeFromListImpl] Finding contributions for Recipe ${recipeId}...`);
        const contributions = await ShoppingListItemContribution.find({
            recipe_id: recipeId,
            // We need to find the items first to ensure they belong to the list
        }).populate({
            path: 'shopping_list_item_id', // Populate the item
            select: 'shopping_list_id name is_custom quantity' // Select needed fields + list ID
        });
         helper.devConsole(`[removeRecipeFromListImpl] Found ${contributions.length} potential contributions.`);

        // Filter contributions to only those whose item belongs to the target list
        const relevantContributions = contributions.filter(c =>
            c.shopping_list_item_id && c.shopping_list_item_id.shopping_list_id.toString() === listId
        );
         helper.devConsole(`[removeRecipeFromListImpl] Filtered to ${relevantContributions.length} relevant contributions on list ${listId}.`);

        if (relevantContributions.length === 0) {
            helper.devConsole(`[removeRecipeFromListImpl] No relevant contributions found for recipe ${recipeId} on list ${listId}. Nothing to remove.`);
            // Use the passed broadcast function directly
             helper.devConsole(`[removeRecipeFromListImpl] Fetching current list state for broadcast/response.`);
            const listToBroadcast = await getListAndBroadcast(listId, kitchentable._id, broadcastToRoomFunc);
            // If called via HTTP, send response
            if (res && typeof res.status === 'function') {
                 helper.devConsole(`[removeRecipeFromListImpl] Responding via HTTP.`);
                 return res.status(200).json({ status: 'success', data: listToBroadcast });
            } else {
                 helper.devConsole(`[removeRecipeFromListImpl] Returning list data (called via WS or similar).`);
                 return listToBroadcast; // If called internally/WS, return the data
            }
        }

        // 2. Process each contribution
         helper.devConsole(`[removeRecipeFromListImpl] Processing ${relevantContributions.length} contributions...`);
        for (const contribution of relevantContributions) {
             helper.devConsole(`[removeRecipeFromListImpl] --- Processing Contribution ID: ${contribution._id}`);
            const listItem = contribution.shopping_list_item_id; // This is the populated item document
            const contributedQty = contribution.contributed_quantity;

            // Safety check for populated item
            if (!listItem) {
                 helper.devConsole(`[removeRecipeFromListImpl] --- Contribution ${contribution._id} references a missing item! Skipping.`);
                 // Optionally delete the orphaned contribution?
                 // await ShoppingListItemContribution.findByIdAndDelete(contribution._id);
                 continue;
            }
             helper.devConsole(`[removeRecipeFromListImpl] --- Item ID: ${listItem._id} (${listItem.name}), Current Qty: ${listItem.quantity}, Contributed Qty: ${contributedQty}`);


            // Reduce item quantity (only if both are numbers)
            listItem.quantity = subtractQuantities(listItem.quantity, contributedQty);
            helper.devConsole(`[removeRecipeFromListImpl] --- New Qty after subtraction: ${listItem.quantity}`);

            // Delete the contribution record
            try {
                 await ShoppingListItemContribution.findByIdAndDelete(contribution._id);
                 helper.devConsole(`[removeRecipeFromListImpl] --- Deleted contribution ${contribution._id}`);
            } catch (deleteContribError) {
                 helper.devConsole(`[removeRecipeFromListImpl] --- ERROR deleting contribution ${contribution._id}: ${deleteContribError}`);
                 // Decide: Continue processing other contributions? Throw?
                 // Let's log and continue for now.
                 continue;
            }


            // Check if other contributions exist for this item
            const remainingContributionsCount = await ShoppingListItemContribution.countDocuments({
                shopping_list_item_id: listItem._id
            });

             helper.devConsole(`[removeRecipeFromListImpl] --- Item ${listItem._id} has ${remainingContributionsCount} remaining contributions.`);

            // If no contributions remain AND the item is not custom, delete the item
            if (remainingContributionsCount === 0 && !listItem.is_custom) {
                 helper.devConsole(`[removeRecipeFromListImpl] --- Deleting item ${listItem._id} (${listItem.name}) as no contributions remain and it's not custom.`);
                 try {
                      await ShoppingListItem.findByIdAndDelete(listItem._id);
                      helper.devConsole(`[removeRecipeFromListImpl] --- Successfully deleted item ${listItem._id}`);
                 } catch (deleteItemError) {
                      helper.devConsole(`[removeRecipeFromListImpl] --- ERROR deleting item ${listItem._id}: ${deleteItemError}`);
                      // Item might be already deleted? Log and continue.
                 }
            } else {
                 // Ensure quantity doesn't go below zero (for numeric quantities)
                 const currentNumQty = parseFloat(listItem.quantity);
                 if (!isNaN(currentNumQty) && currentNumQty < 0) {
                      helper.devConsole(`[removeRecipeFromListImpl] --- Warning: Item ${listItem._id} quantity went below zero (${listItem.quantity}), setting to '0.00'.`);
                      listItem.quantity = '0.00'; // Use string for consistency with Mixed type
                 }
                 // Save the updated item quantity if it wasn't deleted
                 try {
                      await listItem.save();
                      helper.devConsole(`[removeRecipeFromListImpl] --- Saved updated quantity for item ${listItem._id}.`);
                 } catch (saveItemError) {
                      helper.devConsole(`[removeRecipeFromListImpl] --- ERROR saving updated item ${listItem._id}: ${saveItemError}`);
                      // Decide: Continue? Throw? Log and continue.
                 }
            }
        } // End of loop through contributions

        helper.devConsole(`[removeRecipeFromListImpl] Successfully removed contributions for recipe ${recipeId} from list ${listId}`);

        // Fetch updated list and broadcast using the passed function
         helper.devConsole(`[removeRecipeFromListImpl] Fetching final list state for broadcast (KT: ${kitchentable._id}).`);
        const listToBroadcast = await getListAndBroadcast(listId, kitchentable._id, broadcastToRoomFunc);
         helper.devConsole(`[removeRecipeFromListImpl] List fetched, returning/sending response.`);

        // If called via HTTP, send response
        if (res && typeof res.status === 'function') {
             return res.status(200).json({
                success: true,
                data: listToBroadcast
            });
        } else {
             return listToBroadcast; // If called internally/WS, return the data
        }

    } catch (error) {
        helper.devConsole(`[removeRecipeFromListImpl] Error processing removal for recipe ${recipeId} from list ${listId}: ${error}`);
        // If called via HTTP, use next()
        if (next && typeof next === 'function') {
             return next(new AppError(`Failed to remove recipe contributions: ${error.message}`, 500));
        } else {
            // If called internally/WS, re-throw
            throw error instanceof AppError ? error : new AppError(`Failed to remove recipe contributions: ${error.message}`, 500);
        }
    }
};

// Export the implementation function for direct use (e.g., WebSocket)
exports.removeRecipeFromListImpl = removeRecipeFromListImpl;

// Export the catchAsync-wrapped version for use in Express routes
exports.removeRecipeFromList = catchAsync(removeRecipeFromListImpl);

exports.addCustomItemToList = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const { name, quantity, unit } = req.body;
    const userId = req.user.id;

    helper.devConsole(`POST /api/shopping-lists/${listId}/items by User ${userId}`);

    if (!name || quantity === undefined || quantity === null) { // Check quantity existence
        return next(new AppError('Item name and quantity are required', 400));
    }

    // --- Authorization ---
     const shoppingList = await ShoppingList.findById(listId).populate({ path: 'kitchentable_id', select: 'members' });
     if (!shoppingList) {
         return next(new AppError('Shopping list not found', 404));
     }

     // Check if the list is completed
     if (shoppingList.is_completed) {
         helper.devConsole(`[addCustomItemToList] Shopping list ${listId} is completed and cannot be modified.`);
         return next(new AppError('Abgeschlossene Listen können nicht bearbeitet werden', 400));
     }

     const kitchentable = shoppingList.kitchentable_id;
     if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
         return next(new AppError('User not authorized for this shopping list', 403));
     }

    // --- Create Item ---
    try {
        // Verwende Frontend-Kategorie falls vorhanden, sonst automatische Kategorisierung
        let category = req.body.category;
        if (!category || category.trim() === '' || category === 'Sonstiges') {
            category = categoryHelper.categorizeItem(name);
            helper.devConsole(`Auto-categorized item "${name}" as "${category}"`);
        } else {
            helper.devConsole(`Using frontend category "${category}" for item "${name}"`);
        }

        const newItem = await ShoppingListItem.create({
            shopping_list_id: listId,
            name: name,
            quantity: quantity,
            unit: unit || null,
            is_purchased: false,
            is_custom: true,
            added_by_user_id: userId,
            category: category
        });

        helper.devConsole(`Created new custom item ${newItem._id} (${newItem.name}) in category ${category} on list ${listId}`);

        // Still broadcast the full list update to other clients
        await getListAndBroadcast(listId, kitchentable._id, req.app.locals.broadcastToRoom);

        // Respond with ONLY the newly created item object
        res.status(201).json({
            success: true, // Use boolean success field
            data: newItem.toObject() // Return the newly created item as a plain object
        });

    } catch (error) {
        helper.devConsole(`Error adding custom item to list ${listId}: ${error}`);
        return next(new AppError('Failed to add custom item', 500));
    }
});

exports.updateShoppingListItem = catchAsync(async (req, res, next) => {
    const { itemId } = req.params;
    const updates = req.body;
    const userId = req.user.id;

    helper.devConsole(`PUT /api/shopping-list-items/${itemId} by User ${userId}`);

    if (Object.keys(updates).length === 0) {
        return next(new AppError('No update data provided', 400));
    }

    const forbiddenUpdates = ['_id', 'id', 'shopping_list_id', 'is_custom', 'added_by_user_id', 'createdAt', 'updatedAt', 'contributions'];
    for (const key of forbiddenUpdates) {
        if (Object.prototype.hasOwnProperty.call(updates, key)) {
            return next(new AppError(`Field '${key}' cannot be updated via this route`, 400));
        }
    }

    // --- Authorization and Find Item ---
    // Use findByIdAndUpdate for atomicity and efficiency if possible
    // Need to fetch the associated list/kitchentable separately for auth check first
    const itemForAuth = await ShoppingListItem.findById(itemId).populate({
        path: 'shopping_list_id',
        select: 'kitchentable_id',
        populate: {
            path: 'kitchentable_id',
            select: 'members'
        }
    });

    if (!itemForAuth) {
        return next(new AppError('Shopping list item not found', 404));
    }

    const list = itemForAuth.shopping_list_id;
    const kitchentable = list ? list.kitchentable_id : null;
    if (!list || !kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
         return next(new AppError('User not authorized to update this item', 403));
    }

    // --- Apply Updates ---
    try {
        // Log before update
        helper.devConsole(`[updateShoppingListItem] Attempting to update item ${itemId} with data:`, updates);

        const updatedItem = await ShoppingListItem.findByIdAndUpdate(itemId, updates, {
             new: true, // Return the modified document
             runValidators: true // Run schema validators on update
        });

        if (!updatedItem) {
             helper.devConsole(`[updateShoppingListItem] Item ${itemId} not found during update attempt.`);
             return next(new AppError('Shopping list item not found during update', 404));
        }

        helper.devConsole(`[updateShoppingListItem] Successfully updated item ${itemId}.`);

        // --- Check if all items are now purchased ---
        let promptFinishList = false;
        if (updatedItem.is_purchased) { // Only check if the updated item was marked as purchased
            helper.devConsole(`[updateShoppingListItem] Item ${itemId} marked as purchased. Checking if list ${list._id} is complete.`);
            try {
                // Count items on the same list that are NOT purchased
                const remainingItemsCount = await ShoppingListItem.countDocuments({
                    shopping_list_id: list._id,
                    _id: { $ne: updatedItem._id }, // Exclude the item just updated if needed? No, check all.
                    is_purchased: false
                });

                helper.devConsole(`[updateShoppingListItem] Found ${remainingItemsCount} remaining non-purchased items on list ${list._id}.`);

                if (remainingItemsCount === 0) {
                    promptFinishList = true;
                    helper.devConsole(`[updateShoppingListItem] All items on list ${list._id} are now purchased. Setting promptFinishList = true.`);
                }
            } catch (countError) {
                helper.devConsole(`[updateShoppingListItem] Error counting remaining items for list ${list._id}: ${countError}`);
                // Don't block the main update if count fails, just don't prompt
            }
        }
        // --- End check ---

        // Log before broadcast
        helper.devConsole(`[updateShoppingListItem] Item updated, preparing to broadcast for list ${list._id}, kitchentable ${kitchentable._id}.`);
        const listToBroadcast = await getListAndBroadcast(list._id, kitchentable._id, req.app.locals.broadcastToRoom);
        helper.devConsole(`[updateShoppingListItem] Broadcast prepared/sent.`);

        // --- CORRECTED RESPONSE STRUCTURE ---
        res.status(200).json({
            success: true,
            data: listToBroadcast,
            newItem: updatedItem.toObject(),
            promptFinishList: promptFinishList
        });
        // --- END CORRECTION ---

    } catch (error) {
        helper.devConsole(`[updateShoppingListItem] FAILED to update item ${itemId}: ${error}`); // Log the specific error
        helper.devConsole(error.stack); // Log stack trace for detailed debugging

        if (error.name === 'ValidationError') {
             return next(new AppError(`Invalid update data: ${error.message}`, 400));
        }
        // Handle potential CastError if itemId is invalid format
        if (error.name === 'CastError') {
            return next(new AppError(`Invalid item ID format: ${itemId}`, 400));
        }
        // Generic error for other unexpected issues
        return next(new AppError('Failed to update item', 500));
    }
});

exports.deleteShoppingListItem = catchAsync(async (req, res, next) => {
    const { itemId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`DELETE /api/shopping-list-items/${itemId} by User ${userId}`);

    // --- Authorization and Find Item ---
     const item = await ShoppingListItem.findById(itemId).populate({
        path: 'shopping_list_id',
        select: 'kitchentable_id',
        populate: {
            path: 'kitchentable_id',
            select: 'members'
        }
    });

    if (!item) {
        helper.devConsole(`Item ${itemId} not found for deletion.`);
        return res.status(204).send();
    }

    const list = item.shopping_list_id;
    const kitchentable = list ? list.kitchentable_id : null;
     if (!list || !kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
         return next(new AppError('User not authorized to delete this item', 403));
    }

    // --- Check if item is custom (MVP constraint) ---
    if (!item.is_custom) {
        return next(new AppError('Deleting non-custom items directly is not allowed. Remove the contributing recipe instead.', 403));
    }

    // --- Delete Item ---
    try {
        await ShoppingListItem.findByIdAndDelete(itemId);
        helper.devConsole(`Deleted custom item ${itemId}`);

        await getListAndBroadcast(list._id, kitchentable._id, req.app.locals.broadcastToRoom);

        res.status(204).send();

    } catch (error) {
        helper.devConsole(`Error deleting item ${itemId}: ${error}`);
        return next(new AppError('Failed to delete item', 500));
    }
});

// --- Finish (deactivate) a shopping list ---
exports.finishShoppingList = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`PUT /api/shopping-lists/${listId}/finish by User ${userId}`);

    // --- Authorization and Find List ---
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id',
        select: 'members'
    });

    if (!shoppingList) {
        return next(new AppError('Shopping list not found', 404));
    }

    const kitchentable = shoppingList.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this shopping list', 403));
    }

    // --- Check if already inactive ---
    if (!shoppingList.is_active) {
        helper.devConsole(`List ${listId} is already inactive.`);
        return res.status(200).json({
            success: true,
            message: 'Shopping list is already inactive.'
        });
    }

    // --- Update list to inactive and completed ---
    try {
        // Aktualisiere die Statistik-Felder für die Historie
        const items = await ShoppingListItem.find({ shopping_list_id: listId });
        const totalItems = items.length;
        const completedItems = items.filter(item => item.is_purchased).length;

        shoppingList.is_active = false;
        shoppingList.is_completed = true; // WICHTIG: Auch als abgeschlossen markieren
        shoppingList.item_count = totalItems;
        shoppingList.completed_item_count = completedItems;

        await shoppingList.save();
        helper.devConsole(`Successfully marked shopping list ${listId} as inactive and completed with ${completedItems}/${totalItems} completed items.`);

        // Broadcast the change to all clients
        if (req.app && req.app.locals && req.app.locals.broadcastToRoom) {
            const roomId = `kitchentable_${kitchentable._id}`;
            const listItems = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ is_purchased: 1, name: 1 });
            const listData = { ...shoppingList.toObject(), items: listItems };
            req.app.locals.broadcastToRoom(roomId, 'zettel_updated', listData);
            req.app.locals.broadcastToRoom(roomId, 'zettel_finished', { listId });
            helper.devConsole(`Broadcasted list update and list finished events to room ${roomId}`);
        }

        res.status(200).json({
            success: true,
            message: 'Shopping list marked as inactive and completed successfully.',
            data: {
                listId: shoppingList._id,
                is_active: shoppingList.is_active,
                is_completed: shoppingList.is_completed
            }
        });

    } catch (error) {
        helper.devConsole(`Error marking list ${listId} as inactive: ${error}`);
        return next(new AppError('Failed to mark shopping list as inactive', 500));
    }
});

/**
 * @openapi
 * /api/kitchentables/{kitchentableId}/shopping-lists/history:
 *   get:
 *     tags: [Shopping List]
 *     summary: Holt die Einkaufszettel-Historie für einen Küchentisch.
 *     description: >
 *       Ruft alle abgeschlossenen (inaktiven) Einkaufslisten für den angegebenen Küchentisch ab,
 *       sortiert nach Erstellungsdatum (neueste zuerst).
 *     parameters:
 *       - in: path
 *         name: kitchentableId
 *         required: true
 *         schema:
 *           type: string
 *           description: Die ID des Küchentisches.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Einkaufszettel-Historie erfolgreich abgerufen.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ShoppingList'
 *       401:
 *         description: Nicht autorisiert (Token fehlt, ist ungültig oder User nicht Mitglied).
 *       404:
 *         description: Küchentisch nicht gefunden.
 *       500:
 *         description: Interner Serverfehler.
 */
exports.getShoppingListHistory = catchAsync(async (req, res, next) => {
    const kitchentableId = req.params.kitchentableId;
    const userId = req.user._id;

    helper.devConsole(`[getShoppingListHistory] Start for KT: ${kitchentableId}, User: ${userId}`);

    // 1. Verify User is part of the Kitchentable
    try {
        const kitchentable = await Kitchentable.findOne({ _id: kitchentableId, 'members.userId': userId });
        if (!kitchentable) {
            helper.devConsole(`[getShoppingListHistory] Kitchentable ${kitchentableId} not found or user ${userId} not member.`);
            return next(new AppError('Kitchentable not found or user not authorized', 404));
        }
        helper.devConsole(`[getShoppingListHistory] Kitchentable ${kitchentableId} found and user ${userId} is member.`);
    } catch (err) {
        helper.devConsole(`[getShoppingListHistory] Error finding Kitchentable ${kitchentableId}: ${err}`);
        return next(new AppError('Error verifying kitchentable access', 500));
    }

    // 2. Fetch all shopping lists for this kitchentable (both active and inactive)
    try {
        const shoppingLists = await ShoppingList.find({
            kitchentable_id: kitchentableId
        }).sort({ createdAt: -1 }); // Newest first

        helper.devConsole(`[getShoppingListHistory] Found ${shoppingLists.length} shopping lists for KT: ${kitchentableId}`);

        // Add item counts and optionally items for each list
        const includeItems = req.query.includeItems === 'true';

        const listsWithCounts = await Promise.all(shoppingLists.map(async (list) => {
            const totalItems = await ShoppingListItem.countDocuments({ shopping_list_id: list._id });
            const completedItems = await ShoppingListItem.countDocuments({
                shopping_list_id: list._id,
                is_purchased: true
            });

            const listObj = list.toObject();
            listObj.item_count = totalItems;
            listObj.completed_item_count = completedItems;

            // Optionally include items if requested
            if (includeItems) {
                const items = await ShoppingListItem.find({ shopping_list_id: list._id })
                    .sort({ is_purchased: 1, name: 1 });
                listObj.items = items;
            }

            return listObj;
        }));

        res.status(200).json({
            success: true,
            data: listsWithCounts
        });
    } catch (err) {
        helper.devConsole(`[getShoppingListHistory] Error fetching shopping lists for KT ${kitchentableId}: ${err}`);
        return next(new AppError('Error fetching shopping list history', 500));
    }
});

/**
 * @openapi
 * /api/shopping-lists/{listId}/activate:
 *   put:
 *     tags: [Shopping List]
 *     summary: Aktiviert eine historische Einkaufsliste.
 *     description: >
 *       Aktiviert eine historische (inaktive) Einkaufsliste und deaktiviert die aktuell aktive Liste.
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: string
 *           description: Die ID der zu aktivierenden Einkaufsliste.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Einkaufsliste erfolgreich aktiviert.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/ShoppingList'
 *       401:
 *         description: Nicht autorisiert (Token fehlt, ist ungültig oder User nicht Mitglied).
 *       404:
 *         description: Einkaufsliste nicht gefunden.
 *       500:
 *         description: Interner Serverfehler.
 */
exports.activateShoppingList = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`[activateShoppingList] PUT /api/shopping-lists/${listId}/activate by User ${userId}`);

    // 1. Find the list and verify access
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id',
        select: 'members'
    });

    if (!shoppingList) {
        return next(new AppError('Shopping list not found', 404));
    }

    const kitchentable = shoppingList.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this shopping list', 403));
    }

    // 2. Check if the list is already active
    if (shoppingList.is_active) {
        helper.devConsole(`[activateShoppingList] List ${listId} is already active.`);
        return res.status(200).json({
            success: true,
            message: 'Shopping list is already active.',
            data: shoppingList
        });
    }

    // 3. Check if the list is completed
    if (shoppingList.is_completed) {
        helper.devConsole(`[activateShoppingList] List ${listId} is completed and cannot be reactivated.`);
        return next(new AppError('Abgeschlossene Listen können nicht reaktiviert werden', 400));
    }

    // 3. Deactivate any currently active list for this kitchentable
    try {
        await ShoppingList.updateMany(
            { kitchentable_id: kitchentable._id, is_active: true },
            { is_active: false }
        );
        helper.devConsole(`[activateShoppingList] Deactivated any active lists for KT ${kitchentable._id}`);
    } catch (err) {
        helper.devConsole(`[activateShoppingList] Error deactivating active lists: ${err}`);
        return next(new AppError('Error deactivating current active list', 500));
    }

    // 4. Activate the requested list
    try {
        shoppingList.is_active = true;
        await shoppingList.save();
        helper.devConsole(`[activateShoppingList] Successfully activated shopping list ${listId}`);

        // 5. Broadcast the change if needed
        if (req.app && req.app.locals && req.app.locals.broadcastToRoom) {
            await getListAndBroadcast(listId, kitchentable._id, req.app.locals.broadcastToRoom);
        }

        res.status(200).json({
            success: true,
            message: 'Shopping list activated successfully.',
            data: shoppingList
        });
    } catch (err) {
        helper.devConsole(`[activateShoppingList] Error activating list ${listId}: ${err}`);
        return next(new AppError('Error activating shopping list', 500));
    }
});

/**
 * @openapi
 * /api/shopping-lists/{listId}/categorize-items:
 *   post:
 *     tags: [Shopping List]
 *     summary: Kategorisiert alle Artikel einer Einkaufsliste.
 *     description: >
 *       Kategorisiert alle Artikel einer Einkaufsliste, die noch keine Kategorie haben.
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: string
 *           description: Die ID der Einkaufsliste.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Artikel erfolgreich kategorisiert.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "5 Artikel wurden kategorisiert"
 *       401:
 *         description: Nicht autorisiert (Token fehlt, ist ungültig oder User nicht Mitglied).
 *       404:
 *         description: Einkaufsliste nicht gefunden.
 *       500:
 *         description: Interner Serverfehler.
 */
exports.categorizeListItems = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`[categorizeListItems] POST /api/shopping-lists/${listId}/categorize-items by User ${userId}`);

    // 1. Find the list and verify access
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id',
        select: 'members'
    });

    if (!shoppingList) {
        return next(new AppError('Shopping list not found', 404));
    }

    const kitchentable = shoppingList.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this shopping list', 403));
    }

    // 2. Find all items without a category or with 'Sonstiges' category
    const items = await ShoppingListItem.find({
        shopping_list_id: listId,
        $or: [
            { category: { $exists: false } },
            { category: 'Sonstiges' }
        ]
    });

    helper.devConsole(`[categorizeListItems] Found ${items.length} items to categorize for list ${listId}`);

    // 3. Categorize each item
    let updated = 0;
    for (const item of items) {
        const oldCategory = item.category || 'None';
        item.category = categoryHelper.categorizeItem(item.name);

        // Only save if category changed
        if (oldCategory !== item.category) {
            await item.save();
            updated++;
            helper.devConsole(`[categorizeListItems] Categorized item "${item.name}" from "${oldCategory}" to "${item.category}"`);
        }
    }

    // 4. Broadcast the change if needed
    if (updated > 0 && req.app && req.app.locals && req.app.locals.broadcastToRoom) {
        await getListAndBroadcast(listId, kitchentable._id, req.app.locals.broadcastToRoom);
    }

    res.status(200).json({
        success: true,
        message: `${updated} Artikel wurden kategorisiert`
    });
});

// --- Update shopping list name ---
exports.updateShoppingListName = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const { name } = req.body;
    const userId = req.user.id;

    helper.devConsole(`PUT /api/shopping-lists/${listId}/name by User ${userId}`);

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
        return next(new AppError('Name is required and must be a non-empty string', 400));
    }

    // --- Authorization and Find List ---
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id',
        select: 'members'
    });

    if (!shoppingList) {
        return next(new AppError('Shopping list not found', 404));
    }

    const kitchentable = shoppingList.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this shopping list', 403));
    }

    // --- Update the name ---
    try {
        shoppingList.name = name.trim();
        await shoppingList.save();

        helper.devConsole(`Successfully updated shopping list ${listId} name to: ${name.trim()}`);

        // Broadcast the change if needed
        if (req.app && req.app.locals && req.app.locals.broadcastToRoom) {
            const roomId = `kitchentable_${kitchentable._id}`;
            req.app.locals.broadcastToRoom(roomId, 'shopping_list_name_updated', {
                listId: listId,
                name: shoppingList.name
            });
        }

        res.status(200).json({
            success: true,
            message: 'Shopping list name updated successfully.',
            data: {
                _id: shoppingList._id,
                name: shoppingList.name,
                is_active: shoppingList.is_active
            }
        });
    } catch (err) {
        helper.devConsole(`Error updating shopping list name ${listId}: ${err}`);
        return next(new AppError('Error updating shopping list name', 500));
    }
});

// --- Synchronize offline changes ---
exports.syncOfflineChanges = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const userId = req.user.id;
    const actions = req.body; // Expecting an array of actions

    helper.devConsole(`POST /api/shopping-lists/${listId}/sync-offline-changes by User ${userId} with ${actions?.length || 0} actions.`);

    // --- Basic Input Validation ---
    if (!Array.isArray(actions)) {
        return next(new AppError('Request body must be an array of actions.', 400));
    }
    if (actions.length === 0) {
        helper.devConsole(`Sync request for list ${listId} received with 0 actions. Nothing to do.`);
        // Fetch and return the current list state as if sync was successful
        // This avoids the frontend needing a separate fetch after an empty sync
        const currentList = await ShoppingList.findById(listId)
            .populate({
                path: 'items', // Populate items directly if using virtual populate or adjust as needed
                options: { sort: { is_purchased: 1, name: 1 } }
            });
             // If not using virtual populate, fetch items separately:
             // const items = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ is_purchased: 1, name: 1 });
             // const listData = currentList.toObject();
             // listData.items = items;

        if (!currentList) {
            return next(new AppError('Shopping list not found', 404));
        }
        // Need to ensure user is authorized even for empty sync
        const listForAuth = await ShoppingList.findById(listId).populate({ path: 'kitchentable_id', select: 'members' });
        const kitchentable = listForAuth?.kitchentable_id;
        if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
            return next(new AppError('User not authorized for this shopping list', 403));
        }

        // Populate items if not done above
        const items = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ is_purchased: 1, name: 1 });
        const listData = currentList.toObject();
        listData.items = items;

        return res.status(200).json({
            success: true,
            data: listData // Return current state
        });
    }

    // --- Authorization ---
    // Fetch list and kitchentable for auth check
    const shoppingList = await ShoppingList.findById(listId).populate({
        path: 'kitchentable_id',
        select: 'members'
    });

    if (!shoppingList) {
        return next(new AppError('Shopping list not found', 404));
    }

    const kitchentable = shoppingList.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this shopping list', 403));
    }

    // --- Process Actions (Last Write Wins) ---
    let changesMade = false;
    const appliedActionIds = []; // Keep track of applied actions for logging/debugging
    const ignoredActionIds = [];  // Keep track of ignored actions

    // Use a loop that allows async operations inside (e.g., for...of)
    for (const action of actions) {
        // Validate individual action structure
        if (!action.itemId || !action.field || action.value === undefined || !action.timestamp) {
            helper.devConsole(`Invalid action structure in sync request for list ${listId}:`, action);
            // Decide: skip this action or fail the whole request?
            // Let's skip for now, could make it stricter.
            ignoredActionIds.push(action.itemId || 'unknown');
            continue;
        }

        // Validate field (currently only support is_purchased)
        if (action.field !== 'is_purchased') {
            helper.devConsole(`Unsupported field '${action.field}' in sync action for list ${listId}. Skipping.`);
            ignoredActionIds.push(action.itemId);
            continue;
        }

        // Validate timestamp format (basic check)
        const clientTimestamp = new Date(action.timestamp);
        if (isNaN(clientTimestamp.getTime())) {
             helper.devConsole(`Invalid timestamp format '${action.timestamp}' in sync action for list ${listId}. Skipping.`);
             ignoredActionIds.push(action.itemId);
            continue;
        }

        try {
            // Find the item on the server
            const item = await ShoppingListItem.findById(action.itemId);

            if (!item) {
                helper.devConsole(`Item ${action.itemId} not found on server for sync action (list ${listId}). Skipping.`);
                ignoredActionIds.push(action.itemId);
                continue;
            }

            // Ensure the item belongs to the correct list (important!)
            if (item.shopping_list_id.toString() !== listId) {
                 helper.devConsole(`Item ${action.itemId} does not belong to list ${listId}. Skipping sync action.`);
                 ignoredActionIds.push(action.itemId);
                 continue;
            }

            // Compare timestamps (Last Write Wins)
            const serverUpdatedAt = item.updatedAt; // Mongoose Date object
            helper.devConsole(`Comparing timestamps for item ${item._id}: Client=${clientTimestamp.toISOString()}, Server=${serverUpdatedAt.toISOString()}`);

            if (clientTimestamp > serverUpdatedAt) {
                helper.devConsole(`Applying change for item ${item._id}: ${action.field} = ${action.value}`);
                // Apply the change
                item[action.field] = action.value;
                // Save the item (Mongoose automatically updates `updatedAt` on save)
                await item.save();
                // changesMade = true; // This variable seems unused, commenting out to fix linter error
                appliedActionIds.push(item._id.toString());
            } else {
                helper.devConsole(`Ignoring change for item ${item._id}: Server version is newer or same.`);
                ignoredActionIds.push(item._id.toString());
            }

        } catch (error) {
            helper.devConsole(`Error processing sync action for item ${action.itemId} on list ${listId}: ${error}`);
            // Decide: skip action, fail request, or just log?
            // Let's log and skip the specific action for robustness.
            ignoredActionIds.push(action.itemId);
        }
    }

    helper.devConsole(`Sync finished for list ${listId}. Applied: ${appliedActionIds.length}, Ignored: ${ignoredActionIds.length}`);

    // --- Fetch Final State & Respond ---
    try {
        // Refetch the entire list with all items to ensure consistency
        const finalList = await ShoppingList.findById(listId);
        const finalItems = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ is_purchased: 1, name: 1 });

        if (!finalList) {
            // Should not happen if initial check passed, but handle defensively
            return next(new AppError('Shopping list not found after sync', 404));
        }

        const responseData = finalList.toObject();
        responseData.items = finalItems; // Attach the final items

        helper.devConsole(`Responding to sync request for list ${listId} with final state.`);
        res.status(200).json({
            success: true,
            data: responseData
        });

        // if (changesMade) { // Commenting out as changesMade is unused
        //     const broadcastToRoom = req.app.locals.broadcastToRoom;
        //     broadcastToRoom(`kitchentable_${kitchentable._id}`, 'zettel_updated', responseData);
        // }

    } catch (fetchError) {
        helper.devConsole(`Error fetching final list state after sync for list ${listId}: ${fetchError}`);
        return next(new AppError('Could not retrieve final list state after sync', 500));
    }
});

/**
 * @desc    Update the purchase status of a shopping list item via WebSocket
 * @param   {string} itemId - The ID of the ShoppingListItem to update.
 * @param   {boolean} isPurchased - The new purchase status.
 * @param   {string} userId - The ID of the user performing the action (for authorization).
 * @param   {string} kitchentableId - The ID of the kitchentable context (for authorization).
 * @returns {object|null} The updated shopping list item object, or null if not found/authorized.
 * @throws  {AppError} If authorization fails or database update fails.
 */
exports.updateItemPurchaseStatus = async (itemId, isPurchased, userId, kitchentableId) => {
    const functionName = 'shoppingListController.updateItemPurchaseStatus';
    helper.devConsole(`[${functionName}] Attempting to update item ${itemId} to isPurchased=${isPurchased} by User ${userId} in context of Kitchentable ${kitchentableId}`);

    // 1. Basic Validation
    if (!mongoose.Types.ObjectId.isValid(itemId) || !mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(kitchentableId)) {
        helper.devConsole(`[${functionName}] Invalid ID format provided.`);
        // We throw here because this indicates a fundamental issue likely needing a code fix or preventing DB query
        throw new AppError('Invalid ID format provided.', 400);
    }
    if (typeof isPurchased !== 'boolean') {
         helper.devConsole(`[${functionName}] Invalid isPurchased value: ${isPurchased}`);
         throw new AppError('Invalid isPurchased value, must be boolean.', 400);
    }

    // 2. Authorization Check: Ensure the user belongs to the kitchentable
    // We use the kitchentableId passed directly from the WebSocket context
    const kitchentable = await Kitchentable.findOne({
        _id: kitchentableId,
        'members.userId': userId
    });

    if (!kitchentable) {
        helper.devConsole(`[${functionName}] Authorization Failed: User ${userId} is not a member of Kitchentable ${kitchentableId}.`);
        // Return null or throw? Throwing might be better to signal failure clearly back to WS handler.
        throw new AppError('User not authorized for this kitchentable.', 403); // 403 Forbidden
    }
    helper.devConsole(`[${functionName}] Authorization OK: User ${userId} is member of Kitchentable ${kitchentableId}.`);

    // 3. Find and Update the Item
    // We also check if the item belongs to a list associated with the authorized kitchentable
    // This requires joining or checking the shoppingListId from the item

    const item = await ShoppingListItem.findById(itemId);

    if (!item) {
        helper.devConsole(`[${functionName}] Item ${itemId} not found.`);
         throw new AppError('Shopping list item not found.', 404);
    }

    // Optional but recommended: Verify item belongs to a list of the correct kitchentable
    const list = await ShoppingList.findOne({ _id: item.shopping_list_id, kitchentable_id: kitchentableId });
    if (!list) {
        helper.devConsole(`[${functionName}] Security Check Failed: Item ${itemId} does not belong to a shopping list associated with Kitchentable ${kitchentableId}.`);
         throw new AppError('Item does not belong to an accessible shopping list.', 403);
    }
    helper.devConsole(`[${functionName}] Security Check OK: Item ${itemId} belongs to list ${list._id} of Kitchentable ${kitchentableId}.`);

    // Prüfen ob das Item als gekauft markiert werden soll und ob es das letzte ist
    if (isPurchased) {
        // Zähle verbleibende nicht gekaufte Items (außer dem aktuellen)
        const remainingUnchecked = await ShoppingListItem.countDocuments({
            shopping_list_id: item.shopping_list_id,
            is_purchased: false,
            _id: { $ne: itemId }
        });

        helper.devConsole(`[${functionName}] Remaining unchecked items: ${remainingUnchecked}`);

        if (remainingUnchecked === 0) {
            helper.devConsole(`[${functionName}] This is the last unchecked item! Sending confirmation request signal.`);
            // Nicht speichern, stattdessen Signal senden
            return {
                isLastItem: true,
                itemId: itemId,
                listId: item.shopping_list_id
            };
        }
    }

    // Perform the update
    item.is_purchased = isPurchased;
    // Potentially update a timestamp field if needed, e.g., item.lastUpdatedBy = userId;
    await item.save();

    helper.devConsole(`[${functionName}] Successfully updated item ${itemId} isPurchased status to ${isPurchased}.`);

    // 4. Return the updated item
    return {
        isLastItem: false,
        itemId: item._id,
        isPurchased: item.is_purchased
    };

    // Note: No catchAsync wrapper here as it's called internally by the WS handler's async IIFE
    // The try/catch in the WS handler will catch errors thrown from here.
};

/**
 * @desc    Add a manual item to the active shopping list via WebSocket.
 * @param   {string} kitchentableId - The ID of the kitchentable.
 * @param   {string} userId - The ID of the user adding the item.
 * @param   {string} name - The name of the item.
 * @param   {string|number} quantity - The quantity of the item.
 * @param   {string|null} unit - The unit of the item (optional).
 * @returns {object} The newly created shopping list item object.
 * @throws  {AppError} If authorization fails, list cannot be found/created, or item creation fails.
 */
exports.addManualItemViaWebSocket = async (kitchentableId, userId, name, quantity, unit) => {
    const functionName = 'shoppingListController.addManualItemViaWebSocket';
    helper.devConsole(`[${functionName}] User ${userId} adding manual item "${name}" to KT ${kitchentableId}`);

    // 1. Basic Validation (IDs checked upstream, focus on item data)
    if (!name || String(name).trim() === '' || quantity === undefined || quantity === null) {
        throw new AppError('Item name and quantity are required.', 400);
    }

    // 2. Authorization Check: Ensure the user belongs to the kitchentable
    const kitchentable = await Kitchentable.findOne({
        _id: kitchentableId,
        'members.userId': userId
    });
    if (!kitchentable) {
        throw new AppError('User not authorized for this kitchentable.', 403);
    }
    helper.devConsole(`[${functionName}] Authorization OK for User ${userId} on KT ${kitchentableId}.`);

    // 3. Find or Create Active Shopping List for the Kitchentable
    let shoppingList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true });

    // Check if the active list is completed
    if (shoppingList && shoppingList.is_completed) {
        throw new AppError('Abgeschlossene Listen können nicht bearbeitet werden', 400);
    }

    if (!shoppingList) {
        helper.devConsole(`[${functionName}] No active list found for KT ${kitchentableId}. Creating new one.`);
        try {
            const currentWeek = getWeekNumber(new Date());
            const listName = `Einkaufszettel KW${currentWeek}`;
            shoppingList = await ShoppingList.create({
                kitchentable_id: kitchentableId,
                name: listName,
                is_active: true,
            });
             helper.devConsole(`[${functionName}] Created new active ShoppingList ${shoppingList._id}`);
        } catch (creationError) {
             helper.devConsole(`[${functionName}] Error creating ShoppingList: ${creationError}`);
             throw new AppError('Could not create shopping list for manual item.', 500);
        }
    }
     helper.devConsole(`[${functionName}] Using active ShoppingList ${shoppingList._id}`);

    // 4. Create the new ShoppingListItem
    try {
        // Automatische Kategorisierung für WebSocket-Items
        const category = categoryHelper.categorizeItem(String(name).trim());
        helper.devConsole(`[${functionName}] Auto-categorized item "${name}" as "${category}"`);

        const newItem = await ShoppingListItem.create({
            shopping_list_id: shoppingList._id,
            name: String(name).trim(), // Ensure name is a string and trimmed
            quantity: quantity, // Store as provided (can be number or string)
            unit: unit || null, // Use provided unit or null
            is_custom: true,    // Mark as custom item
            is_purchased: false, // Default to not purchased
            added_by_user_id: userId,
            category: category  // Add category
        });
         helper.devConsole(`[${functionName}] Successfully created new manual item ${newItem._id} on list ${shoppingList._id}`);
         return newItem; // Return the newly created item object
    } catch (itemCreationError) {
         helper.devConsole(`[${functionName}] Error creating ShoppingListItem: ${itemCreationError}`);
         // Check for validation errors specifically
         if (itemCreationError.name === 'ValidationError') {
             throw new AppError(`Invalid item data: ${itemCreationError.message}`, 400);
         }
         throw new AppError('Could not save manual item to the shopping list.', 500);
    }
    // Note: No catchAsync needed here either.
};

// Neue Funktion für das Bestätigen des letzten Items und Abschließen der Liste
exports.confirmPurchaseAndFinishList = catchAsync(async (req, res, next) => {
    const { itemId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`POST /api/shopping-list-items/${itemId}/confirm-and-finish by User ${userId}`);

    // Finde das Item und prüfe die Berechtigung
    const item = await ShoppingListItem.findById(itemId).populate({
        path: 'shopping_list_id',
        populate: {
            path: 'kitchentable_id',
            select: 'members'
        }
    });

    if (!item) {
        return next(new AppError('Item not found', 404));
    }

    // Autorisierungsprüfung
    const kitchentable = item.shopping_list_id.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('User not authorized for this item', 403));
    }

    // Markiere das Item als gekauft
    item.is_purchased = true;
    await item.save();
    helper.devConsole(`Marked item ${itemId} as purchased`);

    // Markiere die Liste als inaktiv und abgeschlossen
    const list = item.shopping_list_id;
    list.is_active = false;
    list.is_completed = true;
    await list.save();
    helper.devConsole(`Marked list ${list._id} as inactive and completed (finished)`);

    // Broadcast an alle Clients im gleichen Kitchentable-Raum
    if (req.app && req.app.locals && req.app.locals.broadcastToRoom) {
        const roomId = `kitchentable_${kitchentable._id}`;
        // Event zum Aktualisieren der Liste senden
        const listItems = await ShoppingListItem.find({ shopping_list_id: list._id }).sort({ is_purchased: 1, name: 1 });
        const listData = { ...list.toObject(), items: listItems };
        req.app.locals.broadcastToRoom(roomId, 'zettel_updated', listData);
        // Zusätzliches Event zum Anzeigen einer Erfolgsmeldung senden
        req.app.locals.broadcastToRoom(roomId, 'zettel_finished', { listId: list._id });
        helper.devConsole(`Broadcasted list update and list finished events to room ${roomId}`);
    }

    res.status(200).json({
        success: true,
        message: 'Shopping list item marked as purchased and list finished',
        data: {
            itemId: item._id,
            listId: list._id,
            listFinished: true
        }
    });
});

// Update shopping list name
exports.updateShoppingListName = catchAsync(async (req, res, next) => {
    const { listId } = req.params;
    const { name } = req.body;
    const userId = req.user.id;

    helper.devConsole(`[updateShoppingListName] Start for List: ${listId}, User: ${userId}, Name: ${name}`);

    // Validate input
    if (!name || typeof name !== 'string' || !name.trim()) {
        return next(new AppError('Name ist erforderlich', 400));
    }

    // Find the shopping list
    const list = await ShoppingList.findById(listId).populate('kitchentable_id');
    if (!list) {
        return next(new AppError('Einkaufsliste nicht gefunden', 404));
    }

    // Check if user is authorized (member of kitchentable)
    const kitchentable = list.kitchentable_id;
    if (!kitchentable || !kitchentable.members || !kitchentable.members.some(member => member.userId.toString() === userId.toString())) {
        return next(new AppError('Nicht berechtigt, diese Liste zu bearbeiten', 403));
    }

    // Check if list is completed
    if (list.is_completed) {
        return next(new AppError('Abgeschlossene Listen können nicht bearbeitet werden', 400));
    }

    // Update the name
    list.name = name.trim();
    await list.save();

    helper.devConsole(`[updateShoppingListName] Updated list ${listId} name to: ${name}`);

    // Broadcast the name change to all clients
    if (req.app && req.app.locals && req.app.locals.broadcastToRoom) {
        const roomId = `kitchentable_${kitchentable._id}`;
        req.app.locals.broadcastToRoom(roomId, 'shopping_list_name_updated', {
            listId: listId,
            name: list.name
        });
        helper.devConsole(`Broadcasted name update to room ${roomId}`);
    }

    res.status(200).json({
        success: true,
        message: 'Listenname erfolgreich aktualisiert',
        data: {
            listId: list._id,
            name: list.name
        }
    });
});

module.exports = exports; // Ensure this line is correct if using CommonJS exports pattern

