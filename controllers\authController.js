const { promisify } = require('util')
const { Buffer } = require('buffer');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const stytch = require('stytch');
const helper = require('../utils/helper');
const jwt = require('jsonwebtoken')
const User = require('../models/userModel');
const mongoose = require('mongoose');
const paymentController = require('../controllers/paymentController')
const gptController = require('../controllers/gptController')

//const {expressjwt: jwt } = require("express-jwt");
const jwksRsa = require('jwks-rsa');

const envMode = process.env.NODE_ENV
const jwtSecret = process.env.JWT_SECRET
const jwtExpiresIn = process.env.JWT_EXPIRES_IN
const username_sys = process.env.BASEAUTH_USERNAME
const password_sys = process.env.BASEAUTH_PASSWORD

const client = new stytch.Client({
    project_id: envMode === "development" || envMode === "preview"
        ? process.env.STYTCH_PID_DEV
        : process.env.STYTCH_PID_PROD,

    secret: envMode === "development" || envMode === "preview"
    ? process.env.STYTCH_PASSWORD_DEV
    : process.env.STYTCH_PASSWORD_PROD,

    env: envMode === "development" || envMode === "preview"
      ? stytch.envs.test
      : stytch.envs.live
  });

 //console.log(client)

const signToken = async(id) => {
    // Log the ID being used to sign the token
    // helper.devConsole(`[signToken] Signing token for ID: ${id} (Type: ${typeof id})`);
    return jwt.sign({ id }, jwtSecret,{
        expiresIn: jwtExpiresIn
    });
}

// old v
// see video here (signuser) https://www.udemy.com/course/nodejs-express-mongodb-bootcamp/learn/lecture/15065298#overview
/*exports.regnewapiuser = = catchAsync(async (req, res, next) => {

}*/

// @desc     Standard Auth
// @route    POST /api/v1/auth/reg
exports.auth = catchAsync(async (req, res, next) => {
    helper.devConsole("/api/v1/auth/reg");
    const { token } = req.query;
    helper.devConsole(`Stytch Token: ${token}`);

    if (!token) {
        return next(new AppError('Stytch token missing', 400));
    }

    try {
        // 1. Authenticate Stytch Token
        const stytchResponse = await client.oauth.authenticate(token, {
            session_management_type: 'stytch',
            session_duration_minutes: 23040 // 16 days (16 * 24 * 60)
        });
        helper.devConsole("Stytch authentication successful");
        // Detailliertes Logging der Stytch-Antwort
        helper.devConsole("Stytch Response:", JSON.stringify(stytchResponse, null, 2));

        // 2. Extract necessary info (with checks)
        const stytchUserID = stytchResponse.user_id;
        const providerData = stytchResponse.user.providers && stytchResponse.user.providers[0];
        const providerType = stytchResponse.provider_type;
        const firstName = stytchResponse.user.name?.first_name || '';
        const lastName = stytchResponse.user.name?.last_name || '';
        const email = stytchResponse.user.emails && stytchResponse.user.emails.length > 0 ? stytchResponse.user.emails[0].email : null;
        const img = providerData?.profile_picture_url || '';
        const providerSubject = providerData?.provider_subject; // Use provider_subject for lookup if available

        if (!stytchUserID) {
            return next(new AppError('Missing user ID from Stytch response', 500));
        }
        // Optional: Check for providerSubject if you use that for lookup
        // if (!providerSubject) {
        //     return next(new AppError('Missing provider subject from Stytch response', 500));
        // }

        // Store Stytch session info for redirect
        const sessionToken = stytchResponse.session?.stytch_session?.session_token;
        const sessionJwt = stytchResponse.session?.stytch_session?.session_jwt;
        const sessionExpiresAt = stytchResponse.session?.expires_at; // Extract expires_at

        // 3. Check if user exists in local DB (using Stytch User ID as extId)
        let existingUser = await User.findOne({ extId: stytchUserID });
        // Alternative lookup using provider subject:
        // let existingUser = await User.findOne({ 'providers.provider_subject': providerSubject, 'providers.provider_type': providerType });

        let ourUser;
        let isNewUser = false;

        if (existingUser) {
            //////////////////////// LOGIN FLOW ///////////////////////
            helper.devConsole(`User found in local DB: ${existingUser._id}`);
            ourUser = existingUser;
            // Optional: Update user data from Stytch if necessary (e.g., name, image)
            // existingUser.firstName = firstName;
            // existingUser.lastName = lastName;
            // existingUser.img = img;
            // await existingUser.save({ validateBeforeSave: false });
            isNewUser = false;

        } else {
            //////////////////////// REGISTRATION FLOW ///////////////////////
            helper.devConsole(`User not found with extId ${stytchUserID}. Creating new user...`);
            if (!email) {
                // Optional: Handle case where email is missing but required for new user
                // return next(new AppError('Email is required for new user registration', 400));
                 helper.devConsole("Warning: Email is missing from Stytch response for new user.");
            }

            const newUserPayload = {
                extId: stytchUserID,
                extAuthService: providerType,
                firstName: firstName,
                lastName: lastName,
                email: email, // Might be null if not provided by Stytch
                img: img,
                explanation_trail: 0, // Initialize with default (although Schema default works too)
                // Add default role or other necessary fields
                role: 'user'
                // Example if using provider subject in schema:
                // providers: [{ provider_type: providerType, provider_subject: providerSubject }]
            };

            const newUser = await User.create(newUserPayload);
            helper.devConsole(`New user created with ID: ${newUser._id}`);
            ourUser = newUser;
            isNewUser = true;

            // Generate automatic profile image for new user (non-blocking)
            if (!img || img === '') {
                helper.devConsole("No profile image from OAuth provider, generating AI avatar...");
                // Don't await this - let it run in background
                gptController.generateProfileImageForNewUser(newUser._id, firstName)
                    .then(result => {
                        if (result.success) {
                            helper.devConsole(`Auto profile image generated successfully: ${result.fileName}`);
                        } else {
                            helper.devConsole(`Auto profile image generation failed: ${result.error}`);
                        }
                    })
                    .catch(error => {
                        helper.devConsole(`Auto profile image generation error: ${error.message}`);
                    });
            } else {
                helper.devConsole("Profile image provided by OAuth provider, skipping AI generation");
            }

            // Call Payment Controller Create Customer // Temporarily commented out
            // paymentController.createCustomer(email, "test")
            helper.devConsole("Temporarily skipped paymentController.createCustomer call during registration.");

        }

        // 4. Create JWT token for our application session - REMOVED as we now rely on Stytch session token
        // if (!ourUser || !ourUser._id) {
        //      return next(new AppError('Failed to get user information for token signing', 500));
        // }
        // jwtToken = await signToken(ourUser._id); // NO LONGER NEEDED
        // helper.devConsole(`JWT token created for user ID: ${ourUser._id}`);

        // 5. Prepare redirect URL
        const redirectBaseUrl =
            envMode === "production" ? "https://www.ordy.ch" :
            envMode === "preview" ? "https://test.ordy.ch" :
            "http://localhost:5173"; // Default to development

        // Build query parameters carefully, handling potentially missing values
        const queryParams = new URLSearchParams();
        if (sessionToken) queryParams.set('session_token', sessionToken);
        if (sessionJwt) queryParams.set('session_jwt', sessionJwt); // Consider if frontend really needs this
        if (sessionExpiresAt) queryParams.set('session_expires_at', sessionExpiresAt); // Add session_expires_at
        if (stytchUserID) queryParams.set('user_id', stytchUserID); // Stytch User ID
        if (firstName) queryParams.set('first_name', firstName);
        if (lastName) queryParams.set('last_name', lastName);
        if (img) queryParams.set('img', img);
        if (email) queryParams.set('email', email); // Email from Stytch
        // if (jwtToken) queryParams.set('backendtoken', jwtToken); // REMOVED - Our backend token is no longer needed
        if (ourUser._id) queryParams.set('id', ourUser._id.toString()); // Our MongoDB User ID
        if (ourUser.defaultKitchentable) queryParams.set('defaultKitchentable', ourUser.defaultKitchentable);
        if (ourUser.explanation_trail !== undefined) queryParams.set('explanation_trail', ourUser.explanation_trail.toString()); // Add explanation_trail
        if (ourUser.externalAccess !== undefined) queryParams.set('externalAccess', ourUser.externalAccess.toString()); // Ensure boolean is string
        if (isNewUser) queryParams.set('isNewUser', 'true'); // Optional flag for frontend

        const redirectUrl = `${redirectBaseUrl}?${queryParams.toString()}`;

        helper.devConsole(`Redirecting to: ${redirectUrl}`);
        res.redirect(redirectUrl);

    } catch (error) {
        // Verfeinertes Logging für Fehler
        helper.devConsole("Error during Stytch OAuth authentication or user processing:");
        helper.devConsole("Fehler-Objekt:", JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
        if (error.response) {
            helper.devConsole("Stytch Error Response:", JSON.stringify(error.response, null, 2));
        }
        if (error.error_type) {
            helper.devConsole(`Stytch Error Type: ${error.error_type}`);
        }
        if (error.error_message) {
            helper.devConsole(`Stytch Error Message: ${error.error_message}`);
        }
        return next(new AppError(error.message || 'Stytch OAuth authentication failed', 500));
    }
});

// @desc     Veryfiy the login
// @route    POST /api/v1/auth/logout
// shipped auth, go live problems

exports.logout = catchAsync(async (req, res, next) => {
        helper.devConsole("ROUTE /auth/logout")

        const token = req.headers['authorization']?.split(' ')[1];
        helper.devConsole("Token found in header:", !!token);

        if (token) { // Nur versuchen zu widerrufen, wenn ein Token vorhanden ist
            try {
                const logoutResult = await client.sessions.revoke({
                    session_token: token
                });
                helper.devConsole("Stytch revoke result:", logoutResult);
            } catch (error) {
                helper.devConsole("Error revoking Stytch session:", error.error_type || error.message);
                // Fehler ignorieren, wenn die Sitzung sowieso nicht gefunden wurde oder der Token ungültig war.
                // Stytch Error Types: https://stytch.com/docs/api/errors
                if (error.error_type !== 'session_not_found' &&
                    error.error_type !== 'invalid_session_token' &&
                    error.error_type !== 'no_session_revoke_arguments')
                {
                    // Bei anderen Fehlern -> trotzdem weitergeben (optional, könnte auch ignoriert werden)
                    // return next(new AppError('Stytch logout failed', 500));
                    helper.devConsole("Ignoring Stytch revoke error as session likely already invalid.");
                }
            }
        }

        // Unabhängig vom Stytch-Ergebnis (solange kein schwerwiegender Fehler auftrat):
        // Lokalen Cookie löschen (falls verwendet) und Erfolg melden.
        // TODO: Fügen Sie hier ggf. das Löschen des Cookies hinzu, z.B.:
        // res.cookie('jwt', 'loggedout', { expires: new Date(Date.now() + 10 * 1000), httpOnly: true });

        helper.devConsole("Logout considered successful.");
        return res.status(200).json({ success: true, message: 'Logout success' });

})

// @desc     Verify the login session using Stytch session token
// @route    Middleware for protected routes
exports.verify = catchAsync(async (req, res, next) => {
    const functionName = 'authController.verify'; // For consistent logging
    helper.devConsole(`[${functionName}] Start - Verifying Stytch Session`);

    let sessionToken;
    // 1. Check if Stytch session token exists (sent in Authorization header)
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        sessionToken = req.headers.authorization.split(' ')[1];
        // Log a masked version of the token for security
        const maskedToken = sessionToken.length > 20 ? `${sessionToken.substring(0, 10)}...${sessionToken.substring(sessionToken.length - 5)}` : `${sessionToken.substring(0,3)}...`;
        helper.devConsole(`[${functionName}] Extracted Token: ${maskedToken}`);
    }

    if (!sessionToken) {
        helper.devConsole(`[${functionName}] Stytch session token missing.`);
        return next(new AppError('Authentication token missing. Not a Bearer token or token not provided.', 401));
    }

    try {
        // 2. Verify the token using Stytch client
        helper.devConsole(`[${functionName}] Authenticating Stytch session token...`);
        const stytchResponse = await client.sessions.authenticate({
            session_token: sessionToken,
            session_duration_minutes: 23040, // CORRECTED: Restore longer session duration
        });
        helper.devConsole(`[${functionName}] Stytch session authentication successful.`);

        // CORRECTED: Access user_id from stytchResponse.session.user_id
        const stytchUserId = stytchResponse.session.user_id;

        // 3. If token is valid, there will be a user on the response
        // Check if user exists in DB (Stytch user_id is our extId)
        const user = await User.findOne({ extId: stytchUserId }); // Use corrected stytchUserId
        if (!user) {
            helper.devConsole(`[${functionName}] User for Stytch ID ${stytchUserId} not found in our DB.`); // Use corrected stytchUserId
            return next(new AppError('User belonging to this token does no longer exist.', 401));
        }
        helper.devConsole(`[${functionName}] User ${user.id} (extId: ${stytchUserId}) found in DB.`); // Use corrected stytchUserId

        // 4. Grant access to protected route, User is on req
        req.user = user; // Attach user object to request
        // Also attach Stytch session specific info if needed later
        req.stytchSession = {
            id: stytchResponse.session.session_id,
            jwt: stytchResponse.session_jwt, // This is Stytch's JWT for the session
            customClaims: stytchResponse.session.custom_claims
        };
        helper.devConsole(`[${functionName}] User and Stytch session data attached to request object.`);
        next();

    } catch (error) {
        // Handle Stytch specific errors (e.g., session_not_found)
        helper.devConsole(`[${functionName}] Stytch session authentication FAILED. Full error object:`, error);

        let errorMessage = 'Invalid or expired session.';
        if (error.error_type === 'session_not_found') {
            errorMessage = 'Session could not be found. Please log in again.';
        } else if (error.error_type) { // Other Stytch specific errors
            errorMessage = `Stytch authentication error: ${error.error_message || error.error_type}`;
        } else if (error.message) { // Generic errors
            errorMessage = error.message;
        }

        helper.devConsole(`[${functionName}] Stytch Error Summary: Type: ${error.error_type || 'N/A'}, Message: ${error.message || JSON.stringify(error)}`);
        return next(new AppError(errorMessage, 401));
    }
});

// @desc     Verify Service Client JWT
// @route    Middleware for protected service routes
exports.verifyServiceClient = catchAsync(async (req, res, next) => {
    const functionName = 'authController.verifyServiceClient';
    helper.devConsole(`[${functionName}] Start - Verifying Service Client JWT`);

    let token;
    // 1. Get token and check if it exists
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        token = req.headers.authorization.split(' ')[1];
        // Log a masked version of the token for security
        const maskedToken = token.length > 20 ? `${token.substring(0, 10)}...${token.substring(token.length - 5)}` : `${token.substring(0, 3)}...`;
        helper.devConsole(`[${functionName}] Extracted Token: ${maskedToken}`);
    }

    if (!token) {
        helper.devConsole(`[${functionName}] Service Client JWT missing.`);
        return next(new AppError('Authentication token missing. Not a Bearer token or token not provided.', 401));
    }

    try {
        // 2. Verify token
        helper.devConsole(`[${functionName}] Verifying JWT with secret...`);
        const decoded = await promisify(jwt.verify)(token, jwtSecret); // jwtSecret is defined at the top of the file

        helper.devConsole(`[${functionName}] JWT verified successfully.`);
        helper.devConsole(`[${functionName}] Decoded payload:`, decoded);

        // 3. Attach service client information to request object
        req.serviceClient = decoded; // Store the entire decoded payload
        helper.devConsole(`[${functionName}] Service client data attached to req.serviceClient.`);
        next();

    } catch (error) {
        helper.devConsole(`[${functionName}] JWT verification failed. Error Name: ${error.name}, Message: ${error.message}`);
        if (error.name === 'TokenExpiredError') {
            return next(new AppError('Service client token has expired.', 401));
        }
        if (error.name === 'JsonWebTokenError') {
            return next(new AppError('Invalid service client token.', 401));
        }
        return next(new AppError('Service client authentication failed.', 401));
    }
});

// NEW FUNCTION to verify JWT and return user ID
exports.verifyJwtAndGetUserId = async (token) => {
    const functionName = 'authController.verifyJwtAndGetUserId';
    helper.devConsole(`[${functionName}] Verifying token...`);
    if (!token) {
        helper.devConsole(`[${functionName}] Error: No token provided.`);
        throw new AppError('No token provided', 401);
    }
    try {
        // Verify token
        const decoded = await promisify(jwt.verify)(token, jwtSecret);
        helper.devConsole(`[${functionName}] Token decoded successfully. User ID: ${decoded.id}`);
        // Optional: Check if user still exists (might be overkill for WS auth, depends on requirements)
        /*
        const freshUser = await User.findById(decoded.id);
        if (!freshUser) {
            helper.devConsole(`[${functionName}] Error: User belonging to token not found (ID: ${decoded.id}).`);
            throw new AppError('User belonging to this token does no longer exist.', 401);
        }
        */
        return decoded.id; // Return the user ID
    } catch (err) {
        helper.devConsole(`[${functionName}] Error verifying token: ${err.message}`);
        // Distinguish between expired and invalid tokens
        if (err.name === 'JsonWebTokenError') {
            throw new AppError('Invalid token.', 401);
        }
        if (err.name === 'TokenExpiredError') {
            throw new AppError('Your token has expired!', 401);
        }
        // Generic error for other cases
        throw new AppError('Token verification failed.', 401);
    }
};

// @desc     Get User Data From Stytch
// @route    get /auth/userbyid
exports.getUserAfterReload = catchAsync(async (req, res, next) => {
  // Using console.log for debugging this specific issue
  console.log('[getUserAfterReload] Started. Checking req.user object with console.log:', req.user);

  let userIdString = null; // Initialize with null

  // *** FIX START: Prioritize user ID from token if available ***
  if (req.user && req.user._id) {
    console.log('[getUserAfterReload] Inside IF block: req.user._id seems to exist.');
    try {
        // Attempt to convert to string immediately and log with console.log
        console.log('[getUserAfterReload] Value of req.user._id before toString():', req.user._id);
        userIdString = req.user._id.toString();
        console.log('[getUserAfterReload] Successfully converted req.user._id to string with console.log:', userIdString);
    } catch (conversionError) {
        console.error('[getUserAfterReload] CRITICAL ERROR converting req.user._id to string:', conversionError);
        console.log('[getUserAfterReload] req.user._id was:', req.user._id);
        return next(new AppError('Failed to process user ID from token.', 500));
    }

  } else if (req.body.id) {
    // Fallback: Use ID from body
    console.log('[getUserAfterReload] Inside ELSE IF block: Using user ID from request body.');
    userIdString = req.body.id.toString(); // Ensure string format
    console.log('[getUserAfterReload] Assigned req.body.id to userIdString. Value:', userIdString);

  } else {
    console.log('[getUserAfterReload] Inside ELSE block: No user ID found.');
    return next(new AppError('User ID not provided.', 400));
  }
  // *** FIX END ***

  // Ensure userIdString has a value before proceeding
  if (!userIdString) {
    console.error('[getUserAfterReload] CRITICAL Error: userIdString is still null/undefined/empty after checks!');
    return next(new AppError('Failed to determine User ID.', 500));
  }

  console.log('[getUserAfterReload] Fetching user using final ID string with console.log:', userIdString);
  // *** Use the determined string ID directly in the query ***
  // Re-add populate, but only select the _id field for defaultKitchentable
  const user = await User.findById(userIdString).populate('defaultKitchentable', '_id');

  if (!user) {
    console.log('[getUserAfterReload] User not found for ID:', userIdString);
    return next(new AppError('User not found.', 404));
  }
  console.log('[getUserAfterReload] User found:', user.email);

  // Passwort sollte nie zurückgegeben werden
  user.password = undefined;

  // Leite den User weiter für sendanswer
  // *** FIX: Prepare data for sendanswer middleware ***
  if (!req.body.answerobject) {
      req.body.answerobject = {}; // Initialize if it doesn't exist
  }
  req.body.answerobject.user = user;
  helper.devConsole('[getUserAfterReload] User data attached to req.body.answerobject.user for sendanswer.');

  next();
});

exports.getUserData = catchAsync(async (req, res, next) => {
    try{

        const user = await User.find({
            extId: req.body.user_id
        })

        res.status(201).json({
            success: true,
            data: user[0],
        })

    } catch(err){
          helper.devConsole(err)
        res.status(400).json({
            success: false,
            message: "Error Authenticating",
            error: err
        })
    }
})

// @desc    Get User Data by String
// @route    GET /auth/userdata/bysearchstring/:searchstring
exports.getUserDataByString = catchAsync(async (req, res, next) => {
    try{

        const searchstring = req.params.searchstring

          helper.devConsole(req.params)
          helper.devConsole(new RegExp(searchstring, 'i'))

        const user = await User.find({
            $or: [
                { 'email': { $regex: new RegExp(searchstring, 'i') } },
                { 'firstName': { $regex: new RegExp(searchstring, 'i') } }
            ],
            'externalAccess': true
        })
        .select('email extId');

        helper.devConsole(user)

        res.status(201).json({
            success: true,
            data: user,
        })

    } catch(err){
        //console.log(err)
        res.status(400).json({
            success: false,
            message: "Error Authenticating",
            error: err
        })
    }
})

exports.updateUserData = catchAsync(async (req, res, next) => {

      // Get user ID from the verify middleware (req.user is attached)
      const userIdToUpdate = req.user._id;
      const payloadToUpdate = req.body.payload;

      helper.devConsole("--- authController.updateUserData --- Payload Received:");
      helper.devConsole(payloadToUpdate);
      helper.devConsole("--- User ID to update (from token):", userIdToUpdate);

      // Validate that we have a payload
      if (!payloadToUpdate || Object.keys(payloadToUpdate).length === 0) {
         return next(new AppError('No update payload provided.', 400));
      }
      // Optional: Add validation to prevent updating certain fields like 'role', 'extId' etc.
      // delete payloadToUpdate.role;
      // delete payloadToUpdate.extId;

    try{
        // Use findByIdAndUpdate with the ID from the token
        const updatedUser = await User.findByIdAndUpdate(
            userIdToUpdate,              // Use ID from req.user
            { $set: payloadToUpdate },   // Apply the payload using $set
            {
                new: true, // Return the modified document rather than the original
                runValidators: true // Optional: run schema validators on update
            }
        );

        if (!updatedUser) {
            // This case should ideally not happen if verify succeeded
            return next(new AppError('No user found with the ID from the token', 404));
        }

        helper.devConsole("--- Updated User Data --- (User ID: " + updatedUser._id + ")");
        // helper.devConsole(updatedUser); // Maybe too verbose

        res.status(200).json({
            success: true,
            data: updatedUser, // Send the updated user object back
        });

    } catch(err){
        helper.devConsole("Error during user update:", err)
        // Check for validation errors specifically
        if (err.name === 'ValidationError') {
             return next(new AppError(`Invalid input data: ${err.message}`, 400));
        }
        // Generic error for other issues
        res.status(400).json({ // Or use next(new AppError(...)) for global handler
            success: false,
            message: "Error updating user data",
            error: err.message // Send only the message, not the full error object
        })
    }
})


//////////////////////// JSON WEBTOKEN MICROSOFT AUTH //////////////////////
/////////////////////////////////////////////////////////////////////////////
exports.msauth = catchAsync(async (req, res, next) => {
    const TenantId = "common"

    // MSAL configs
    const config = {
        auth: {
        // 'Application (client) ID' of app registration in the Microsoft Entra admin center - this value is a GUID
        clientId: process.env.MS_PEAX_APPID,

        // Client secret 'Value' (not the ID) from 'Client secrets' in app registration in Microsoft Entra admin center
        clientSecret: process.env.MS_PEAX_CLIENTSECRET,

        // Full directory URL, in the form of https://login.microsoftonline.com/<tenant>
        authority: `https://login.microsoftonline.com/${TenantId}`
        }
    }

    const token = req.headers['authorization']?.split(' ')[1];

    if (!token) {
        return res.status(401).json({ message: 'Access Token not provided' });
    }

    try {
        const decodedHeader = jwt.decode(token, { complete: true });
        const kid = decodedHeader.header.kid;

        //console.log("------S HEADER DECODED------")
        //console.log(decodedHeader)
        //console.log("------E HEADER DECODED------")

        //console.log("------S kid DECODED------")
        //console.log(kid)
        //console.log("------E kid DECODED------")

        // JWKS Client mit Tenant-spezifischer URI initialisieren
        const jwksClient = jwksRsa({
            // könnte auch common oder organization sein anstatt tid
            jwksUri: config.auth.authority + '/discovery/v2.0/keys',
            cache: true,
            rateLimit: true,
            strictSsl: true
        });

        // Funktion zum Abrufen des jwksUri
        const getSigningKey = async (kid) => {
        return new Promise((resolve, reject) => {
            jwksClient.getSigningKey(kid, (err, key) => {
            if (err) {
                return reject(err);
            }
            resolve(key.getPublicKey());
            });
        });
        };

        const signingKey = await getSigningKey(kid);

        //console.log("------S signingKey DECODED------")
        //console.log(signingKey)
        //console.log("------E signingKey DECODED------")
        //console.log(config.auth.authority + '/v2.0')

        // Überprüfe das Token mit dem gelesenen Signing Key
        const decodedToken = await promisify(jwt.verify)(token, signingKey, {
            audience: decodedHeader.payload.aud,//decodedHeader.payload.aud,
            issuer: [
                config.auth.authority + '/v2.0',
                `https://sts.windows.net/${decodedHeader.payload.tid}/`, // Akzeptieren Sie die spezifische Tenant-ID, wenn verfügbar
                `https://login.microsoftonline.com/${process.env.MS_PEAX_TENANTID}/v2.0` // Fallback auf Ihre spezifische Tenant-ID
            ],
            algorithms: ['RS256']
        });

        req.user = decodedToken; // Benutzerinformationen setzen
        next();
    } catch (err) {
        //console.error('Token Verification Error:', err);
        return res.status(401).json({ message: 'Invalid Access Token', error: err.message });
    }
});

exports.basicAuth = catchAsync(async (req, res, next) => {

    // Basic HTTP Auth
    // Check if Authroization Header is present
    if(!req.get('Authorization')){
        return  next(new AppError("Not Authenticated!", 404));
    } else {
        //decode Auth Header Base64 value
        var credentials = Buffer.from(req.get('Authorization').split(' ')[1], 'base64').toString().split(':')
        // ['username':'password']

        var username = credentials[0]
        var password = credentials[1]


        // hier passiert der fehler
        //!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        if(!(username === username_sys && password === password_sys)){
            return next(new AppError('Not Authenticated!2', 401));
        }
        // if everything worked
        next()
    }



})


exports.sendanswer = catchAsync(async (req, res, next) => {
    //helper.devConsole("MIDDLEs authController.sendanswer")
    //helper.devConsole(req.body.answerobject)
    if(
        !req.body.answerobject
    ){
        req.body.answerobject = "finished"
    }

    // Include updated usage data if available from license checker
    if (req.body.updatedUsageData) {
        if (typeof req.body.answerobject === 'object' && req.body.answerobject !== null) {
            req.body.answerobject.updatedUsageData = req.body.updatedUsageData;
        } else {
            // If answerobject is not an object, create a new structure
            req.body.answerobject = {
                result: req.body.answerobject,
                updatedUsageData: req.body.updatedUsageData
            };
        }
        helper.devConsole('[sendanswer] Including updated usage data in response');
    }

    // Log the object before sending
    console.log('[sendanswer] Preparing to send response with req.body.answerobject:', JSON.stringify(req.body.answerobject));

    // Send success response
    res.status(201).json({ // Sendet 201 Created
      status: 'success', // Sendet 'status'-Feld
      success: true,
      data: req.body.answerobject // Sendet { user: { ... } }
    });

  });

// NEW FUNCTION to verify Stytch token for WebSocket and return LOCAL user ID
exports.verifyStytchTokenForWs = async (sessionToken) => {
    const functionName = 'authController.verifyStytchTokenForWs';
    helper.devConsole(`[${functionName}] Verifying Stytch token for WS...`);
    if (!sessionToken) {
        helper.devConsole(`[${functionName}] Error: No Stytch token provided.`);
        // Return an object with isValid: false and an error message
        return { isValid: false, error: 'Authentication token missing.', userId: null };
    }

    try {
        // 1. Authenticate session token with Stytch
        const stytchSessionResponse = await client.sessions.authenticate({
            session_token: sessionToken,
            // No session extension needed for WS check
        });

        const stytchUserID = stytchSessionResponse.session.user_id;
        helper.devConsole(`[${functionName}] Stytch token authenticated. Stytch User ID: ${stytchUserID}`);

        // 2. Find local user by Stytch User ID (extId)
        const localUser = await User.findOne({ extId: stytchUserID });
        if (!localUser) {
            helper.devConsole(`[${functionName}] Error: Local user not found for Stytch ID ${stytchUserID}.`);
            // Return an object with isValid: false and an error message
            return { isValid: false, error: 'User associated with token not found locally.', userId: null };
        }

        helper.devConsole(`[${functionName}] Local user found: ${localUser._id}`);
        // Return an object with isValid: true and the userId
        return { isValid: true, userId: localUser._id.toString(), error: null };

    } catch (err) {
        // Handle errors from Stytch authentication or User.findOne
        helper.devConsole(`[${functionName}] Error during Stytch token verification or local user lookup:`, err.error_type || err.message || err);

        let errorMessage = 'Token verification failed due to an internal error.';
        if (err.status_code === 401 || err.status_code === 404) { // Stytch: session_not_found etc.
             errorMessage = 'Invalid or expired session token.';
        } else if (err.status_code === 400) { // Stytch: validation_error
             errorMessage = 'Invalid token format for verification.';
        }
        // Return an object with isValid: false and an error message
        return { isValid: false, error: errorMessage, userId: null };
    }
};

// @desc    Delete own user account (Local DB & Stytch)
// @route   DELETE /api/v1/auth/me
exports.deleteMe = catchAsync(async (req, res, next) => {
    const functionName = 'authController.deleteMe';
    const userId = req.user._id; // Get local user ID from verify middleware
    const stytchUserId = req.user.extId; // Get Stytch user ID from verify middleware

    // Basic validation: Ensure we have both IDs
    if (!userId || !stytchUserId) {
        helper.devConsole(`[${functionName}] Missing user IDs. Cannot proceed. Local ID: ${userId}, Stytch ID: ${stytchUserId}`);
        return next(new AppError('User identification missing, cannot delete account.', 400));
    }

    helper.devConsole(`[${functionName}] Attempting to delete account for User ID: ${userId}, Stytch ID: ${stytchUserId}`);

    // 1. Delete user from Stytch
    try {
        helper.devConsole(`[${functionName}] Deleting user from Stytch: ${stytchUserId}`);
        await client.users.delete({ user_id: stytchUserId.trim() });
        helper.devConsole(`[${functionName}] Successfully deleted user from Stytch.`);
    } catch (stytchErr) {
        helper.devConsole(`[${functionName}] FAILED to delete user from Stytch: ${stytchUserId}`);
        console.error("Stytch Deletion Error:", stytchErr);
        // Log Stytch specific error if available
        if (stytchErr.error_type === 'user_not_found') {
            helper.devConsole(`[${functionName}] Stytch user ${stytchUserId} not found, likely already deleted. Proceeding with local deletion.`);
            // Allow proceeding if user not found in Stytch
        } else {
            // For other Stytch errors, stop and return an error.
            return next(new AppError('Failed to delete user account from authentication provider. Please try again later.', 500));
        }
    }

    // 2. Delete user from local MongoDB
    try {
        helper.devConsole(`[${functionName}] Deleting user from local DB: ${userId}`);
        const deleteResult = await User.findByIdAndDelete(userId);
        if (!deleteResult) {
             // This case should ideally not happen if verify middleware worked correctly,
             // but handle it defensively.
             helper.devConsole(`[${functionName}] User ${userId} not found in local DB during deletion attempt.`);
             // Even if user not found locally, Stytch deletion might have succeeded.
             // We can still return success (204) as the end state (user gone) is achieved.
        } else {
             helper.devConsole(`[${functionName}] Successfully deleted user from local DB.`);
        }
    } catch (dbErr) {
         helper.devConsole(`[${functionName}] FAILED to delete user from local DB: ${userId}`);
         console.error("Local DB Deletion Error:", dbErr);
         // Return an internal server error.
         return next(new AppError('Failed to delete user account locally. Please contact support.', 500));
    }

    // 3. Send Success Response (204 No Content)
    helper.devConsole(`[${functionName}] Account deletion process completed for User ID: ${userId}`);
    res.status(204).json({
        status: 'success',
        data: null
    });
});

// @desc     Refresh Stytch session token
// @route    POST /api/v1/auth/session/refresh
// @access   Private (expects valid Stytch session_token in Authorization header)
exports.refreshSession = catchAsync(async (req, res, next) => {
    const functionName = 'authController.refreshSession';
    helper.devConsole(`[${functionName}] Start - Refreshing Stytch Session`);

    let currentSessionToken;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        currentSessionToken = req.headers.authorization.split(' ')[1];
    }

    if (!currentSessionToken) {
        helper.devConsole(`[${functionName}] Stytch session token missing in Authorization header.`);
        return next(new AppError('Authentication token missing.', 401));
    }

    try {
        const stytchResponse = await client.sessions.authenticate({
            session_token: currentSessionToken,
            session_duration_minutes: parseInt(process.env.STYTCH_SESSION_DURATION_MINUTES, 10) || 23040 // Use configured duration
        });

        helper.devConsole(`[${functionName}] Stytch session authentication/refresh successful.`);

        // Ensure the necessary session details are present
        const newSessionToken = stytchResponse.session_token;
        const newSessionJwt = stytchResponse.session_jwt;
        const newExpiresAt = stytchResponse.session?.expires_at;

        if (!newSessionToken || !newSessionJwt || !newExpiresAt) {
            helper.devConsole(`[${functionName}] Critical session details missing from Stytch response after refresh.`);
            return next(new AppError('Failed to refresh session properly, essential token data missing.', 500));
        }

        res.status(200).json({
            status: 'success',
            session_token: newSessionToken,
            session_jwt: newSessionJwt,
            session_expires_at: newExpiresAt
        });

    } catch (error) {
        helper.devConsole(`[${functionName}] Error during Stytch session refresh:`, error.error_type || error.message, error);
        // Handle common Stytch errors for invalid/expired tokens
        if (error.status_code === 401 || error.error_type === 'session_not_found' || error.error_type === 'unauthorized_credentials' || error.error_type === 'invalid_session_token') {
            return next(new AppError('Invalid or expired session token. Please re-authenticate.', 401));
        }
        // For other errors, return a generic server error
        return next(new AppError('Could not refresh session.', 500));
    }
});

// --------------- AZURE AD AUTHENTICATION ---------------
// @desc     Initiates Azure AD OAuth flow
// ... existing code ...