const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { connection1 } = require('../db'); // Import the specific connection
// const crypto = require('crypto'); // No longer needed here

const serviceClientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a service name'],
    trim: true
  },
  clientId: {
    type: String,
    required: [true, 'Client ID is required'],
    unique: true,
    // Example generator: crypto.randomBytes(16).toString('hex')
  },
  clientSecret: {
    type: String,
    required: [true, 'Client Secret is required'],
    select: false // Do not include clientSecret in query results by default
    // Example generator: crypto.randomBytes(32).toString('hex')
  },
  createdAt: {
    type: Date,
    default: Date.now()
  }
});

// Pre-save middleware to hash the clientSecret before saving
serviceClientSchema.pre('save', async function(next) {
  // Only run this function if clientSecret was actually modified (or is new)
  if (!this.isModified('clientSecret')) return next();

  // Hash the clientSecret with cost of 12
  this.clientSecret = await bcrypt.hash(this.clientSecret, 12);

  next();
});

// Instance method to compare provided secret with the hashed secret in the DB
serviceClientSchema.methods.correctSecret = async function(
  candidateSecret,
  clientSecretHash
) {
  return await bcrypt.compare(candidateSecret, clientSecretHash);
};

// Use the imported connection1 when creating the model
const ServiceClient = connection1.model('ServiceClient', serviceClientSchema);

module.exports = ServiceClient; 