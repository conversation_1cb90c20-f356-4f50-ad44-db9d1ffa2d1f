const express = require('express');
const marketingContentController = require('../controllers/marketingContentController');
const authController = require('../controllers/authController'); // Für geschützte Routen

const router = express.Router();

// --- Routes ---

// GET the latest marketing content entry
router
  .route('/')
  .get(marketingContentController.getLatestMarketingContent);

// Route to get all marketing content (protected)
router
  .route('/all')
  .get(authController.verify, marketingContentController.getAllMarketingContent);

// Route to get marketing content by ID
router
  .route('/:id')
  .get(marketingContentController.getMarketingContentById);

// Route to publish marketing content to social media (protected)
router
  .route('/publish')
  .post(authController.verify, marketingContentController.publishToSocialMedia);

// Route to auto-publish latest content to Pinterest (protected)
router
  .route('/auto-publish-pinterest')
  .post(authController.verify, marketingContentController.autoPublishToPinterest);

// Route to auto-publish latest content to TikTok (protected)
router
  .route('/auto-publish-tiktok')
  .post(authController.verify, marketingContentController.autoPublishToTikTok);

// Route to auto-publish latest content to Instagram (protected)
router
  .route('/auto-publish-instagram')
  .post(authController.verify, marketingContentController.autoPublishToInstagram);

module.exports = router;