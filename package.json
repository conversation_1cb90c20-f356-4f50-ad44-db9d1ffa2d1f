{"name": "orderapi-tst", "version": "2.3.4", "description": "API für das Ordy Projekt", "main": "server.js", "scripts": {"_comment": "ACHTUNG!  Nach development oder production dürfen keine Leerzeichen kommen, sonst funktionieren die ENV abgfragen nicht", "dev": "set NODE_ENV=development&&nodemon server.js", "prev": "set NODE_ENV=preview&&nodemon server.js", "prod": "set NODE_ENV=production&&nodemon server.js", "start": "node start.js", "start:azure": "node start.js", "build": "echo \"Build process skipped for Azure deployment\"", "test": "cross-env NODE_ENV=preview test", "security-fix": "node security-fix.js", "security-check": "node scripts/security-check.js", "production-build": "node scripts/production-build.js", "postinstall": "npx playwright install --with-deps", "install:browsers": "PLAYWRIGHT_BROWSERS_PATH=./playwright-browsers npx playwright install chromium --with-deps"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.428.0", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "0.12.15", "@tailwindcss/postcss": "^4.0.0-beta.8", "@tailwindcss/vite": "^4.0.0-beta.8", "ajv": "^8.17.1", "atob": "^2.1.2", "aws-sdk": "^2.1476.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dayjs": "^1.11.11", "dotenv": "^16.0.3", "esbuild": "^0.25.4", "express": "^4.21.0", "express-jwt": "^8.4.1", "express-jwt-authz": "^2.4.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.7.0", "express-session": "^1.17.3", "express-ws": "^5.0.2", "ffmpeg-static": "5.2.0", "fluent-ffmpeg": "2.1.3", "helmet": "^6.1.5", "hpp": "^0.2.3", "jimp": "^0.22.10", "json-schema": "^0.4.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "latest": "^0.2.0", "minimist": "1.2.8", "moment": "^2.30.1", "mongodb": "^6.16.0", "mongoose": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "ndb": "^0.2.4", "ngrok": "^5.0.0-beta.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "openai": "^4.0.0", "passport": "^0.6.0", "passport-google-oauth2": "^0.2.0", "passport-google-oauth20": "^2.0.0", "playwright": "^1.52.0", "pug": "^3.0.2", "punycode": "^2.3.1", "qs": "6.11.0", "sharp": "^0.34.2", "stripe": "^17.3.1", "stytch": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwindcss": "^4.0.0-beta.8", "tough-cookie": "4.1.3", "webpack-node-externals": "^3.0.0", "websocket": "^1.0.35", "whatwg-url": "^14.0.0", "ws": "^8.18.0", "xss-clean": "^0.1.1"}, "engines": {"node": "^22"}, "devDependencies": {"@babel/core": "^7.23.9", "@eslint/js": "^9.9.0", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "eslint": "^8.57.0", "globals": "^15.9.0", "terser": "^5.27.0", "webpack": "^5.90.0", "webpack-cli": "^5.1.4"}, "resolutions": {"json-schema": "^0.4.0", "minimist": "^1.2.8", "tough-cookie": "^4.1.3", "qs": "^6.11.0", "semver": "^7.5.2", "minimatch": "^3.0.5", "tar": "^6.2.0"}}