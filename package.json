{"name": "ordy", "version": "2.4.4", "private": false, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "test:critical": "node scripts/run-critical-tests.js", "test:critical-headed": "playwright test tests/critical-functions.spec.js --headed", "test:install": "playwright install", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "security-check": "node scripts/security-check.js", "production-build": "node scripts/production-build.js"}, "dependencies": {"@capacitor/cli": "^6.2.0", "@capacitor/core": "^6.2.0", "@capacitor/ios": "^6.2.0", "@lottiefiles/vue-lottie-player": "^1.1.0", "@playwright/test": "^1.52.0", "@stytch/vanilla-js": "^5.23.0", "@types/pulltorefreshjs": "^0.1.7", "@vercel/analytics": "^1.4.1", "@vuepic/vue-datepicker": "^10.0.0", "axios": "^1.7.9", "compress.js": "^2.1.2", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "heic2any": "^0.0.4", "node": "^23.5.0", "pica": "^9.0.1", "pinia": "^2.3.0", "playwright": "^1.52.0", "pulltorefreshjs": "^0.1.22", "sass": "^1.83.0", "stytch": "^11.10.0", "v-calendar": "^3.1.2", "vite-plugin-vue-devtools": "^7.6.8", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-carousel": "^0.11.0", "vue3-google-oauth2": "^1.0.7", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@tailwindcss/vite": "^4.1.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.32.0", "jsdom": "^25.0.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^4.0.0-beta.8", "vite": "^6.0.6", "vite-plugin-pwa": "^0.21.1"}}