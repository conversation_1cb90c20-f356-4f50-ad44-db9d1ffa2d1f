const mongoose = require('mongoose')
const { connection1 } = require('../db.js');
const helper = require('../utils/helper.js')
const User = require('./userModel.js')
const Menu = require('./menuModel.js')
//const Roles = require('./roleModel')

const KitchenTableSchema = new mongoose.Schema({
    tableAddress_street: {
        type: String,
        required: true
    },
    tableAddress_plztown: {
        type: String,
        required: true
    },
    tableAddress_country: {
        type: String,
        required: true
    },
    creatorUserId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    members: [
        {
            roleId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Role'
            },
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            }
        }
    ],
    createdAt: {
        type: String,
        default: Date.now()
    }
})

/*
UserSchema.pre(/^find/, function(next){
    helper.devConsole("pre on weekplan find executed")
    this.populate('menu')
    next()
});
*/




module.exports = connection1.model('Kitchentable', KitchenTableSchema)