# Azure Deployment-Simulator für Ordy API

Diese Tools helfen bei der Simulation des Azure Web App-Deployment-Prozesses in einer lokalen Umgebung. 
Sie können damit Probleme beim Azure-Deployment diagnostizieren, ohne den tatsächlichen Deployment-Prozess starten zu müssen.

## Überblick der Tools

1. **azure-env-validator.js**: Überprüft, ob alle erforderlichen Umgebungsvariablen für das Azure-Deployment vorhanden sind.
2. **azure-local-build.js**: Simuliert den Build-Prozess, den Azure während des Deployments ausführt.
3. **azure-test-app.js**: Führt grundlegende Tests durch, um zu prüfen, ob die API korrekt funktioniert.
4. **deploy-azure-local.js**: Kombiniert alle Schritte in einem einzigen Skript.
5. **web.config**: Azure-spezifische Konfigurationsdatei für IIS-Webserver.

## Verwendung

### Überprüfung der Umgebungsvariablen

```bash
node azure-env-validator.js
```

### Lokaler Build und Test

```bash
node azure-local-build.js preview  # Für die Test-Umgebung
node azure-local-build.js production  # Für die Produktionsumgebung
```

### Kombinierter Deployment-Prozess

```bash
node deploy-azure-local.js preview  # Simuliert den vollständigen Deployment-Prozess für die Test-Umgebung
node deploy-azure-local.js production  # Simuliert den vollständigen Deployment-Prozess für die Produktionsumgebung
```

## Fehlersuche bei Azure-Deployments

Wenn das Azure-Deployment fehlschlägt, können diese Schritte helfen:

1. **Umgebungsvariablen überprüfen**: Führen Sie `node azure-env-validator.js` aus, um sicherzustellen, dass alle erforderlichen Umgebungsvariablen korrekt gesetzt sind.

2. **Lokalen Build testen**: Führen Sie `node azure-local-build.js preview` aus, um zu prüfen, ob die Anwendung lokal gebaut werden kann.

3. **Webserver-Konfiguration prüfen**: Überprüfen Sie die `web.config`-Datei auf mögliche Probleme.

4. **Abhängigkeiten überprüfen**: Stellen Sie sicher, dass die `package.json`-Datei ordnungsgemäß ist und dass alle Abhängigkeiten installiert werden können.

5. **Node.js-Version überprüfen**: Stellen Sie sicher, dass die Node.js-Version mit der in `package.json` unter "engines" angegebenen Version übereinstimmt.

## Azure Web App Spezifische Konfiguration

Azure Web Apps verwenden bestimmte Konfigurationseinstellungen, die in einer lokalen Umgebung nicht verfügbar sind. Die wichtigsten sind:

- **WEBSITE_NODE_DEFAULT_VERSION**: Legt die Node.js-Version für die App fest
- **WEBSITE_SITE_NAME**: Der Name der Web App
- **WEBSITE_HOSTNAME**: Der Hostname der App
- **WEBSITE_INSTANCE_ID**: Eine eindeutige ID für die Instanz

Diese Skripte simulieren diese Umgebungsvariablen, um eine möglichst genaue Replikation der Azure-Umgebung zu erzielen.

## GitHub Actions Workflow

Die reale Deployment-Pipeline ist in den `.github/workflows/`-Dateien definiert:

- `main_ordy-tst.yml`: Deployment für die Test-Umgebung (preview)
- `main_ordy-prd.yml`: Deployment für die Produktionsumgebung

Diese Simulator-Skripte folgen denselben Schritten wie die tatsächlichen GitHub Actions-Workflows. 