/**
 * Legacy StableID Migration System
 *
 * Dieses System stellt sicher, dass alte Rezepte ohne StableIDs
 * automatisch kompatibel gemacht werden, ohne die Datenbank zu ändern.
 *
 * Funktionen:
 * 1. Erkennt Legacy-Rezepte (ohne stableId Felder)
 * 2. Weist automatisch StableIDs zu (nur im Frontend)
 * 3. Erhält Kompatibilität mit neuen Features
 * 4. Migriert schrittweise bei Bearbeitung
 */

// Helper-Funktionen direkt implementiert (ohne Store-Abhängigkeit)
const devConsole = (message, data = null) => {
    if (import.meta.env.DEV) {
        if (data) {
            console.log(message, data);
        } else {
            console.log(message);
        }
    }
};

/**
 * Prüft ob ein Rezept Legacy-Format hat (ohne StableIDs)
 * @param {Object} recipe - Das zu prüfende Rezept
 * @returns {boolean} - true wenn Legacy-Format
 */
export function isLegacyRecipe(recipe) {
    if (!recipe?.menuchilds?.menuChildId?.ingredients) {
        return false;
    }

    const ingredients = recipe.menuchilds.menuChildId.ingredients;

    // Prüfe ob mindestens eine Zutat keine stableId hat
    const hasLegacyIngredients = ingredients.some(ingredient =>
        !ingredient.stableId || typeof ingredient.stableId !== 'number'
    );

    // Prüfe ob maxUsedStableId fehlt
    const hasNoMaxUsedId = !recipe.menuchilds.menuChildId.maxUsedStableId;

    const isLegacy = hasLegacyIngredients || hasNoMaxUsedId;

    if (isLegacy) {
        devConsole('🔍 Legacy recipe detected:', {
            recipeId: recipe._id,
            recipeName: recipe.name,
            ingredientsCount: ingredients.length,
            hasLegacyIngredients,
            hasNoMaxUsedId,
            currentMaxUsedId: recipe.menuchilds.menuChildId.maxUsedStableId
        });
    }

    return isLegacy;
}

/**
 * Migriert ein Legacy-Rezept zu StableID-Format (nur Frontend)
 * @param {Object} recipe - Das zu migrierende Rezept
 * @returns {Object} - Das migrierte Rezept
 */
export function migrateLegacyRecipe(recipe) {
    if (!isLegacyRecipe(recipe)) {
        devConsole('✅ Recipe already has StableIDs, no migration needed');
        return recipe;
    }

    devConsole('🔄 Starting legacy recipe migration...');

    const ingredients = recipe.menuchilds.menuChildId.ingredients;
    let maxUsedId = recipe.menuchilds.menuChildId.maxUsedStableId || 0;

    // Finde bereits vorhandene StableIDs
    ingredients.forEach(ingredient => {
        if (ingredient.stableId && typeof ingredient.stableId === 'number') {
            maxUsedId = Math.max(maxUsedId, ingredient.stableId);
        }
    });

    devConsole(`📊 Migration starting with maxUsedId: ${maxUsedId}`);

    // Weise fehlende StableIDs zu
    let assignedCount = 0;
    ingredients.forEach((ingredient, index) => {
        if (!ingredient.stableId || typeof ingredient.stableId !== 'number') {
            maxUsedId++;
            ingredient.stableId = maxUsedId;
            assignedCount++;

            devConsole(`🆔 Assigned stableId ${maxUsedId} to ingredient "${ingredient.name?.name || 'Unknown'}" at index ${index}`);
        } else {
            devConsole(`✅ Keeping existing stableId ${ingredient.stableId} for ingredient "${ingredient.name?.name || 'Unknown'}"`);
        }
    });

    // Setze maxUsedStableId
    recipe.menuchilds.menuChildId.maxUsedStableId = maxUsedId;

    devConsole('✅ Legacy migration completed:', {
        recipeId: recipe._id,
        recipeName: recipe.name,
        totalIngredients: ingredients.length,
        newStableIdsAssigned: assignedCount,
        finalMaxUsedId: maxUsedId,
        allStableIds: ingredients.map(ing => ing.stableId)
    });

    // Markiere als migriert (nur für Logging)
    recipe._legacyMigrated = true;

    return recipe;
}

/**
 * Repariert beschädigte Datenstrukturen in Legacy-Rezepten
 * @param {Object} recipe - Das zu reparierende Rezept
 * @returns {Object} - Das reparierte Rezept
 */
export function repairLegacyDataStructure(recipe) {
    if (!recipe?.menuchilds?.menuChildId?.ingredients) {
        return recipe;
    }

    devConsole('🔧 Repairing legacy data structure...');

    const ingredients = recipe.menuchilds.menuChildId.ingredients;
    let repairCount = 0;

    ingredients.forEach((ingredient, index) => {
        let wasRepaired = false;

        // Repariere ingredient.name
        if (!ingredient.name || typeof ingredient.name !== 'object') {
            const originalName = ingredient.name;
            ingredient.name = {
                name: originalName || '',
                _id: originalName || ''
            };
            devConsole(`🔧 Repaired ingredient.name at index ${index}: "${originalName}" -> object`);
            wasRepaired = true;
        }

        // Repariere ingredient.unit
        if (!ingredient.unit || typeof ingredient.unit !== 'object') {
            const originalUnit = ingredient.unit;
            ingredient.unit = {
                name: originalUnit || '',
                _id: originalUnit || ''
            };
            devConsole(`🔧 Repaired ingredient.unit at index ${index}: "${originalUnit}" -> object`);
            wasRepaired = true;
        }

        // Repariere ingredient.amount
        if (ingredient.amount === undefined || ingredient.amount === null) {
            ingredient.amount = 0;
            devConsole(`🔧 Repaired ingredient.amount at index ${index}: null/undefined -> 0`);
            wasRepaired = true;
        }

        if (wasRepaired) {
            repairCount++;
        }
    });

    devConsole(`✅ Data structure repair completed: ${repairCount} ingredients repaired`);

    return recipe;
}

/**
 * Vollständige Legacy-Migration mit Datenstruktur-Reparatur
 * @param {Object} recipe - Das zu migrierende Rezept
 * @returns {Object} - Das vollständig migrierte Rezept
 */
export function fullLegacyMigration(recipe) {
    if (!recipe) {
        return recipe;
    }

    devConsole('🚀 Starting full legacy migration for recipe:', recipe.name);

    // Schritt 1: Datenstruktur reparieren
    const repairedRecipe = repairLegacyDataStructure(recipe);

    // Schritt 2: StableIDs zuweisen
    const migratedRecipe = migrateLegacyRecipe(repairedRecipe);

    devConsole('🎉 Full legacy migration completed successfully');

    return migratedRecipe;
}

/**
 * Prüft ob ein Rezept nach Migration gespeichert werden sollte
 * @param {Object} recipe - Das migrierte Rezept
 * @returns {boolean} - true wenn Speicherung empfohlen
 */
export function shouldSaveMigratedRecipe(recipe) {
    // Speichere nur wenn das Rezept tatsächlich migriert wurde
    return recipe._legacyMigrated === true;
}
