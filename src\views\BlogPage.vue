<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-md z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
            <router-link to="/rezepte" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-ordypurple-100 font-medium">Blog</router-link>
            <router-link to="/about" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
            <router-link to="/login" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all transform hover:scale-105 font-medium shadow-lg">
              Kostenlos starten
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- Header -->
    <header class="pt-32 pb-16 bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-YesevaOne text-gray-900 mb-4">
            Ordy <span class="text-ordypurple-100">Blog</span>
          </h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Entdecke Tipps, Tricks und wissenschaftliche Erkenntnisse rund um nachhaltiges Kochen, 
            Ernährung und Familienzeit
          </p>
        </div>
      </div>
    </header>

    <!-- Featured Article -->
    <section class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-3xl overflow-hidden shadow-2xl">
          <div class="grid lg:grid-cols-2 gap-0">
            <div class="p-8 lg:p-12 text-white">
              <div class="mb-4">
                <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                  ⭐ Featured
                </span>
              </div>
              <h2 class="text-3xl md:text-4xl font-YesevaOne mb-6">
                Wie KI die Zukunft des Kochens revolutioniert
              </h2>
              <p class="text-white/90 text-lg mb-6">
                Eine wissenschaftliche Analyse darüber, wie künstliche Intelligenz dabei hilft, 
                Lebensmittelverschwendung zu reduzieren und personalisierte Ernährung zu ermöglichen.
              </p>
              <div class="mb-6">
                <div class="text-white/70 text-sm">15. November 2024</div>
              </div>
              <button @click="openArticle('featured')" 
                      class="bg-white text-ordypurple-100 px-6 py-3 rounded-full font-medium hover:bg-gray-50 transition-all">
                Artikel lesen
              </button>
            </div>
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop" 
                   alt="KI und Kochen" 
                   class="w-full h-full object-cover">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories -->
    <section class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap gap-4 justify-center">
          <button v-for="category in categories" 
                  :key="category.id"
                  @click="selectedCategory = category.id"
                  :class="[
                    'px-6 py-3 rounded-full font-medium transition-all',
                    selectedCategory === category.id 
                      ? 'bg-ordypurple-100 text-white shadow-lg' 
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                  ]">
            {{ category.name }}
          </button>
        </div>
      </div>
    </section>

    <!-- Articles Grid -->
    <section class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <article v-for="article in filteredArticles" 
                   :key="article.id" 
                   class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                   @click="openArticle(article.id)">
            <div class="relative">
              <img :src="article.image" 
                   :alt="article.title" 
                   class="w-full h-48 object-cover">
              <div class="absolute top-4 left-4">
                <span :class="[
                  'px-3 py-1 rounded-full text-sm font-medium',
                  getCategoryColor(article.category)
                ]">
                  {{ getCategoryName(article.category) }}
                </span>
              </div>
              <div class="absolute bottom-4 right-4">
                <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {{ article.readTime }} Min Lesezeit
                </span>
              </div>
            </div>
            
            <div class="p-6">
              <h3 class="font-YesevaOne text-xl text-gray-900 mb-3 line-clamp-2">
                {{ article.title }}
              </h3>
              <p class="text-gray-600 mb-4 line-clamp-3">
                {{ article.excerpt }}
              </p>
              
              <div class="flex items-center justify-between">
                <div class="text-xs text-gray-500">{{ formatDate(article.publishedAt) }}</div>
                <button class="text-ordypurple-100 hover:text-ordypurple-200 font-medium text-sm">
                  Lesen →
                </button>
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-4">
          Bleib auf dem <span class="text-ordypurple-100">Laufenden</span>
        </h2>
        <p class="text-xl text-gray-600 mb-8">
          Erhalte wöchentlich neue Artikel, Rezepte und Tipps direkt in dein Postfach
        </p>
        
        <div class="max-w-md mx-auto">
          <div class="flex gap-3">
            <input v-model="newsletterEmail" 
                   type="email" 
                   placeholder="Deine E-Mail-Adresse"
                   class="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
            <button @click="subscribeNewsletter" 
                    :disabled="!newsletterEmail || isSubscribing"
                    class="bg-ordypurple-100 text-white px-6 py-3 rounded-full font-medium hover:bg-ordypurple-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
              {{ isSubscribing ? 'Wird gesendet...' : 'Abonnieren' }}
            </button>
          </div>
          <p class="text-sm text-gray-500 mt-3">
            Kostenlos und jederzeit kündbar. Kein Spam, versprochen!
          </p>
        </div>
      </div>
    </section>

    <!-- Related Topics -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-4">
            Beliebte <span class="text-ordypurple-100">Themen</span>
          </h2>
        </div>
        
        <div class="grid md:grid-cols-3 gap-8">
          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-3">Nachhaltigkeit</h3>
            <p class="text-gray-600 mb-4">
              Erfahre, wie du durch bewusstes Kochen einen Beitrag zum Umweltschutz leistest.
            </p>
            <button class="text-ordypurple-100 hover:text-ordypurple-200 font-medium">
              Artikel entdecken →
            </button>
          </div>
          
          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-3">Ernährung</h3>
            <p class="text-gray-600 mb-4">
              Wissenschaftlich fundierte Tipps für eine gesunde und ausgewogene Familienernährung.
            </p>
            <button class="text-ordypurple-100 hover:text-ordypurple-200 font-medium">
              Artikel entdecken →
            </button>
          </div>
          
          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-3">Familie</h3>
            <p class="text-gray-600 mb-4">
              Wie gemeinsames Kochen Familien stärkt und Kindern wichtige Werte vermittelt.
            </p>
            <button class="text-ordypurple-100 hover:text-ordypurple-200 font-medium">
              Artikel entdecken →
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// State
const selectedCategory = ref('all')
const newsletterEmail = ref('')
const isSubscribing = ref(false)

// Data
const categories = [
  { id: 'all', name: 'Alle Artikel' },
  { id: 'sustainability', name: 'Nachhaltigkeit' },
  { id: 'nutrition', name: 'Ernährung' },
  { id: 'family', name: 'Familie' },
  { id: 'technology', name: 'Technologie' },
  { id: 'recipes', name: 'Rezepte' }
]

const articles = ref([])

// Computed
const filteredArticles = computed(() => {
  if (selectedCategory.value === 'all') {
    return articles.value
  }
  return articles.value.filter(article => article.category === selectedCategory.value)
})

// Methods
const getCategoryName = (categoryId) => {
  const category = categories.find(cat => cat.id === categoryId)
  return category ? category.name : ''
}

const getCategoryColor = (categoryId) => {
  const colors = {
    sustainability: 'bg-green-100 text-green-700',
    nutrition: 'bg-blue-100 text-blue-700',
    family: 'bg-purple-100 text-purple-700',
    technology: 'bg-gray-100 text-gray-700',
    recipes: 'bg-red-100 text-red-700'
  }
  return colors[categoryId] || 'bg-gray-100 text-gray-700'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('de-DE', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

const openArticle = (articleId) => {
  // Create a simple article detail view
  const article = articles.value.find(a => a.id === articleId) ||
                  { title: 'Wie KI die Zukunft des Kochens revolutioniert', content: 'Featured article content...' }

  // For now, show an alert with article info - in production this would navigate to a detail page
  alert(`Artikel: ${article.title}\n\nDieser Artikel würde in einer vollständigen Implementierung auf einer eigenen Seite angezeigt werden.`)
}

const subscribeNewsletter = async () => {
  if (!newsletterEmail.value) return
  
  isSubscribing.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  alert('Danke für dein Interesse! Du erhältst eine Bestätigungs-E-Mail.')
  newsletterEmail.value = ''
  isSubscribing.value = false
}

// Generate mock articles
const generateMockArticles = () => {
  const mockArticles = [
    {
      id: 1,
      title: 'Die Wissenschaft hinter Lebensmittelverschwendung: Warum wir 40% unserer Lebensmittel wegwerfen',
      excerpt: 'Eine tiefgreifende wissenschaftliche Analyse der psychologischen, sozialen und strukturellen Faktoren, die zu massiver Lebensmittelverschwendung führen. Wir untersuchen, wie kognitive Verzerrungen beim Einkaufen entstehen, warum Haushalte systematisch zu viel kaufen und wie moderne KI-Technologie dabei helfen kann, diese Muster zu durchbrechen. Basierend auf aktuellen Studien aus der Verhaltensökonomie und Umweltpsychologie zeigen wir konkrete Lösungsansätze auf.',
      category: 'sustainability',
      image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop',
      publishedAt: '2024-11-10',
      readTime: 8
    },
    {
      id: 2,
      title: 'Wie KI personalisierte Ernährung revolutioniert: Ein Blick in die Zukunft',
      excerpt: 'Maschinelles Lernen und fortschrittliche Algorithmen ermöglichen es heute, Ernährungspläne zu erstellen, die perfekt auf individuelle Bedürfnisse, Allergien, Vorlieben und Gesundheitsziele zugeschnitten sind. Wir beleuchten die neuesten Entwicklungen in der KI-gestützten Ernährungsberatung, von personalisierten Nährstoffempfehlungen bis hin zu intelligenten Rezeptvorschlägen, die sich an den Biorhythmus und Lifestyle der Nutzer anpassen. Ein faszinierender Einblick in die Zukunft des Essens.',
      category: 'technology',
      image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop',
      publishedAt: '2024-11-08',
      readTime: 12
    },
    {
      id: 3,
      title: 'Gemeinsam kochen stärkt Familienbande: Neue Studien belegen positive Effekte',
      excerpt: 'Aktuelle Forschungsergebnisse aus der Familienpsychologie zeigen eindeutig, dass Familien, die regelmäßig zusammen kochen, nicht nur stärkere emotionale Bindungen entwickeln, sondern auch bessere Kommunikationsfähigkeiten aufbauen. Kinder lernen dabei wichtige Lebenskompetenzen, entwickeln ein gesünderes Verhältnis zum Essen und zeigen verbesserte schulische Leistungen. Wir analysieren die wissenschaftlichen Erkenntnisse und geben praktische Tipps für den Familienalltag.',
      category: 'family',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
      publishedAt: '2024-11-05',
      readTime: 6
    },
    {
      id: 4,
      title: 'Meal Prep mit System: Wissenschaftlich bewährte Strategien für die Wochenplanung',
      excerpt: 'Entdecke evidenzbasierte Methoden aus der Ernährungswissenschaft und Zeitmanagement-Forschung, um deine Kochzeit zu optimieren und dabei gesünder zu essen. Von der strategischen Menüplanung über effiziente Einkaufslisten bis hin zu cleveren Vorbereitungstechniken - wir zeigen dir, wie du mit wissenschaftlich fundierten Ansätzen mehr Zeit für deine Familie gewinnst, ohne Kompromisse bei der Ernährungsqualität einzugehen.',
      category: 'nutrition',
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      publishedAt: '2024-11-03',
      readTime: 10
    },
    {
      id: 5,
      title: 'Saisonale Küche: Warum lokale Zutaten besser für Umwelt und Gesundheit sind',
      excerpt: 'Eine umfassende wissenschaftliche Betrachtung der Vorteile saisonaler Ernährung für Nachhaltigkeit, Nährstoffdichte und Geschmack. Wir analysieren aktuelle Studien zu CO2-Fußabdrücken, Vitamingehalten und Umweltauswirkungen verschiedener Anbaumethoden. Erfahre, wie du durch bewusste Auswahl regionaler und saisonaler Produkte nicht nur deiner Gesundheit, sondern auch dem Planeten hilfst - inklusive praktischer Saisonkalender und Rezeptideen.',
      category: 'sustainability',
      image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop',
      publishedAt: '2024-11-01',
      readTime: 7
    },
    {
      id: 6,
      title: 'Die Psychologie des Geschmacks: Wie Kinder neue Lebensmittel lieben lernen',
      excerpt: 'Neurowissenschaftliche Erkenntnisse und entwicklungspsychologische Forschung zeigen, wie Eltern ihren Kindern dabei helfen können, vielfältige Geschmäcker zu entwickeln und eine positive Beziehung zum Essen aufzubauen. Von der Rolle der Spiegelneuronen beim Essverhalten bis hin zu praktischen Strategien gegen Neophobien - ein wissenschaftlich fundierter Leitfaden für entspannte Familienmahlzeiten.',
      category: 'family',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
      publishedAt: '2024-10-28',
      readTime: 9
    }
  ]
  
  articles.value = mockArticles
}

onMounted(() => {
  generateMockArticles()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
