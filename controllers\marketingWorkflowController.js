const mongoose = require('mongoose');
const { Buffer } = require('buffer');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const helper = require('../utils/helper');
const MarketingContent = require('../models/marketingContentModel');
const { _internal_fetchRandomFreeMenuData } = require('./menuController'); // Internal function from menuController
const { recordMarketingVideo } = require('../marketingVideoRecorder');
const { uploadImageData } = require('../utils/awsStorage'); // For image buffer upload
const gptController = require('./gptController'); // Assuming this exists and has the functions

// Set base URL based on environment
let TARGET_VIDEO_PAGE_BASE_URL;
if (process.env.NODE_ENV === 'production') {
  TARGET_VIDEO_PAGE_BASE_URL = 'https://ordy.ch/marketing/video1';
} else if (process.env.NODE_ENV === 'preview') {
  TARGET_VIDEO_PAGE_BASE_URL = 'https://test.ordy.ch/marketing/video1';
} else {
  TARGET_VIDEO_PAGE_BASE_URL = 'http://localhost:5173/marketing/video1';
}

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview') {
  console.log('------------TARGET_VIDEO_PAGE_BASE_URL---------------');
  console.log('TARGET_VIDEO_PAGE_BASE_URL ist ' + TARGET_VIDEO_PAGE_BASE_URL);
  console.log('-------------------------------------------------------');
}

// Helper function for background processing
async function processImagesAndVideoInBackground(contentId, textOne, hook) {
    const functionName = 'processImagesAndVideoInBackground';
    log(functionName, `Starting background processing for content ID: ${contentId}`);

    try {
        // --- Step 5 (Background): Generate Images ---
        log(functionName, `Generating 4 images based on Hook="${hook}" and Panel 1="${textOne.substring(0, 50)}..."`);
        if (!gptController || !gptController.generateImagesFromPrompt) {
             log(functionName, 'ERROR: gptController.generateImagesFromPrompt function not found.');
             return; // Stop background task
        }
        const imageResults = await gptController.generateImagesFromPrompt(hook, textOne, 4);

        // --- Step 6 (Background): Upload Images ---
        const imageUrls = [null, null, null, null]; // Initialize with nulls
        if (!imageResults || imageResults.length === 0) {
            log(functionName, 'WARN: No images were generated successfully.');
        } else {
             log(functionName, `Uploading ${imageResults.length} generated images...`);
             const uploadPromises = imageResults.map(async (imgData, index) => {
                 if (!imgData || !Buffer.isBuffer(imgData.data) || !imgData.contentType) {
                     log(functionName, `WARN: Invalid image data structure for image ${index + 1}. Skipping upload.`);
                     return null;
                 }
                 // Ensure contentId is included in filename for uniqueness
                 const imgFileName = `marketing-img-${contentId}-${index + 1}.${imgData.contentType.split('/')[1] || 'png'}`;
                 try {
                     const uploadResult = await uploadImageData(imgData.data, imgFileName, imgData.contentType);
                     if (!uploadResult || !uploadResult.Location) {
                         log(functionName, `WARN: Failed to upload image ${index + 1}.`);
                         return null;
                     }
                     log(functionName, `Image ${index + 1} uploaded to: ${uploadResult.Location}`);
                     return uploadResult.Location;
                 } catch (uploadError) {
                      log(functionName, `ERROR uploading image ${index + 1}: ${uploadError.message}`);
                      return null;
                 }
            });
             // Wait for all uploads to finish and assign URLs
             const uploadedUrls = await Promise.all(uploadPromises);
             uploadedUrls.forEach((url, index) => {
                 if (url && index < 4) { // Assign to correct slot if upload was successful
                     imageUrls[index] = url;
                 }
             });
        }
         log(functionName, `Finished image uploads. URLs: ${JSON.stringify(imageUrls)}`);

        // --- Step 7 (Background): Update DB with Image URLs ---
        log(functionName, `Updating MarketingContent entry ${contentId} with image URLs...`);
        try {
            await MarketingContent.findByIdAndUpdate(contentId, {
                imgOneUrl: imageUrls[0],
                imgTwoUrl: imageUrls[1],
                imgThreeUrl: imageUrls[2],
                imgFourUrl: imageUrls[3],
            });
             log(functionName, 'MarketingContent entry updated with image URLs.');
        } catch (dbUpdateError) {
            log(functionName, `ERROR updating DB with image URLs for ${contentId}: ${dbUpdateError.message}`);
             // Continue to video recording even if image update fails? Or return? For now, continue.
        }

        // --- Step 8 (Background): Record Video ---
        log(functionName, 'Starting video recording...');
        const videoPageUrl = `${TARGET_VIDEO_PAGE_BASE_URL}?contentId=${contentId}`;
        let videoS3Url = null;
        try {
            videoS3Url = await recordMarketingVideo(videoPageUrl);
            if (!videoS3Url) {
                log(functionName, 'WARN: Video recording or S3 upload did not return a URL.');
            } else {
                 log(functionName, `Video recorded and uploaded to S3: ${videoS3Url}`);
            }
        } catch (videoError) {
            log(functionName, `ERROR during video recording/upload: ${videoError.message}`);
        }

        // --- Step 9 (Background): Update DB with Video URL ---
        if (videoS3Url) {
            log(functionName, `Updating MarketingContent entry ${contentId} with video URL...`);
            try {
                await MarketingContent.findByIdAndUpdate(contentId, { videoS3Url: videoS3Url });
                 log(functionName, 'MarketingContent entry updated with video URL.');
            } catch (dbUpdateError) {
                log(functionName, `ERROR updating DB with video URL for ${contentId}: ${dbUpdateError.message}`);
            }
        }

        log(functionName, `Background processing finished for content ID: ${contentId}`);

    } catch (error) {
        // Catch any unexpected errors during background processing
        log(functionName, `FATAL ERROR during background processing for ${contentId}: ${error.message}`);
        console.error(error.stack);
    }
}

// @desc    Trigger the initial marketing content generation (text, recipe)
// @route   POST /api/v1/marketing-workflow/trigger
// @access  Protected
exports.triggerWorkflow = catchAsync(async (req, res, next) => {
    const functionName = 'marketingWorkflowController.triggerWorkflow';
    log(functionName, 'Workflow trigger received (foreground).');
    let contentId = null;

    try {
        // --- Step 1: Generate Marketing Story ---
        log(functionName, 'Generating marketing story...');
        if (!gptController || !gptController.generateMarketingStory) {
            throw new Error('gptController.generateMarketingStory function not found or controller not imported.');
        }
        const marketingStory = await gptController.generateMarketingStory();
        if (!marketingStory || !marketingStory.hook || !marketingStory.textOne || !marketingStory.textTwo || !marketingStory.socialMediaTextMitLink || !marketingStory.socialMediaTextOhneLink) {
            log(functionName, 'ERROR: Failed to generate valid marketing story parts. Received:', marketingStory);
            throw new Error(`Failed to generate valid marketing story parts (hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink). Received: ${JSON.stringify(marketingStory)}`);
        }
        const hook = marketingStory.hook.trim();
        const textOne = marketingStory.textOne.trim();
        const textTwo = marketingStory.textTwo.trim();
        const socialMediaTextMitLink = marketingStory.socialMediaTextMitLink.trim();
        const socialMediaTextOhneLink = marketingStory.socialMediaTextOhneLink.trim();
        log(functionName, `Generated Story: Hook='${hook}', Panel1='${textOne.substring(0,50)}...', Panel2='${textTwo.substring(0,50)}...'`);
        log(functionName, `Generated Social Text (Mit Link - Placeholder): '${socialMediaTextMitLink.substring(0,50)}...'`);
        log(functionName, `Generated Social Text (Ohne Link): '${socialMediaTextOhneLink.substring(0,50)}...'`);

        // --- Step 2: Fetch Random Recipe ---
        log(functionName, 'Fetching random free recipe...');
        const randomRecipe = await _internal_fetchRandomFreeMenuData();
        if (!randomRecipe) {
            throw new Error('Failed to fetch a random free recipe.');
        }
        log(functionName, `Fetched recipe: ${randomRecipe.name} (ID: ${randomRecipe._id})`);
        const standardMenuChild = randomRecipe.menuchilds && randomRecipe.menuchilds.length > 0 ? randomRecipe.menuchilds[0].menuChildId : null;

        // --- Step 2.5: Replace Placeholder in Social Media Text ---
        const recipeLink = `https://www.ordyapp.com/meinerezepte/menu/${randomRecipe._id}`;
        const finalSocialMediaTextMitLink = socialMediaTextMitLink.replace('{recipeLinkPlaceholder}', recipeLink);
        log(functionName, `Generated Social Text (Mit Link - Final): '${finalSocialMediaTextMitLink.substring(0,80)}...'`);

        // --- Step 3: Create Initial MarketingContent Entry ---
        log(functionName, 'Creating INITIAL MarketingContent entry in DB (without images/video)...');
        const newMarketingContentEntry = await MarketingContent.create({
            generationDate: new Date(),
            marketingHook: hook,
            textOne: textOne,
            textTwo: textTwo,
            socialMediaTextMitLink: finalSocialMediaTextMitLink,
            socialMediaTextOhneLink: socialMediaTextOhneLink,
            // Image and Video URLs initially null/absent
            imgOneUrl: null,
            imgTwoUrl: null,
            imgThreeUrl: null,
            imgFourUrl: null,
            videoS3Url: null,
            // Recipe details
            recipeId: randomRecipe._id,
            recipeName: randomRecipe.name,
            recipeImageUrl: randomRecipe.imagelink,
            recipeNumberOfPersons: standardMenuChild ? standardMenuChild.seatCount : null,
            recipeCookingTime: standardMenuChild ? standardMenuChild.cookingTime : null,
        });
        contentId = newMarketingContentEntry._id;
        log(functionName, `Initial MarketingContent entry created with ID: ${contentId}`);

        // --- Step 4: Send Response ---
        log(functionName, 'Sending immediate response to client.');
        res.status(200).json({
            status: 'success',
            message: 'Marketing workflow initiated successfully. Image and video processing started in background.',
            marketingContentId: contentId
        });

        // --- Step 5 (Trigger Background): Process Images & Video ---
        // Call the background function WITHOUT await after sending response
        log(functionName, `Triggering background processing for ID: ${contentId}`);
        // Using setImmediate to ensure the response is sent before background task starts heavily
        // Pass hook in addition to contentId and textOne
        setImmediate(() => {
             processImagesAndVideoInBackground(contentId, textOne, hook);
        });

    } catch (error) {
        // Catch errors ONLY from the foreground processing (Steps 1-4)
        log(functionName, `ERROR during foreground workflow: ${error.message}`);
        console.error(error.stack);

        // If initial DB entry was attempted but failed, contentId might be null 
        // No need to delete anything as it likely failed before creation or on creation
        if (contentId) {
             log(functionName, `Workflow failed during foreground processing for ${contentId}.`);
        } else {
             log(functionName, 'Workflow failed before initial DB entry was created.');
        }

        let errorToPass = error;
        if (!(error instanceof AppError)) {
            // Provide more context in the operational error sent to the client
            errorToPass = new AppError(`Marketing workflow initiation failed: ${error.message}`, 500);
        }
        // Pass error to global handler
        return next(errorToPass);
    }
    // NO code should execute here after sending the response and triggering background task.
});

// Helper function for logging
function log(functionName, message, data = null) {
    const logMessage = `[${functionName}] ${message}`;
    if (helper && helper.devConsole) {
        if (data) { helper.devConsole(logMessage, data); }
        else { helper.devConsole(logMessage); }
    } else {
        if (data) { console.log(`[${new Date().toISOString()}] ${logMessage}`, data); }
        else { console.log(`[${new Date().toISOString()}] ${logMessage}`); }
    }
} 