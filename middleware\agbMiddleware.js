const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');

/**
 * Middleware to check if a user has accepted the AGB (Terms and Conditions)
 * If not, it will return a 403 Forbidden error
 * The frontend should handle this by showing the AGB acceptance modal
 */
exports.requireAgbAcceptance = catchAsync(async (req, res, next) => {
    const functionName = 'agbMiddleware.requireAgbAcceptance';
    
    // Skip check for certain routes that should be accessible without AGB acceptance
    const bypassRoutes = [
        '/api/v1/auth/userdata/update', // Allow updating user data (including AGB acceptance)
        '/api/v1/auth/logout',          // Allow logout
        '/api/v1/auth/reg',             // Allow registration
        '/api/v1/auth/userbyid'         // Allow fetching user data
    ];
    
    // Check if the current route is in the bypass list
    const currentPath = req.originalUrl;
    if (bypassRoutes.some(route => currentPath.includes(route))) {
        helper.devConsole(`[${functionName}] Bypassing AGB check for route: ${currentPath}`);
        return next();
    }
    
    // Check if user exists and has accepted AGB
    if (!req.user) {
        helper.devConsole(`[${functionName}] No user found in request. This middleware should be used after authController.verify`);
        return next(new AppError('Authentication required', 401));
    }
    
    // Check if user has accepted AGB (gdpr value of 2 means accepted)
    if (req.user.gdpr !== 2) {
        helper.devConsole(`[${functionName}] User ${req.user._id} has not accepted AGB (gdpr=${req.user.gdpr})`);
        return next(new AppError('AGB acceptance required', 403));
    }
    
    helper.devConsole(`[${functionName}] User ${req.user._id} has accepted AGB`);
    next();
});
