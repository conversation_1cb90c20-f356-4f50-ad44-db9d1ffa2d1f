const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
//const MenuesRelations = require('../models/menuRelationModel')

// POST @/menu/one/:id
exports.createOneRelationUserToMenu = catchAsync(async (req, res, next) => {
  //console.log("drin")
  //console.log(res.data._id)
  //console.log(req.body.user_id)
  console.log(req.body)
  console.log(req.body.data)
  try{
    //const createRelation = await MenuesRelations.create({menu: res.data._id, user: req.body.user_id, role:"owner"});
    //console.log(createRelation)
    const createRelation = 0;
    // 3) Create session as response
    res.status(201).json({
      status: 'success',
      data: {
        menue: createRelation.data
      }
    });
  } catch(error){
    // Send response
    helper.devConsole(error)
    res.status(403).json({
      status: 'error',
      data: error
    });
  }
});

// get @/menu/related/oneuser/:id
exports.getAllMenuesFromOneUser = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController @/menu/related/oneuser/:id")
    //console.log(req)
    //load relations from the user
    const menues = await MenuesRelations.find({user: req.params.id});
    //console.log(menues)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: menues
      }
    });

  } catch(error){
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
   console.log(error)
  }

});

// get @/menu/related/oneuser/:id/:searchString
exports.getAllMenuesFromOneUserBySearchstring = catchAsync(async (req, res, next) => {
  try {
    console.log(req.params)
    helper.devConsole("menuRelationController @/menu/related/oneuser/:id/:searchstring")
    //console.log(req)
    
    //load relations from the user
    const searchstring = req.params.searchstring;
    const pipeline = [
      { $match: { user: req.params.id } },
      {
        $lookup: {
          from: 'menus', // Der Name der Collection, in der die Menü-Dokumente gespeichert sind.
          localField: 'menu', // Der Feldname in 'MenuesRelations', der auf 'menu' verweist.
          foreignField: '_id', // Der Schlüsselfeldname in der 'menu'-Collection.
          as: 'menu'
        }
      },
      { $unwind: '$menu' },
      { $match: { 'menu.Name': { $regex: new RegExp(searchstring, 'i') } } },
      { $limit: 3 }
    ];

    const result = await MenuesRelations.aggregate(pipeline).exec();

    for (let doc of result) {
      await handlePostInit(doc.menu);
    }

    async function handlePostInit(menuDoc) {
      if(menuDoc.imagelink){
        const newDocumentPath = process.env.S3_PATH + menuDoc.imagelink
        menuDoc.imagelink = newDocumentPath
      } else {
        menuDoc.imagelink = ""
      }
    }

console.log(result)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: result
      }
    });

  } catch(error){
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
   console.log(error)
  }

});

// relation between users and menus
// post @/menu/related/:id
exports.postOneRelation = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController post @/menu/related/:id")
    //console.log(req)
    //load relations from the user
    const menues = await MenuesRelations.create({_id: req.params.id});
    //console.log(menues)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: menues
      }
    });

  } catch(error){
    // Send response
    /*
    res.status(401).json({
      status: 'error',
      data: error
    });
    */
   console.log(error)
  }

});

// patch @/menu/related/:id
exports.patchOneRelation = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController patch @/menu/related/:id")
    helper.devConsole("inPatchOneMenu")
    helper.devConsole(req.body)

    const updateObject = req.body;
    const menue = await MenuesRelations.updateOne({menu: req.params.id}, {$set: updateObject});

    //console.log(menue)

    // Send success response
    res.status(201).json({
      success: true,
      data: {
        menue
      }
    });
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(403).json({
      status: 'error',
      data: error
    });
  }

});

// delete @/menu/related/:id
exports.deleteOneRelation = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController delete @/menu/related/:id")
    //console.log(req)
    //load relations from the user
    const menues = await MenuesRelations.deleteOne({_id: req.params.id});
    //console.log(menues)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: menues
      }
    });

  } catch(error){
    // Send response
    /*
    res.status(401).json({
      status: 'error',
      data: error
    });
    */
   console.log(error)
  }

});

// get @/menu/related/:id
exports.getOneRelation = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController get @/menu/related")
    //helper.devConsole(req.params)

    const menue = await MenuesRelations.findOne({menu: req.params.id});

    //console.log(menue)

    // Send success response
    res.status(201).json({
      success: true,
      data: {
        menue
      }
    });
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(403).json({
      status: 'error',
      data: error
    });
  }

});

// get @/menu/related/severalusers/public
exports.getAllMenuesWithFreeAccess = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController get @/menu/related/public")
    //helper.devConsole(req.params)

    const menue = await MenuesRelations.find({freeAccess: true});

    console.log(menue)

    // Send success response
    res.status(201).json({
      success: true,
      data: {
        menue
      }
    });
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(403).json({
      status: 'error',
      data: error
    });
  }

});

// get @/menu/related/severalusers/public
exports.getAllMenuesWithFreeAccessBySearchstring = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("menuRelationController get @/menu/related/public/:searchstring")
    helper.devConsole(req.params)

    //load relations from the user
    const searchstring = req.params.searchstring;
    const pipeline = [
      { $match: { freeAccess: true } },
      {
        $lookup: {
          from: 'menus', // Der Name der Collection, in der die Menü-Dokumente gespeichert sind.
          localField: 'menu', // Der Feldname in 'MenuesRelations', der auf 'menu' verweist.
          foreignField: '_id', // Der Schlüsselfeldname in der 'menu'-Collection.
          as: 'menu'
        }
      },
      { $unwind: '$menu' },
      { $match: { 'menu.Name': { $regex: new RegExp(searchstring, 'i') } } },
      { $limit: 5 }
    ];

    const result = await MenuesRelations.aggregate(pipeline).exec();

    for (let doc of result) {
      await handlePostInit(doc.menu);
    }

    async function handlePostInit(menuDoc) {
      if(menuDoc.imagelink){
        const newDocumentPath = process.env.S3_PATH + menuDoc.imagelink
        menuDoc.imagelink = newDocumentPath
      } else {
        menuDoc.imagelink = ""
      }
    }

    //const menue = await MenuesRelations.find({freeAccess: true});
    // Name: req.params.searchstring
    console.log(result)

    // Send success response
    res.status(201).json({
      success: true,
      data: {
        result
      }
    });
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(403).json({
      status: 'error',
      data: error
    });
  }

});


//getAllMenuesWithFreeAccessBySearchstring