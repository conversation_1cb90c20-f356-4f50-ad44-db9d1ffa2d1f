/**
 * Netzwerküberwachungsdienst für Ordy
 *
 * Dieser Service überwacht den Netzwerkstatus und benachrichtigt registrierte Callbacks
 * über Änderungen des Netzwerkstatus.
 */

import { useHelperStore } from '../../utils/helper';

// Singleton-Instanz
let instance = null;

class NetworkMonitor {
  constructor() {
    if (instance) {
      return instance;
    }

    this.isOnline = navigator.onLine;
    this.listeners = [];
    this.reconnectListeners = [];
    this.disconnectListeners = [];
    this.lastOnlineTime = this.isOnline ? Date.now() : null;
    this.lastOfflineTime = !this.isOnline ? Date.now() : null;
    this.helper = useHelperStore();

    // Event-Listener für Online/Offline-Ereignisse
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Regelmäßige Überprüfung des Netzwerkstatus (als Fallback) - weniger häufig
    this.checkInterval = setInterval(() => this.checkConnection(), 30000); // Alle 30 Sekunden statt 10

    instance = this;

    this.helper.devConsole(`NetworkMonitor initialisiert. Aktueller Status: ${this.isOnline ? 'online' : 'offline'}`);
  }

  /**
   * Überprüft die Netzwerkverbindung durch einen Ping an den Server
   * Stille Überprüfung ohne Konsolen-Fehler
   */
  async checkConnection() {
    // Nur prüfen, wenn wir denken, dass wir online sind
    // Wenn wir bereits wissen, dass wir offline sind, sparen wir uns die Anfrage
    if (!navigator.onLine) {
      if (this.isOnline) {
        this.isOnline = false;
        this.handleOffline();
      }
      return;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // Kürzerer Timeout

      // Verwende nur navigator.onLine für die Erkennung in der Entwicklungsumgebung
      // In der Produktion können wir echte Ping-Anfragen machen
      const isDevelopment = import.meta.env.DEV;

      if (isDevelopment) {
        // In der Entwicklung verlassen wir uns hauptsächlich auf navigator.onLine
        clearTimeout(timeoutId);
        const wasOffline = !this.isOnline;
        this.isOnline = navigator.onLine;

        if (wasOffline && this.isOnline) {
          this.handleOnline();
        } else if (!wasOffline && !this.isOnline) {
          this.handleOffline();
        }
        return;
      }

      // In der Produktion: Versuche eine echte Netzwerkprüfung
      let response;
      try {
        // Versuche zuerst, die lokale ping.json-Datei zu laden
        response = await fetch(`/ping.json?t=${Date.now()}`, {
          method: 'GET',
          signal: controller.signal,
          cache: 'no-store'
        });
      } catch (localError) {
        // Wenn die lokale Datei nicht verfügbar ist, versuche den API-Server
        response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/ping?t=${Date.now()}`, {
          method: 'HEAD',
          signal: controller.signal,
          cache: 'no-store'
        });
      }

      clearTimeout(timeoutId);

      const wasOffline = !this.isOnline;
      this.isOnline = response.ok;

      if (wasOffline && this.isOnline) {
        this.handleOnline();
      } else if (!wasOffline && !this.isOnline) {
        this.handleOffline();
      }
    } catch (error) {
      // Stille Behandlung von Netzwerkfehlern
      // Nur den Status ändern, wenn wir vorher online waren
      if (this.isOnline) {
        this.isOnline = false;
        this.handleOffline();
      }
      // Keine Konsolen-Ausgabe für erwartete Netzwerkfehler
    }
  }

  /**
   * Behandelt das Online-Ereignis
   */
  handleOnline() {
    this.helper.devConsole('Netzwerkverbindung wiederhergestellt');
    this.isOnline = true;
    this.lastOnlineTime = Date.now();

    // Benachrichtige alle Listener
    this.listeners.forEach(callback => {
      try {
        callback(true);
      } catch (error) {
        console.error('Fehler in Online-Listener:', error);
      }
    });

    // Benachrichtige alle Reconnect-Listener
    this.reconnectListeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Fehler in Reconnect-Listener:', error);
      }
    });

    // Führe eine sofortige Verbindungsprüfung durch
    this.checkConnection();
  }

  /**
   * Behandelt das Offline-Ereignis
   */
  handleOffline() {
    this.helper.devConsole('Netzwerkverbindung verloren');
    this.isOnline = false;
    this.lastOfflineTime = Date.now();

    // Benachrichtige alle Listener
    this.listeners.forEach(callback => {
      try {
        callback(false);
      } catch (error) {
        console.error('Fehler in Offline-Listener:', error);
      }
    });

    // Benachrichtige alle Disconnect-Listener
    this.disconnectListeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Fehler in Disconnect-Listener:', error);
      }
    });
  }

  /**
   * Registriert einen Listener für Netzwerkstatusänderungen
   * @param {Function} callback - Callback-Funktion, die bei Statusänderungen aufgerufen wird
   * @returns {Function} - Funktion zum Entfernen des Listeners
   */
  addStatusListener(callback) {
    this.listeners.push(callback);

    // Rufe den Callback sofort mit dem aktuellen Status auf
    setTimeout(() => callback(this.isOnline), 0);

    // Gib eine Funktion zurück, um den Listener zu entfernen
    return () => {
      this.listeners = this.listeners.filter(cb => cb !== callback);
    };
  }

  /**
   * Registriert einen Listener, der nur bei Wiederverbindung aufgerufen wird
   * @param {Function} callback - Callback-Funktion, die bei Wiederverbindung aufgerufen wird
   * @returns {Function} - Funktion zum Entfernen des Listeners
   */
  addReconnectListener(callback) {
    this.reconnectListeners.push(callback);

    // Gib eine Funktion zurück, um den Listener zu entfernen
    return () => {
      this.reconnectListeners = this.reconnectListeners.filter(cb => cb !== callback);
    };
  }

  /**
   * Registriert einen Listener, der nur bei Verbindungsverlust aufgerufen wird
   * @param {Function} callback - Callback-Funktion, die bei Verbindungsverlust aufgerufen wird
   * @returns {Function} - Funktion zum Entfernen des Listeners
   */
  addDisconnectListener(callback) {
    this.disconnectListeners.push(callback);

    // Gib eine Funktion zurück, um den Listener zu entfernen
    return () => {
      this.disconnectListeners = this.disconnectListeners.filter(cb => cb !== callback);
    };
  }

  /**
   * Gibt zurück, ob die Anwendung online ist
   * @returns {boolean} - true, wenn online, sonst false
   */
  getIsOnline() {
    return this.isOnline;
  }

  /**
   * Gibt die Zeit zurück, die seit dem letzten Verbindungswechsel vergangen ist
   * @returns {number} - Zeit in Millisekunden
   */
  getTimeSinceLastChange() {
    const lastChangeTime = Math.max(this.lastOnlineTime || 0, this.lastOfflineTime || 0);
    return Date.now() - lastChangeTime;
  }

  /**
   * Bereinigt den NetworkMonitor (für Tests und Komponenten-Unmount)
   */
  cleanup() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);

    this.listeners = [];
    this.reconnectListeners = [];
    this.disconnectListeners = [];
  }
}

// Exportiere eine Funktion, die eine Singleton-Instanz zurückgibt
export function useNetworkMonitor() {
  return new NetworkMonitor();
}
