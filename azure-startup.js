'use strict';

/**
 * Azure Startup Logging Script
 *
 * Dieses Skript wird vor dem Start des eigentlichen Servers ausgeführt und
 * protokolliert Informationen zum Deployment und zur Umgebung.
 */

const fs = require('fs');
const os = require('os');
const path = require('path');
const { execSync } = require('child_process');

// Verzeichnis für Protokolle erstellen (falls nicht vorhanden)
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const logFile = path.join(logDir, 'azure-startup.log');

/**
 * Eine Nachricht protokollieren
 * @param {string} message - Die zu protokollierende Nachricht
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  // In die Konsole und in die Datei schreiben
  console.log(message);
  fs.appendFileSync(logFile, logMessage);
}

log('=== AZURE DEPLOYMENT STARTUP LOGS ===');
log(`Deployment gestartet: ${new Date().toISOString()}`);

// Systeminformationen protokollieren
log('\n=== SYSTEM INFORMATION ===');
log(`Hostname: ${os.hostname()}`);
log(`Platform: ${os.platform()}`);
log(`Architecture: ${os.arch()}`);
log(`Node.js-Version: ${process.version}`);
log(`Total Memory: ${Math.round(os.totalmem() / 1024 / 1024)} MB`);
log(`Free Memory: ${Math.round(os.freemem() / 1024 / 1024)} MB`);
log(`CPU Count: ${os.cpus().length}`);

// Azure-spezifische Umgebungsvariablen protokollieren
log('\n=== AZURE ENVIRONMENT VARIABLES ===');
const azureVars = [
  'WEBSITE_SITE_NAME',
  'WEBSITE_HOSTNAME',
  'WEBSITE_INSTANCE_ID',
  'WEBSITE_NODE_DEFAULT_VERSION',
  'WEBSITE_OWNER_NAME',
  'WEBSITE_RESOURCE_GROUP',
];

azureVars.forEach(varName => {
  const value = process.env[varName] || '(nicht gesetzt)';
  log(`${varName}: ${value}`);
});

// App-spezifische Umgebungsvariablen protokollieren (ohne sensible Daten)
log('\n=== APPLICATION ENVIRONMENT VARIABLES ===');
log(`NODE_ENV: ${process.env.NODE_ENV || '(nicht gesetzt)'}`);
log(`PORT: ${process.env.PORT || '(nicht gesetzt)'}`);

// Dateisystemüberprüfung
log('\n=== FILE SYSTEM CHECK ===');
const criticalFiles = [
  'server.js',
  'app.js',
  'package.json',
  'web.config',
  '.deployment'
];

criticalFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  log(`${file}: ${exists ? 'Vorhanden' : 'FEHLT!'}`);
});

// Version der Anwendung aus package.json protokollieren
try {
  const packageJson = require('./package.json');
  log(`\nApp-Version: ${packageJson.name}@${packageJson.version}`);
} catch (error) {
  log(`\nFehler beim Lesen der package.json: ${error.message}`);
}

// Untersuchen, ob MongoDB verbunden werden kann (ohne tatsächlich eine Verbindung herzustellen)
log('\n=== MONGODB CONNECTION CHECK ===');
try {
  if (process.env.NODE_ENV === 'production') {
    log(`MongoDB-Verbindung wird überprüft für: Produktion`);
    // Hier nur prüfen, ob die Variablen existieren, keine echte Verbindung
    const hasConnectionString = !!process.env.DATABASE_PRD;
    const hasPassword = !!process.env.DATABASE_PRD_PASSWORD;
    log(`Verbindungsstring vorhanden: ${hasConnectionString}`);
    log(`Passwort vorhanden: ${hasPassword}`);
  } else {
    log(`MongoDB-Verbindung wird überprüft für: Entwicklung/Test`);
    // Hier nur prüfen, ob die Variablen existieren, keine echte Verbindung
    const hasConnectionString = !!process.env.DATABASE_DEV;
    const hasPassword = !!process.env.DATABASE_DEV_PASSWORD;
    log(`Verbindungsstring vorhanden: ${hasConnectionString}`);
    log(`Passwort vorhanden: ${hasPassword}`);
  }
} catch (error) {
  log(`Fehler bei der MongoDB-Überprüfung: ${error.message}`);
}

// Installierte npm-Pakete überprüfen
log('\n=== NPM PACKAGE CHECK ===');
try {
  const npmOutput = execSync('npm list --depth=0').toString().split('\n');
  // Nur die ersten Zeilen zur Übersicht ausgeben
  npmOutput.slice(0, 10).forEach(line => log(line));
  if (npmOutput.length > 10) {
    log(`... und ${npmOutput.length - 10} weitere Pakete`);
  }
} catch (error) {
  log(`Fehler bei der npm-Paketüberprüfung: ${error.message}`);
}

log('\n=== STARTUP COMPLETED ===');
log(`Startup-Überprüfung abgeschlossen: ${new Date().toISOString()}`);
log('Starte jetzt den Hauptserver...\n');

// Hier können wir den eigentlichen Server starten, wenn gewünscht
// require('./server.js');

// Wenn dieses Skript als eigenständig ausgeführt wird, beenden wir es hier
if (require.main === module) {
  log('Startup-Skript wurde direkt ausgeführt. Beende.');
}

module.exports = { log }; 