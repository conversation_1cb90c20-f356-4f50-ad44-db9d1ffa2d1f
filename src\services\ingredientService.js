import axios from 'axios';

/**
 * Service für intelligente Zutat-Kategorisierung
 * Prüft zuerst die Datenbank, dann KI-Kategorisierung falls nötig
 */
class IngredientService {
  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL
    });

    // Cache für bereits kategorisierte Zutaten
    this.categoryCache = new Map();

    // Cache für Datenbank-Zutaten
    this.ingredientCache = new Map();
  }

  /**
   * Prüft ob eine Zutat bereits in der Datenbank existiert
   * @param {string} ingredientName - Name der Zutat
   * @returns {Promise<Object|null>} Zutat-Objekt oder null
   */
  async checkIngredientInDatabase(ingredientName) {
    const cacheKey = ingredientName.toLowerCase().trim();

    // Prüfe Cache zuerst
    if (this.ingredientCache.has(cacheKey)) {
      return this.ingredientCache.get(cacheKey);
    }

    try {
      const token = localStorage.getItem('session_token');
      if (!token) {
        console.log('IngredientService: No session token found');
        return null;
      }

      // API-Call zur Prüfung der Zutat in der Datenbank
      const response = await this.api.get(`/api/v1/grocerylist/search/grocery/${encodeURIComponent(ingredientName)}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      const ingredient = response.data?.data || null;

      // Cache das Ergebnis
      this.ingredientCache.set(cacheKey, ingredient);

      console.log(`IngredientService: Database check for "${ingredientName}":`, ingredient ? 'Found' : 'Not found');
      return ingredient;

    } catch (error) {
      console.log(`IngredientService: Database check failed for "${ingredientName}":`, error.message);
      // Cache negative Ergebnisse für kurze Zeit
      this.ingredientCache.set(cacheKey, null);
      return null;
    }
  }

  /**
   * Kategorisiert eine Zutat mit KI
   * @param {string} input - User-Eingabe (z.B. "500ml Rahm")
   * @returns {Promise<Object>} Kategorisiertes Ergebnis
   */
  async categorizeWithAI(input) {
    const cacheKey = input.toLowerCase().trim();

    // Prüfe Cache zuerst
    if (this.categoryCache.has(cacheKey)) {
      console.log(`IngredientService: Using cached AI result for "${input}"`);
      return this.categoryCache.get(cacheKey);
    }

    try {
      const token = localStorage.getItem('session_token');
      if (!token) {
        throw new Error('No session token found');
      }

      console.log(`IngredientService: Calling AI categorization for "${input}"`);

      const response = await this.api.post('/api/v1/creator/functions/ingredients/categorize', {
        input: input
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = response.data?.data?.result;
      if (!result) {
        throw new Error('Invalid AI response format');
      }

      // Cache das Ergebnis
      this.categoryCache.set(cacheKey, result);

      console.log(`IngredientService: AI categorization result for "${input}":`, result);
      return result;

    } catch (error) {
      console.log(`IngredientService: AI categorization failed for "${input}":`, error.message);

      // Fallback zur lokalen Kategorisierung
      const fallbackResult = this.fallbackCategorization(input);
      this.categoryCache.set(cacheKey, fallbackResult);
      return fallbackResult;
    }
  }

  /**
   * Fallback-Kategorisierung wenn KI nicht verfügbar ist
   * @param {string} input - User-Eingabe
   * @returns {Object} Kategorisiertes Ergebnis
   */
  fallbackCategorization(input) {
    console.log(`IngredientService: Using fallback categorization for "${input}"`);

    // Einfache Parsing-Logik (ähnlich der bestehenden parseItemInput)
    const text = input.trim().toLowerCase();
    let name = input.trim();
    let quantity = null;
    let unit = null;

    // Versuche Menge und Einheit zu extrahieren
    const numberMatch = text.match(/(\d+(?:[.,]\d+)?)\s*(ml|l|g|kg|stk|stück|prise|el|tl|pck|packung|bund)?/i);
    if (numberMatch) {
      quantity = numberMatch[1].replace(',', '.');
      unit = numberMatch[2] || 'Stk';
      name = input.replace(numberMatch[0], '').trim();
    }

    // Einfache Kategorisierung basierend auf Schlüsselwörtern
    const category = this.getBasicCategory(name.toLowerCase());

    return {
      name: name,
      quantity: quantity,
      unit: unit,
      category: category
    };
  }

  /**
   * Basis-Kategorisierung basierend auf Schlüsselwörtern
   * @param {string} name - Zutatennamen
   * @returns {string} Kategorie
   */
  getBasicCategory(name) {
    const lowerName = name.toLowerCase().trim();

    // Grundnahrungsmittel (erweitert für häufige Zutaten)
    if (/mehl|zucker|salz|pfeffer|reis|nudeln|pasta|spaghetti|penne|fusilli|tagliatelle|lasagne|gnocchi|couscous|bulgur|quinoa|haferflocken|müsli|cornflakes|grieß|stärke|backpulver|hefe|natron|essig|öl|olivenöl|sonnenblumenöl|rapsöl|kokosnussöl|sesam|senf|ketchup|mayonnaise|sojasoße|worcester|tabasco|curry|paprika|zimt|muskat|kardamom|kümmel|koriander|oregano|basilikum|thymian|rosmarin|lorbeer|gewürz|vanille|vanillezucker|puderzucker|rohrzucker|brauner zucker|weißer zucker|kristallzucker|feinzucker|grobzucker|kandiszucker|honig|ahornsirup|agavendicksaft|stevia|süßstoff|backpulver|natron|weinstein|gelatine|agar|speisestärke|maisstärke|kartoffelstärke|paniermehl|semmelbrösel/i.test(lowerName)) {
      return 'Grundnahrungsmittel';
    }

    // Gemüse & Früchte
    if (/apfel|äpfel|birne|banane|orange|zitrone|tomate|gurke|salat|karotte|möhre|zwiebel|knoblauch|paprika|broccoli|brokkoli|spinat|kohl|pilz|champignon|beere|erdbeere|himbeere|blaubeere|heidelbeere|melone|ananas|kiwi|mango|avocado|zucchini|aubergine|kürbis|spargel|lauch|porree|sellerie|rübe|rettich|radieschen|ingwer|petersilie|basilikum|koriander|minze|thymian|rosmarin|schnittlauch|dill|obst|gemüse|frucht|früchte|trauben|kirschen|pflaumen|pfirsich|nektarine|aprikose|feige|dattel|kohlrabi|blumenkohl|rosenkohl|wirsing|weißkohl|rotkohl|chinakohl|fenchel|artischocke|rucola|feldsalat|eisbergsalat|kopfsalat|endivie|chicorée|mangold|pak choi|okra|süßkartoffel|kartoffel|erdäpfel|batate/i.test(lowerName)) {
      return 'Gemüse & Früchte';
    }

    // Milchprodukte & Molkereiprodukte
    if (/milch|joghurt|jogurt|butter|rahm|sahne|käse|quark|schmand|frischkäse|mozzarella|parmesan|camembert|gouda|emmentaler|ricotta|mascarpone|crème|creme|buttermilch|sauerrahm|schlagsahne|schlagrahm|kondensmilch|dosenmilch|trockenmilch|milchpulver|kefir|dickmilch|molke|feta|brie|roquefort|gorgonzola|cheddar|gruyère|appenzeller|tilsiter|limburger|münster|hüttenkäse|cottage cheese|philadelphia|boursin|babybel|leerdammer|bergkäse|hartkäse|weichkäse|schnittkäse|frischkäse|ziegenkäse|schafskäse/i.test(lowerName)) {
      return 'Milchprodukte & Molkereiprodukte';
    }

    // Brotwaren & Backwaren
    if (/brot|brötchen|gipfeli|kuchen|sandwich|toast|croissant|bagel|wecken|zopf|semmel|laugenstange|baguette|ciabatta|vollkornbrot|weißbrot|schwarzbrot|pumpernickel|knäckebrot|zwieback|keks|plätzchen|muffin|donut|berliner|hefezopf|stollen|lebkuchen|brezel|laugenbrezel|focaccia|pita|naan|tortilla|wrap|fladenbrot|sauerteigbrot|roggenbrot|weizenbrot|dinkelbrot|mehrkornbrot|körnerbrot|toastbrot|sandwichbrot|hamburgerbrötchen|hotdog brötchen|kaiser semmel|kornspitz|kornweckerl/i.test(lowerName)) {
      return 'Brotwaren & Backwaren';
    }

    // Fleisch, Wurst & Fisch
    if (/fleisch|wurst|schinken|speck|salami|leberwurst|bratwurst|wiener|frankfurter|mortadella|prosciutto|chorizo|bacon|hähnchen|huhn|pute|truthahn|rind|schwein|lamm|kalb|hirsch|wild|fisch|lachs|thunfisch|forelle|kabeljau|seelachs|hering|makrele|sardine|garnele|shrimp|krabbe|muschel|tintenfisch|calamari|rindersteak|schweinesteak|schnitzel|hackfleisch|faschiertes|gehacktes|bratwurst|weißwurst|blutwurst|mettwurst|teewurst|cervelat|landjäger|bündnerfleisch|bresaola|pancetta|guanciale|coppa|bresaola|serrano|jamón|iberico|parma|san daniele|schwarzwälder|kassler|leberkäse|fleischkäse|corned beef|pastrami|roastbeef|filet|entrecôte|ribeye|t-bone|porterhouse|sirloin|tenderloin|brisket|short ribs|spare ribs|pulled pork|chicken wings|chicken breast|chicken thigh|turkey breast|duck|goose|rabbit|venison|boar|elk|bison|ostrich|quail|pheasant|partridge|grouse|salmon|tuna|cod|haddock|halibut|sole|flounder|sea bass|sea bream|red snapper|grouper|mahi mahi|swordfish|marlin|shark|ray|skate|monkfish|turbot|john dory|plaice|dab|whiting|pollock|hake|ling|gurnard|mullet|mackerel|sardines|anchovies|herring|sprats|pilchards|kippers|smoked salmon|smoked trout|smoked mackerel|smoked haddock|prawns|shrimp|lobster|crab|scallops|mussels|clams|oysters|cockles|whelks|periwinkles|sea urchin|octopus|squid|cuttlefish|calamari/i.test(lowerName)) {
      return 'Fleisch, Wurst & Fisch';
    }

    // Getränke
    if (/wasser|saft|limonade|cola|fanta|sprite|bier|wein|sekt|champagner|whisky|vodka|rum|gin|tequila|brandy|likör|schnaps|kaffee|tee|cappuccino|espresso|latte|smoothie|milchshake|energy|drink|mineralwasser|sprudel|schorle|apfelsaft|orangensaft|traubensaft|cranberrysaft|tomatensaft|karottensaft|multivitaminsaft|nektar|sirup|konzentrat|isotonisch|sportgetränk|energydrink|softdrink|erfrischungsgetränk|fruchtsaft|gemüsesaft|direktsaft|fruchtnektar|fruchtsaftgetränk|limonade|brause|spezi|mezzo mix|schweppes|bitter lemon|tonic water|ginger ale|club mate|fritz kola|bionade|kombucha|kefir wasser|kokoswasser|aloe vera|grüner tee|schwarzer tee|früchtetee|kräutertee|kamillentee|pfefferminztee|rooibos|chai|matcha|oolong|weißer tee|pu erh|earl grey|english breakfast|darjeeling|assam|ceylon|jasmin tee|ingwertee|brennnesseltee|fencheltee|melissentee|salbeitee|thymiantee|lavendeltee|hibiskustee|hagebuttentee|holunderblütentee|lindenblütentee|malventee|passionsblumentee|baldriantee|johanniskrauttee|ginkgotee|ginseng tee|mate tee|guarana|koffein|teein|filterkaffee|instantkaffee|espresso|cappuccino|latte macchiato|café au lait|café con leche|cortado|flat white|americano|lungo|ristretto|macchiato|affogato|irish coffee|vienna coffee|turkish coffee|greek coffee|cold brew|nitro coffee|frappé|iced coffee|eiskaffee|milchkaffee|schwarzkaffee|entkoffeiniert|decaf|single origin|blend|arabica|robusta|fair trade|bio kaffee|röstung|bohnen|gemahlen|instant|löslich|kaffeepads|kapseln|nespresso|dolce gusto|tassimo|senseo/i.test(lowerName)) {
      return 'Getränke';
    }

    // Tiefkühlprodukte
    if (/tiefkühl|gefror|eis|eiscreme|sorbet|tiefgefroren|tk-|frozen|tiefkühltruhe|gefrierschrank|gefrierbeutel|eiswürfel|crushed ice|gelato|frozen yogurt|popsicle|eis am stiel/i.test(lowerName)) {
      return 'Tiefkühlprodukte';
    }

    // Frühstück & Cerealien
    if (/müsli|cornflakes|haferflocken|granola|cerealien|frühstück|marmelade|konfitüre|honig|nutella|erdnussbutter|mandelbutter|ahornsirup|agavendicksaft|croissant|pain|brioche|porridge|overnight oats|chia samen|leinsamen|sonnenblumenkerne|kürbiskerne|sesam|mohn|quinoa|amaranth|buchweizen|hirse|dinkel|hafer|gerste|roggen|weizen|mais|reis|vollkorn|mehrkorn|6-korn|7-korn|9-korn|12-korn|fitness|protein|low carb|glutenfrei|bio|demeter|naturland|bioland|alnatura|dm bio|ja natürlich|zurück zum ursprung|spar natur pur|rewe bio|edeka bio|penny naturgut|netto bio|lidl bio|aldi bio|kaufland bio|real bio|tegut bio|globus bio|famila bio|marktkauf bio|combi bio|v-markt bio|hit bio/i.test(lowerName)) {
      return 'Frühstück & Cerealien';
    }

    // Süsswaren & Snacks
    if (/schokolade|bonbon|gummibärchen|chips|kekse|plätzchen|kuchen|torte|muffin|donut|eis|süßigkeit|süßwaren|snack|nüsse|mandeln|erdnüsse|haselnüsse|walnüsse|pistazien|cashew|macadamia|rosinen|datteln|feigen|trockenfrüchte|popcorn|cracker|salzstangen|brezel|haribo|ferrero|lindt|milka|ritter sport|toblerone|mars|snickers|twix|bounty|kit kat|smarties|m&ms|skittles|mentos|tic tac|fisherman's friend|ricola|halls|em eukal|werther's original|storck|august storck|nimm2|center shock|ahoj brause|brause|sherbet|pop rocks|rock candy|zuckerwatte|cotton candy|lakritz|lakritze|salzlakritz|süßlakritz|katjes|haribo|red band|klene|venco|drop|salmiak|ammoniak|aniseed|fennel|star anise|cardamom|cinnamon|cloves|nutmeg|allspice|ginger|turmeric|paprika|chili|cayenne|jalapeño|habanero|ghost pepper|carolina reaper|scotch bonnet|bird's eye|thai chili|serrano|poblano|chipotle|ancho|guajillo|pasilla|mulato|cascabel|de arbol|pequin|tepin|chiltepin|mirasol|aji amarillo|aji panca|rocoto|malagueta|piri piri|bird pepper|cherry pepper|banana pepper|bell pepper|sweet pepper|hot pepper|chili pepper|chili powder|paprika powder|smoked paprika|sweet paprika|hot paprika|hungarian paprika|spanish paprika|turkish paprika|aleppo pepper|urfa biber|maras pepper|pul biber|isot pepper|nigella|black cumin|fennel seeds|coriander seeds|mustard seeds|fenugreek|methi|ajwain|carom seeds|celery seeds|dill seeds|caraway seeds|anise seeds|star anise|cardamom pods|green cardamom|black cardamom|cinnamon sticks|cassia bark|cloves|whole cloves|nutmeg|mace|allspice berries|juniper berries|pink peppercorns|black peppercorns|white peppercorns|green peppercorns|long pepper|cubeb pepper|grains of paradise|melegueta pepper|sichuan peppercorns|timut pepper|voatsiperifery|andaliman|sancho pepper|japanese pepper|korean pepper|mountain pepper|tasmanian pepper|bush pepper|native pepper|wild pepper|forest pepper|jungle pepper|rainforest pepper|exotic pepper|rare pepper|heirloom pepper|heritage pepper|ancient pepper|traditional pepper|authentic pepper/i.test(lowerName)) {
      return 'Süsswaren & Snacks';
    }

    // Non-Food & Haushaltsartikel
    if (/putzmittel|waschmittel|seife|shampoo|toilettenpapier|küchenpapier|serviette|zahnpasta|zahnbürste|duschgel|deo|deodorant|parfüm|kosmetik|make-up|lippenstift|mascara|nagellack|creme|lotion|sonnencreme|taschentücher|wattepads|wattestäbchen|binden|tampons|windeln|feuchttücher|rasierer|rasierschaum|aftershave|haarspray|haargel|haarwachs|föhn|glätteisen|kamm|bürste|spülmittel|spülmaschinen|weichspüler|reiniger|schwamm|lappen|besen|mopp|staubsauger|müllbeutel|alufolie|frischhaltefolie|backpapier|kerze|batterie|glühbirne|lampe|kabel|werkzeug|schraube|nagel|klebeband|schere|stift|papier|notizblock|drucker|tinte|toner/i.test(lowerName)) {
      return 'Non-Food & Haushaltsartikel';
    }

    console.log(`getBasicCategory: No specific category found for "${name}"`);

    // Intelligenterer Fallback basierend auf häufigen Mustern
    if (/zucker|mehl|salz|pfeffer|öl|essig|gewürz/i.test(lowerName)) {
      console.log(`getBasicCategory: "${name}" matches basic ingredients pattern -> Grundnahrungsmittel`);
      return 'Grundnahrungsmittel';
    }

    if (/milch|rahm|sahne|butter|käse|joghurt|quark/i.test(lowerName)) {
      console.log(`getBasicCategory: "${name}" matches dairy pattern -> Milchprodukte & Molkereiprodukte`);
      return 'Milchprodukte & Molkereiprodukte';
    }

    if (/brot|brötchen|toast|kuchen/i.test(lowerName)) {
      console.log(`getBasicCategory: "${name}" matches bread pattern -> Brotwaren & Backwaren`);
      return 'Brotwaren & Backwaren';
    }

    console.log(`getBasicCategory: No pattern match for "${name}", using Sonstiges`);
    return 'Sonstiges';
  }

  /**
   * Hauptfunktion: Intelligente Zutat-Verarbeitung
   * @param {string} input - User-Eingabe
   * @returns {Promise<Object>} Verarbeitetes Ergebnis
   */
  async processIngredient(input) {
    console.log(`IngredientService: Processing ingredient "${input}"`);

    // Erstmal lokale Kategorisierung als Basis
    const localResult = this.fallbackCategorization(input);
    console.log(`IngredientService: Local categorization result:`, localResult);

    try {
      // 1. Extrahiere groben Namen aus der Eingabe für Datenbank-Check
      const roughName = this.extractRoughName(input);
      console.log(`IngredientService: Extracted rough name: "${roughName}"`);

      // 2. Prüfe ob Zutat in Datenbank existiert
      const existingIngredient = await this.checkIngredientInDatabase(roughName);

      if (existingIngredient) {
        console.log(`IngredientService: Found existing ingredient in database:`, existingIngredient);
        return {
          name: existingIngredient.name || localResult.name,
          quantity: localResult.quantity || '1',
          unit: localResult.unit || 'Stk',
          category: existingIngredient.category || localResult.category,
          source: 'database'
        };
      }

      // 3. Verwende KI-Kategorisierung für neue Zutaten (außer wenn lokale Kategorisierung sehr spezifisch war)
      const shouldUseAI = localResult.category === 'Sonstiges' ||
                         localResult.category === 'Grundnahrungsmittel' ||
                         localResult.category === 'Gemüse & Früchte'; // Oft zu allgemein

      if (shouldUseAI) {
        console.log(`IngredientService: Local result is "${localResult.category}", trying AI categorization`);
        try {
          const aiResult = await this.categorizeWithAI(input);
          console.log(`IngredientService: AI result:`, aiResult);

          // Verwende AI-Ergebnis, aber behalte lokale Parsing-Ergebnisse als Fallback
          return {
            name: aiResult.name || localResult.name,
            quantity: aiResult.quantity || localResult.quantity || '1',
            unit: aiResult.unit || localResult.unit || 'Stk',
            category: aiResult.category || localResult.category,
            source: 'ai'
          };
        } catch (aiError) {
          console.log(`IngredientService: AI categorization failed, using local result:`, aiError.message);
          return {
            ...localResult,
            quantity: localResult.quantity || '1',
            unit: localResult.unit || 'Stk',
            source: 'fallback-after-ai-error'
          };
        }
      } else {
        // Lokale Kategorisierung war erfolgreich und spezifisch
        console.log(`IngredientService: Local categorization was specific (${localResult.category}), using it`);
        return {
          ...localResult,
          quantity: localResult.quantity || '1',
          unit: localResult.unit || 'Stk',
          source: 'local'
        };
      }

    } catch (error) {
      console.log(`IngredientService: Error processing ingredient "${input}":`, error.message);

      // Fallback zur lokalen Verarbeitung
      return {
        ...localResult,
        quantity: localResult.quantity || '1',
        unit: localResult.unit || 'Stk',
        source: 'fallback'
      };
    }
  }

  /**
   * Extrahiert einen groben Namen aus der Eingabe für Datenbank-Suche
   * @param {string} input - User-Eingabe
   * @returns {string} Grober Name
   */
  extractRoughName(input) {
    // Entferne Zahlen und häufige Einheiten am Anfang
    return input
      .replace(/^\d+(?:[.,]\d+)?\s*(ml|l|g|kg|stk|stück|prise|el|tl|pck|packung|bund)?\s*/i, '')
      .trim();
  }

  /**
   * Cache leeren (für Tests oder Reset)
   */
  clearCache() {
    this.categoryCache.clear();
    this.ingredientCache.clear();
    console.log('IngredientService: Cache cleared');
  }
}

// Singleton-Instanz exportieren
export const ingredientService = new IngredientService();
