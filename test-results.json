{"config": {"configFile": "C:\\src\\ordy\\ordy-at-vite5\\playwright.config.js", "rootDir": "C:/src/ordy/ordy-at-vite5/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 600000, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["list", null], ["json", {"outputFile": "test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/src/ordy/ordy-at-vite5/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/src/ordy/ordy-at-vite5/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 1, "webServer": null}, "suites": [{"title": "person-count-buttons.spec.js", "file": "person-count-buttons.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Person Count <PERSON><PERSON> Test", "file": "person-count-buttons.spec.js", "line": 3, "column": 6, "specs": [{"title": "should change person count when clicking + and - buttons", "ok": true, "tags": [], "tests": [{"timeout": 300000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 129903, "errors": [], "stdout": [{"text": "🚀 Starting person count buttons test...\n"}, {"text": "🖥️ Browser warning: The key \"orientation\" is not recognized and ignored.\n"}, {"text": "🖥️ Browser debug: [vite] connecting...\n"}, {"text": "🖥️ Browser debug: [vite] connected.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher triggered (user only): userId='null', firstName='null', email='null'\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher checks (user only): isUserIdentified=false, isFirstNameMissing=true, isEmailMissing=true, profileIncomplete=true\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher final decision (user only): Set showCompleteProfilePopup to false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Reason not showing: User is not identified (userId is null).\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] App.vue mounted. Attempting auth initialization...\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/store/userStore.js] No refresh token found. User is logged out.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Initializing session management\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] No session token found, skipping refresh\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Token refresh interval set up\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA-Setup abgeschlossen. App-Version: 2.4.4\n"}, {"text": "🖥️ Browser log: 🍍 \"oneReciept\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"weekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"grocerylist\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"kitchentable\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"reciepts\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"tempweekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard] Proceeding with navigation to \"/\".\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Portrait-Simu<PERSON> deaktiviert\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): A call to screen.orientation.lock() or screen.orientation.unlock() canceled this call.\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Orientierungssperre initialisiert: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): screen.orientation.lock() is not available on this device.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA: Orientierungssperre konnte nicht aktiviert werden\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA Orientierungsstatus: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Debug mode is enabled by default in development. No requests will be sent to the server. color: rgb(120, 120, 120) color: inherit\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /, path: /}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /, path: /}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/ color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748797915408}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/ color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748797915408}\n"}, {"text": "📍 Navigated to app on port 5174\n"}, {"text": "⏳ Waiting 2 minutes for manual login...\n"}, {"text": "🔑 Please log in manually in the browser window that opened\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] Die App ist jetzt offline-bereit.\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 2): screen.orientation.lock() is not available on this device.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /kochbuch, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] Auth required but no sessionToken found, redirecting to login.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /login, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard] Proceeding with navigation to \"/login\".\n"}, {"text": "🖥️ Browser log: 🔍 PWA Install Card mounted, checking conditions: {disabled: false, count: 0, max_count: 4, install_apple_app: false, isIOS: false}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/login color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/login, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748797919420}\n"}, {"text": "🖥️ Browser log: Wake Lock active\n"}, {"text": "📡 Request failed: https://test.stytch.com/sdk/v1/events - net::ERR_ABORTED\n"}, {"text": "🖥️ Browser error: Failed to load resource: the server responded with a status of 400 ()\n"}, {"text": "🖥️ Browser error: [<PERSON>yt<PERSON>] StytchAPIError: [400] bad_domain_for_stytch_sdk\nThis website has not been registered as an allowed domain for the Stytch SDK. Please add it here: https://stytch.com/dashboard/sdk-configuration\nSee https://stytch.com/docs/api/errors/400#bad_domain_for_stytch_sdk for more information.\nrequest_id: request-id-test-9e73e1fb-31c6-4e8b-9893-e7704464a699\n\n    at _callee2$ (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1901:17)\n    at tryCatch (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1431:40)\n    at Generator.<anonymous> (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1508:18)\n    at Generator.next (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1454:21)\n    at fulfilled (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1763:24)\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Token refresh interval cleared\n"}, {"text": "📡 Request failed: https://test.stytch.com/sdk/v1/events - net::ERR_ABORTED\n"}, {"text": "🖥️ Browser verbose: [DOM] Input elements should have autocomplete attributes (suggested: \"current-password\"): (More info: https://goo.gl/9p2vKq) %o\n"}, {"text": "🖥️ Browser log: %c%s color: red; background: yellow; font-size: 24px; WARNING!\n"}, {"text": "🖥️ Browser log: %c%s font-size: 18px; Using this console may allow attackers to impersonate you and steal your information using an attack called Self-XSS.\nDo not enter or paste code that you do not understand.\n"}, {"text": "✅ Login wait period completed\n"}, {"text": "🖥️ Browser warning: The key \"orientation\" is not recognized and ignored.\n"}, {"text": "🖥️ Browser debug: [vite] connecting...\n"}, {"text": "🖥️ Browser debug: [vite] connected.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher triggered (user only): userId='null', firstName='null', email='null'\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher checks (user only): isUserIdentified=false, isFirstNameMissing=true, isEmailMissing=true, profileIncomplete=true\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher final decision (user only): Set showCompleteProfilePopup to false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Reason not showing: User is not identified (userId is null).\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] App.vue mounted. Attempting auth initialization...\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/store/userStore.js] No refresh token found. User is logged out.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Initializing session management\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] No session token found, skipping refresh\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Token refresh interval set up\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA-Setup abgeschlossen. App-Version: 2.4.4\n"}, {"text": "🖥️ Browser log: 🍍 \"oneReciept\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"weekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"grocerylist\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"kitchentable\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"reciepts\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"tempweekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /kochbuch, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] Auth required but no sessionToken found, redirecting to login.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /login, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard] Proceeding with navigation to \"/login\".\n"}, {"text": "🖥️ Browser log: 🔍 PWA Install Card mounted, checking conditions: {disabled: false, count: 0, max_count: 4, install_apple_app: false, isIOS: false}\n"}, {"text": "🖥️ Browser log: Wake Lock active\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Debug mode is enabled by default in development. No requests will be sent to the server. color: rgb(120, 120, 120) color: inherit\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /, path: /}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /login, path: /login}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/ color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748798035951}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/login color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/login, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748798035951}\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Portrait-Simu<PERSON> deaktiviert\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Orientierungssperre initialisiert: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): A call to screen.orientation.lock() or screen.orientation.unlock() canceled this call.\n"}, {"text": "📍 Navigated to kochbuch\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): screen.orientation.lock() is not available on this device.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA: Orientierungssperre konnte nicht aktiviert werden\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA Orientierungsstatus: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser error: Failed to load resource: the server responded with a status of 400 ()\n"}, {"text": "🖥️ Browser error: [<PERSON>yt<PERSON>] StytchAPIError: [400] bad_domain_for_stytch_sdk\nThis website has not been registered as an allowed domain for the Stytch SDK. Please add it here: https://stytch.com/dashboard/sdk-configuration\nSee https://stytch.com/docs/api/errors/400#bad_domain_for_stytch_sdk for more information.\nrequest_id: request-id-test-6cc871dc-51ae-40f0-8f01-0687375c7e77\n\n    at _callee2$ (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1901:17)\n    at tryCatch (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1431:40)\n    at Generator.<anonymous> (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1508:18)\n    at Generator.next (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1454:21)\n    at fulfilled (http://localhost:5174/node_modules/.vite/deps/@stytch_vanilla-js.js?v=1d6e624a:1763:24)\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 2): screen.orientation.lock() is not available on this device.\n"}, {"text": "📡 Request failed: https://test.stytch.com/sdk/v1/events - net::ERR_ABORTED\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Token refresh interval cleared\n"}, {"text": "🖥️ Browser log: Page hidden, wake lock likely released by browser\n"}, {"text": "🖥️ Browser warning: The key \"orientation\" is not recognized and ignored.\n"}, {"text": "🖥️ Browser debug: [vite] connecting...\n"}, {"text": "🖥️ Browser debug: [vite] connected.\n"}, {"text": "🖥️ Browser warning: [Vue Router warn]: No match found for location with path \"/kochbuch/683b5cdf192a97fc76bb4d89\"\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher triggered (user only): userId='null', firstName='null', email='null'\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher checks (user only): isUserIdentified=false, isFirstNameMissing=true, isEmailMissing=true, profileIncomplete=true\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Watcher final decision (user only): Set showCompleteProfilePopup to false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] Reason not showing: User is not identified (userId is null).\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/App.vue] App.vue mounted. Attempting auth initialization...\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/store/userStore.js] No refresh token found. User is logged out.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Initializing session management\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] No session token found, skipping refresh\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/services/sessionService.js] Token refresh interval set up\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA-Setup abgeschlossen. App-Version: 2.4.4\n"}, {"text": "🖥️ Browser log: 🍍 \"oneReciept\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"weekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"grocerylist\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"kitchentable\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"reciepts\" store installed 🆕\n"}, {"text": "🖥️ Browser log: 🍍 \"tempweekplan\" store installed 🆕\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard START] To: /kochbuch/683b5cdf192a97fc76bb4d89, From: /, LS id: 'null', LS session: false, userStore.id: null\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/router/index.js] [Router Guard] Proceeding with navigation to \"/kochbuch/683b5cdf192a97fc76bb4d89\".\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Debug mode is enabled by default in development. No requests will be sent to the server. color: rgb(120, 120, 120) color: inherit\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /, path: /}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c Running queued event color: rgb(120, 120, 120) color: inherit pageview {route: /kochbuch/683b5cdf192a97fc76bb4d89, path: /kochbuch/683b5cdf192a97fc76bb4d89}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/ color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748798039207}\n"}, {"text": "🖥️ Browser log: %c[Vercel Web Analytics]%c [pageview] http://localhost:5174/kochbuch/683b5cdf192a97fc76bb4d89 color: rgb(120, 120, 120) color: inherit {o: http://localhost:5174/kochbuch/683b5cdf192a97fc76bb4d89, sv: 0.1.3, sdkn: @vercel/analytics/vue, sdkv: 1.4.1, ts: 1748798039207}\n"}, {"text": "📍 Direct navigation to recipe\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Portrait-Simu<PERSON> deaktiviert\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: Orientierungssperre initialisiert: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): A call to screen.orientation.lock() or screen.orientation.unlock() canceled this call.\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 1): screen.orientation.lock() is not available on this device.\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA: Orientierungssperre konnte nicht aktiviert werden\n"}, {"text": "🖥️ Browser log: Screen Orientation API: angle=0, isLandscape=false\n"}, {"text": "🖥️ Browser log: [http://localhost:5174/src/pwa.js] PWA Orientierungsstatus: {isLocked: false, lockAttempts: 1, canUseLockAPI: true, isCurrentlyLandscape: false, orientation: 0}\n"}, {"text": "🖥️ Browser warning: Orientierungssperre fehlgeschlagen (Versuch 2): screen.orientation.lock() is not available on this device.\n"}, {"text": "🔍 Looking for person count elements...\n"}, {"text": "📄 Current page title: <PERSON><PERSON>\n"}, {"text": "📄 Current URL: http://localhost:5174/kochbuch/683b5cdf192a97fc76bb4d89\n"}, {"text": "📄 Body content length: \u001b[33m104\u001b[39m\n"}, {"text": "📄 Body content preview: \n    \n    \n  \nimport registerDevSW from '/@vite-plugin-pwa/pwa-entry-point-loaded';\nregisterDevSW();\n\n\n\n\n"}, {"text": "📄 Vue app element found: \u001b[33mtrue\u001b[39m\n"}, {"text": "📄 Error elements found: 0\n"}, {"text": "📄 Loading elements found: 0\n"}, {"text": "📸 Screenshot saved\n"}, {"text": "🔍 Found 0 total buttons on page\n"}, {"text": "🔍 Found 0 total clickable elements on page\n"}, {"text": "❌ No plus button found\n"}, {"text": "❌ No minus button found\n"}, {"text": "📸 Final screenshot saved\n"}, {"text": "✅ Test completed\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-01T17:11:52.534Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "7a045a6b6405d9738d11-1324b860f949d92d7af7", "file": "person-count-buttons.spec.js", "line": 4, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-01T17:11:51.642Z", "duration": 131094.13400000002, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}