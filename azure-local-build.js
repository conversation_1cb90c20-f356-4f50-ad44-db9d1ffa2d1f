/**
 * Azure Local Build Simulation Script
 * 
 * Dieses Skript simuliert den Azure Web App Deployment-Prozess lokal.
 * Es durchläuft ähnliche Schritte wie die GitHub Actions-Pipeline in .github/workflows/
 */

'use strict';

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Laden der Umgebungsvariablen
dotenv.config({ path: './config.env' });

// Konfiguration
const ENV = process.argv[2] || 'preview'; // 'preview' oder 'production'
if (!['preview', 'production'].includes(ENV)) {
  console.error('Fehler: Umgebung muss entweder "preview" oder "production" sein.');
  process.exit(1);
}

console.log(`Starting Azure local build simulation for ${ENV} environment`);

// 1. Umgebungsvariable setzen (wie in GitHub Actions)
process.env.NODE_ENV = ENV;
console.log(`NODE_ENV is set to: ${process.env.NODE_ENV}`);

try {
  // 2. Abhängigkeiten installieren (wie in GitHub Actions)
  console.log('Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // 3. Build-Prozess (falls vorhanden)
  if (fs.existsSync('./package.json')) {
    const packageJson = require('./package.json');
    if (packageJson.scripts && packageJson.scripts.build) {
      console.log('Running build script...');
      execSync('npm run build', { stdio: 'inherit' });
    } else {
      console.log('No build script found in package.json, skipping build step');
    }
  }

  // 4. Azure Web App spezifische Konfiguration
  // In Azure Web Apps wird standardmäßig die Datei server.js oder app.js ausgeführt
  console.log('Preparing for Azure Web App deployment...');
  
  // Überprüfen Sie, ob die Datei web.config existiert, falls nicht, erstellen Sie sie
  if (!fs.existsSync('./web.config')) {
    console.log('Creating web.config file for Azure...');
    const webConfigContent = `<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <webSocket enabled="false" />
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <rule name="StaticContent">
          <action type="Rewrite" url="public{REQUEST_URI}"/>
        </rule>
        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="server.js"/>
        </rule>
      </rules>
    </rewrite>
    <security>
      <requestFiltering removeServerHeader="true">
        <hiddenSegments>
          <remove segment="bin"/>
        </hiddenSegments>
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
      </customHeaders>
    </httpProtocol>
    <httpErrors existingResponse="PassThrough" />
  </system.webServer>
</configuration>`;
    fs.writeFileSync('./web.config', webConfigContent);
  }

  // 5. Simulieren des Azure-Starts
  console.log('Starting application as it would run on Azure...');
  console.log('Using NODE_ENV:', process.env.NODE_ENV);
  
  // Auf Azure wird "npm start" ausgeführt, aber in der Regel wird direkt node server.js ausgeführt
  console.log('Running: node server.js');
  execSync('node server.js', { stdio: 'inherit' });
  
} catch (error) {
  console.error('Error during Azure build simulation:', error.message);
  process.exit(1);
} 