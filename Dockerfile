# --- Build-Stage (optional, falls Build-Prozess nötig ist) ---
FROM node:22 AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
# Falls ein Build-Prozess nötig ist, hier aktivieren:
# RUN npm run build
# docker stop $(docker ps -q)
# docker run -p 8080:8080 --env-file config.env ordy-api
# docker exec -it 5c0aa773146f /bin/sh

# docker build -t ordy-api .

# Image hochladen bei docker
# docker tag dodorus/ordy-api:latest dodorus/ordy-api:v1.0.1
# docker push dodorus/ordy-api:v1.0.0

# --- Production-Stage ---
FROM node:22-slim AS production
WORKDIR /app
# ENV NODE_ENV=development entfernt, damit die Umgebung von außen gesetzt werden kann
# Nur die notwendigen Dateien übernehmen
COPY --from=build /app/package*.json ./
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/server.js ./
COPY --from=build /app/app.js ./
COPY --from=build /app/start.js ./
COPY --from=build /app/db.js ./
COPY --from=build /app/config.env ./
COPY --from=build /app/public ./public
COPY --from=build /app/routes ./routes
# Kopiere alle Modelldateien und stelle sicher, dass kitchentableModel.js enthalten ist
COPY --from=build /app/models ./models
# Erstelle eine leere kitchentableModel.js-Datei, falls sie nicht existiert
RUN if [ ! -f ./models/kitchentableModel.js ]; then \
    echo "const mongoose = require('mongoose'); \
    const { connection1 } = require('../db.js'); \
    const helper = require('../utils/helper.js'); \
    const User = require('./userModel.js'); \
    const Menu = require('./menuModel.js'); \
    const KitchenTableSchema = new mongoose.Schema({ \
        tableAddress_street: { type: String, required: true }, \
        tableAddress_plztown: { type: String, required: true }, \
        tableAddress_country: { type: String, required: true }, \
        creatorUserId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, \
        members: [ \
            { \
                roleId: { type: mongoose.Schema.Types.ObjectId, ref: 'Role' }, \
                userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' } \
            } \
        ], \
        createdAt: { type: String, default: Date.now() } \
    }); \
    module.exports = connection1.model('Kitchentable', KitchenTableSchema);" > ./models/kitchentableModel.js; \
fi
COPY --from=build /app/controllers ./controllers
COPY --from=build /app/middleware ./middleware
COPY --from=build /app/utils ./utils
# Erstelle marketing_recordings Verzeichnis
RUN mkdir -p ./marketing_recordings
# Erstelle stores Verzeichnis
RUN mkdir -p ./stores
COPY --from=build /app/marketingVideoRecorder.js ./
COPY --from=build /app/security-fix.js ./
COPY --from=build /app/fix-vulnerabilities.js ./
COPY --from=build /app/azure-test-app.js ./
COPY --from=build /app/cleanup.js ./
COPY --from=build /app/azure-startup.js ./
COPY --from=build /app/azure-env-validator.js ./
COPY --from=build /app/azure-local-build.js ./
COPY --from=build /app/deploy-azure-local.js ./
# Falls weitere Verzeichnisse/Dateien benötigt werden, hier ergänzen

# Port für Azure (und lokal)
EXPOSE 8080

# Playwright-Browser installieren (falls benötigt)
RUN npx playwright install --with-deps || true

CMD ["node", "server.js"]