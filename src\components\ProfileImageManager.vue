<template>
    <div class="w-full h-auto">
        <!-- Header -->
        <div class="relative bg-gradient-to-r mt-6 from-ordypurple-100 to-ordypink-200 rounded-3xl p-6 mb-8 shadow-custom shadow-[#E0ADFF]">
            <h1 class="font-YesevaOne text-2xl text-white mb-2">Profilbild verwalten</h1>
            <p class="text-white/80 text-sm">Lade ein eigenes Bild hoch oder lasse eins von der KI generieren</p>

            <!-- Current Profile Image -->
            <div class="absolute top-4 right-4 w-16 h-16 rounded-full overflow-hidden border-4 border-white/30 shadow-lg bg-white/20 flex items-center justify-center">
                <img v-if="hasValidImage" :src="currentImageUrl" :alt="userStore.user.firstName"
                     class="w-full h-full object-cover" />
                <div v-else class="text-white font-bold text-lg">
                    {{ userInitials }}
                </div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

            <!-- Upload Card -->
            <div class="bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center gap-4 mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-YesevaOne text-lg text-gray-800">Bild hochladen</h3>
                        <p class="text-sm text-gray-500">Eigenes Foto verwenden</p>
                    </div>
                </div>

                <!-- File Upload Area -->
                <div class="border-2 border-dashed border-gray-300 rounded-2xl p-6 text-center hover:border-blue-400 transition-colors duration-300"
                     @dragover.prevent @drop.prevent="handleFileDrop" @click="triggerFileInput">
                    <input ref="fileInput" type="file" accept="image/*" @change="handleFileSelect" class="hidden" />

                    <div v-if="!selectedFile" class="cursor-pointer">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <p class="text-gray-600 font-medium">Klicken oder Datei hierher ziehen</p>
                        <p class="text-sm text-gray-400 mt-1">PNG, JPG, HEIC bis 5MB</p>
                    </div>

                    <div v-else class="cursor-pointer">
                        <img :src="previewUrl" class="w-20 h-20 object-cover rounded-full mx-auto mb-2" />
                        <p class="text-sm text-gray-600">{{ selectedFile.name }}</p>
                        <button @click.stop="clearSelection" class="text-red-500 text-xs mt-1 hover:underline">Entfernen</button>
                    </div>
                </div>

                <button @click="uploadImage" :disabled="!selectedFile || uploading"
                        class="w-full mt-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-2xl font-semibold hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300">
                    <span v-if="uploading" class="flex items-center justify-center gap-2">
                        <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Wird hochgeladen...
                    </span>
                    <span v-else>Bild hochladen</span>
                </button>
            </div>

            <!-- AI Generation Card -->
            <div class="bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center gap-4 mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-ordypurple-100 to-ordypink-200 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-YesevaOne text-lg text-gray-800">KI-Generierung</h3>
                        <p class="text-sm text-gray-500">Automatisch erstellen lassen</p>
                    </div>
                </div>

                <!-- AI Prompt Input -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Beschreibung (optional)</label>
                        <textarea v-model="aiPrompt"
                                  placeholder="z.B. Professionelles Portrait, freundlich lächelnd..."
                                  class="w-full p-3 border border-gray-300 rounded-2xl resize-none focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent"
                                  rows="3"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Stil</label>
                        <select v-model="aiStyle"
                                class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
                            <option value="professional">Professionell</option>
                            <option value="realistic">Realistisch</option>
                            <option value="cartoon">Cartoon</option>
                            <option value="artistic">Künstlerisch</option>
                        </select>
                    </div>
                </div>

                <button @click="generateAIImage" :disabled="generating"
                        class="w-full mt-4 bg-gradient-to-r from-ordypurple-100 to-ordypink-200 text-white py-3 rounded-2xl font-semibold hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300">
                    <span v-if="generating" class="flex items-center justify-center gap-2">
                        <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        KI generiert...
                    </span>
                    <span v-else>KI-Bild generieren</span>
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 flex justify-center">
            <button @click="generateQuickAI" :disabled="generating"
                    class="bg-white border-2 border-ordypurple-100 text-ordypurple-100 px-6 py-3 rounded-2xl font-semibold hover:bg-ordypurple-100 hover:text-white transition-all duration-300 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Schnell-Generierung (basierend auf Namen)
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '../store/userStore';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';

const { setNotification } = useNotification();
const userStore = useUserStore();

// Reactive data
const selectedFile = ref(null);
const previewUrl = ref('');
const uploading = ref(false);
const generating = ref(false);
const aiPrompt = ref('');
const aiStyle = ref('professional');
const fileInput = ref(null);

// Computed
const currentImageUrl = computed(() => {
    if (userStore.user.img && userStore.user.img !== 'default.png' && userStore.user.img !== '') {
        // Check if it's already a full URL (from OAuth providers)
        if (userStore.user.img.startsWith('http')) {
            return userStore.user.img;
        }
        // Otherwise, it's a filename from our S3 bucket
        return `https://ordy-images.s3.amazonaws.com/${userStore.user.img}`;
    }
    return '/default-avatar.png'; // Fallback image
});

const hasValidImage = computed(() => {
    return userStore.user.img &&
           userStore.user.img !== 'default.png' &&
           userStore.user.img !== '';
});

const userInitials = computed(() => {
    const firstName = userStore.user.firstName || '';
    const lastName = userStore.user.lastName || '';
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U';
});

// Methods
const triggerFileInput = () => {
    fileInput.value?.click();
};

const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
};

const handleFileDrop = (event) => {
    const file = event.dataTransfer.files[0];
    if (file) {
        processFile(file);
    }
};

const processFile = (file) => {
    if (file.size > 5 * 1024 * 1024) {
        setNotification('Datei ist zu groß. Maximum 5MB erlaubt.', 'alert');
        return;
    }

    selectedFile.value = file;

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
        previewUrl.value = e.target.result;
    };
    reader.readAsDataURL(file);
};

const clearSelection = () => {
    selectedFile.value = null;
    previewUrl.value = '';
    if (fileInput.value) {
        fileInput.value.value = '';
    }
};

const uploadImage = async () => {
    if (!selectedFile.value) return;

    uploading.value = true;
    try {
        const formData = new FormData();
        formData.append('profileImage', selectedFile.value);

        const response = await axios.post(
            `${import.meta.env.VITE_API_BASE_URL}/api/v1/creator/profile/image/upload`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }
        );

        if (response.data.status === 'success') {
            // Update user store
            userStore.user.img = response.data.data.user.img;
            setNotification('Profilbild erfolgreich hochgeladen!', 'success');
            clearSelection();
        }
    } catch (error) {
        console.error('Upload error:', error);
        setNotification('Fehler beim Hochladen des Profilbildes.', 'alert');
    } finally {
        uploading.value = false;
    }
};

const generateAIImage = async () => {
    generating.value = true;
    try {
        const response = await axios.post(
            `${import.meta.env.VITE_API_BASE_URL}/api/v1/creator/profile/image/generate`,
            {
                prompt: aiPrompt.value,
                style: aiStyle.value
            }
        );

        if (response.data.status === 'success') {
            // Update user store
            userStore.user.img = response.data.data.user.img;
            setNotification('KI-Profilbild erfolgreich generiert!', 'success');
            aiPrompt.value = '';
        }
    } catch (error) {
        console.error('AI generation error:', error);
        setNotification('Fehler beim Generieren des KI-Profilbildes.', 'alert');
    } finally {
        generating.value = false;
    }
};

const generateQuickAI = async () => {
    generating.value = true;
    try {
        const response = await axios.post(
            `${import.meta.env.VITE_API_BASE_URL}/api/v1/creator/profile/image/generate`,
            {
                style: 'professional'
            }
        );

        if (response.data.status === 'success') {
            userStore.user.img = response.data.data.user.img;
            setNotification('Schnell-Profilbild erfolgreich generiert!', 'success');
        }
    } catch (error) {
        console.error('Quick AI generation error:', error);
        setNotification('Fehler beim Generieren des Profilbildes.', 'alert');
    } finally {
        generating.value = false;
    }
};
</script>

<style scoped>

</style>
