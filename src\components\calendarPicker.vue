<script setup>
import { ref, reactive } from 'vue'
import { DatePicker } from 'v-calendar'
import 'v-calendar/dist/style.css';

const selectedColor = ref('purple');
const date = ref(new Date())
date.value.setDate(Number(date.value.getDate() + 5))
const range = reactive({
    start: new Date(),
    end: date.value
})

</script>
<template>
    <div class="">
        <DatePicker 
            v-model="range" 
            mode="date" 
            :columns="2" 
            :color="selectedColor"
            borderless
            is-range
            :update-on-input="false"
        >
            <template v-slot="{ inputValue, inputEvents }">
                <input
                    class="bg-white px-2 py-1 rounded font-OpenSans text-sm"
                    id="start"
                    :value="inputValue.start"
                    v-on="inputEvents.start"
                />
                <input
                    class="bg-white px-2 py-1 rounded ml-5 font-OpenSans text-sm"
                    id="end"
                    :value="inputValue.end"
                    readonly
                />
            </template>
        </DatePicker>
    </div>
</template>
<style scoped>

</style>