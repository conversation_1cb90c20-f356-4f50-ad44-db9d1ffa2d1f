<template>
  <!-- Column Builder-->
  <div class="flex md:flex-row flex-col min-h-screen px-6">

    <!-- Middle Container -->
    <div class="md:w-3/4 md:pr-12 w-full">

      <!-- Head-->
      <GoBack />
      <div class="w-full flex md:flex-row flex-col mt-10">
        <textarea
          ref="titleTextarea"
          type="text"
          class="field-sizing-content resize-none font-YesevaOne h-auto mt-1 bg-transparent w-full md:w-9/12 float-left overflow-hidden"
          :value='store.oneReciept.name'
          contenteditable="true"
          @input="handleTitleInput"
          @change="store.changeMenu($event, 'name')"
          placeholder="Rezept-Titel..."
          rows="1" />

        <!-- Action buttons -->
        <div class="md:w-3/12 w-full flex flex-wrap mt-4 md:mt-0">
          <button v-if="store.oneReciept.permission === 'edit'" @click="store.recreateImageOfReciept" class="min-w-[50px] max-w-[51px] max-h-[40px] md:mx-auto mt-5">
              <img src="../../assets/icons/imageedit.png" class="w-1/2 mx-auto" />
              <div class="text-tiny w-full text-center mt-1 font-YesevaOne">Bild</div>
          </button>



          <button v-if="store.oneReciept.permission === 'edit'" class="min-w-[50px] max-w-[51px] max-h-[40px] md:mx-auto mt-5">
              <img
                  @click="store.deleteUserFromMenu(store.oneReciept._id, store.oneReciept.name)"
                  src="../../assets/icons/delete.png"
                  class="w-1/2 mx-auto cursor-pointer" />
              <div class="text-tiny w-full text-center font-YesevaOne">Löschen</div>
          </button>
        </div>
      </div>
      <div class="bg-gray-50 rounded-xl p-4 mt-6 md:w-3/4 w-full">
        <textarea type="text" class="text-sm field-sizing-content bg-transparent w-full resize-none border-none outline-none" :value='store.oneReciept.description' contenteditable="true" @input="handleDescriptionInput" @change="store.changeMenu($event, 'description')" placeholder="Beschreibung des Rezepts..." />
      </div>
      <!-- Head-->

      <!-- HeaderImage -->
      <div
        class="rounded-2xl antialiased bg-cover h-52 w-full mb-8 mt-12"
        :style="{ 'background-image': 'url(' + store.oneReciept.imagelink + ')' }"
      >
      <!-- :style="[store.oneReciept.imagelink ? { 'background-image': store.oneReciept.imagelink } : { 'background': '#FFF' }]" -->
      <div class="w-32 h-32 pt-12 mx-auto z-20">
        <input @change="store.uploadNewImage" class="w-full h-32 opacity-0" type="file" name="image" id="image" >
        <div class="w-full">
          <div class="flex flex-col items-center justify-center -mt-24">
              <template v-if="store.is_dragover === false">
                  <img class="w-12 h-12" src="../../assets/icons/editimage.png" />
                  <p class="pt-1 text-sm text-center tracking-wider text-gray-400 group-hover:text-gray-600">
                      Ziehe dein Bild hier hin
                  </p>
              </template>
              <template v-else>
                  <div class="w-full h-24 mt-0 pt-0 bg-sfggreen-500">
                      <p class="text-white text-center pt-4 w-full mx-auto my-auto">Hochgeladen</p>
                  </div>
              </template>
          </div>
        </div>
      </div>
      </div>
      <!-- HeaderImage -->

      <!-- ContentBlock - Responsive Layout -->
      <div class="flex xl:flex-row flex-col">
        <!-- Zutaten-Sektion - Bei 1200px+ links, sonst oben -->
        <div class="xl:w-3/12 w-full xl:order-1 order-1 xl:pr-6">
          <h2 class="font-YesevaOne">{{ store.oneReciept.menuchilds.numberOfPersons === 1 ? 'Person' : 'Personen' }}</h2>
          <div class="flex flex-row items-center">
            <div class="w-1/4 text-sm mt-1 bg-transparent pt-2 pl-2 min-w-[30px]" editable="false">{{ store.oneReciept.menuchilds.numberOfPersons }}</div>
            <button @click="handlePersonCountChange('up')" :disabled="isUpdatingPersonCount" class="w-10 p-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
              <img v-if="!isUpdatingPersonCount" src="../../assets/icons/plus.png" class="h-5 w-5" />
              <svg v-else class="animate-spin h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
            <button @click="handlePersonCountChange('down')" :disabled="isUpdatingPersonCount" class="w-10 p-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
              <img v-if="!isUpdatingPersonCount" src="../../assets/icons/minus.png" class="h-5 w-5" />
              <svg v-else class="animate-spin h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
            <!-- Alternative Anzeige Ladezustand (optional)
            <div v-if="isUpdatingPersonCount" class="ml-2 text-sm">Lädt...</div>
            -->
          </div>
          <br />
          <h2 class="font-YesevaOne">Zeit</h2>
          <div class="flex flex-row">
            <input class="text-sm mt-1 bg-transparent w-1/4 float-left" :value='store.oneReciept.menuchilds.menuChildId.cookingTime' contenteditable="true" @blur="handleCookingTimeBlur" />
            <label class="text-sm mt-1 bg-transparent float-left text-left">min</label>
          </div>
          <br />
          <br />
          <h2 class="mt-1 font-YesevaOne">Zutaten</h2>
          <p class="text-sm mt-1">Alle Zutaten in der Übersicht</p>
          <br />
          <div v-for="(ingredient, index) in store.oneReciept.menuchilds.menuChildId.ingredients" :key="index">
            <span class="flex flex-row text-xs mt-5 break-words h-6 items-center group">
                <span class="w-6 h-6 bg-purple-100 text-purple-700 rounded-full flex items-center justify-center text-[10px] font-semibold mr-2 flex-shrink-0" :title="`Stabile ID: ${ingredient.stableId || 'Nicht gesetzt'} (Index: ${index})`">
                  {{ ingredient.stableId || '?' }}
                </span>
                <input class="w-12 h-full md:w-10 bg-transparent border-b border-gray-200 focus:border-gray-300 outline-none" :value="ingredient.amount || ''" @blur="(e) => handleIngredientBlur(e, index, 'amount')" placeholder="Menge" />
                <input class="w-16 h-full md:w-10 bg-transparent border-b border-gray-200 focus:border-gray-300 outline-none ml-1" :value="ingredient.unit?.name || ''" @blur="(e) => handleIngredientUnitBlur(e, index)" placeholder="Einheit" />
                <input class="w-24 h-full md:w-24 bg-transparent border-b border-gray-200 focus:border-gray-300 outline-none ml-1" :value="ingredient.name?.name || ''" @blur="(e) => handleIngredientNameBlur(e, index)" placeholder="Zutat" />

                <!-- Delete Button - MINI: Sehr kleine Größe -->
                <button
                  @click="deleteIngredient(index)"
                  class="ml-3 w-2 h-2 md:w-6 md:h-6 bg-red-100 hover:bg-red-200 text-red-600 rounded-full flex items-center justify-center opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
                  <svg class="w-2 h-2 md:w-3 md:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
            </span>
          </div>

          <!-- Add Ingredient Button -->
          <div class="mt-4">
            <button
              @click="addIngredient"
              class="flex items-center gap-2 px-3 py-2 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-lg transition-colors duration-200 text-sm">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Zutat hinzufügen
            </button>
          </div>

        </div>
        <!-- Left Inner Col -->
        <!-- Zubereitung-Sektion - Bei 1200px+ rechts, sonst unten -->
        <div class="xl:w-9/12 w-full xl:order-2 order-2 xl:mt-0 mt-12">
          <div class="w-full flex flex-row">
            <h2 class="w-11/12  font-YesevaOne">Zubereitung</h2>
            <div class="w-1/12">
            </div>
          </div>
          <div class="mt-6 w-full h-auto">
            <div class="block" v-for="(step, index) in store.oneReciept.menuchilds.menuChildId.preperation" :key="index">
              <input class="w-full block border-r-4 bg-transparent mt-4" :value="step.head" @blur="(e) => handlePreparationHeadBlur(e, index)" />
              <textarea placeholder="Write the messages.." class="p-3 field-sizing-content text-xs md:text-normal rezise h-auto block bg-transparent chat-input-area form-input placeholder:text-slate-400/70 w-full" role="textbox" :value="step.content" @blur="(e) => handlePreparationContentBlur(e, index)" contenteditable></textarea>
            </div>
            <img src="../../assets/icons/plus.png" class="w-10 p-2 " @click="store.createEmptyPreperationStep()" />
          </div>
        </div>
        <!-- Left Inner Col -->
      </div>
      <!-- ContentBlock -->
    </div>



    <!-- Right Container -->
    <div class="w-full md:w-1/4 md:p-10 mt-12">
      <div class="w-full">
       <h2>Nährwerte</h2>
       <p>Die Nährwerte dieses Rezept.</p>
       <div class="mt-2 w-full">
        <div class="" v-for="(nutritions, index) in store.oneReciept.menuchilds.menuChildId.nutritions" :key="index">
          <div class="flex flex-row">
            <p>{{ nutritions.name }}:&nbsp;</p>
            <p>{{ nutritions.amount }}&nbsp;</p>
            <p>{{ nutritions.unit }}</p>
          </div>
        </div>
        </div>
      </div>
      <div class="w-full">
        <click-button @click.prevent="store.loadNutrionScore()" :element="{'buttondescription': 'Nährwerte neu berechnen', 'active': store.loadingStateNahrwerte, 'buttonicon': 'reload.png', 'infoboxtext': 'Die Nährwerte werden mit einer AI berechnet und können sehr ungenau sein.', 'iconneeded': true }" :index="1"  />
      </div>

      <div class="w-full mt-12">
       <h2>Zugriff</h2>
        <p>Das Rezept der Community zur Verfügung stellen.</p>
        <div class="w-full flex flex-row mx-auto p-4 bg-white rounded-2xl font-YesevaOne text-xs mt-4 border-black border-2 shadow-custom default-button-shadow">
          <span class="w-10/12 text-center">Rezept veröffentlichen</span>
          <input
            @click="store.setMenuRelationFreeAccessState()"
            class="w-2/12 accent-black"
            type="checkbox"
            v-model="store.oneReciept.freeAccess"
            :true-value=true
            :false-value=false
          >
        </div>
      </div>

      <div class="w-full mt-12">
       <h2>Version</h2>
        <p>Sieh dir deine Versionshistory an</p>
        <div class="text-gray-25">
          Funktion wird bald veröffentlicht..
        </div>
      </div>
    </div>

  </div>

</template>
<script setup>
import { reactive, ref, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import useNotification from '../../../modules/notificationInformation';
import { useMenuStore } from '../../store/menuStore'
import { useAssistantStore } from '../../store/assistantStore'
import { useConfirmationStore } from '../../../utils/helper'
import clickButton from '../../components/clickBorderButton.vue'
import GoBack from '../../body/goBack.vue';

const { setNotification } = useNotification();
const route = useRoute();
const store = useMenuStore();
const assistantStore = useAssistantStore();
const confirmationStore = useConfirmationStore();

// Ref für Titel-Textarea
const titleTextarea = ref(null);

// Auto-resize Funktionalität für Titel mit Auto-Save
const handleTitleInput = (event) => {
  const textarea = event.target;

  // Reset height to auto to get the correct scrollHeight
  textarea.style.height = 'auto';

  // Set height based on scrollHeight
  textarea.style.height = textarea.scrollHeight + 'px';

  // Call original change handler
  store.changeMenu(event, 'name');

  // Trigger auto-save
  triggerAutoSave();
};

// Auto-Save für Beschreibung
const handleDescriptionInput = (event) => {
  store.changeMenu(event, 'description');
  triggerAutoSave();
};

// Auto-Save für Zutaten-Inputs - Nur bei Blur (Feld verlassen)
const handleIngredientBlur = (event, index, field) => {
  try {
    // Spezielle Behandlung für amount - konvertiere zu Number
    if (field === 'amount') {
      const value = parseFloat(event.target.value) || 0;
      store.oneReciept.menuchilds.menuChildId.ingredients[index].amount = value;
      console.log(`📝 Ingredient ${field} updated at index ${index}:`, value, '(converted to number)');
    } else {
      store.changeMenuchildArray(event, index, 'ingredients', field);
      console.log(`📝 Ingredient ${field} updated at index ${index}:`, event.target.value);
    }
    triggerAutoSave();
  } catch (error) {
    console.error('Error updating ingredient:', error);
    setNotification('Fehler beim Aktualisieren der Zutat', 'alert');
  }
};

const handleIngredientUnitBlur = (event, index) => {
  try {
    store.changeMenuchildSecondLevel(event, index, 'ingredients', 'unit', 'name');
    console.log(`📝 Ingredient unit updated at index ${index}:`, event.target.value);
    triggerAutoSave();
  } catch (error) {
    console.error('Error updating ingredient unit:', error);
    setNotification('Fehler beim Aktualisieren der Einheit', 'alert');
  }
};

const handleIngredientNameBlur = (event, index) => {
  try {
    store.changeMenuchildSecondLevel(event, index, 'ingredients', 'name', 'name');
    console.log(`📝 Ingredient name updated at index ${index}:`, event.target.value);
    triggerAutoSave();
  } catch (error) {
    console.error('Error updating ingredient name:', error);
    setNotification('Fehler beim Aktualisieren des Zutatennamen', 'alert');
  }
};

// Auto-Save für Kochzeit - Nur bei Blur
const handleCookingTimeBlur = (event) => {
  store.changeMenuchild(event, 'cookingTime');
  triggerAutoSave();
};

// Auto-Save für Zubereitungsschritte - Nur bei Blur
const handlePreparationHeadBlur = (event, index) => {
  try {
    store.changeMenuchildArray(event, index, 'preperation', 'head');
    console.log(`📝 Preparation head updated at index ${index}:`, event.target.value);
    triggerAutoSave();
  } catch (error) {
    console.error('Error updating preparation head:', error);
    setNotification('Fehler beim Aktualisieren des Zubereitungsschritts', 'alert');
  }
};

const handlePreparationContentBlur = (event, index) => {
  try {
    store.changeMenuchildArray(event, index, 'preperation', 'content');
    console.log(`📝 Preparation content updated at index ${index}:`, event.target.value);
    triggerAutoSave();
  } catch (error) {
    console.error('Error updating preparation content:', error);
    setNotification('Fehler beim Aktualisieren des Zubereitungsinhalts', 'alert');
  }
};

// Auto-resize beim Mount und Legacy-Migration + StableIDs initialisieren
onMounted(async () => {
  if (titleTextarea.value) {
    titleTextarea.value.style.height = 'auto';
    titleTextarea.value.style.height = titleTextarea.value.scrollHeight + 'px';
  }

  // KRITISCH: Legacy-Migration und StableID-Initialisierung
  if (store.oneReciept?.menuchilds?.menuChildId?.ingredients) {
    console.log('🔧 Starting legacy migration and StableID initialization...');

    // Importiere Legacy-Migration
    const { fullLegacyMigration, shouldSaveMigratedRecipe } = await import('../../utils/legacyStableIdMigration.js');

    // Führe Legacy-Migration durch (falls nötig)
    const originalRecipe = JSON.stringify(store.oneReciept);
    store.oneReciept = fullLegacyMigration(store.oneReciept);
    const wasModified = originalRecipe !== JSON.stringify(store.oneReciept);

    // Prüfe ob Auto-Save für migrierte Rezepte nötig ist
    if (shouldSaveMigratedRecipe(store.oneReciept) || wasModified) {
      console.log('💾 Recipe was migrated or modified, triggering auto-save...');
      triggerAutoSave();
    }

    // Logge finale StableID-Übersicht
    const ingredients = store.oneReciept.menuchilds.menuChildId.ingredients;
    console.log('🔒 Final STABLE IDs state (PERMANENT):', ingredients.map((ing, idx) => ({
      index: idx,
      name: ing.name?.name,
      stableId: ing.stableId
    })));
    console.log(`🔢 Recipe max stable ID: ${store.oneReciept.menuchilds.menuChildId.maxUsedStableId} (per recipe, never reused)`);

    console.log('✅ Legacy migration and StableID initialization completed');
  }
});

// Auto-Save Funktionalität - OPTIMIERT für bessere Performance
const autoSaveTimeoutId = ref(null);
const isSaving = ref(false);
const pendingChanges = ref(false);

const triggerAutoSave = () => {
  // Markiere dass Änderungen vorhanden sind
  pendingChanges.value = true;

  if (autoSaveTimeoutId.value) {
    clearTimeout(autoSaveTimeoutId.value);
  }

  // 🔧 KRITISCH: Reduzierter Debounce für schnelleres Speichern
  autoSaveTimeoutId.value = setTimeout(async () => {
    // Prüfe ob bereits ein Save-Vorgang läuft
    if (isSaving.value) {
      console.log('🔄 Auto-save already in progress, skipping...');
      return;
    }

    // Prüfe ob überhaupt Änderungen vorhanden sind
    if (!pendingChanges.value) {
      console.log('🔄 No pending changes, skipping auto-save...');
      return;
    }

    try {
      isSaving.value = true;
      pendingChanges.value = false;
      console.log('🔄 Auto-save starting for menu child:', store.oneReciept.menuchilds.menuChildId._id);

      // Verwende die korrekte Funktion aus dem menuStore
      await store.updateMenuchildAtDB(store.oneReciept.menuchilds.menuChildId._id);
      console.log('✅ Auto-save completed successfully');
      // Keine Erfolgs-Notification, da Auto-Save im Hintergrund läuft
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      // KEINE Notifications für Auto-Save Fehler - läuft im Hintergrund
      console.warn('🚨 Auto-save error suppressed to avoid user spam - data may still be saved');

      // Nur bei kritischen Netzwerkfehlern (keine Verbindung) eine Notification
      if (!error.response && error.code === 'NETWORK_ERROR') {
        setNotification('Keine Internetverbindung - Änderungen werden lokal gespeichert', 'alert');
      }
      // Alle anderen Fehler werden nur geloggt, keine User-Notification
    } finally {
      isSaving.value = false;
    }
  }, 500); // 🔧 KRITISCH: Reduziert von 1000ms auf 500ms für schnelleres Speichern
};

// Zutaten-Management Funktionen mit Auto-Save
const deleteIngredient = async (index) => {
  if (store.oneReciept.menuchilds.menuChildId.ingredients.length > 1) {
    const ingredientToDelete = store.oneReciept.menuchilds.menuChildId.ingredients[index];
    const ingredientStableId = ingredientToDelete.stableId;

    // Prüfe, ob die Zutat im Rezepttext verlinkt ist
    const instructionText = store.oneReciept.menuchilds.menuChildId.instruction || '';

    // Prüfe auch in preperation array
    let allInstructionText = instructionText;
    if (store.oneReciept.menuchilds.menuChildId.preperation) {
      store.oneReciept.menuchilds.menuChildId.preperation.forEach(step => {
        allInstructionText += ' ' + (step.content || '');
      });
    }

    const isLinkedInText = allInstructionText.includes(`\${ID:${ingredientStableId}}`);

    console.log(`🔍 Checking if ingredient ${ingredientStableId} is linked in text:`, {
      stableId: ingredientStableId,
      isLinked: isLinkedInText,
      searchText: `\${ID:${ingredientStableId}}`,
      fullText: allInstructionText,
      instructionText: instructionText,
      preperation: store.oneReciept.menuchilds.menuChildId.preperation
    });



    if (isLinkedInText) {
      // Zeige erweiterten Bestätigungsdialog für globale Löschung
      const confirmed = await confirmationStore.showConfirmation(
        'Zutat aus allen Varianten löschen?',
        `Die Zutat "${ingredientToDelete.name?.name || 'Unbekannt'}" wird noch in der Zubereitung verwendet und wird aus ALLEN Personenanzahl-Varianten dieses Rezepts gelöscht. Möchten Sie fortfahren? Die Platzhalter im Text werden dann nicht mehr funktionieren.`
      );

      if (!confirmed) {
        return; // Abbruch
      }
    } else {
      // Auch für nicht-verlinkte Zutaten fragen, ob global gelöscht werden soll
      const confirmed = await confirmationStore.showConfirmation(
        'Zutat aus allen Varianten löschen?',
        `Soll die Zutat "${ingredientToDelete.name?.name || 'Unbekannt'}" aus ALLEN Personenanzahl-Varianten dieses Rezepts gelöscht werden? Dies betrifft alle vorhandenen Varianten (2 Personen, 4 Personen, etc.).`
      );

      if (!confirmed) {
        return; // Abbruch
      }
    }

    console.log(`🗑️ Globally deleting ingredient with stableId ${ingredientStableId}:`, {
      name: ingredientToDelete.name?.name,
      stableId: ingredientToDelete.stableId,
      wasLinked: isLinkedInText,
      parentId: store.oneReciept.menuchilds.menuChildId.parentId
    });

    try {
      // Verwende die neue globale Lösch-API
      const sessionToken = localStorage.getItem('session_token');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/recipe/${store.oneReciept.menuchilds.menuChildId.parentId}/ingredient/${ingredientStableId}/global-delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🌍 Global ingredient deletion result:', result);

      // Entferne die Zutat auch lokal aus der aktuellen Ansicht
      store.oneReciept.menuchilds.menuChildId.ingredients.splice(index, 1);

      console.log(`✅ Ingredient with stableId ${ingredientStableId} removed from ${result.data.updatedMenuChildsCount} recipe variants`);

      // Zeige Erfolgs-Notification
      setNotification(`Zutat aus ${result.data.updatedMenuChildsCount} Rezept-Varianten entfernt`, 'success');

    } catch (error) {
      console.error('❌ Error during global ingredient deletion:', error);
      setNotification('Fehler beim Löschen der Zutat aus allen Varianten', 'alert');
    }
  } else {
    setNotification('Mindestens eine Zutat ist erforderlich', 'alert');
  }
};

const addIngredient = async () => {
  try {
    console.log('🌍 Adding ingredient globally to all recipe variants...');

    // Zeige Bestätigungsdialog für globale Hinzufügung
    const confirmed = await confirmationStore.showConfirmation(
      'Zutat zu allen Varianten hinzufügen?',
      `Soll die neue Zutat zu ALLEN Personenanzahl-Varianten dieses Rezepts hinzugefügt werden? Die Mengen werden automatisch entsprechend der Personenanzahl skaliert.`
    );

    if (!confirmed) {
      return; // Abbruch
    }

    const basePersonCount = store.oneReciept.menuchilds.numberOfPersons;
    const newIngredient = {
      amount: 1, // Standard-Menge für die aktuelle Personenanzahl
      unit: { name: '' }, // Wird von groceryAndUnitChecker verarbeitet
      name: { name: 'Neue Zutat' } // Platzhalter-Name für groceryAndUnitChecker
      // stableId wird vom Backend vergeben
    };

    console.log(`🌍 Adding ingredient globally with base person count: ${basePersonCount}`);

    // Verwende die neue globale Hinzufügungs-API
    const sessionToken = localStorage.getItem('session_token');
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/recipe/${store.oneReciept.menuchilds.menuChildId.parentId}/ingredient/global-add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${sessionToken}`
      },
      body: JSON.stringify({
        ingredients: [newIngredient], // Array für groceryAndUnitChecker Middleware
        basePersonCount: basePersonCount
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('🌍 Global ingredient addition result:', result);

    // Füge die Zutat auch lokal zur aktuellen Ansicht hinzu
    const localIngredient = {
      ...newIngredient,
      stableId: result.data.assignedStableId
    };

    store.oneReciept.menuchilds.menuChildId.ingredients.push(localIngredient);

    // Aktualisiere maxUsedStableId
    store.oneReciept.menuchilds.menuChildId.maxUsedStableId = result.data.assignedStableId;

    console.log(`✅ Ingredient with stableId ${result.data.assignedStableId} added to ${result.data.updatedMenuChildsCount} recipe variants`);

    // Zeige Erfolgs-Notification
    setNotification(`Zutat zu ${result.data.updatedMenuChildsCount} Rezept-Varianten hinzugefügt`, 'success');

  } catch (error) {
    console.error('❌ Error during global ingredient addition:', error);
    setNotification('Fehler beim Hinzufügen der Zutat zu allen Varianten', 'alert');

    // Fallback: Lokale Hinzufügung
    console.log('🔄 Falling back to local ingredient addition...');
    addIngredientLocal();
  }
};

// Fallback-Funktion für lokale Zutat-Hinzufügung
const addIngredientLocal = () => {
  // KRITISCH: Stabile ID - PERMANENT, nie wieder verwendet
  const ingredients = store.oneReciept.menuchilds.menuChildId.ingredients;

  // Verwende das gespeicherte Maximum und erhöhe es
  let maxUsedId = store.oneReciept.menuchilds.menuChildId.maxUsedStableId || 0;
  const newStableId = maxUsedId + 1;

  // Aktualisiere das Maximum
  store.oneReciept.menuchilds.menuChildId.maxUsedStableId = newStableId;

  const newIngredient = {
    amount: 1, // Number, nicht String
    unit: { name: '' }, // Wird von groceryAndUnitChecker verarbeitet
    name: { name: 'Neue Zutat' }, // Platzhalter-Name für groceryAndUnitChecker
    stableId: newStableId // PERMANENTE ID - ändert sich NIE
  };

  ingredients.push(newIngredient);
  console.log(`🆕 LOCAL ingredient added with PERMANENT stable ID ${newStableId} (max used: ${newStableId})`);

  // Keine Notification mehr - nur Auto-Save
  triggerAutoSave();
};

// --- Loading state for person count ---
const isUpdatingPersonCount = ref(false);
const safetyTimeoutId = ref(null);
const updateTimeoutId = ref(null);

const handlePersonCountChange = (direction) => {
  if (isUpdatingPersonCount.value) {
    console.log("Already updating person count, ignoring click.");
    return;
  }

  // Sofortige lokale Aktualisierung für bessere UX
  const currentCount = store.oneReciept.menuchilds.numberOfPersons;
  const newCount = direction === 'up' ? currentCount + 1 : Math.max(1, currentCount - 1);

  if (newCount === currentCount) {
    return; // Keine Änderung nötig (z.B. bei Versuch unter 1 zu gehen)
  }

  isUpdatingPersonCount.value = true;
  console.log(`Starting update for direction: ${direction}, ${currentCount} -> ${newCount}`);

  // Sofortige lokale Aktualisierung der Personenanzahl
  store.oneReciept.menuchilds.numberOfPersons = newCount;

  // Debounced API-Call für Backend-Update
  if (updateTimeoutId.value) {
    clearTimeout(updateTimeoutId.value);
  }

  updateTimeoutId.value = setTimeout(async () => {
    try {
      // Nur ein einziger API-Call, keine lokale Manipulation
      await store.countPerson(direction);
      console.log("Backend update completed");
      // Auto-save nach Personen-Änderung
      triggerAutoSave();
    } catch (error) {
      console.error("Backend update failed:", error);
      setNotification('Fehler beim Aktualisieren der Personenanzahl', 'alert');
    } finally {
      isUpdatingPersonCount.value = false;
    }
  }, 300); // 300ms Debounce
};

// Watch for changes in the person count coming from the store
watch(
  () => store.oneReciept.menuchilds.numberOfPersons,
  (newValue, oldValue) => {
    console.log(`Watcher detected numberOfPersons change: ${oldValue} -> ${newValue}`);

    // KRITISCH: Datenstruktur-Reparatur nach Personenanzahl-Änderung
    if (store.oneReciept?.menuchilds?.menuChildId?.ingredients) {
      console.log('🔧 Checking ingredient data structure after person count change...');

      store.oneReciept.menuchilds.menuChildId.ingredients.forEach((ingredient, index) => {
        // Repariere fehlende oder beschädigte Datenstrukturen
        if (!ingredient.name || typeof ingredient.name !== 'object') {
          console.warn(`⚠️ Repairing ingredient.name at index ${index} after person count change:`, ingredient.name);
          ingredient.name = { name: ingredient.name || '', _id: ingredient.name || '' };
        }

        if (!ingredient.unit || typeof ingredient.unit !== 'object') {
          console.warn(`⚠️ Repairing ingredient.unit at index ${index} after person count change:`, ingredient.unit);
          ingredient.unit = { name: ingredient.unit || '', _id: ingredient.unit || '' };
        }

        // Stelle sicher, dass amount ein Wert ist
        if (ingredient.amount === undefined || ingredient.amount === null) {
          console.warn(`⚠️ Repairing ingredient.amount at index ${index} after person count change:`, ingredient.amount);
          ingredient.amount = 0;
        }
      });

      console.log('✅ Ingredient data structure repair completed after person count change');
    }

    // Check if an update was in progress when the change occurred
    if (isUpdatingPersonCount.value && newValue !== oldValue) {
      console.log("Change detected while updating, resetting loading state.");
      isUpdatingPersonCount.value = false;
      // Clear the safety timeout as the watch successfully detected the change
      if (safetyTimeoutId.value) {
        clearTimeout(safetyTimeoutId.value);
        safetyTimeoutId.value = null;
      }
    }
  }
);
// --- End Loading state ---

store.getOneMenue(route.params.id)

//console.log(menue.data.data.menue)
//store.setMenue(menue.data.data.menue)

// pinia statemanagement

</script>
<style scoped>
.shake {
  animation: shake 3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>