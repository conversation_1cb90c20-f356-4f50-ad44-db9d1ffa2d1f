import { defineStore } from 'pinia';
import axios from 'axios';
import { useNotification } from '@/composables/useNotification';

const { setNotification } = useNotification();
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL
});

export const useShoppingListStore = defineStore('shoppingList', {
  state: () => ({
    items: [],
    is_active: true,
    activeListId: null,
    isLoading: false,
    historyLists: [], // Neue Eigenschaft für historische Listen
    historyLoading: false
  }),
  
  actions: {
    // Lade aktive Einkaufsliste
    async loadActiveShoppingList() {
      this.isLoading = true;
      try {
        const token = localStorage.getItem('session_token');
        if (!token) {
          setNotification('Nicht eingeloggt. Bitte anmelden.', 'error');
          return;
        }
        
        const response = await api.get('/api/v1/shopping-lists/active', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        if (response.data.data) {
          this.items = response.data.data.items || [];
          this.is_active = response.data.data.is_active;
          this.activeListId = response.data.data._id;
        }
      } catch (error) {
        console.error('Fehler beim Laden der aktiven Einkaufsliste:', error);
        setNotification('Fehler beim Laden der Einkaufsliste', 'error');
      } finally {
        this.isLoading = false;
      }
    },
    
    // Lade Einkaufszettel-Historie
    async loadShoppingListHistory() {
      this.historyLoading = true;
      try {
        const token = localStorage.getItem('session_token');
        if (!token) return;
        
        const response = await api.get('/api/v1/shopping-lists/history', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        if (response.data.data) {
          this.historyLists = response.data.data;
        }
      } catch (error) {
        console.error('Fehler beim Laden der Einkaufszettel-Historie:', error);
      } finally {
        this.historyLoading = false;
      }
    },
    
    // Aktiviere historischen Einkaufszettel
    async activateHistoricalList(listId) {
      if (this.activeListId === listId) return;
      
      this.isLoading = true;
      try {
        const token = localStorage.getItem('session_token');
        if (!token) return;
        
        const response = await api.get(`/api/v1/shopping-lists/${listId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        if (response.data.data) {
          // Setze aktive Liste
          this.activeListId = listId;
          this.items = response.data.data.items || [];
          this.is_active = response.data.data.is_active;
          
          // Benachrichtigung anzeigen
          setNotification(`Einkaufszettel vom ${new Date(response.data.data.createdAt).toLocaleDateString('de-CH')} geladen`, 'success');
        }
      } catch (error) {
        console.error('Fehler beim Aktivieren des historischen Einkaufszettels:', error);
        setNotification('Fehler beim Laden des historischen Einkaufszettels', 'error');
      } finally {
        this.isLoading = false;
      }
    }
  }
});