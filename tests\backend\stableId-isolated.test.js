// Isolierter Test für StableID-Manager ohne DB-Dependencies
const path = require('path');

// Mock helper module to avoid DB dependencies
const mockHelper = {
  devConsole: function(...args) {
    console.log('[DEV]', ...args);
  }
};

// Temporarily replace the helper module
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(id) {
  if (id === '../utils/helper') {
    return mockHelper;
  }
  return originalRequire.apply(this, arguments);
};

// Now require the stableIdManager
const stableIdManager = require('../../utils/stableIdManager');

// Restore original require
Module.prototype.require = originalRequire;

console.log('🧪 Starting Isolated StableID Manager Tests...\n');

// Test 1: Vollständiger Workflow - Neues Rezept erstellen
console.log('📋 Test 1: Vollständiger Workflow - Neues Rezept');

try {
  // Schritt 1: Neues Rezept mit Zutaten ohne stableIds
  const newRecipeIngredients = [
    { name: 'Mehl', amount: 500, unit: 'g' },
    { name: 'Milch', amount: 250, unit: 'ml' },
    { name: 'Eier', amount: 2, unit: 'Stück' }
  ];
  
  console.log('   Schritt 1: Erstelle neues Rezept mit 3 Zutaten');
  const step1 = stableIdManager.assignStableIds(newRecipeIngredients, 0);
  
  console.log('   ✅ StableIDs zugewiesen:', step1.updatedIngredients.map(i => `${i.name}:${i.stableId}`));
  console.log('   ✅ MaxUsedStableId:', step1.newMaxUsedId);
  
  // Schritt 2: Füge eine weitere Zutat hinzu
  console.log('   Schritt 2: Füge "Zucker" hinzu');
  const step2 = stableIdManager.addIngredientWithStableId(
    step1.updatedIngredients,
    { name: 'Zucker', amount: 100, unit: 'g' },
    step1.newMaxUsedId
  );
  
  console.log('   ✅ Zucker hinzugefügt mit stableId:', step2.assignedStableId);
  console.log('   ✅ Neue MaxUsedStableId:', step2.newMaxUsedId);
  
  // Schritt 3: Entferne eine Zutat (Milch an Index 1)
  console.log('   Schritt 3: Entferne "Milch"');
  const step3 = stableIdManager.removeIngredientKeepStableIds(
    step2.updatedIngredients,
    1, // Index von Milch
    step2.newMaxUsedId
  );
  
  console.log('   ✅ Milch entfernt, stableId:', step3.removedStableId);
  console.log('   ✅ Verbleibende Zutaten:', step3.updatedIngredients.map(i => `${i.name}:${i.stableId}`));
  
  // Schritt 4: Füge eine neue Zutat hinzu (sollte ID 5 bekommen, nicht 2)
  console.log('   Schritt 4: Füge "Salz" hinzu (ID sollte 5 sein, nicht 2)');
  const step4 = stableIdManager.addIngredientWithStableId(
    step3.updatedIngredients,
    { name: 'Salz', amount: 5, unit: 'g' },
    step2.newMaxUsedId // MaxUsedId bleibt 4, auch nach Löschung
  );
  
  console.log('   ✅ Salz hinzugefügt mit stableId:', step4.assignedStableId);
  console.log('   ✅ Finale MaxUsedStableId:', step4.newMaxUsedId);
  
  // Validierung des finalen Zustands
  const finalValidation = stableIdManager.validateStableIds({
    ingredients: step4.updatedIngredients,
    maxUsedStableId: step4.newMaxUsedId
  });
  
  if (finalValidation.isValid && step4.assignedStableId === 5) {
    console.log('✅ Test 1 PASSED: Vollständiger Workflow funktioniert korrekt\n');
  } else {
    console.error('❌ Test 1 FAILED: Workflow-Validierung fehlgeschlagen\n');
  }
  
} catch (error) {
  console.error('❌ Test 1 FAILED:', error.message, '\n');
}

// Test 2: Edge Case - Bestehende Zutaten mit gemischten stableIds
console.log('📋 Test 2: Edge Case - Gemischte stableIds');

try {
  const mixedIngredients = [
    { name: 'Mehl', amount: 500, unit: 'g', stableId: 1 },
    { name: 'Milch', amount: 250, unit: 'ml' }, // Keine stableId
    { name: 'Eier', amount: 2, unit: 'Stück', stableId: 5 }, // Hohe stableId
    { name: 'Zucker', amount: 100, unit: 'g' }, // Keine stableId
    { name: 'Salz', amount: 5, unit: 'g', stableId: 3 }
  ];
  
  console.log('   Eingabe: Gemischte stableIds [1, null, 5, null, 3]');
  
  const result = stableIdManager.assignStableIds(mixedIngredients, 5);
  
  console.log('   ✅ Ergebnis:', result.updatedIngredients.map(i => `${i.name}:${i.stableId}`));
  console.log('   ✅ MaxUsedStableId:', result.newMaxUsedId);
  
  // Prüfe, dass bestehende IDs erhalten bleiben und neue korrekt zugewiesen werden
  const expectedIds = [1, 6, 5, 7, 3]; // Milch=6, Zucker=7
  const actualIds = result.updatedIngredients.map(i => i.stableId);
  
  if (JSON.stringify(actualIds) === JSON.stringify(expectedIds) && result.newMaxUsedId === 7) {
    console.log('✅ Test 2 PASSED: Gemischte stableIds korrekt behandelt\n');
  } else {
    console.error('❌ Test 2 FAILED: Erwartete IDs:', expectedIds, 'Erhalten:', actualIds, '\n');
  }
  
} catch (error) {
  console.error('❌ Test 2 FAILED:', error.message, '\n');
}

// Test 3: Performance Test - Viele Zutaten
console.log('📋 Test 3: Performance Test - 100 Zutaten');

try {
  const startTime = Date.now();
  
  // Erstelle 100 Zutaten ohne stableIds
  const manyIngredients = Array.from({ length: 100 }, (_, i) => ({
    name: `Zutat_${i + 1}`,
    amount: 100,
    unit: 'g'
  }));
  
  const result = stableIdManager.assignStableIds(manyIngredients, 0);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`   ✅ 100 Zutaten verarbeitet in ${duration}ms`);
  console.log('   ✅ Erste 5 stableIds:', result.updatedIngredients.slice(0, 5).map(i => i.stableId));
  console.log('   ✅ Letzte 5 stableIds:', result.updatedIngredients.slice(-5).map(i => i.stableId));
  console.log('   ✅ MaxUsedStableId:', result.newMaxUsedId);
  
  if (result.newMaxUsedId === 100 && duration < 100) { // Sollte unter 100ms sein
    console.log('✅ Test 3 PASSED: Performance-Test bestanden\n');
  } else {
    console.error('❌ Test 3 FAILED: Performance oder Korrektheit nicht erfüllt\n');
  }
  
} catch (error) {
  console.error('❌ Test 3 FAILED:', error.message, '\n');
}

// Test 4: Reparatur-Funktion
console.log('📋 Test 4: StableID-Reparatur');

try {
  const brokenMenuChild = {
    ingredients: [
      { name: 'Mehl', stableId: 1 },
      { name: 'Milch' }, // Fehlende stableId
      { name: 'Eier', stableId: 3 },
      { name: 'Zucker' }, // Fehlende stableId
    ],
    maxUsedStableId: 3
  };
  
  console.log('   Eingabe: MenuChild mit fehlenden stableIds');
  
  const repaired = stableIdManager.repairStableIds(brokenMenuChild);
  
  console.log('   ✅ Repariert:', repaired.ingredients.map(i => `${i.name}:${i.stableId}`));
  console.log('   ✅ Neue MaxUsedStableId:', repaired.maxUsedStableId);
  
  const validation = stableIdManager.validateStableIds(repaired);
  
  if (validation.isValid && repaired.maxUsedStableId === 5) {
    console.log('✅ Test 4 PASSED: StableID-Reparatur funktioniert\n');
  } else {
    console.error('❌ Test 4 FAILED: Reparatur nicht erfolgreich\n');
  }
  
} catch (error) {
  console.error('❌ Test 4 FAILED:', error.message, '\n');
}

console.log('🎉 Alle isolierten StableID-Tests abgeschlossen!');
console.log('');
console.log('📊 Test-Zusammenfassung:');
console.log('✅ Vollständiger CRUD-Workflow');
console.log('✅ Edge Cases mit gemischten stableIds');
console.log('✅ Performance mit 100 Zutaten');
console.log('✅ Automatische Reparatur-Funktion');
console.log('');
console.log('🚀 Das StableID-System ist bereit für die Integration!');
