<template>
  <!-- Column MenuDetails Builder-->
  <div class="flex md:flex-row flex-col min-h-screen px-6">

    <!-- Middle MenuDetails Container -->
    <div class="md:w-3/4 md:pr-12 w-full">
      <!-- Head-->
      <GoBack />

      <!-- Assistant Animation -->
      <div
        v-show="assistantStore.activeAssistant"
        class="absolute w-12 h-12 bg-ordypink-200 rounded-full float-end ml-72 z-40 animate-ping"
      >
      </div>
      <div
        v-show="assistantStore.activeAssistantOnPause"
        class="absolute w-12 h-12 bg-ordypink-200 rounded-full float-end ml-72 z-40"
      >
      </div>

      <div class="w-full flex md:flex-row flex-col mt-10">
        <h1 class="w-full xl:w-9/12 lg:w-8/12 md:w-7/12 h-auto pb-3 leading-tight">{{ menustore.oneReciept.name }}</h1>
        <!-- Action buttons -->
        <div class="xl:w-3/12 lg:w-4/12 md:w-5/12 w-full">
          <div class="flex flex-row justify-center md:justify-end gap-4 mt-5">

            <!-- Neutrale Action Buttons mit Icons -->
            <button v-if="menustore.oneReciept.permission === 'edit'" @click="menustore.recreateImageOfReciept" class="flex flex-col items-center min-w-[60px]">
                <img src="../../assets/icons/imageedit.png" class="w-6 h-6 mb-1 object-contain" />
                <div class="text-tiny text-center font-YesevaOne">Bild</div>
            </button>

            <button v-if="menustore.oneReciept.permission === 'edit'" @click="menustore.goToEditModeOfReciept" class="flex flex-col items-center min-w-[60px]">
                <img src="../../assets/icons/recieptedit.png" class="w-6 h-6 mb-1 object-contain" />
                <div class="text-tiny text-center font-YesevaOne">Bearbeiten</div>
            </button>

            <button v-if="menustore.oneReciept.permission === 'edit'" @click="assistantStore.toggleAssistant()" class="flex flex-col items-center min-w-[60px]">
                <img
                  v-show="!assistantStore.activeAssistant"
                  src="../../assets/icons/assistent_off.png"
                  class="w-6 h-6 mb-1 object-contain transition-opacity duration-1000 ease-in-out opacity-100"
                />

                <img
                  v-show="assistantStore.activeAssistant"
                  src="../../assets/icons/assistent_on.png"
                  class="w-6 h-6 mb-1 object-contain transition-opacity duration-1000 ease-in-out opacity-100"
                />

                <div class="text-tiny text-center font-YesevaOne">Assistent</div>
            </button>

            <button v-if="menustore.oneReciept.permission === 'edit'" @click="menustore.deleteUserFromMenu(menustore.oneReciept._id, menustore.oneReciept.name)" class="flex flex-col items-center min-w-[60px]">
                <img src="../../assets/icons/delete.png" class="w-6 h-6 mb-1 object-contain" />
                <div class="text-tiny text-center font-YesevaOne">Löschen</div>
            </button>

          </div>
        </div>
        <!-- Action buttons -->
      </div>
      <div class="bg-gray-50 rounded-xl p-4 mt-6 md:w-2/3 w-full">
        <p class="text-gray-700 leading-relaxed">{{ menustore.oneReciept.description }}</p>
      </div>
      <!-- Head-->

      <!-- HeaderImage -->
      <div
        v-if="!menustore.is_loading"
        class="rounded-2xl antialiased bg-cover h-52 w-full mt-8 mb-8 bg-center"
        :style="{ 'background-image': 'url(' + menustore.oneReciept.imagelink + ')' }"

      >
      </div>

      <!-- loading state -->
      <div
        v-if="menustore.is_loading"
        class="rounded-2xl antialiased bg-cover h-52 w-full mt-8 mb-8 bg-center bg-ordypurple-100 pt-16"
      >
        <div class="w-auto h-auto mx-auto">
          <img src="../../assets/icons/reload.png" class="bg-white rounded-[200px] animate-spin mx-auto my-auto w-auto h-auto" />
        </div>
      </div>
      <!-- loading state -->
      <!-- HeaderImage -->

      <!-- ContentBlock - Responsive Layout -->
      <div class="flex xl:flex-row flex-col">
        <!-- Left Inner Col -->
        <!-- Info-Cards - Responsive Grid Layout -->
        <div class="w-full xl:w-3/12 xl:order-1 order-1 xl:pr-6">

          <!-- Grid Layout für alle Breakpoints -->
          <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-1 gap-4 mb-6">

            <!-- Personen Card -->
            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <div class="flex items-center justify-between mb-3">
                <h3 class="font-semibold text-gray-800">Personen</h3>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                </svg>
              </div>
              <div class="flex items-center gap-3">
                <button
                  @click="handlePersonCountChange('down')"
                  :disabled="isUpdatingPersonCount || menustore.oneReciept.menuchilds.numberOfPersons <= 1"
                  class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                  </svg>
                </button>

                <span class="text-2xl font-bold text-gray-900 min-w-[3rem] text-center">
                  {{ menustore.oneReciept.menuchilds.numberOfPersons }}
                </span>

                <button
                  @click="handlePersonCountChange('up')"
                  :disabled="isUpdatingPersonCount"
                  class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </button>

                <div v-if="isUpdatingPersonCount" class="ml-2">
                  <svg class="animate-spin h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Zeit Card -->
            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <div class="flex items-center justify-between mb-3">
                <h3 class="font-semibold text-gray-800">Zubereitungszeit</h3>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="text-2xl font-bold text-gray-900">
                {{ menustore.oneReciept.menuchilds.menuChildId.cookingTime }} <span class="text-sm font-normal text-gray-500">min</span>
              </div>
            </div>
          </div>

          <!-- Zutaten Card -->
          <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="font-semibold text-gray-800">Zutaten</h3>
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
            </div>
            <div class="space-y-1">
              <div v-for="(ingredient, index) in menustore.oneReciept.menuchilds.menuChildId.ingredients" :key="index"
                   class="flex items-center gap-3 py-2 px-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span :class="getCategoryColor(ingredient)" class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold flex-shrink-0" :title="`Stabile ID: ${ingredient.stableId || 'Nicht gesetzt'} (Index: ${index})`">
                  {{ ingredient.stableId || '?' }}
                </span>
                <span class="text-sm text-gray-700 flex-1">
                  <span class="font-medium">{{ getScaledAmount(ingredient) }} {{ ingredient.unit?.name || '' }}</span>
                  {{ ingredient.name?.name || 'Unbekannte Zutat' }}
                </span>
              </div>
            </div>
          </div>

        </div>
        <!-- Left Inner Col -->
        <!-- Left Inner Col -->
        <div class="w-full xl:w-9/12 xl:order-2 order-2 xl:mt-0 mt-12 pb-32">



          <div class="w-full flex flex-row">
            <h2 class="w-11/12">Zubereitung</h2>
            <div class="w-1/12">
              <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
                <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
                <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
              </svg>
            </div>
          </div>
          <div class="mt-6 w-full h-auto">
            <div v-for="(step, index) in menustore.oneReciept.menuchilds.menuChildId.preperation" :key="index">
              <h3 class="w-full font-bold mt-3">{{ step.head }}</h3>
              <p class="w-full h-auto mt-1" v-html="processedInstructionContent(step.content)"></p>
            </div>
          </div>
        </div>
        <!-- Left Inner Col -->
      </div>
      <!-- ContentBlock -->

    </div>


    <!-- Right Container -->
    <div class="md:w-1/4 w-full md:p-10 md:mt-12 mt-2 pb-12">
      <!--- Nährwerte -->
       <h2>Nährwerte</h2>
       <div v-for="(nutritions, index) in menustore.oneReciept.menuchilds.menuChildId.nutritions" :key="index">
        <div class="flex flex-row mt-1">
          <p>{{ nutritions.name }}:&nbsp;</p>
          <p>{{ nutritions.amount }}&nbsp;</p>
          <p>{{ nutritions.unit }}</p>
        </div>
       </div>
       <!--- Share the reciept -->
       <div class="mt-12">
        <h2>Teilen</h2>
        <click-button @click.prevent="menustore.createShareableLink()" :element="{'buttondescription': 'Link generieren', 'buttonicon': 'share.png', 'infoboxtext': 'Die Nährwerte werden mit einer AI berechnet und können sehr ungenau sein.', 'iconneeded': true }" :index="1"  />
       </div>
    </div>

  </div>

</template>
<script setup>
import { reactive, ref, computed, watch, watchEffect, onBeforeUnmount, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../../components/menuCard.vue'
import dayCard from '../../components/dayCard.vue'
import useNotification from '../../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../../store/menuStore'
import { useUserStore } from '../../store/userStore'
import { useAssistantStore } from '../../store/assistantStore'
import GoBack from '../../body/goBack.vue';
import { useHelperStore } from '../../../utils/helper'
import clickButton from '../../components/clickBorderButton.vue'
import { processInstructionText } from '../../utils/recipeUtils'

const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const menustore = useMenuStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore();
const assistantStore = useAssistantStore();
const helper = useHelperStore()

// --- Performance-optimierte Personen +/- Funktionalität ---
const isUpdatingPersonCount = ref(false);
const updateTimeoutId = ref(null);

// Funktion für gerundete Zutaten-Mengen
const getScaledAmount = (ingredient) => {
  if (!menustore.oneReciept?.menuchilds?.menuChildId) return ingredient.amount;

  const currentPersons = menustore.oneReciept.menuchilds.numberOfPersons || 1;
  const originalPersons = menustore.oneReciept.menuchilds.menuChildId.seatCount || currentPersons;
  const scaleFactor = currentPersons / originalPersons;

  return Math.round((ingredient.amount * scaleFactor) * 10) / 10;
};

// Kategorie-basierte Farben für Zutaten
const getCategoryColor = (ingredient) => {
  const ingredientName = ingredient.name?.name?.toLowerCase() || '';

  // Gemüse & Früchte - Grün
  if (ingredientName.includes('brokkoli') || ingredientName.includes('tomate') || ingredientName.includes('zwiebel') ||
      ingredientName.includes('karotte') || ingredientName.includes('paprika') || ingredientName.includes('salat') ||
      ingredientName.includes('apfel') || ingredientName.includes('banane') || ingredientName.includes('orange')) {
    return 'bg-green-100 text-green-700';
  }

  // Milchprodukte - Blau
  if (ingredientName.includes('milch') || ingredientName.includes('käse') || ingredientName.includes('joghurt') ||
      ingredientName.includes('butter') || ingredientName.includes('sahne') || ingredientName.includes('quark') ||
      ingredientName.includes('parmesan') || ingredientName.includes('frischkäse')) {
    return 'bg-blue-100 text-blue-700';
  }

  // Fleisch & Fisch - Rot
  if (ingredientName.includes('fleisch') || ingredientName.includes('hähnchen') || ingredientName.includes('rind') ||
      ingredientName.includes('schwein') || ingredientName.includes('fisch') || ingredientName.includes('lachs') ||
      ingredientName.includes('thunfisch') || ingredientName.includes('garnele')) {
    return 'bg-red-100 text-red-700';
  }

  // Getreide & Pasta - Orange
  if (ingredientName.includes('pasta') || ingredientName.includes('nudel') || ingredientName.includes('reis') ||
      ingredientName.includes('brot') || ingredientName.includes('mehl') || ingredientName.includes('haferflocken')) {
    return 'bg-orange-100 text-orange-700';
  }

  // Gewürze & Kräuter - Gelb (statt Violett wegen App-Hintergrund)
  if (ingredientName.includes('salz') || ingredientName.includes('pfeffer') || ingredientName.includes('petersilie') ||
      ingredientName.includes('basilikum') || ingredientName.includes('oregano') || ingredientName.includes('kräuter')) {
    return 'bg-yellow-100 text-yellow-700';
  }

  // Standard - Grau
  return 'bg-gray-100 text-gray-700';
};

const handlePersonCountChange = (direction) => {
  if (isUpdatingPersonCount.value) {
    console.log("Already updating person count, ignoring click.");
    return;
  }

  const currentCount = menustore.oneReciept.menuchilds.numberOfPersons;
  const newCount = direction === 'up' ? currentCount + 1 : Math.max(1, currentCount - 1);

  if (newCount === currentCount) {
    return; // Keine Änderung nötig
  }

  isUpdatingPersonCount.value = true;
  console.log(`Person count change: ${currentCount} -> ${newCount} (direction: ${direction})`);

  // Debounced API-Call OHNE lokale Aktualisierung
  if (updateTimeoutId.value) {
    clearTimeout(updateTimeoutId.value);
  }

  updateTimeoutId.value = setTimeout(async () => {
    try {
      // Nur ein einziger API-Call, keine lokale Manipulation
      await menustore.countPerson(direction);
      console.log("Backend update completed");
    } catch (error) {
      console.error("Backend update failed:", error);
      setNotification('Fehler beim Aktualisieren der Personenanzahl', 'alert');
    } finally {
      isUpdatingPersonCount.value = false;
    }
  }, 300); // 300ms Debounce
};

// Computed function for processing instruction content with dynamic quantities
const processedInstructionContent = (instructionText) => {
  if (!instructionText || !menustore.oneReciept?.menuchilds?.menuChildId) {
    return instructionText;
  }

  const ingredients = menustore.oneReciept.menuchilds.menuChildId.ingredients || [];
  const currentPersons = menustore.oneReciept.menuchilds.numberOfPersons || 1;
  const originalPersons = menustore.oneReciept.menuchilds.menuChildId.seatCount || currentPersons;

  // Process the instruction text with dynamic quantities
  const processedText = processInstructionText(
    instructionText,
    ingredients,
    currentPersons,
    originalPersons
  );

  // Convert line breaks to HTML for proper display
  return processedText.replace(/\n/g, '<br>');
};

// Screen Wake Lock
let wakeLock = null;

const requestWakeLock = async () => {
  if ('wakeLock' in navigator) {
    try {
      wakeLock = await navigator.wakeLock.request('screen');
      wakeLock.addEventListener('release', () => {
        helper.devConsole('Screen Wake Lock wurde vom System freigegeben.');
        wakeLock = null; // Wichtig, um den Status zu aktualisieren
      });
      helper.devConsole('Screen Wake Lock ist aktiv.');
    } catch (err) {
      helper.devConsole(`Screen Wake Lock konnte nicht angefordert werden: ${err.name}, ${err.message}`);
    }
  } else {
    helper.devConsole('Screen Wake Lock API nicht unterstützt.');
  }
};

const releaseWakeLock = async () => {
  if (wakeLock !== null) {
    try {
      await wakeLock.release();
      wakeLock = null;
      helper.devConsole('Screen Wake Lock wurde freigegeben.');
    } catch (err) {
      helper.devConsole(`Screen Wake Lock konnte nicht freigegeben werden: ${err.name}, ${err.message}`);
    }
  }
};

const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible' && !wakeLock) {
    helper.devConsole('Seite wieder sichtbar, versuche Wake Lock erneut anzufordern.');
    requestWakeLock();
  } else if (document.visibilityState === 'hidden' && wakeLock) {
    // Browser sollte es automatisch handhaben, aber zur Sicherheit:
    // releaseWakeLock(); // Überlege, ob das nötig ist, da 'release' Event gefeuert wird
    helper.devConsole('Seite unsichtbar, Wake Lock sollte freigegeben werden (oder wurde bereits).');
  }
};

// KRITISCH: Funktion zur stabilen ID-Zuweisung (PERMANENT)
const ensureStableIds = () => {
  if (menustore.oneReciept?.menuchilds?.menuChildId?.ingredients) {
    const ingredients = menustore.oneReciept.menuchilds.menuChildId.ingredients;

    console.log('🔧 Ensuring stable IDs (PERMANENT assignment - MenuDetails)...');

    // Finde die höchste bereits verwendete ID in diesem Rezept
    let maxUsedId = 0;
    ingredients.forEach(ingredient => {
      if (ingredient.stableId && typeof ingredient.stableId === 'number' && ingredient.stableId > maxUsedId) {
        maxUsedId = ingredient.stableId;
      }
    });

    // Speichere das Maximum im Rezept (pro Rezept, nicht global)
    if (!menustore.oneReciept.menuchilds.menuChildId.maxUsedStableId) {
      menustore.oneReciept.menuchilds.menuChildId.maxUsedStableId = maxUsedId;
    } else {
      maxUsedId = Math.max(maxUsedId, menustore.oneReciept.menuchilds.menuChildId.maxUsedStableId);
    }

    // Vergebe nur fehlende IDs - bestehende bleiben PERMANENT
    let hasChanges = false;
    ingredients.forEach((ingredient, index) => {
      if (!ingredient.stableId || typeof ingredient.stableId !== 'number') {
        maxUsedId++;
        ingredient.stableId = maxUsedId;
        hasChanges = true;
        console.log(`🆔 NEW stable ID ${maxUsedId} assigned to ingredient: "${ingredient.name?.name || 'Unknown'}" at index ${index}`);
      } else {
        console.log(`✅ PERMANENT stable ID ${ingredient.stableId} kept for ingredient: "${ingredient.name?.name || 'Unknown'}"`);
      }
    });

    // Aktualisiere das Maximum
    menustore.oneReciept.menuchilds.menuChildId.maxUsedStableId = maxUsedId;

    console.log('🔒 STABLE IDs final state (PERMANENT - MenuDetails):', ingredients.map((ing, idx) => ({
      arrayIndex: idx,
      name: ing.name?.name,
      stableId: ing.stableId
    })));
    console.log(`🔢 Recipe max stable ID: ${maxUsedId} (per recipe, never reused)`);
  }
};

// Watcher für Rezept-Änderungen - stableIds sofort zuweisen
watch(() => menustore.oneReciept?.menuchilds?.menuChildId?.ingredients, () => {
  console.log('🔄 Ingredients changed, ensuring stable IDs...');
  ensureStableIds();
}, { immediate: true, deep: true });

onMounted(() => {
  requestWakeLock();
  document.addEventListener('visibilitychange', handleVisibilityChange);
  document.addEventListener('fullscreenchange', handleVisibilityChange); // Auch bei Fullscreen-Änderungen prüfen

  // Stelle sicher, dass stableIds beim Mount vorhanden sind
  ensureStableIds();
});

onBeforeUnmount(() => {
  releaseWakeLock();
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  document.removeEventListener('fullscreenchange', handleVisibilityChange);
  if(assistantStore.activeAssistant){
    assistantStore.toggleAssistant()
  }
})

    // Development URLs - remove in production
    // Example: /kochbuch/menu/[menuId]?freeAccess=true
  //helper.devConsole("route.query.freeAccess")
  //helper.devConsole(route.query.freeAccess)
  if(route.query.freeAccess){
    menustore.getOneMenue(route.params.id, true)
  } else {
    menustore.getOneMenue(route.params.id, false)
  }

</script>

<style scoped>
/* Fancy Styling für dynamische Platzhalter */
:deep(.ingredient-placeholder) {
  animation: fadeInScale 0.3s ease-out;
  cursor: pointer;
  position: relative;
}

:deep(.ingredient-placeholder:hover) {
  transform: scale(1.05);
  z-index: 10;
}

:deep(.ingredient-placeholder::before) {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.3), transparent);
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

:deep(.ingredient-placeholder:hover::before) {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>