const mongoose = require('mongoose')
const { connection1 } = require('./../db.js');
const helper = require('../utils/helper')

const WeekplanSchema = new mongoose.Schema({
    kitchentableId: {
        type: String,
        required: true
    },
    menuId: {
        type: mongoose.Schema.ObjectId,
        ref: 'Menu'
    },
    date: {
        type: Date,
        required: true
    },
    daytime: {
        type: String,
        required:true
    },
    numberOfPersons: {
        type: Number,
        required:true
    },
    createdAt: {
        type: String,
        default: Date.now()
    }
})

/*
WeekplanSchema.pre(/^find/, function(next){
    helper.devConsole("pre on weekplan find executed")
    this.populate('menu')
    next()
});
*/



module.exports = connection1.model('Weekplan', WeekplanSchema)