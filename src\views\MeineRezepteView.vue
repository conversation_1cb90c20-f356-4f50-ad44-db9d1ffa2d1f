<template>
  <!-- Column Builder-->
  <div class="flex flex-col md:flex-row min-h-screen px-6">

    <!-- Middle Container -->
    <div class="w-full md:w-3/4 md:pr-12">
      <!-- Head-->
      <div class="w-full flex flex-row mt-10">
        <h1 class="w-11/12 h-auto leading-[3rem]">Kochbuch</h1>
        <div class="w-1/12 mt-8">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="w-full md:w-1/2 pt-4 md:pt-0">Alle Rezepte in der Übersicht</p>
      <!-- Head-->

      <!-- component switcher -->
      <div class="relative flex items-center w-full h-12 bg-ordypurple-200 mt-12 rounded-2xl pb-4 shadow-custom shadow-[#E0ADFF] overflow-hidden">
        <div class="absolute inset-0 h-full rounded-2xl bg-white transition-all duration-300 ease-in-out"
            :style="{ width: '50%', left: myRecieptsStore.recipeScope === 'my_reciepts' ? '0%' : '50%' }"></div>
        <button @click="myRecieptsStore.recipeScope = 'my_reciepts'"
                class="z-10 w-1/2 h-12 text-xs pt-4 font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Meine Rezepte
        </button>
        <button @click="myRecieptsStore.recipeScope = 'kitchentable'"
                class="z-10 w-1/2 h-12 text-xs pt-4 font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Küchentisch
        </button>
      </div>
      <!-- component switcher -->

      <!-- Conditional Rendering based on scope and kitchentable -->
      <template v-if="shouldShowRecipeList">
        <!-- Select Filter -->
        <div class="flex flex-row gap-2 md:w-10/12 w-full md:mt-10 mt-5 h-10 flex-nowrap">
          <!-- Search Button -->
           <button class="flex-initial w-full md:w-8/12 bg-white rounded-lg flex">
              <input @input.prevent="myRecieptsStore.searchForRecieps()" v-model="myRecieptsStore.searchMyReciepts"
                @keyup.enter="dismissKeyboard"
                class="outline-none focus:outline-none focus:ring-0 focus:border-transparent bg-transparent p-3 pt-2 w-10/12 border-0" placeholder="Nach Rezepten suchen"
                ref="searchInput"
              />
              <div class="w-2/12"><img class="w-6 pt-2 right-0" src="../assets/icons/search.png" /></div>
          </button>
          <!-- Existing Date Filter -->
          <ClickIconButton
            :element="{
                searchString: 'filterCreatedAt',
                index: 0,
                name:'filterBoxDate',
                store:'recieptsStore'
              }"
            >
            </ClickIconButton>
          <!-- Existing Time Filter -->
          <ClickIconButton
            :element="{
                searchString: 'filterCookingTime',
                index: 0,
                name:'filterBoxTime',
                store:'recieptsStore'
              }"
            >
            </ClickIconButton>
          <!-- UPDATED Shopping List Filter Button -->
          <button
            @click="myRecieptsStore.toggleShoppingListFilterAndReload()"
            class="flex-none w-10 h-10 rounded-lg flex justify-center items-center transition-colors duration-200 focus:outline-none"
            title="Nur Rezepte auf dem Einkaufszettel anzeigen"
          >
            <img
              class="w-5 h-5"
              :src="myRecieptsStore.isShoppingListFilterActive ? shoppingListActiveIcon : shoppingListIcon"
              alt="Filter Einkaufszettel"
            />
          </button>
        </div>
        <!-- Select Filter -->

        <!-- Menue -->
        <div class="flex flex-wrap gap-5 pb-12 mt-5">
          <!-- Loading indicator -->
          <div v-if="myRecieptsStore.isLoadingMore && myRecieptsStore.myReciepts.length === 0" class="w-full text-center p-6 flex justify-center items-center">
             <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-ordyblue-500"></div>
          </div>
          <!-- Recipe Cards - Iterate over store recipes directly -->
          <div v-for="(item, index) in myRecieptsStore.myReciepts" :key="item._id">
              <MenuCard class="bg-sfgyellow-500" :element="item" :index="index" :menuPlanType="{ addToMenu: true, editMenu: false, none: false }" :weekplanParentData="{'plannedSeats': null}" />
          </div>
           <!-- No Results Message - Bind to store state -->
           <div v-if="!myRecieptsStore.isLoadingMore && myRecieptsStore.myReciepts.length === 0 && myRecieptsStore.hasLoadedOnce" class="w-full text-center p-6">
             <p v-if="!myRecieptsStore.isShoppingListFilterActive" class="text-gray-500">Keine Rezepte für die aktuelle Auswahl gefunden.</p>
             <p v-else class="text-gray-500">Keine Rezepte auf dem Einkaufszettel gefunden.</p>
          </div>
        </div>
        <!-- Menue -->

        <!-- Infinite Scroll Loading Indicator - Use store list length -->
        <div v-if="myRecieptsStore.isLoadingMore && myRecieptsStore.myReciepts.length > 0" class="text-center p-6 flex justify-center items-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-ordyblue-500"></div>
        </div>
      </template>

      <!-- Info Box if 'Kitchentable' scope is active and no table selected -->
      <div v-else-if="shouldShowNoTableMessage" class="mt-10 p-6 bg-white rounded-lg shadow text-center">
          <h3 class="font-semibold mb-2">Kein Küchentisch ausgewählt</h3>
          <p class="text-gray-600 mb-4">
              Hier würden die Rezepte deines ausgewählten Küchentisches angezeigt.
              Bitte wähle zuerst einen Küchentisch aus oder erstelle einen neuen.
          </p>
          <router-link
              to="/kuechentisch"
              class="inline-block bg-ordypurple-200 text-white font-semibold py-2 px-4 rounded-xl hover:bg-ordyblue-600 transition-colors"
          >
              Zum Küchentisch
          </router-link>
      </div>
    </div>


    <!-- Right Container -->
    <div class="w-full md:w-1/4 md:p-10 pb-32">
      <h2 class="w-full">Weiteres</h2>
      <p class="w-full">Erstelle oder erfasse neue Rezepte und probiere etwas aus</p>

      <click-button
        @click.prevent="createRezept"
        :element="{'buttondescription': 'Neues leeres Rezept erstellen', 'active': false, 'buttonicon': 'add.png', 'iconneeded': true}"
        :index="1"
      />

      <click-button
        @click.prevent="goToUploadMenu"
        :element="{'buttondescription': 'Rezept aus Kochbuch erfassen', 'active': false, 'buttonicon': 'recieptedit.png', 'iconneeded': true }"
        :index="1"
      />

    </div>

  </div>

</template>
<script setup>

///////////////////// IMPORT /////////////////////////////////
import { reactive, ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../components/menuCard.vue'
import clickButton from '../components/clickBorderButton.vue'
import dayCard from '../components/dayCard.vue'
import useNotification from '../../modules/notificationInformation';
import { useUserStore } from '../store/userStore'
import { useMenuesStore } from '../store/menuStore'
import MenuCard from '../components/menuCard.vue';
import ClickIconButton from '../components/clickIconButton.vue';
import { useHelperStore } from '../../utils/helper';

// Import icons directly
import shoppingListActiveIcon from '../assets/icons/calendar_menuplan_active.png';
import shoppingListIcon from '../assets/icons/calendar_menuplan.png';

  ///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const myRecieptsStore = useMenuesStore();
  const helper = useHelperStore();

  const clickedButtonOnce = ref(false);

  // Template Refs
  const searchInput = ref(null);

  ////////////////////// SETUP ////////////////////////////////

  // Computed properties for showing sections (unchanged, but check usage)
  const shouldShowRecipeList = computed(() => {
    const show = myRecieptsStore.recipeScope === 'my_reciepts' ||
                 (myRecieptsStore.recipeScope === 'kitchentable' && !!userStore.user.defaultKitchentable);
    // helper.devConsole(`Computed shouldShowRecipeList: ${show}`);
    return show;
  });
  const shouldShowNoTableMessage = computed(() => {
    const show = myRecieptsStore.recipeScope === 'kitchentable' && !userStore.user.defaultKitchentable;
    // helper.devConsole(`Computed shouldShowNoTableMessage: ${show}`);
    return show;
  });

  // --- UPDATED/SIMPLIFIED Watcher ---
  // Watch for changes that require a *fresh* load (User ID, Scope, Table ID)
  watch([() => userStore.user.id, () => myRecieptsStore.recipeScope, () => userStore.user.defaultKitchentable],
        ([newUserId, newScope, newTableId], [oldUserId, oldScope, oldTableId]) => {
    // Trigger fresh load only if User ID changes or Scope changes
    // Or if Scope is Kitchentable and the Table ID changes
    const userIdChanged = newUserId !== oldUserId;
    const scopeChanged = newScope !== oldScope;
    const tableIdChanged = newScope === 'kitchentable' && newTableId !== oldTableId;

    // Only proceed if user ID is available
    if (!newUserId) {
        helper.devConsole("Watcher: User ID cleared, resetting store state.");
        myRecieptsStore.resetLoadState(); // Assuming a reset function exists or implement one
        return;
    }

    if (userIdChanged || scopeChanged || tableIdChanged) {
      helper.devConsole(`Watcher triggered: User/Scope/Table ID changed. Calling store loadAllMenusByUser (fresh).`);
      // Reset relevant filters when main context changes?
      // myRecieptsStore.searchMyReciepts = ''; // Optional reset
      // myRecieptsStore.isShoppingListFilterActive = false; // Optional reset
      myRecieptsStore.loadAllMenusByUser(newUserId, newScope === 'kitchentable' ? newTableId : null, { isFreshLoad: true });
    }
}, { immediate: true }); // immediate: true to load initially when user is available


// ---------- Infinite Scroll Logic -----------
const handleScroll = () => {
    if (!shouldShowRecipeList.value) return;
    let bottomOfWindow = document.documentElement.scrollTop + window.innerHeight >= document.documentElement.offsetHeight - 200;

    // Debug-Logs hinzufügen
    if (bottomOfWindow) {
      helper.devConsole(`Bottom reached (200px early)! hasMoreData: ${myRecieptsStore.hasMoreData}, isLoadingMore: ${myRecieptsStore.isLoadingMore}, recipes count: ${myRecieptsStore.myReciepts.length}, limit: ${myRecieptsStore.limit || 9}`);
    }

    if (bottomOfWindow && myRecieptsStore.hasMoreData && !myRecieptsStore.isLoadingMore) {
      helper.devConsole("Calling store loadMoreRecipes...");
      myRecieptsStore.loadMoreRecipes(); // Call store action directly
    }
};

onMounted(() => {
    window.addEventListener('scroll', handleScroll);
    // Initial load is handled by the watcher with immediate: true
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
    // Optional: Reset store state on unmount?
    // myRecieptsStore.resetLoadState();
});
// ---------- Infinite Scroll Logic Ende -----------

// CREATE MENU
const createRezept = async () => {

    const data = {
      user_id: userStore.user.id,
      menu: {
        name: "Neues Rezept ohne Namen",
        description: "-",
        menuchilds: [],
        users: [],
        imagelink: " ",
        freeAccess: false
      },
      menuchild: {
          seatCount: 1,
          cookingTime: 1,
          preperation:[{head:"schritt1", content: "Inhalt von Schritt 1"}],
          nutritions: [],
          versions: []
      },
      ingredients: [{unit:"-", amount: "1", name: "Milch"}]
    }
    console.log('data', data)
    const createdMenu = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/complete', {
      menuid: menuId,
      kitchenid: kitchentableid,
      name: menuTitel.value,
      // ... existing code ...
    });
    //console.log(createdMenu)
    await awaitReciepe(createdMenu)

    //router.push({ name: `/rezept_edit/${username}` })
    //$router.go('rezept_edit')
  }

  // PUSH TO URL AS SOON THE ITEM WAS CREATED
  const awaitReciepe = (data) => {
    //{ name: 'login', params: createdMenu.data.menue._id }

    // forward to url
    //console.log(data)
    router.push({ name: 'rezept_edit', params: { id: data.data.data._id }  })
  }


  const goToUploadMenu = () => {
    router.push({ name: 'uploadMenu'})
  };


  // --- NEW: Method to dismiss keyboard ---
  const dismissKeyboard = () => {
    if (searchInput.value) {
      searchInput.value.blur(); // Remove focus to hide keyboard
      helper.devConsole("Enter pressed in search, dismissing keyboard.");
    }
  }
  // -------------------------------------

</script>
<style scoped>
.shake {
  animation: shake 3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>