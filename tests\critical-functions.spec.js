/**
 * Kritische Funktionen Tests - Ordy <PERSON>end
 *
 * Diese Tests prüfen alle kürzlich geänderten Funktionen:
 * 1. <PERSON><PERSON><PERSON><PERSON> +/- Buttons (ohne 500-Error)
 * 2. Zutaten hinzufügen/löschen (ohne User-Fehlermeldungen)
 * 3. StableID-System (permanente ID-Erhaltung)
 * 4. Input-Feld-Styles (graue statt purple/pink Rahmen)
 * 5. Legacy-Rezepte (automatische Migration)
 */

import { test, expect } from '@playwright/test';

// Test-Konfiguration
const BASE_URL = 'http://localhost:5174';
const BACKEND_URL = 'http://localhost:8080';

// Test-Daten
const TEST_RECIPE_ID = '683aa7d8a0ff1ea39335d890'; // Beispiel-Rezept-ID
const LOGIN_EMAIL = '<EMAIL>';
const LOGIN_PASSWORD = 'testpassword';

test.describe('Kritische Funktionen Tests', () => {

  test.beforeEach(async ({ page }) => {
    // Login vor jedem Test
    await page.goto(`${BASE_URL}/login`);
    await page.fill('input[type="email"]', LOGIN_EMAIL);
    await page.fill('input[type="password"]', LOGIN_PASSWORD);
    await page.click('button[type="submit"]');

    // Warte auf erfolgreichen Login
    await page.waitForURL('**/kochbuch**');
    console.log('✅ Login erfolgreich');
  });

  test('1. Personenanzahl +/- Buttons funktionieren ohne 500-Error', async ({ page }) => {
    console.log('🧪 Test: Personenanzahl-Buttons');

    // Navigiere zu Rezept-Ansicht
    await page.goto(`${BASE_URL}/kochbuch/menu/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Finde aktuelle Personenanzahl
    const personCountElement = await page.locator('[data-testid="person-count"]').first();
    const initialCount = await personCountElement.textContent();
    console.log(`📊 Initiale Personenanzahl: ${initialCount}`);

    // Überwache Netzwerk-Requests für 500-Errors
    let has500Error = false;
    page.on('response', response => {
      if (response.status() === 500 && response.url().includes('createifnotexists')) {
        has500Error = true;
        console.log('❌ 500-Error detected:', response.url());
      }
    });

    // Teste + Button
    await page.click('[data-testid="increase-persons"]');
    await page.waitForTimeout(2000); // Warte auf Backend-Response

    // Prüfe ob 500-Error aufgetreten ist
    expect(has500Error).toBe(false);

    // Prüfe ob Personenanzahl erhöht wurde
    const newCount = await personCountElement.textContent();
    console.log(`📊 Neue Personenanzahl: ${newCount}`);
    expect(parseInt(newCount)).toBeGreaterThan(parseInt(initialCount));

    // Teste - Button
    has500Error = false; // Reset
    await page.click('[data-testid="decrease-persons"]');
    await page.waitForTimeout(2000);

    expect(has500Error).toBe(false);
    console.log('✅ Personenanzahl-Buttons funktionieren ohne 500-Error');
  });

  test('2. Zutaten hinzufügen ohne User-Fehlermeldungen', async ({ page }) => {
    console.log('🧪 Test: Zutaten hinzufügen ohne Fehlermeldungen');

    // Navigiere zu Rezept-Edit-Modus
    await page.goto(`${BASE_URL}/kochbuch/menu/edit/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Überwache Notifications/Fehlermeldungen
    let hasErrorNotification = false;
    page.on('console', msg => {
      if (msg.type() === 'error' && msg.text().includes('Es ist ein Problem aufgetreten')) {
        hasErrorNotification = true;
        console.log('❌ Error notification detected:', msg.text());
      }
    });

    // Zähle aktuelle Zutaten
    const initialIngredients = await page.locator('[data-testid="ingredient-row"]').count();
    console.log(`📊 Initiale Zutaten-Anzahl: ${initialIngredients}`);

    // Füge neue Zutat hinzu
    await page.click('[data-testid="add-ingredient"]');
    await page.waitForTimeout(3000); // Warte auf Auto-Save

    // Prüfe ob Fehlermeldung aufgetreten ist
    expect(hasErrorNotification).toBe(false);

    // Prüfe ob Zutat hinzugefügt wurde
    const newIngredients = await page.locator('[data-testid="ingredient-row"]').count();
    expect(newIngredients).toBe(initialIngredients + 1);

    console.log('✅ Zutat hinzugefügt ohne User-Fehlermeldungen');
  });

  test('3. StableID-System erhält IDs permanent', async ({ page }) => {
    console.log('🧪 Test: StableID-System');

    await page.goto(`${BASE_URL}/kochbuch/menu/edit/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Sammle StableIDs vor Änderung
    const stableIdsBefore = await page.evaluate(() => {
      const store = window.Vue?.config?.globalProperties?.$pinia?._s?.get('menu');
      return store?.oneReciept?.menuchilds?.menuChildId?.ingredients?.map(ing => ({
        name: ing.name?.name,
        stableId: ing.stableId
      })) || [];
    });

    console.log('📊 StableIDs vor Änderung:', stableIdsBefore);

    // Ändere Personenanzahl (sollte StableIDs erhalten)
    await page.click('[data-testid="increase-persons"]');
    await page.waitForTimeout(3000);

    // Sammle StableIDs nach Änderung
    const stableIdsAfter = await page.evaluate(() => {
      const store = window.Vue?.config?.globalProperties?.$pinia?._s?.get('menu');
      return store?.oneReciept?.menuchilds?.menuChildId?.ingredients?.map(ing => ({
        name: ing.name?.name,
        stableId: ing.stableId
      })) || [];
    });

    console.log('📊 StableIDs nach Änderung:', stableIdsAfter);

    // Prüfe ob StableIDs erhalten geblieben sind
    expect(stableIdsBefore.length).toBe(stableIdsAfter.length);

    for (let i = 0; i < stableIdsBefore.length; i++) {
      expect(stableIdsBefore[i].stableId).toBe(stableIdsAfter[i].stableId);
      console.log(`✅ StableID ${stableIdsBefore[i].stableId} für "${stableIdsBefore[i].name}" erhalten`);
    }

    console.log('✅ StableID-System erhält IDs permanent');
  });

  test('4. Input-Felder haben graue statt purple/pink Rahmen', async ({ page }) => {
    console.log('🧪 Test: Input-Feld-Styles');

    await page.goto(`${BASE_URL}/kochbuch/menu/edit/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Teste verschiedene Input-Felder
    const inputSelectors = [
      'input[placeholder="Menge"]',
      'input[placeholder="Einheit"]',
      'input[placeholder="Zutat"]'
    ];

    for (const selector of inputSelectors) {
      const input = page.locator(selector).first();

      // Fokussiere Input-Feld
      await input.focus();

      // Prüfe Border-Color beim Focus
      const borderColor = await input.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return styles.borderColor;
      });

      console.log(`📊 Border-Color für ${selector}: ${borderColor}`);

      // Prüfe dass es NICHT purple/pink ist
      expect(borderColor).not.toContain('rgb(168, 85, 247)'); // purple-400
      expect(borderColor).not.toContain('rgb(236, 72, 153)'); // pink-400
      expect(borderColor).not.toContain('purple');
      expect(borderColor).not.toContain('pink');

      // Sollte grau sein
      expect(borderColor).toMatch(/rgb\(209, 213, 219\)|rgb\(156, 163, 175\)|gray/);
    }

    console.log('✅ Input-Felder haben graue statt purple/pink Rahmen');
  });

  test('5. Legacy-Rezepte werden automatisch migriert', async ({ page }) => {
    console.log('🧪 Test: Legacy-Migration');

    // Simuliere Legacy-Rezept (ohne StableIDs)
    await page.goto(`${BASE_URL}/kochbuch/menu/edit/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Prüfe ob Migration-Logs erscheinen
    let migrationDetected = false;
    page.on('console', msg => {
      if (msg.text().includes('Legacy recipe detected') ||
          msg.text().includes('Starting full legacy migration')) {
        migrationDetected = true;
        console.log('✅ Legacy-Migration detected:', msg.text());
      }
    });

    // Lade Seite neu um Migration zu triggern
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Prüfe ob alle Zutaten StableIDs haben
    const ingredientsWithStableIds = await page.evaluate(() => {
      const store = window.Vue?.config?.globalProperties?.$pinia?._s?.get('menu');
      const ingredients = store?.oneReciept?.menuchilds?.menuChildId?.ingredients || [];
      return ingredients.every(ing => ing.stableId && typeof ing.stableId === 'number');
    });

    expect(ingredientsWithStableIds).toBe(true);
    console.log('✅ Alle Zutaten haben StableIDs nach Migration');
  });

  test('6. Keine 500-Errors bei Auto-Save Operationen', async ({ page }) => {
    console.log('🧪 Test: Auto-Save ohne 500-Errors');

    await page.goto(`${BASE_URL}/kochbuch/menu/edit/${TEST_RECIPE_ID}`);
    await page.waitForLoadState('networkidle');

    // Überwache 500-Errors
    const errors500 = [];
    page.on('response', response => {
      if (response.status() === 500) {
        errors500.push({
          url: response.url(),
          status: response.status()
        });
      }
    });

    // Triggere Auto-Save durch Änderung
    await page.fill('input[placeholder="Menge"]', '250');
    await page.keyboard.press('Tab'); // Trigger blur event
    await page.waitForTimeout(3000); // Warte auf Auto-Save

    // Prüfe ob 500-Errors aufgetreten sind
    console.log('📊 500-Errors detected:', errors500);

    // 500-Errors sind erlaubt, aber sollten keine User-Notifications verursachen
    // Prüfe dass keine Error-Notifications angezeigt werden
    const errorNotifications = await page.locator('[data-testid="notification"][data-type="error"]').count();
    expect(errorNotifications).toBe(0);

    console.log('✅ Auto-Save funktioniert ohne User-Fehlermeldungen');
  });
});
