# Zutaten-System Umsetzungsplan

## Phase 1: Backend StableID-System ✅ COMPLETED

### Task 1.1: StableID-Middleware implementieren ✅ COMPLETED
**Priorität:** KRITISCH
**Geschätzte Zeit:** 2-3 Stunden

**Subtasks:**
- [x] StableID-Utility-Funktionen erstellen
- [x] MenuChild-Controller erweitern
- [x] Middleware für automatische StableID-Zuweisung
- [x] Backend-Validierung implementieren
- [x] Unit-Tests für StableID-System
- [x] Neue Route für Zutaten-Löschung

**Dateien:**
- ✅ `utils/stableIdManager.js` (neu)
- ✅ `controllers/menuchildController.js`
- ✅ `models/menuchildModel.js`
- ✅ `routes/menuRoutes.js`
- ✅ `tests/backend/stableId.test.js` (neu)

## Phase 2: Integration & Tests ✅ COMPLETED

### Task 2.1: Backend-Tests ausführen ✅ COMPLETED
**Priorität:** HOCH
**Geschätzte Zeit:** 1 Stunde

**Subtasks:**
- [x] Unit-Tests für StableID-System ausführen
- [x] StableID-Manager Funktionalität validiert
- [x] Alle 5 Kern-Tests bestanden
- [x] Performance-Test mit 100 Zutaten erfolgreich

### Task 2.2: Frontend-Backend Integration testen ⏳ BEREIT
**Priorität:** HOCH
**Geschätzte Zeit:** 2 Stunden

**Subtasks:**
- [x] Backend StableID-System implementiert
- [x] API-Routen für Zutaten-CRUD erstellt
- [x] Validierung und Fehlerbehandlung implementiert
- [ ] Frontend-Tests mit laufendem Server (bereit für Ausführung)

## Phase 3: Playwright End-to-End Tests ✅ COMPLETED

### Task 3.1: E2E-Tests erstellen ✅ COMPLETED
**Priorität:** MITTEL
**Geschätzte Zeit:** 3 Stunden

**Subtasks:**
- [x] Rezept-Erstellung Tests
- [x] Zutaten-Management Tests
- [x] StableID-Persistenz Tests
- [x] Einkaufszettel-Integration Tests
- [x] Platzhalter-System Tests

## Implementierte Features

### ✅ StableID-Manager (`utils/stableIdManager.js`)
- **assignStableIds()** - Weist neuen Zutaten stabile IDs zu
- **validateStableIds()** - Validiert StableID-Konsistenz
- **repairStableIds()** - Repariert StableID-Inkonsistenzen
- **removeIngredientKeepStableIds()** - Entfernt Zutaten ohne ID-Wiederverwendung
- **addIngredientWithStableId()** - Fügt neue Zutaten mit StableID hinzu

### ✅ MenuChild-Controller Erweiterungen
- **createOneMenuchild()** - StableID-Zuweisung bei Erstellung
- **patchOneMenuchild()** - StableID-Management bei Updates
- **addOneIngredientToIngredients()** - Neue Zutaten mit StableIDs
- **removeOneIngredientFromIngredients()** - Zutaten-Löschung mit ID-Erhaltung

### ✅ Model-Validierung
- **Pre-Save Middleware** - Automatische StableID-Validierung
- **Konsistenz-Prüfung** - Verhindert doppelte oder ungültige IDs

### ✅ API-Routen
- **POST** `/api/v1/menu/one/:menuid/child/:menuchildid/ingredient` - Zutat hinzufügen
- **DELETE** `/api/v1/menu/one/:menuid/child/:menuchildid/ingredient/:ingredientIndex` - Zutat löschen

### ✅ Unit-Tests
- **StableID-Zuweisung** - Tests für neue ID-Vergabe
- **ID-Erhaltung** - Tests für bestehende ID-Beibehaltung
- **Validierung** - Tests für Konsistenz-Prüfung
- **Edge-Cases** - Tests für Fehlerfälle

## 🚀 SYSTEM BEREIT - Nächste Schritte

### Sofort verfügbar:
1. **Backend StableID-System** - Vollständig implementiert und getestet
2. **Unit-Tests** - Alle Tests bestehen (5/5 erfolgreich)
3. **E2E-Tests** - Playwright-Tests für vollständige Workflows erstellt
4. **API-Routen** - Neue Endpunkte für Zutaten-Management

### Für Produktions-Deployment:
1. **Server starten** - `npm run dev` im Backend-Verzeichnis
2. **Frontend starten** - `npm run dev` im Frontend-Verzeichnis
3. **E2E-Tests ausführen** - `npx playwright test tests/e2e/stable-ids.spec.js`
4. **Manuelle Tests** - Rezept-Erstellung und -Bearbeitung testen

### Test-Kommandos:
```bash
# Backend-Tests
cd ordy-api
node tests/backend/stableId.test.js

# E2E-Tests (nach Server-Start)
cd ordy-at-vite5
npx playwright test tests/e2e/stable-ids.spec.js
```

## Technische Details

### StableID-System Regeln
- **1-basierte IDs** - Erste Zutat = ID 1
- **Permanente IDs** - Nie wiederverwendet nach Löschung
- **Pro-Rezept Scope** - Jedes Rezept hat eigene ID-History
- **Konsistenz-Validierung** - Automatische Prüfung bei Speicherung

### Platzhalter-Format
- **${ID:1}** - Verknüpfung zur Zutat mit stableId 1
- **Automatische Skalierung** - Mengen ändern sich mit Personenanzahl
- **Fehlerbehandlung** - Ungültige Platzhalter werden ausgeblendet
