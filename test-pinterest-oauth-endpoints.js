// Test script for Pinterest OAuth endpoints
require('dotenv').config({ path: './config.env' });
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test function for OAuth start
async function testOAuthStart() {
  try {
    console.log('Testing Pinterest OAuth start...');
    
    const response = await axios.get(`${BASE_URL}/api/v1/pinterest/oauth/start`);
    
    console.log('OAuth Start Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.data && response.data.data.authUrl) {
      console.log('\n=== NEXT STEPS ===');
      console.log('1. Visit this URL in your browser:');
      console.log(response.data.data.authUrl);
      console.log('\n2. After authorization, copy the "code" parameter from the callback URL');
      console.log('3. Use the code to test the callback endpoint');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error testing OAuth start:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Test function for integration test
async function testIntegration() {
  try {
    console.log('Testing Pinterest integration...');
    
    const response = await axios.post(`${BASE_URL}/api/v1/pinterest/test`);
    
    console.log('Integration Test Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Error testing integration:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Test function for token refresh
async function testTokenRefresh() {
  try {
    console.log('Testing Pinterest token refresh...');
    
    const response = await axios.post(`${BASE_URL}/api/v1/pinterest/oauth/refresh`);
    
    console.log('Token Refresh Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('Error testing token refresh:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('Pinterest OAuth Endpoints Test');
  console.log('==============================\n');
  
  // Check environment variables
  console.log('Environment Variables:');
  console.log('- PINTEREST_APP_ID:', !!process.env.PINTEREST_APP_ID);
  console.log('- PINTEREST_SECRET_KEY:', !!process.env.PINTEREST_SECRET_KEY);
  console.log('- PINTEREST_ACCESS_TOKEN:', !!process.env.PINTEREST_ACCESS_TOKEN);
  console.log('- PINTEREST_REFRESH_TOKEN:', !!process.env.PINTEREST_REFRESH_TOKEN);
  console.log('- PINTEREST_BOARD_ID:', !!process.env.PINTEREST_BOARD_ID);
  console.log('- NODE_ENV:', process.env.NODE_ENV || 'development');
  console.log('');
  
  // Test 1: OAuth Start
  console.log('=== TEST 1: OAuth Start ===');
  await testOAuthStart();
  console.log('');
  
  // Test 2: Integration Test (if tokens are available)
  if (process.env.PINTEREST_ACCESS_TOKEN) {
    console.log('=== TEST 2: Integration Test ===');
    await testIntegration();
    console.log('');
  } else {
    console.log('=== TEST 2: Integration Test ===');
    console.log('Skipped - No access token available');
    console.log('');
  }
  
  // Test 3: Token Refresh (if refresh token is available)
  if (process.env.PINTEREST_REFRESH_TOKEN) {
    console.log('=== TEST 3: Token Refresh ===');
    await testTokenRefresh();
    console.log('');
  } else {
    console.log('=== TEST 3: Token Refresh ===');
    console.log('Skipped - No refresh token available');
    console.log('');
  }
  
  console.log('Tests completed!');
}

// Check command line arguments
const args = process.argv.slice(2);
if (args.length > 0) {
  const command = args[0];
  
  switch (command) {
    case 'start':
      testOAuthStart();
      break;
    case 'test':
      testIntegration();
      break;
    case 'refresh':
      testTokenRefresh();
      break;
    default:
      console.log('Usage:');
      console.log('  node test-pinterest-oauth-endpoints.js [start|test|refresh]');
      console.log('  node test-pinterest-oauth-endpoints.js (runs all tests)');
  }
} else {
  runTests();
}
