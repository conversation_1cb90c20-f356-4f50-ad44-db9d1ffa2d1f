<template>
    <div 
        class="rounded-2xl bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 h-auto min-h-128 w-full flex flex-col"
    >
        <!-- up -->
        <div class="w-full rounded-2xl mt-auto overflow-y-auto bg-gradient-to-tl from-gray-25 to-white h-[90%] p-6 flex flex-col">
            <span class="w-full font-YesevaOne text-base">hallo. ich bin boty</span>


            <div 
                v-for="(message, index) in sessionStore.messageBody"
                :key="index"
                class=""
            >
            <!-- role system or assistant -->
                <div 
                    v-if="message.role == 'assistant'" 
                    class="w-9/12 min-h-12 h-auto bg-gradient-to-br from-gray-75 to-gray-50 rounded-r-xl rounded-tl-xl p-3 mt-4 font-OpenSans text-xs"
                > 
                    <!-- basic answer -->
                    <p v-if="message.function == ''" class="text-xs">{{ message.content }}</p>
                    <!-- basic answer -->

                    <!-- ingridients answer -->
                    <p v-if="message.function == 'get_ordy_menus'" class="text-xs">Dazu habe ich zum Beispiel folgendes Rezept gefunden:</p>
                    <br /><img v-if="message.function == 'get_ordy_menus'" :src="message.body" class="w-full h-auto rounded-2xl" />
                    <p v-if="message.function == 'get_ordy_menus'" class="text-xs"><br />Um das Rezept zu kochen, musst du dich zuerst registrieren.<br /></p>
                    <!-- ingridients answer -->

                </div>
            <!-- role user -->
                <div 
                    v-if="message.role == 'user'"
                    class="w-9/12 float-end min-h-12 h-auto bg-gradient-to-tl from-gray-75 to-gray-50 rounded-l-xl rounded-tr-xl p-3 mt-4 font-OpenSans text-xs"
                > 
                    <p class="text-xs">{{ message.content }}</p>
                </div>
            </div>

            <!--
            <input class="w-full h-12 rounded-xl mt-auto bg-ordypurple-100 text-white p-2 text-xs font-OpenSans" />
            --> 
            
            <div class="mt-auto">   
                <div class="flex flex-row bg-ordypurple-100 rounded-xl mt-2">
                    <div class="w-1/12 inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                        <svg class="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input v-model="sessionStore.botyInputText" @keyup.enter="sessionStore.createQuestion();" class="w-8/12 !outline-none h-auto break-normal block p-4 text-xs text-white rounded-lg bg-ordypurple-100"></input>
                    <button @click="sessionStore.createQuestion()" @keyup.enter="sessionStore.createQuestion();" type="submit" class="bg-white w-3/12 mx-3 my-4 text-ordypurple-100 end-2.5 bottom-2.5 font-medium rounded-lg text-sm md:px-4 px-1 py-2">ordy fragen</button>
                </div>
            </div>
        
        </div>
    </div>
</template>
<script setup>
    import { ref } from 'vue';
    import axios from 'axios';
    import { useSessionStore } from '../store/sessionStore'

    const sessionStore = useSessionStore();



</script>
<style>
/*
    .imglinkcss {
        background-image: v-bind('element.imagelink');
    }
*/
</style>

