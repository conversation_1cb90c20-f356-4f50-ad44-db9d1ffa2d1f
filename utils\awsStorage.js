const dotenv = require('dotenv');
const S3 = require('aws-sdk/clients/s3')
const fs = require('fs')


const s3 = new S3({
    credentials: {
        accessKeyId: process.env.S3_ACCESSKEY,
        secretAccessKey: process.env.S3_SECRET
    },
    region: 'us-east-1'
  });

function uploadFileByLink(file){

    console.log(file)

    //const fileContent = Buffer.from(req.file.data.data, 'binary')

    const uploadParams = {
        Bucket: process.env.S3_BUCKETNAME,
        Body: file.body,
        Key: file.name,
        ContentType: file.imageType
    }

    return s3.upload(uploadParams).promise()
}


exports.uploadFileByLink = uploadFileByLink

function uploadFile(file){

    console.log(file)

    const fileStream = fs.createReadStream(file.path)

    //const fileContent = Buffer.from(req.file.data.data, 'binary')

    const uploadParams = {
        Bucket: process.env.S3_BUCKETNAME,
        Body: fileStream,
        Key: file.filename
    }

    return s3.upload(uploadParams).promise()
}

exports.uploadFile = uploadFile

function getFileStream(fileKey){
    const downloadParams = {
        Key: fileKey,
        Bucket: process.env.S3_BUCKETNAME
    }

    return s3.getObject(downloadParams).createReadStream()
}

exports.getFileStream = getFileStream

// --- NEU: Funktion zum Hochladen von Bilddaten (Buffer) ---
async function uploadImageData(imageDataBuffer, fileName, contentType = 'image/png', s3Folder = 'marketing-images') {
  // TODO: Implementiere die Logik, um den Buffer hochzuladen
  // Ähnlich wie uploadFile, aber Body ist der Buffer statt eines Streams
  console.log(`[uploadImageData] Uploading image data for ${fileName} (Content-Type: ${contentType})...`);

  const uploadParams = {
      Bucket: process.env.S3_BUCKETNAME,
      Body: imageDataBuffer,
      Key: `${s3Folder}/${fileName}`,
      ContentType: contentType
  };

  try {
      // s3.upload() ist besser für Streams, für Buffer ist putObject() oft direkter:
      // const result = await s3.putObject(uploadParams).promise();
      // Alternativ weiterhin s3.upload() verwenden, wenn es einfacher ist:
      const result = await s3.upload(uploadParams).promise();
      console.log(`[uploadImageData] Image upload successful for ${fileName}. Location: ${result.Location}`);
      return result; // Enthält { ETag, Location, Key, Bucket }
  } catch (error) {
      console.error(`[uploadImageData] Error uploading image ${fileName}:`, error);
      throw error; // Fehler weitergeben
  }
}

exports.uploadImageData = uploadImageData;