const User = require('../models/userModel');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const authController = require('./authController');

// @desc    Verify admin authentication and return user data
// @route   GET /api/v1/admin/verify
// @access  Protected (Admin)
exports.verifyAdmin = catchAsync(async (req, res, next) => {
    const functionName = 'adminController.verifyAdmin';
    helper.devConsole(`[${functionName}] Verifying admin authentication...`);

    // First verify the user is authenticated
    await authController.verify(req, res, async () => {
        try {
            // Get user from database with admin permissions
            const user = await User.findById(req.user.id).select('+adminPermissions');
            
            if (!user) {
                return next(new AppError('User not found', 404));
            }
            
            // Check if user has admin permissions
            if (!user.adminPermissions || !user.adminPermissions.isAdmin) {
                helper.devConsole(`[${functionName}] Access denied for user: ${user.email}`);
                return next(new AppError('Access denied. Admin permissions required.', 403));
            }
            
            helper.devConsole(`[${functionName}] Admin verification successful for user: ${user.email}`);
            
            res.status(200).json({
                status: 'success',
                message: 'Admin authentication verified',
                user: {
                    id: user._id,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    adminPermissions: user.adminPermissions
                }
            });
        } catch (error) {
            helper.devConsole(`[${functionName}] Error:`, error.message);
            return next(new AppError('Authentication error', 500));
        }
    });
});

// @desc    Get admin dashboard data
// @route   GET /api/v1/admin/dashboard
// @access  Protected (Admin)
exports.getDashboard = catchAsync(async (req, res, next) => {
    const functionName = 'adminController.getDashboard';
    helper.devConsole(`[${functionName}] Getting admin dashboard data...`);

    try {
        // Get Pinterest token status
        const PinterestToken = require('../models/pinterestTokenModel');
        const pinterestToken = await PinterestToken.getCurrentToken();
        
        // Get latest marketing content
        const MarketingContent = require('../models/marketingContentModel');
        const latestContent = await MarketingContent.findOne()
            .sort({ generationDate: -1 })
            .limit(1);
        
        // Get user count
        const totalUsers = await User.countDocuments();
        const adminUsers = await User.countDocuments({ 'adminPermissions.isAdmin': true });
        
        const dashboardData = {
            pinterest: {
                hasToken: !!(pinterestToken && pinterestToken.accessToken),
                isActive: pinterestToken ? pinterestToken.isActive : false,
                lastRefreshed: pinterestToken ? pinterestToken.lastRefreshed : null
            },
            marketing: {
                hasContent: !!latestContent,
                latestContentDate: latestContent ? latestContent.generationDate : null,
                latestRecipeName: latestContent ? latestContent.recipeName : null
            },
            users: {
                total: totalUsers,
                admins: adminUsers
            },
            system: {
                environment: process.env.NODE_ENV || 'development',
                uptime: process.uptime()
            }
        };

        res.status(200).json({
            status: 'success',
            data: dashboardData
        });
    } catch (error) {
        helper.devConsole(`[${functionName}] Error:`, error.message);
        return next(new AppError('Failed to load dashboard data', 500));
    }
});

// @desc    Grant admin permissions to a user
// @route   POST /api/v1/admin/grant-permissions
// @access  Protected (Super Admin)
exports.grantAdminPermissions = catchAsync(async (req, res, next) => {
    const functionName = 'adminController.grantAdminPermissions';
    const { userEmail, permissions } = req.body;
    
    helper.devConsole(`[${functionName}] Granting admin permissions to: ${userEmail}`);

    if (!userEmail || !permissions) {
        return next(new AppError('User email and permissions are required', 400));
    }

    // Find user by email
    const user = await User.findOne({ email: userEmail });
    
    if (!user) {
        return next(new AppError('User not found', 404));
    }

    // Update admin permissions
    const adminPermissions = {
        isAdmin: true,
        canAccessBackend: permissions.includes('backend_access'),
        canManagePinterest: permissions.includes('pinterest_management'),
        canManageMarketing: permissions.includes('marketing_management'),
        canViewAnalytics: permissions.includes('analytics_view'),
        permissions: permissions
    };

    await User.findByIdAndUpdate(user._id, { adminPermissions });

    helper.devConsole(`[${functionName}] Admin permissions granted to: ${userEmail}`);

    res.status(200).json({
        status: 'success',
        message: `Admin permissions granted to ${userEmail}`,
        permissions: adminPermissions
    });
});

// @desc    Revoke admin permissions from a user
// @route   POST /api/v1/admin/revoke-permissions
// @access  Protected (Super Admin)
exports.revokeAdminPermissions = catchAsync(async (req, res, next) => {
    const functionName = 'adminController.revokeAdminPermissions';
    const { userEmail } = req.body;
    
    helper.devConsole(`[${functionName}] Revoking admin permissions from: ${userEmail}`);

    if (!userEmail) {
        return next(new AppError('User email is required', 400));
    }

    // Find user by email
    const user = await User.findOne({ email: userEmail });
    
    if (!user) {
        return next(new AppError('User not found', 404));
    }

    // Remove admin permissions
    const adminPermissions = {
        isAdmin: false,
        canAccessBackend: false,
        canManagePinterest: false,
        canManageMarketing: false,
        canViewAnalytics: false,
        permissions: []
    };

    await User.findByIdAndUpdate(user._id, { adminPermissions });

    helper.devConsole(`[${functionName}] Admin permissions revoked from: ${userEmail}`);

    res.status(200).json({
        status: 'success',
        message: `Admin permissions revoked from ${userEmail}`
    });
});
