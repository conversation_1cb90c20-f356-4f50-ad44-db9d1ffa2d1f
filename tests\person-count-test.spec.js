/**
 * Gezielter Test für Personenanzahl +/- Buttons
 * 
 * Testet die spezifische Funktionalität der Personenanzahl-Änderung
 * und überwacht Backend-Logs für 500-Errors
 */

import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5174';
const BACKEND_URL = 'http://localhost:8080';

// Test-Rezept-ID (muss existieren)
const TEST_RECIPE_ID = '683aa7d8a0ff1ea39335d890';

test.describe('Personenanzahl Buttons Test', () => {
  
  test('Personenanzahl +/- Buttons funktionieren ohne 500-Error', async ({ page }) => {
    console.log('🧪 Starte Personenanzahl-Test...');
    
    // Überwache alle Netzwerk-Requests
    const networkRequests = [];
    const errors500 = [];
    
    page.on('request', request => {
      if (request.url().includes('createifnotexists')) {
        networkRequests.push({
          url: request.url(),
          method: request.method(),
          timestamp: new Date().toISOString()
        });
        console.log(`📡 Request: ${request.method()} ${request.url()}`);
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('createifnotexists')) {
        console.log(`📡 Response: ${response.status()} ${response.url()}`);
        if (response.status() === 500) {
          errors500.push({
            url: response.url(),
            status: response.status(),
            timestamp: new Date().toISOString()
          });
        }
      }
    });
    
    // Überwache Console-Logs für detaillierte Diagnose
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('🔍 Sample ingredients structure:') || 
          text.includes('📦 Complete payload being sent to backend:') ||
          text.includes('countPerson 500 error details:')) {
        console.log(`🔍 Frontend Log: ${text}`);
      }
    });
    
    // Navigiere direkt zu einem Rezept
    console.log(`📍 Navigiere zu Rezept: ${TEST_RECIPE_ID}`);
    await page.goto(`${BASE_URL}/kochbuch/menu/${TEST_RECIPE_ID}`);
    
    // Warte bis Seite geladen ist
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Prüfe ob Personenanzahl-Buttons existieren
    const increaseButton = page.locator('button').filter({ hasText: '+' }).first();
    const decreaseButton = page.locator('button').filter({ hasText: '-' }).first();
    
    await expect(increaseButton).toBeVisible();
    await expect(decreaseButton).toBeVisible();
    
    console.log('✅ Personenanzahl-Buttons gefunden');
    
    // Finde aktuelle Personenanzahl
    const personDisplay = page.locator('text=/\\d+\\s*Person/').first();
    await expect(personDisplay).toBeVisible();
    
    const initialText = await personDisplay.textContent();
    const initialCount = parseInt(initialText.match(/\d+/)[0]);
    console.log(`📊 Initiale Personenanzahl: ${initialCount}`);
    
    // Teste + Button
    console.log('🔼 Teste + Button...');
    await increaseButton.click();
    
    // Warte auf Backend-Response
    await page.waitForTimeout(3000);
    
    // Prüfe ob 500-Error aufgetreten ist
    if (errors500.length > 0) {
      console.log('❌ 500-Errors detected:', errors500);
      
      // Zeige detaillierte Error-Info
      for (const error of errors500) {
        console.log(`❌ Error: ${error.status} at ${error.timestamp} - ${error.url}`);
      }
      
      // Test schlägt fehl bei 500-Errors
      expect(errors500.length).toBe(0);
    } else {
      console.log('✅ Keine 500-Errors beim + Button');
    }
    
    // Prüfe ob Personenanzahl erhöht wurde
    const newText = await personDisplay.textContent();
    const newCount = parseInt(newText.match(/\d+/)[0]);
    console.log(`📊 Neue Personenanzahl: ${newCount}`);
    
    expect(newCount).toBe(initialCount + 1);
    console.log('✅ Personenanzahl korrekt erhöht');
    
    // Reset für - Button Test
    errors500.length = 0;
    
    // Teste - Button
    console.log('🔽 Teste - Button...');
    await decreaseButton.click();
    
    // Warte auf Backend-Response
    await page.waitForTimeout(3000);
    
    // Prüfe ob 500-Error aufgetreten ist
    if (errors500.length > 0) {
      console.log('❌ 500-Errors detected:', errors500);
      expect(errors500.length).toBe(0);
    } else {
      console.log('✅ Keine 500-Errors beim - Button');
    }
    
    // Prüfe ob Personenanzahl verringert wurde
    const finalText = await personDisplay.textContent();
    const finalCount = parseInt(finalText.match(/\d+/)[0]);
    console.log(`📊 Finale Personenanzahl: ${finalCount}`);
    
    expect(finalCount).toBe(initialCount);
    console.log('✅ Personenanzahl korrekt verringert');
    
    // Zusammenfassung
    console.log('\n📋 TEST ZUSAMMENFASSUNG:');
    console.log(`- Initiale Personenanzahl: ${initialCount}`);
    console.log(`- Nach +: ${newCount}`);
    console.log(`- Nach -: ${finalCount}`);
    console.log(`- 500-Errors: ${errors500.length}`);
    console.log(`- Network Requests: ${networkRequests.length}`);
    
    if (errors500.length === 0) {
      console.log('🎉 TEST ERFOLGREICH: Personenanzahl-Buttons funktionieren ohne 500-Errors!');
    } else {
      console.log('❌ TEST FEHLGESCHLAGEN: 500-Errors aufgetreten');
    }
  });
});
