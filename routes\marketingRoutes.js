const express = require('express');
const marketingContentController = require('../controllers/marketingContentController');
const marketingVideoRecorder = require('../controllers/marketingVideoRecorderController'); 
const authController = require('../controllers/authController');
const publishController = require('../controllers/publishController'); // Import publishController

const router = express.Router();

// --- Routen für Marketing Content Generierung & Video Aufnahme ---
router.post('/trigger', marketingContentController.triggerFullMarketingWorkflow);
router.get('/video-test', marketingVideoRecorder.triggerVideoRecording); // Testroute für Video

// NEU: Route zum direkten Publizieren des nächsten geeigneten Contents
router.post('/publish/now', authController.protect, publishController.publishNow); // <PERSON><PERSON><PERSON><PERSON><PERSON> von authController.protect, falls nur authentifizierte Benutzer publishen dürfen

module.exports = router; 