<template>
    <button @click.prevent="toggleFilter" class="flex flex-initial md:w-1/12 gap-2 rounded-lg">
        <img :src="activeIconUrl" class="mx-auto my-auto" />
    </button>
</template>
<script setup>
    import { defineProps, toRefs, ref, computed } from 'vue';
    import { useMenuesStore } from '../store/menuStore'
    import { useUserStore } from '../store/userStore'
    import { useHelperStore } from '../../utils/helper'
    
    const menuesStore = useMenuesStore();
    const userStore = useUserStore();
    const helper = useHelperStore()

    const props = defineProps({
        element: Object,
        index: Number,
    })
    const { element } = toRefs(props);

    // always keep image accurate
    const activeIconUrl = computed(() => {
        // helper.devConsole(element.value) // Reduced logging
        if(element.value.store == 'recieptsStore'){
            // Check if the filter array exists and is an array before finding active
            const filterArray = menuesStore[element.value.name];
            if(Array.isArray(filterArray)) {
                const activeReceipt = filterArray.find(receipt => receipt.active);
                return activeReceipt
                    ? new URL(`../assets/icons/${activeReceipt.name}`, import.meta.url).href
                    : new URL(`../assets/icons/default-icon.png`, import.meta.url).href; // Fallback-Icon
            } else {
                 helper.devConsole(`Error: Filter array menuesStore[${element.value.name}] not found or not an array.`);
                 return new URL(`../assets/icons/default-icon.png`, import.meta.url).href; // Fallback
            }
        } else {
            return new URL(`../assets/icons/default-icon.png`, import.meta.url).href; // Fallback for other stores
        }
    })

    // --- MODIFIED: Call specific toggle functions based on element name ---
    const toggleFilter = () => {
        if (element.value.name === 'filterBoxDate') {
            menuesStore.toggleDateFilter();
        } else if (element.value.name === 'filterBoxTime') {
            menuesStore.toggleTimeFilter();
            // Note: The 'index' prop doesn't seem necessary for simple toggling
            // If specific index logic was needed, it would be passed here.
        } else {
            helper.devConsole(`Unknown filter name in clickIconButton: ${element.value.name}`);
        }
        // The watchers in menuStore will handle reloading the recipes.
    }
    // --- END MODIFICATION ---

    /* Old filter function removed
    const filter = () => {
        menuesStore.filter(element.value.name, element.value.searchString)
    }
    */


</script>

