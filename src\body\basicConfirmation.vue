<template>
  <!-- Overlay: Covers the whole screen, high z-index -->
  <div
    v-if="confirmationStore.confirmations.showState"
    class="fixed inset-0 bg-[rgba(0,0,0,0.5)] z-[9998] flex justify-center items-center p-4"
    @click.self="cancelDialog"
  >
    <!-- Dialog Box: Centered, basic styling -->
    <div
      class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto"
      role="alertdialog"
      aria-modal="true"
      :aria-labelledby="dialogTitleId"
      :aria-describedby="dialogDescriptionId"
    >
      <!-- Title (optional but good for accessibility) -->
      <h2 :id="dialogTitleId" class="text-lg text-gray-900 mb-4 text-center">Bestätigung</h2>
      
      <!-- Text Content -->
      <p :id="dialogDescriptionId" class="text-gray-700 text-center mb-6 text-sm">
        {{ confirmationStore.confirmations.confirmationText }}
      </p>

      <!-- Buttons: Centered -->
      <div class="flex justify-center gap-4">
        <button
          @click="confirmDialog"
          class="px-6 py-2 bg-ordypurple-100 text-white rounded-md font-medium hover:bg-ordypurple-200"
          ref="confirmButton"
        >
          Ja
        </button>
        <button
          @click="cancelDialog"
          class="px-6 py-2 bg-gray-200 text-gray-700 rounded-md font-medium hover:bg-gray-300"
        >
          Nein
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useConfirmationStore } from '../../utils/helper';
import { useHelperStore } from '../../utils/helper';
import { watch, ref, nextTick } from 'vue';

const helper = useHelperStore();
const confirmationStore = useConfirmationStore();

// Refs for accessibility
const dialogTitleId = 'confirmation-dialog-title';
const dialogDescriptionId = 'confirmation-dialog-description';
const confirmButton = ref(null);

// Watcher for debugging and focus management
watch(() => confirmationStore.confirmations.showState, async (newValue) => {
  helper.devConsole(`>>> basicConfirmation (Promise): showState changed to ${newValue}`);
  if (newValue) {
    // Focus the confirm button when dialog opens for accessibility
    await nextTick(); // Wait for the DOM to update
    if (confirmButton.value) {
      confirmButton.value.focus();
    }
  }
});

const confirmDialog = () => {
  helper.devConsole(">>> basicConfirmation (Promise): confirmDialog clicked");
  confirmationStore.confirm(); // Resolves promise with true
};

const cancelDialog = () => {
  helper.devConsole(">>> basicConfirmation (Promise): cancelDialog clicked");
  confirmationStore.cancel(); // Resolves promise with false
};

</script>

<style scoped>
/* Minimal scoped styles if needed, rely mostly on Tailwind */
</style>