---
description: 
globs: 
alwaysApply: true
---

# <PERSON><PERSON> jeder <PERSON> wird zwingend folgendes geprüft:

1) Alle Logs aus dem Terminal lesen.
2) <PERSON>utze playwright-mcp, logge dich falls nötig per klick auf Microsoft Login ein.
3) Prüfe ob die Änderungen funktioniert korrekt umgesetzt wurden. Nutze dazu einerseits das Tool um einen Screenshot zu erstellen. Nutze andererseits auch die Console Logs um den Verlauf zu analysieren.
4) Analysiere jetzt den Screenshot und die Console Logs. Falls alles wie gewünscht funktioniert - perfekt. Falls es ein Problem gibt, änder<PERSON> was n<PERSON><PERSON>g ist, damit es wieder funktioniert oder den Anforderungen entspricht. 
5) Melde zurück, was alles geändert wurde und was du herausgefunden hast.
6) Starte bei Schritt 1 und wiederhole das Prozedere, bis die Anforderung wie gewünscht funktioniert und oder umgesetzt ist.
7) Wenn erfolg<PERSON><PERSON> getestet, Do<PERSON>mentiere wenn nötig das neu Erschaffene im DOKU Ordner unter FRONTEND_DOCUMENTATION.md
