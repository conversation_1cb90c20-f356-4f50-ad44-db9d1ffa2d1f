const mongoose = require('mongoose');

const shoppingListItemSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Ein Artikel muss einen Namen haben']
  },
  quantity: {
    type: String,
    default: '1'
  },
  unit: {
    type: String,
    default: 'Stk'
  },
  is_purchased: {
    type: Boolean,
    default: false
  },
  is_custom: {
    type: Boolean,
    default: true
  },
  list: {
    type: mongoose.Schema.ObjectId,
    ref: 'ShoppingList',
    required: [true, 'Ein Artikel muss einer Einkaufsliste zugeordnet sein']
  },
  recipe: {
    type: mongoose.Schema.ObjectId,
    ref: 'Menu',
    default: null
  },
  category: {
    type: String,
    enum: [
      'Gemüse & Früchte',
      'Brotwaren & Backwaren',
      'Milchprodukte & Molkereiprodukte',
      'Fleisch, Wurst & Fisch',
      'Tiefkühlprodukte',
      'Grundnahrungsmittel',
      'Frühstück & Cerealien',
      'Süsswaren & Snacks',
      'Getränke',
      'Non-Food & Haushaltsartikel',
      'Sonstiges'
    ],
    default: 'Sonstiges'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Middleware zum Aktualisieren des updatedAt-Felds
shoppingListItemSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const ShoppingListItem = mongoose.model('ShoppingListItem', shoppingListItemSchema);

module.exports = ShoppingListItem;