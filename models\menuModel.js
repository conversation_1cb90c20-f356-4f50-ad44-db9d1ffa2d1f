const mongoose = require('mongoose');
const { connection1 } = require('./../db.js');
const helper = require("../utils/helper.js")
//const MenuesRelations = require('../models/menuRelationModel')
const Unit = require('./unitModel')
const Grocery = require('./groceryModel');

const menuSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
    },
    menuchilds: [
        {
            numberOfPersons: {
                type: Number,
            },
            menuChildId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'MenuChild'
            }
        }
    ],
    users: [
        {
            roleId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Role'
            },
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            }
        }
    ],
    imagelink: { 
        type: String,
    },
    freeAccess: {
            type: Boolean,
            default: false
    },
    createdAt: {
        type: Date,
        default: Date.now()
    }
})

// add image path the imagelink prop
menuSchema.post('init', async function(doc){

    //helper.devConsole("createImagePath @menuModel")
    if(doc.imagelink){
        const newDocumentPath = process.env.S3_PATH + doc.imagelink
        doc.imagelink = newDocumentPath
    } else {
        doc.imagelink = ""
    }
});

module.exports = connection1.model('Menu', menuSchema)