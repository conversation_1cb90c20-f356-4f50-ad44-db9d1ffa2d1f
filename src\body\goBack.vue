<template>
    <div v-if="userStore.user.id" class="w-1/4 md:w-full fixed md:relative bottom-20 md:top-4 rounded-2xl md:bg-white bg-[#000000] z-40">
        <button @click="$router.go(-1), userStore.closeTopNavigation()" class="flex flex-row py-2 pl-1 pr-2">
            <div>
                <svg class="icon w-5 mt-1 text-white fill-current md:text-black md:fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M15.41 16.59L10.83 12l4.58-4.59L12 6l-6 6 6 6z"/>
                </svg>
            </div> 
            <div class="font-bold md:text-black text-white">zurück</div>
        </button>
    </div>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/userStore'

  const router = useRouter();

  const { setNotification } = useNotification();
  const userStore = useUserStore()

  
</script>