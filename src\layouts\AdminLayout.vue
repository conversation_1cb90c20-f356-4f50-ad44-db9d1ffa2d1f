<template>
  <div class="admin-layout">
    <!-- Admin Header -->
    <header class="admin-header">
      <div class="header-container">
        <div class="header-content">
          <!-- Logo & Title -->
          <div class="logo-section">
            <router-link to="/admin" class="logo-link">
              <img src="../assets/ordy_logo.svg" alt="Ordy" class="logo" />
              <span class="title">Ordy Admin</span>
            </router-link>
          </div>

          <!-- User Menu -->
          <div class="user-menu">
            <span class="user-name">
              {{ userStore.user?.firstName }} {{ userStore.user?.lastName }}
            </span>
            <button @click="$router.push('/kochbuch')" class="back-button">
              Zurück zur App
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { useUserStore } from '../store/userStore';

const userStore = useUserStore();
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  background-color: #f9fafb;
}

.admin-header {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e5e7eb;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo {
  height: 2rem;
  width: 2rem;
  margin-right: 0.75rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-name {
  font-size: 0.875rem;
  color: #6b7280;
}

.back-button {
  background: #f3f4f6;
  color: #374151;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #e5e7eb;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}
</style>
