/**
 * Migrationsskript zur Kategorisierung bestehender Einkaufslistenartikel
 * und Aktualisierung der Statistik-Felder für die Einkaufszettel-Historie.
 * 
 * Ausführung: node scripts/migrateShoppingListItems.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { connection1 } = require('../db');
const categoryHelper = require('../utils/categoryHelper');
const ShoppingList = require('../models/shoppingListModel');
const ShoppingListItem = require('../models/shoppingListItemModel');

// Lade Umgebungsvariablen
dotenv.config({ path: './config.env' });

// Verbindung zur Datenbank wird über db.js hergestellt

const migrateItems = async () => {
  console.log('Starte Migration der Einkaufslistenartikel...');
  
  try {
    // 1. Kategorisiere alle Artikel ohne Kategorie
    const itemsWithoutCategory = await ShoppingListItem.find({
      category: { $exists: false }
    });
    
    console.log(`Gefunden: ${itemsWithoutCategory.length} Artikel ohne Kategorie`);
    
    let categorizedCount = 0;
    for (const item of itemsWithoutCategory) {
      const category = categoryHelper.categorizeItem(item.name);
      item.category = category;
      await item.save();
      categorizedCount++;
      
      if (categorizedCount % 100 === 0) {
        console.log(`${categorizedCount} Artikel kategorisiert...`);
      }
    }
    
    console.log(`Abgeschlossen: ${categorizedCount} Artikel wurden kategorisiert`);
    
    // 2. Aktualisiere die Statistik-Felder für alle Einkaufslisten
    const shoppingLists = await ShoppingList.find();
    console.log(`Gefunden: ${shoppingLists.length} Einkaufslisten für Statistik-Update`);
    
    let updatedListsCount = 0;
    for (const list of shoppingLists) {
      const items = await ShoppingListItem.find({ shopping_list_id: list._id });
      const totalItems = items.length;
      const completedItems = items.filter(item => item.is_purchased).length;
      
      list.item_count = totalItems;
      list.completed_item_count = completedItems;
      await list.save();
      updatedListsCount++;
      
      if (updatedListsCount % 50 === 0) {
        console.log(`${updatedListsCount} Listen aktualisiert...`);
      }
    }
    
    console.log(`Abgeschlossen: ${updatedListsCount} Einkaufslisten wurden aktualisiert`);
    
    console.log('Migration erfolgreich abgeschlossen!');
    process.exit(0);
  } catch (error) {
    console.error('Fehler bei der Migration:', error);
    process.exit(1);
  }
};

// Führe die Migration aus
migrateItems();
