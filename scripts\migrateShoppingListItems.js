/**
 * Migrationsskript zur Kategorisierung bestehender Einkaufslistenartikel
 * und Aktualisierung der Statistik-Felder für die Einkaufszettel-Historie.
 *
 * Ausführung: node scripts/migrateShoppingListItems.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const categoryHelper = require('../utils/categoryHelper');

// Lade Umgebungsvariablen
dotenv.config({ path: './config.env' });

// Stelle direkte Verbindung zur Datenbank her
console.log('Stelle Verbindung zur Datenbank her...');

// Mögliche MongoDB-Verbindungen
const devDbUri = 'mongodb+srv://dominickunz19:<EMAIL>/test?retryWrites=true&w=majority';
const prodDbUri = 'mongodb+srv://dominickunz19:<EMAIL>/prod?retryWrites=true&w=majority';

// Versuche verschiedene Verbindungen
let connection;

const connectToMongoDB = async () => {
  const uris = [devDbUri, prodDbUri];

  for (const uri of uris) {
    try {
      console.log(`Versuche Verbindung zu: ${uri.substring(0, uri.indexOf('@') > 0 ? uri.indexOf('@') : uri.length)}...`);

      connection = mongoose.createConnection(uri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 5000, // Kürzeres Timeout für schnelleres Fehlschlagen
        connectTimeoutMS: 5000
      });

      // Warte auf Verbindung oder Fehler
      await new Promise((resolve, reject) => {
        connection.once('connected', () => {
          console.log(`Verbindung zu ${uri.substring(0, uri.indexOf('@') > 0 ? uri.indexOf('@') : uri.length)} erfolgreich!`);
          resolve();
        });

        connection.once('error', (err) => {
          console.log(`Verbindung zu ${uri.substring(0, uri.indexOf('@') > 0 ? uri.indexOf('@') : uri.length)} fehlgeschlagen: ${err.message}`);
          reject(err);
        });
      });

      // Wenn wir hier ankommen, war die Verbindung erfolgreich
      return true;
    } catch (err) {
      console.log(`Fehler bei Verbindung zu ${uri.substring(0, uri.indexOf('@') > 0 ? uri.indexOf('@') : uri.length)}: ${err.message}`);
      continue; // Versuche die nächste URI
    }
  }

  // Wenn wir hier ankommen, waren alle Verbindungsversuche erfolglos
  throw new Error('Keine Verbindung zur Datenbank möglich');
};

// Hauptfunktion, die alles ausführt
const main = async () => {
  // Verbindung herstellen
  try {
    await connectToMongoDB();
  } catch (err) {
    console.error('Fehler bei der Datenbankverbindung:', err.message);
    process.exit(1);
  }

// Modelle definieren
const shoppingListSchema = new mongoose.Schema({
  kitchentable_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Kitchentable',
    required: true,
    index: true
  },
  name: {
    type: String,
    trim: true,
  },
  is_active: {
    type: Boolean,
    default: true,
    required: true
  },
  menu_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu',
    default: null
  },
  item_count: {
    type: Number,
    default: 0
  },
  completed_item_count: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const shoppingListItemSchema = new mongoose.Schema({
  shopping_list_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ShoppingList',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  quantity: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  unit: {
    type: String,
    trim: true,
    default: null
  },
  is_purchased: {
    type: Boolean,
    default: false,
    required: true
  },
  is_custom: {
    type: Boolean,
    default: false,
    required: true
  },
  added_by_user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  recipeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu',
    required: false,
    default: null,
    index: true
  },
  category: {
    type: String,
    enum: [
      'Gemüse & Früchte',
      'Brotwaren & Backwaren',
      'Milchprodukte & Molkereiprodukte',
      'Fleisch, Wurst & Fisch',
      'Tiefkühlprodukte',
      'Grundnahrungsmittel',
      'Frühstück & Cerealien',
      'Süsswaren & Snacks',
      'Getränke',
      'Non-Food & Haushaltsartikel',
      'Sonstiges'
    ],
    default: 'Sonstiges'
  }
}, {
  timestamps: true
});

  // Modelle definieren, nachdem die Verbindung hergestellt wurde
  const ShoppingList = connection.model('ShoppingList', shoppingListSchema);
  const ShoppingListItem = connection.model('ShoppingListItem', shoppingListItemSchema);

  console.log('Starte Migration der Einkaufslistenartikel...');

  try {
    // 1. Kategorisiere alle Artikel ohne Kategorie
    const itemsWithoutCategory = await ShoppingListItem.find({
      category: { $exists: false }
    });

    console.log(`Gefunden: ${itemsWithoutCategory.length} Artikel ohne Kategorie`);

    let categorizedCount = 0;
    for (const item of itemsWithoutCategory) {
      const category = categoryHelper.categorizeItem(item.name);
      item.category = category;
      await item.save();
      categorizedCount++;

      if (categorizedCount % 100 === 0) {
        console.log(`${categorizedCount} Artikel kategorisiert...`);
      }
    }

    console.log(`Abgeschlossen: ${categorizedCount} Artikel wurden kategorisiert`);

    // 2. Aktualisiere die Statistik-Felder für alle Einkaufslisten
    const shoppingLists = await ShoppingList.find();
    console.log(`Gefunden: ${shoppingLists.length} Einkaufslisten für Statistik-Update`);

    let updatedListsCount = 0;
    for (const list of shoppingLists) {
      const items = await ShoppingListItem.find({ shopping_list_id: list._id });
      const totalItems = items.length;
      const completedItems = items.filter(item => item.is_purchased).length;

      list.item_count = totalItems;
      list.completed_item_count = completedItems;
      await list.save();
      updatedListsCount++;

      if (updatedListsCount % 50 === 0) {
        console.log(`${updatedListsCount} Listen aktualisiert...`);
      }
    }

    console.log(`Abgeschlossen: ${updatedListsCount} Einkaufslisten wurden aktualisiert`);

    console.log('Migration erfolgreich abgeschlossen!');
    process.exit(0);
  } catch (error) {
    console.error('Fehler bei der Migration:', error);
    process.exit(1);
  }
};

// Führe die Hauptfunktion aus
main();
