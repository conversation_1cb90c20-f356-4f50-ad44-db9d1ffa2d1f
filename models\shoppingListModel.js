const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const shoppingListSchema = new mongoose.Schema({
  // kitchentable_id will be implicitly linked if this schema is nested
  // Or explicitly linked if it's a top-level collection:
  kitchentable_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Kitchentable', // Reference the 'Kitchentable' model
    required: true,
    index: true // Index for faster lookups by kitchentable
  },
  name: {
    type: String,
    trim: true,
    // Default name can be set in the controller logic upon creation
  },
  is_active: {
    type: Boolean,
    default: true,
    required: true
  },
  is_completed: {
    type: Boolean,
    default: false,
    required: true
  },
  // Felder für die Verknüpfung mit Menüs
  menu_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu',
    default: null
  },
  // Statistik-Felder für die Historie-Anzeige
  item_count: {
    type: Number,
    default: 0
  },
  completed_item_count: {
    type: Number,
    default: 0
  },
  // Mongoose adds createdAt and updatedAt automatically with timestamps: true
}, {
  timestamps: true,
  // Define collection name explicitly if needed, Mongoose defaults to plural lowercase ('shoppinglists')
  // collection: 'shopping_lists'
});

// Define relationships virtually or via population if needed elsewhere.
// Mongoose doesn't enforce FK constraints like SQL.

// Pre-hook to ensure only one active list per kitchentable (optional, handled in controller for now)
// shoppingListSchema.pre('save', async function(next) {
//   if (this.is_active && this.isNew) {
//     await this.constructor.updateMany({ kitchentable_id: this.kitchentable_id, _id: { $ne: this._id } }, { is_active: false });
//   }
//   next();
// });

// Use the imported connection to create the model
const ShoppingList = connection1.model('ShoppingList', shoppingListSchema);

module.exports = ShoppingList;