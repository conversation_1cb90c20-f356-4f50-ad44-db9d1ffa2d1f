const express = require('express');
const gptController = require('../controllers/gptController');
const authController = require('../controllers/authController');
//const menuRelationController = require('../controllers/menuRelationController');
const kinoController = require('../controllers/boty/kinoController');
const unitController = require('../controllers/unitController');
const menuController = require('../controllers/menuController');
const menuchildController = require('../controllers/menuchildController');
const paymentController = require('../controllers/paymentController');
const { devConsole } = require('../utils/helper');
const multer = require('multer');

const router = express.Router();

//////////////////////////////////////// SPEECH ASSISTANT API ////////////////////////////////////////

// sessionStore.js
// WEBSOCKET
/*
router.ws('/startconversation', async function (ws, req) {
    // --- AUTHENTICATION --- START
    let authResult;
    try {
        const token = req.query.token; // Expect Stytch session_token in query param
        devConsole('[WS /assistant] Connection attempt...');

        if (!token) {
            devConsole('[WS /assistant] Auth Error: No token provided.');
            ws.send(JSON.stringify({ event: 'auth_error', data: { message: 'Authentication token required.' }}));
            return ws.close(1008, "Authentication token required.");
        }

        // Verify Stytch token
        authResult = await authController.verifyStytchTokenForWs(token);

        if (!authResult || !authResult.isValid) {
             const errorMessage = authResult?.error || 'Invalid or expired session token.';
             devConsole(`[WS /assistant] Auth Error: Token verification failed - ${errorMessage}`);
             ws.send(JSON.stringify({ event: 'auth_error', data: { message: errorMessage }}));
             return ws.close(1008, errorMessage);
        }

        // Attach userId to the ws connection object for later use in the controller
        ws.userId = authResult.userId;
        devConsole(`[WS /assistant] Auth Success: User ${ws.userId} authenticated.`);

        // --- AUTHENTICATION --- END

        // If authentication is successful, proceed to the controller
        gptController.startGPTConversation(ws, req);

    } catch (error) {
        // Catch any unexpected errors during authentication
        const errorMessage = error.message || 'Internal server error during authentication.';
        devConsole(`[WS /assistant] UNEXPECTED Auth Error: ${errorMessage}`);
        try {
            ws.send(JSON.stringify({ event: 'auth_error', data: { message: 'Authentication failed.' }}));
        } catch (sendError) {
             devConsole(`[WS /assistant] Error sending error message before closing: ${sendError}`);
        }
        ws.close(1011, "Authentication failed."); // 1011: Internal Server Error
    }
});
*/

//////////////////////////////////////// SPEECH ASSISTANT API ////////////////////////////////////////

// Create Reciept from Image // ! Has to be at top regarding the limit 120kb
// Multer speichern, hier im Speicher; du kannst es anpassen, um Dateien auf der Festplatte zu speichern
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

/**
 * @openapi
 * /api/v1/creator/functions/reciept/createbyimage:
 *   post:
 *     tags:
 *       - GPT Creator - Recipes
 *     summary: Erstellt ein Rezept aus einem hochgeladenen Bild.
 *     description: Analysiert ein Bild, extrahiert Zutaten/Anleitung, erstellt ein Menü und optional ein KI-Bild.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image: # Der Name muss mit upload.single('image') übereinstimmen
 *                 type: string
 *                 format: binary
 *                 description: Das Bild des Rezepts.
 *               user_id: # Beispiel für zusätzliche Felder
 *                  type: string
 *                  format: objectId
 *                  description: Die ID des Benutzers (wird evtl. von verify gesetzt).
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Rezept erfolgreich erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseMenu' # Annahme eines Standard-API-Antwortschemas
 *       '400':
 *         description: Ungültige Anfrage oder Fehler bei der Bildanalyse/Rezeptgenerierung.
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Lizenzprüfung fehlgeschlagen.
 */
router
  .route('/functions/reciept/createbyimage')
  .post(
    upload.single('image'), // Dies fügt die Middleware hinzu, um die Datei zu verarbeiten
    authController.verify,
    paymentController.menuuploadsLicenceChecker,
    paymentController.licenceChecker,
    gptController.createRecieptByImage,
    gptController.createRecieptByText,
    unitController.groceryAndUnitChecker,
    menuController.createOneMenu,
    menuchildController.createOneMenuchild,
    menuchildController.addOneMenuchildToMenu,
    gptController.createImage,
    menuController.patchOneMenu,
    // 🔧 KRITISCH: Übertrage Menu-ID und lade populated Ingredients
    menuController.transferMenuIdToParams,
    menuController.getOneMenuPopulated,
    authController.sendanswer
  );

//router.use(viewsController.alerts);
/* protected */
/**
 * @openapi
 * /api/v1/creator/menulist:
 *   post:
 *     tags:
 *       - GPT Creator - Planning
 *     summary: Erstellt eine Menüliste/Wochenplan basierend auf Benutzereingaben.
 *     description: Generiert Vorschläge für einen Wochenplan mittels GPT.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               prompt: # Beispiel-Eingabefeld
 *                 type: string
 *                 description: Die Anfrage des Benutzers für den Wochenplan.
 *                 example: "Erstelle einen vegetarischen Wochenplan für 2 Personen für 5 Tage."
 *               # ... weitere notwendige Felder ...
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Menüliste erfolgreich generiert.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject' # Generisches Antwortschema
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Lizenzprüfung fehlgeschlagen.
 */
router.post('/menulist',
    authController.verify,
    paymentController.menucreationsLicenceChecker,
    paymentController.licenceChecker,
    gptController.createOverview
);

//SearchmenuView.vue | Zeile 500
/**
 * @openapi
 * /api/v1/creator/functions/reciept/createbytext:
 *   post:
 *     tags:
 *       - GPT Creator - Recipes
 *     summary: Erstellt ein Rezept aus einer Texteingabe.
 *     description: Verarbeitet eine Texteingabe (z.B. Zutatenliste, Beschreibung), generiert ein strukturiertes Rezept, erstellt ein Menü und optional ein KI-Bild.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recieptText: # Beispiel
 *                 type: string
 *                 description: Der Text, aus dem das Rezept generiert werden soll.
 *                 example: "Pasta mit Tomatensauce und Basilikum für 4 Personen..."
 *               user_id: # Beispiel
 *                 type: string
 *                 format: objectId
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Rezept erfolgreich erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseMenu'
 *       '400':
 *         description: Ungültige Anfrage oder Fehler bei der Textanalyse/Rezeptgenerierung.
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Lizenzprüfung fehlgeschlagen.
 */
router
.route('/functions/reciept/createbytext')
.post(
    authController.verify,
    paymentController.menuuploadsLicenceChecker,
    paymentController.licenceChecker,
    gptController.createRecieptByText,
    unitController.groceryAndUnitChecker,
    menuController.createOneMenu,
    menuchildController.createOneMenuchild,
    menuchildController.addOneMenuchildToMenu,
    // NOT NEED THIS HANDLER, BECAUSE ITS CREATED THE FIRS TIME:
    // menuchildController.updateRelatedMenuchildsIsStandardField
    gptController.createImage,
    menuController.patchOneMenu,
    menuController.menuChecker,
    // 🔧 KRITISCH: Übertrage Menu-ID und lade populated Ingredients
    menuController.transferMenuIdToParams,
    menuController.getOneMenuPopulated,
    authController.sendanswer
);

/**
 * @openapi
 * /api/v1/creator/functions/reciept/createbyurl:
 *   post:
 *     tags:
 *       - GPT Creator - Recipes
 *     summary: Erstellt ein Rezept aus einer URL.
 *     description: Lädt den Inhalt einer Webseite, extrahiert das Rezept, erstellt ein Menü und optional ein KI-Bild.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 format: url
 *                 description: Die URL der Webseite mit dem Rezept.
 *               user_id:
 *                 type: string
 *                 format: objectId
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Rezept erfolgreich erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseMenu'
 *       '400':
 *         description: Ungültige URL oder Fehler beim Extrahieren/Generieren.
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Lizenzprüfung fehlgeschlagen.
 */
router
.route('/functions/reciept/createbyurl')
.post(
    authController.verify,
    paymentController.menuuploadsLicenceChecker,
    paymentController.licenceChecker,
    gptController.createRecieptByURL,
    gptController.createRecieptByText,
    unitController.groceryAndUnitChecker,
    menuController.createOneMenu,
    menuchildController.createOneMenuchild,
    menuchildController.addOneMenuchildToMenu,
    // NOT NEED THIS HANDLER, BECAUSE ITS CREATED THE FIRS TIME:
    // menuchildController.updateRelatedMenuchildsIsStandardField
    gptController.createImage,
    menuController.patchOneMenu,
    // 🔧 KRITISCH: Übertrage Menu-ID und lade populated Ingredients
    menuController.transferMenuIdToParams,
    menuController.getOneMenuPopulated,
    authController.sendanswer
);

// OLD:
//router.post('/functions/reciept/createbyurl', authController.verify, gptController.createRecieptByURL, gptController.createRecieptByText, unitController.arrayGroceryAndUnitChecker, gptController.createRecieptAndImage)
//router.post('/functions/reciept/createimageandsavereciept', authController.verify, unitController.arrayGroceryAndUnitChecker, gptController.createRecieptAndImage);

/**
 * @openapi
 * /api/v1/creator/details:
 *   get:
 *     tags:
 *       - GPT Creator - General
 *     summary: Ruft GPT-Modelldetails ab.
 *     description: Gibt Informationen über das verwendete GPT-Modell zurück.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200': # Annahme: OK Status
 *         description: Modelldetails erfolgreich abgerufen.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 *       '401':
 *         description: Nicht autorisiert.
 */
router.get('/details', authController.verify, gptController.getDetails);

/**
 * @openapi
 * /api/v1/creator/functions/ingredients/proof/{ingridient}:
 *   get:
 *     tags:
 *       - GPT Creator - Ingredients
 *     summary: Überprüft eine Zutat mit GPT.
 *     description: Sendet einen Zutatennamen an GPT zur Überprüfung oder Anreicherung.
 *     parameters:
 *       - in: path
 *         name: ingridient
 *         required: true
 *         schema:
 *           type: string
 *         description: Der Name der zu überprüfenden Zutat.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Zutat erfolgreich überprüft.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 *       '401':
 *         description: Nicht autorisiert.
 */
router.get('/functions/ingredients/proof/:ingridient', authController.verify, gptController.proofIngridient, authController.sendanswer);

/**
 * @openapi
 * /api/v1/creator/functions/ingredients/toreciept:
 *   post:
 *     tags:
 *       - GPT Creator - Ingredients
 *     summary: Erstellt Rezeptvorschläge basierend auf Zutaten.
 *     description: Nimmt eine Liste von Zutaten entgegen und generiert passende Rezeptvorschläge mittels GPT.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ingredients:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Eine Liste von verfügbaren Zutaten.
 *                 example: ["Tomaten", "Zwiebeln", "Knoblauch", "Pasta"]
 *               # ... weitere Optionen ...
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Rezeptvorschläge erfolgreich generiert.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Lizenzprüfung fehlgeschlagen.
 */
router.post('/functions/ingredients/toreciept', authController.verify, paymentController.cookeasyLicenceChecker, paymentController.licenceChecker, gptController.createReciepsByIngridients, authController.sendanswer)

/**
 * @openapi
 * /api/v1/creator/assistant/session/start:
 *   post:
 *     tags:
 *       - GPT Creator - Assistant
 *     summary: Start assistant session with license check
 *     description: Checks realtime API license before allowing assistant session to start
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: License check passed, assistant session can start.
 *       '401':
 *         description: Nicht autorisiert.
 *       '402':
 *          description: Realtime API Lizenz aufgebraucht.
 */
router.post('/assistant/session/start', authController.verify, paymentController.realtimeApiLicenceChecker, paymentController.licenceChecker, authController.sendanswer)

/**
 * @openapi
 * /api/v1/creator/profile/image/upload:
 *   post:
 *     tags:
 *       - User Profile
 *     summary: Upload profile image
 *     description: Upload a new profile image for the authenticated user
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               profileImage:
 *                 type: string
 *                 format: binary
 *     responses:
 *       '200':
 *         description: Profile image uploaded successfully.
 *       '401':
 *         description: Nicht autorisiert.
 *       '400':
 *         description: Ungültige Datei oder Fehler beim Upload.
 */
router.post('/profile/image/upload', authController.verify, gptController.uploadProfileImage)

/**
 * @openapi
 * /api/v1/creator/profile/image/generate:
 *   post:
 *     tags:
 *       - User Profile
 *     summary: Generate AI profile image
 *     description: Generate a profile image using AI based on user prompt
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Description for AI image generation
 *               style:
 *                 type: string
 *                 enum: [realistic, cartoon, artistic, professional]
 *                 description: Style of the generated image
 *     responses:
 *       '200':
 *         description: Profile image generated successfully.
 *       '401':
 *         description: Nicht autorisiert.
 *       '400':
 *         description: Fehler bei der KI-Generierung.
 */
router.post('/profile/image/generate', authController.verify, gptController.generateProfileImage)

/**
 * @openapi
 * /api/v1/creator/profile/image/upload:
 *   post:
 *     tags:
 *       - User Profile
 *     summary: Upload profile image
 *     description: Upload a new profile image for the authenticated user
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               profileImage:
 *                 type: string
 *                 format: binary
 *     responses:
 *       '200':
 *         description: Profile image uploaded successfully.
 *       '401':
 *         description: Nicht autorisiert.
 *       '400':
 *         description: Ungültige Datei oder Fehler beim Upload.
 */
router.post('/profile/image/upload', authController.verify, gptController.uploadProfileImage, authController.sendanswer)

/**
 * @openapi
 * /api/v1/creator/profile/image/generate:
 *   post:
 *     tags:
 *       - User Profile
 *     summary: Generate AI profile image
 *     description: Generate a profile image using AI based on user prompt
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Description for AI image generation
 *               style:
 *                 type: string
 *                 enum: [realistic, cartoon, artistic, professional]
 *                 description: Style of the generated image
 *     responses:
 *       '200':
 *         description: Profile image generated successfully.
 *       '401':
 *         description: Nicht autorisiert.
 *       '400':
 *         description: Fehler bei der KI-Generierung.
 */
router.post('/profile/image/generate', authController.verify, gptController.generateProfileImage, authController.sendanswer)
/**
 * @openapi
 * /api/v1/creator/functions/ingredients/nutritionalcalc:
 *   post:
 *     tags:
 *       - GPT Creator - Ingredients
 *     summary: Berechnet Nährwertangaben für Zutaten/Rezept.
 *     description: Sendet Zutaten oder ein Rezept an GPT zur Nährwertberechnung.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipeData: # Beispiel
 *                 type: object # oder string
 *                 description: Die Daten (Zutaten, Mengen etc.) für die Nährwertberechnung.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Nährwerte erfolgreich berechnet.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 *       '401':
 *         description: Nicht autorisiert.
 */
router.post('/functions/ingredients/nutritionalcalc', authController.verify, gptController.createCalculationOfNutrions, authController.sendanswer)

/**
 * @openapi
 * /api/v1/creator/functions/ingredients/categorize:
 *   post:
 *     tags:
 *       - GPT Creator - Ingredients
 *     summary: Kategorisiert Zutaten mit KI und extrahiert Name, Menge und Einheit.
 *     description: Verwendet GPT-3.5-turbo zur intelligenten Kategorisierung von Zutaten-Eingaben. Extrahiert automatisch Name, Menge, Einheit und ordnet die passende Kategorie zu.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               input:
 *                 type: string
 *                 description: Die Zutat-Eingabe des Users (z.B. "500ml Rahm", "2 Äpfel")
 *                 example: "500ml Rahm"
 *               name:
 *                 type: string
 *                 description: Alternativer Zutatennamen (falls input nicht verwendet wird)
 *                 example: "Rahm"
 *               quantity:
 *                 type: string
 *                 description: Optionale Mengenangabe (falls bereits bekannt)
 *                 example: "500"
 *               unit:
 *                 type: string
 *                 description: Optionale Einheit (falls bereits bekannt)
 *                 example: "ml"
 *             required:
 *               - input
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: Zutat erfolgreich kategorisiert.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 result:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Rahm"
 *                     quantity:
 *                       type: string
 *                       example: "500"
 *                     unit:
 *                       type: string
 *                       example: "ml"
 *                     category:
 *                       type: string
 *                       example: "Milchprodukte & Molkereiprodukte"
 *                 originalInput:
 *                   type: string
 *                   example: "500ml Rahm"
 *       '400':
 *         description: Ungültige Eingabe.
 *       '401':
 *         description: Nicht autorisiert.
 *       '500':
 *         description: KI-Kategorisierung fehlgeschlagen.
 */
router.post('/functions/ingredients/categorize', authController.verify, gptController.categorizeIngredientWithAI, authController.sendanswer)

///// Create Image based on Reciept
/**
 * @openapi
 * /api/v1/creator/functions/image/createoneimagebyreciept:
 *   post:
 *     tags:
 *       - GPT Creator - Images
 *     summary: Erstellt ein KI-Bild basierend auf einem Rezept.
 *     description: Sendet Rezeptdetails an eine KI, um ein passendes Bild zu generieren und speichert den Link im Menü.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               menu: # Beispiel
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: objectId
 *                   name:
 *                     type: string
 *                   description:
 *                     type: string
 *                 required: [id, name] # Mindestanforderung
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Bild erfolgreich generiert und im Menü gespeichert.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseMenu'
 *       '400':
 *         description: Fehler bei der Bildgenerierung.
 *       '401':
 *         description: Nicht autorisiert.
 */
router.post('/functions/image/createoneimagebyreciept', authController.verify, gptController.createImage, menuController.patchOneMenu, authController.sendanswer)
// OLD:
//router.post('/functions/reciept/createbyurl', authController.verify, gptController.createRecieptByURL, gptController.createRecieptByText, unitController.arrayGroceryAndUnitChecker, gptController.createRecieptAndImage)
//router.post('/functions/reciept/createbytext', authController.verify, gptController.createRecieptByText, unitController.arrayGroceryAndUnitChecker, gptController.createRecieptAndImage)
// OLD FUNCTION, decrapched 2.5.24, replaced by /functions/reciept/createbytext | // router.post('/onemenubytext', authController.verify, gptController.createRecieptByText);
/* protected */

/**
 * @openapi
 * /api/v1/creator/functions/basic:
 *   post:
 *     tags:
 *       - GPT Creator - General
 *     summary: Einfache Chat-Funktion mit GPT.
 *     description: Sendet eine Nachricht an GPT und erhält eine Antwort (nicht geschützt).
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 example: "Hallo GPT!"
 *     responses:
 *       '200':
 *         description: Erfolgreiche Antwort von GPT.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 */
/* not protected */
router.post('/functions/basic', gptController.onlyChat)
/* not protected */

/* kinnovation routes */
/* not protected */
/**
 * @openapi
 * /api/v1/creator/functions/kinnovations/basic:
 *   post:
 *     tags:
 *       - GPT Creator - Innovation
 *     summary: Basisfunktion für Innovationen.
 *     description: Führt grundlegende Innovationen durch.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               # ... weitere notwendige Felder ...
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Innovation erfolgreich durchgeführt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 */
router.post('/functions/kinnovations/basic', kinoController.kinnovations)
/**
 * @openapi
 * /api/v1/creator/functions/kinnovations/createmeeting:
 *   post:
 *     tags:
 *       - GPT Creator - Innovation
 *     summary: Erstellt ein neues Treffen für Innovationen.
 *     description: Erstellt ein neues Treffen für Innovationen.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               # ... weitere notwendige Felder ...
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Treffen erfolgreich erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 */
router.post('/functions/kinnovations/createmeeting', kinoController.createmeeting)
/**
 * @openapi
 * /api/v1/creator/functions/kinnovations/getfreecalendarslots:
 *   get:
 *     tags:
 *       - GPT Creator - Innovation
 *     summary: Ruft freie Kalenderplätze für Innovationen ab.
 *     description: Ruft freie Kalenderplätze für Innovationen ab.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: Freie Kalenderplätze erfolgreich abgerufen.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 */
router.get('/functions/kinnovations/getfreecalendarslots', kinoController.freeslot, kinoController.createAirtableRow)
/**
 * @openapi
 * /api/v1/creator/functions/kinnovations/responsePhoneRequest:
 *   post:
 *     tags:
 *       - GPT Creator - Innovation
 *     summary: Antwort auf eine Telefonanfrage für Innovationen.
 *     description: Antwort auf eine Telefonanfrage für Innovationen.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               # ... weitere notwendige Felder ...
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Antwort erfolgreich gesendet.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponseObject'
 */
router.post('/functions/kinnovations/responsePhoneRequest', kinoController.getAllAirtableRows, kinoController.responsePhoneRequest)
/* not protected */


module.exports = router;

// Referenzierte Schemata (könnten in menuRoutes.js oder einer zentralen Datei sein)
/**
 * @openapi
 * components:
 *   schemas:
 *     ApiResponseMenu:
 *       type: object
 *       properties:
 *         status: { type: string, example: success }
 *         success: { type: boolean, example: true }
 *         data: { $ref: '#/components/schemas/Menu' }
 *     ApiResponseObject:
 *       type: object
 *       properties:
 *         status: { type: string, example: success }
 *         success: { type: boolean, example: true }
 *         data: { type: object } # Generisches Objekt für diverse Antworten
 */