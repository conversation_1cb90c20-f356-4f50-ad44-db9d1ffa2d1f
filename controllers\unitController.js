const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const Units = require('../models/unitModel');
const Grocery = require('../models/groceryModel');
const Menu = require('../models/menuModel');
const AppError = require('../utils/appError');
const { categorizeAndCreateGrocery } = require('../utils/intelligentCategorizer');


//@POST /menu/one/ingredientsrow/checker
exports.einheitAndNamePreChecker = catchAsync(async (req, res, next) => {

  //////////////////// SETTINGS /////////////////////////////
  // Needs following Items to survive
  // Purpose is to pre check if the values are correct, and if they exist link or create new props and link them
  // req.body.value => eg. l, dl, M<PERSON> or Milch, milch
  // req.body.firstLevelKey => name (grocery) or einheit (unit)
  ///////////////////////////////////////////////////////////

  helper.devConsole("einheitChecker in unitController")
  helper.devConsole(req.body.firstLevelKey)
  helper.devConsole(req.body.value)
  //console.log(req.body)
  //console.log("go")

  try{
    
    let responseKey = ''

    //if einheit
    if(req.body.firstLevelKey == "unit"){
      
      var regex = new RegExp(["^", req.body.value, "$"].join(""), "i");
      const units = await Units.find({name: regex})
      //console.log(units)
      if(units.length == 0 || null){
        // if unit is unknown,
        // set unit to -
        responseKey = {"name":"-","description":"-"}
        
      } else {
        //set unit to found unit id
        //console.log(units)
        responseKey = units[0]
      }
    }

    
    //if name
    if(req.body.firstLevelKey == "name"){
      //console.log("req.body")
      //console.log(req.body)
      //console.log("-------------")
      var regex = new RegExp(["^", req.body.value, "$"].join(""), "i");
      const grocery = await Grocery.find({name: regex})
      //console.log("grocery")
      //console.log(grocery)
      if(grocery.length == 0){
        // if grocery is unknown,
        // create grocery
        //console.log("create grocery")
        const createdGrocery = await Grocery.create({name: req.body.value})
        //console.log(createdGrocery)
        // set created grocery id
        // responseKey
        responseKey = createdGrocery
      } else {
        //set grocery to id
        responseKey = grocery[0]
      }
    }

    //console.log("responseKey")
    //console.log(responseKey)

    res.status(201).json({
      status: 'success',
      data: responseKey
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    /*res.status(401).json({
      status: 'error',
      data: error
    });*/
  }

});

//@- only serverside use
exports.arrayGroceryAndUnitChecker = catchAsync(async (req, res, next) => {
  helper.devConsole("!!!!! FUNCTION NOT FINISHED !!!!!! arrayGroceryAndUnitChecker in unitController")
  helper.devConsole(req.body.data.ingredients)
  if( 
    !req.body.data.ingredients
  ){
      next(new AppError('Not every data was given at unitController.arrayGroceryAndUnitChecker', 500))
  }
    //console.log(req.body.data.ingredients.length)

    for (let i = 0; i < req.body.data.ingredients.length; i++){
      //////////////////////// UNIT CHECK ///////////////////////////
      // check if unit is okey
      const units = await Units.find({name: req.body.data.ingredients[i].unit})
      //console.log("units")
      //console.log(units)
      if(units.length == 0){
        // if unit is unknown,
        // set unit to - and search for id in Unit Table

        const defaultUnit = await Units.findOne({"name": "-"})
        //console.log("defaultUnit")
        //console.log(defaultUnit)

        req.body.ingredients[i].unit = defaultUnit._id
      } else {
        //set unit to found unit id
        helper.devConsole(units)
        req.body.data.ingredients[i].unit = units[0]._id
      }
      
      ////////////////////// GROCERY CHECK ///////////////////////////
      const grocery = await Grocery.find({name: req.body.data.ingredients[i].name})
      //console.log("grocery")
      //console.log(grocery)
      if(grocery.length == 0){
        // if grocery is unknown,
        // create grocery
        helper.devConsole("create grocery")
        const createdGrocery = await Grocery.create({name: req.body.data.ingredients[i].name})
        helper.devConsole(createdGrocery)
        // set created grocery id
        req.body.data.ingredients[i].name = createdGrocery._id
      } else {
        //set grocery to id
        req.body.data.ingredients[i].name = grocery[0]._id
      }
    }

    
    //console.log(req.body.data.ingredients.length)
    //console.log(req.body.data.ingredients)

    // Send success response
    next()
    

});

//@- only serverside use
// main groceryAndUnitChecker inside "Creation" Navigation

function isFractionOrNumeric(value) {
  // Definiere die häufigsten Bruchzeichen
  const fractionMap = {
    '½': 0.5,
    '¼': 0.25,
    '¾': 0.75,
    '⅓': 1/3,
    '⅔': 2/3,
    '⅕': 1/5,
    '⅖': 2/5,
    '⅗': 3/5,
    '⅘': 4/5,
    '⅙': 1/6,
    '⅚': 5/6,
    // Hier können weitere Brüche hinzugefügt werden
  };

  // Prüfen, ob der Wert in der Bruchmap existiert
  if (value in fractionMap) {
    return fractionMap[value]; // Gibt den numerischen Wert zurück
  }

  // Prüfen, ob der Wert eine Zahl ist
  const parsedValue = parseFloat(value);
  return isNaN(parsedValue) ? null : parsedValue; // Gibt null zurück, wenn es eine NaN ist oder den Zahlwert
}

exports.groceryAndUnitChecker = catchAsync(async (req, res, next) => {
  helper.devConsole("groceryAndUnitChecker in unitController")
  //helper.devConsole(req.body)
  if( 
      !req.body.ingredients
  ){
      next(new AppError('Not every data was given at unitController.groceryAndUnitChecker', 500))
  }

  for (let i = 0; i < req.body.ingredients.length; i++){
    //////////////////////// UNIT CHECK ///////////////////////////

    // 🔧 KRITISCH: Prüfe ob unit bereits eine ObjectId ist (von AI-Validator)
    if (typeof req.body.ingredients[i].unit === 'object' && req.body.ingredients[i].unit._id) {
      // Unit ist bereits eine ObjectId - keine weitere Verarbeitung nötig
      helper.devConsole(`✅ Ingredient unit already processed as ObjectId: ${req.body.ingredients[i].unit._id}`);
      req.body.ingredients[i].unit = req.body.ingredients[i].unit._id;
    } else if (typeof req.body.ingredients[i].unit === 'string' && req.body.ingredients[i].unit.match(/^[0-9a-fA-F]{24}$/)) {
      // Unit ist bereits eine ObjectId-String - keine weitere Verarbeitung nötig
      helper.devConsole(`✅ Ingredient unit already processed as ObjectId string: ${req.body.ingredients[i].unit}`);
    } else {
      // Unit ist ein String oder verschachtelte Struktur - normale Verarbeitung
      let unitName = req.body.ingredients[i].unit;

      // 🔧 KRITISCH: Extrahiere Unit-Name aus verschiedenen Strukturen
      if (typeof unitName === 'object' && unitName.name !== undefined) {
        unitName = unitName.name; // {name: "g"} → "g"
      }

      helper.devConsole(`🔧 Processing unit name: "${unitName}"`);

      const units = await Units.find({name: unitName})
      helper.devConsole("units")
      helper.devConsole(units)
      if(units.length == 0){
        // if unit is unknown,
        // set unit to -

        const defaultUnit = await Units.findOne({"name": "-"})
        //console.log("defaultUnit")
        //console.log(defaultUnit)

        req.body.ingredients[i].unit = defaultUnit._id
      } else {
        //set unit to found unit id
        //console.log(units)
        req.body.ingredients[i].unit = units[0]._id
      }
    }
    
    ////////////////////// GROCERY CHECK WITH INTELLIGENT CATEGORIZATION ///////////////////////////

    // 🔧 KRITISCH: Prüfe ob name bereits eine ObjectId ist (von AI-Validator)
    if (typeof req.body.ingredients[i].name === 'object' && req.body.ingredients[i].name._id) {
      // Name ist bereits eine ObjectId - keine weitere Verarbeitung nötig
      helper.devConsole(`✅ Ingredient name already processed as ObjectId: ${req.body.ingredients[i].name._id}`);
      req.body.ingredients[i].name = req.body.ingredients[i].name._id;
    } else if (typeof req.body.ingredients[i].name === 'string' && req.body.ingredients[i].name.match(/^[0-9a-fA-F]{24}$/)) {
      // Name ist bereits eine ObjectId-String - keine weitere Verarbeitung nötig
      helper.devConsole(`✅ Ingredient name already processed as ObjectId string: ${req.body.ingredients[i].name}`);
    } else {
      // Name ist ein String oder verschachtelte Struktur - normale Verarbeitung
      let groceryName = req.body.ingredients[i].name;

      // 🔧 KRITISCH: Extrahiere Grocery-Name aus verschiedenen Strukturen
      if (typeof groceryName === 'object' && groceryName.name !== undefined) {
        groceryName = groceryName.name; // {name: "Neue Zutat"} → "Neue Zutat"
      }

      helper.devConsole(`🔧 Processing grocery name: "${groceryName}"`);

      try {
        // 🤖 KRITISCH: Verwende intelligente Kategorisierung für neue Groceries
        const categoryResult = await categorizeAndCreateGrocery(groceryName, true);

        // Setze die Grocery-ID
        req.body.ingredients[i].name = categoryResult.groceryId;

        helper.devConsole(`🔧 Grocery processed: ${groceryName} → ID: ${req.body.ingredients[i].name} → Category: ${categoryResult.category} (${categoryResult.confidence.toFixed(2)} confidence, ${categoryResult.method})`);

      } catch (categoryError) {
        helper.devConsole(`⚠️ Intelligent categorization failed for "${groceryName}", falling back to basic creation`);

        // Fallback: Basis-Grocery-Erstellung ohne Kategorisierung
        const grocery = await Grocery.find({name: groceryName})
        if(grocery.length == 0){
          const createdGrocery = await Grocery.create({name: groceryName})
          req.body.ingredients[i].name = createdGrocery._id
          helper.devConsole("create grocery (fallback)")
        } else {
          req.body.ingredients[i].name = grocery[0]._id
        }
      }
    }

    //////////////////////// AMOUNT CHECK ///////////////////////////
    /*if (isNaN(parseFloat(req.body.ingredients[i].amount))) {
      // wenn amount nicht in zahlen umgewandelt werden kann, eine Zahl setzen
      req.body.ingredients[i].amount = 0
    }*/
    // check auf sonderzeichen
    const numericValue = isFractionOrNumeric(req.body.ingredients[i].amount);
    req.body.ingredients[i].amount = numericValue !== null ? numericValue : 0;
    //////////////////////// AMOUNT CHECK ///////////////////////////

  }

  //refactor object for next()
  if(req.body.menuchild){
    req.body.menuchild.ingredients = req.body.ingredients
  } else {
    // /one/child/:menuchildid/ingredientschecker
    req.body.menuchild = {}
    req.body.menuchild.ingredients = req.body.ingredients
  }

  // Send success response
  next()

});




// ONLY ACTIVE FOR DATA MANIPULATION
//@- only serverside use
/*
exports.datamanipulationUnit = catchAsync(async (req, res, next) => {
  helper.devConsole("unitChecker in unitController")
  try{
    helper.devConsole(req.body)
    //check units inside units
    
    const Menus = await Menu.find()

    helper.devConsole(Menus)

    for (let i = 0; i < Menus.length; i++){
      helper.devConsole(i)
      helper.devConsole("Menu")
      
      for (let o = 0; o < Menus[i].zutaten.length; o++){
        helper.devConsole("add item to zutaten")
        //zutat
        let newRow = Menus[i].zutaten[o]
        helper.devConsole(newRow)
        helper.devConsole(newRow.name)
        // check if unit is okey
        newRow.unit = '-'
        Menus[i].zutaten[o]["good"] = '-'
        helper.devConsole("Menus[i].zutaten[o].good")
        helper.devConsole(Menus[i].zutaten[o].good)
        Menus[i].zutaten[o] = newRow
      }

      helper.devConsole("update menu")
      helper.devConsole(Menus[i])
      //const Menus = await Menu.find()
    }
    
    // Send success response
    res.status(201).json({
      status: 'error',
      data: Menus
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});
*/