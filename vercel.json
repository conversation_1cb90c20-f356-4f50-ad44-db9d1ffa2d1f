{"rewrites": [{"source": "/manifest.json", "destination": "/manifest.json"}, {"source": "/service-worker.js", "destination": "/service-worker.js"}, {"source": "/assets/(.*)", "destination": "/assets/$1"}, {"source": "/:path*", "destination": "/index.html"}], "headers": [{"source": "/:path*", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/manifest.json", "headers": [{"key": "X-Password-Protected", "value": "false"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}]}, {"source": "/service-worker.js", "headers": [{"key": "X-Password-Protected", "value": "false"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}]}, {"source": "/assets/(.*)", "headers": [{"key": "X-Password-Protected", "value": "false"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}