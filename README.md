# ordy

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

### DB PRD Update (bevore delete)
Following to do:
1) Save "groceries" and import it to "groceries" after DB reset.
2) Save "units" and import it to "units" after DB reset.
2) Save "roles" and import it to "roles" after DB reset.
3) Save ID from "-" element from units. Think about to update the ID in unitController.groceryAndUnitChecker (3x) after new PRD DB is initialised. Otherwise error on ingredients management will happen.

## Env's explained

```sh
npm install
```


### Development

```sh
npm run dev
```
Backend: http://localhost:8080/api/v1/
Frontend: localhost:5173

Choose which env should be target.
![alt text](image.png)

### Preview

```sh
npm run prev
or vercel
or git push on main branch
```
Backend: https://ordy-tst.azurewebsites.net/api/v1/
Frontend: https://ordy-dodorus-ordyproject.vercel.app/wochenplan

### Production

```sh
npm run prod
or vercel --prod
or git push on prd branch
```
Backend: https://ordy-prd.azurewebsites.net/api/v1/
Frontend: ordy.ch

```
