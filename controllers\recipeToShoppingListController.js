/**
 * Recipe to Shopping List Integration Controller
 * Handles adding recipe ingredients to shopping lists
 */

const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const AppError = require('../utils/appError');
const ShoppingList = require('../models/shoppingListModel');
const ShoppingListItem = require('../models/shoppingListItemModel');
const Menuchild = require('../models/menuchildModel');
const Kitchentable = require('../models/kitchentableModel');

/**
 * Fügt alle Zutaten eines Rezepts zum aktiven Einkaufszettel hinzu
 * POST /api/v1/recipes/:menuChildId/add-to-shopping-list
 */
const addRecipeToShoppingList = catchAsync(async (req, res, next) => {
    const { menuChildId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`[addRecipeToShoppingList] Adding recipe ${menuChildId} to shopping list by user ${userId}`);

    // 1. Lade das MenuChild mit populated ingredients
    const menuChild = await Menuchild.findById(menuChildId)
        .populate([
            {
                path: 'ingredients.unit',
                model: 'Unit'
            },
            {
                path: 'ingredients.name',
                model: 'Grocery'
            }
        ]);

    if (!menuChild) {
        return next(new AppError('Recipe not found', 404));
    }

    if (!menuChild.ingredients || menuChild.ingredients.length === 0) {
        return next(new AppError('Recipe has no ingredients', 400));
    }

    // 2. Finde die Kitchentable des Users
    const kitchentable = await Kitchentable.findOne({
        members: { $elemMatch: { user: userId } }
    });

    if (!kitchentable) {
        return next(new AppError('User is not member of any kitchentable', 404));
    }

    // 3. Finde oder erstelle aktive Shopping List
    let shoppingList = await ShoppingList.findOne({
        kitchentable_id: kitchentable._id,
        is_active: true
    });

    if (!shoppingList) {
        const currentWeek = getWeekNumber(new Date());
        const listName = `Einkaufszettel KW${currentWeek}`;
        
        shoppingList = await ShoppingList.create({
            kitchentable_id: kitchentable._id,
            name: listName,
            is_active: true
        });
        
        helper.devConsole(`[addRecipeToShoppingList] Created new shopping list: ${shoppingList._id}`);
    }

    // 4. Konvertiere Ingredients zu Shopping List Items
    const newItems = [];
    let addedCount = 0;
    let skippedCount = 0;

    for (const ingredient of menuChild.ingredients) {
        try {
            // Prüfe ob Item bereits existiert (gleicher Name und Rezept)
            const existingItem = await ShoppingListItem.findOne({
                shopping_list_id: shoppingList._id,
                name: ingredient.name.name,
                recipeId: menuChild.parentId,
                is_purchased: false
            });

            if (existingItem) {
                // Aggregiere Mengen wenn Item bereits existiert
                const newQuantity = parseFloat(existingItem.quantity) + parseFloat(ingredient.amount);
                await ShoppingListItem.updateOne(
                    { _id: existingItem._id },
                    { 
                        quantity: newQuantity,
                        unit: ingredient.unit.name 
                    }
                );
                
                helper.devConsole(`[addRecipeToShoppingList] Updated existing item: ${ingredient.name.name} (${newQuantity}${ingredient.unit.name})`);
                addedCount++;
            } else {
                // Erstelle neues Item
                const category = await categorizeIngredient(ingredient.name.name);
                
                const newItem = await ShoppingListItem.create({
                    shopping_list_id: shoppingList._id,
                    name: ingredient.name.name,
                    quantity: ingredient.amount,
                    unit: ingredient.unit.name,
                    is_custom: false,
                    added_by_user_id: userId,
                    recipeId: menuChild.parentId,
                    category: category
                });

                newItems.push(newItem);
                helper.devConsole(`[addRecipeToShoppingList] Created new item: ${ingredient.name.name} (${ingredient.amount}${ingredient.unit.name}) in category ${category}`);
                addedCount++;
            }
        } catch (itemError) {
            helper.devConsole(`[addRecipeToShoppingList] Error processing ingredient ${ingredient.name?.name}: ${itemError.message}`);
            skippedCount++;
        }
    }

    // 5. Aktualisiere Shopping List Statistiken
    const totalItems = await ShoppingListItem.countDocuments({
        shopping_list_id: shoppingList._id
    });
    
    const completedItems = await ShoppingListItem.countDocuments({
        shopping_list_id: shoppingList._id,
        is_purchased: true
    });

    await ShoppingList.updateOne(
        { _id: shoppingList._id },
        {
            item_count: totalItems,
            completed_item_count: completedItems,
            menu_id: menuChild.parentId // Verknüpfe mit Rezept
        }
    );

    // 6. Broadcast Update (falls WebSocket verfügbar)
    if (req.app.locals.broadcastToRoom) {
        try {
            await getListAndBroadcast(shoppingList._id, kitchentable._id, req.app.locals.broadcastToRoom);
        } catch (broadcastError) {
            helper.devConsole(`[addRecipeToShoppingList] Broadcast failed: ${broadcastError.message}`);
        }
    }

    helper.devConsole(`[addRecipeToShoppingList] Successfully added ${addedCount} items, skipped ${skippedCount} items`);

    res.status(200).json({
        success: true,
        message: `${addedCount} Zutaten zum Einkaufszettel hinzugefügt`,
        data: {
            shoppingListId: shoppingList._id,
            addedItems: addedCount,
            skippedItems: skippedCount,
            totalItems: totalItems
        }
    });
});

/**
 * Entfernt alle Zutaten eines Rezepts vom Einkaufszettel
 * DELETE /api/v1/recipes/:menuChildId/remove-from-shopping-list
 */
const removeRecipeFromShoppingList = catchAsync(async (req, res, next) => {
    const { menuChildId } = req.params;
    const userId = req.user.id;

    helper.devConsole(`[removeRecipeFromShoppingList] Removing recipe ${menuChildId} from shopping list by user ${userId}`);

    // 1. Lade das MenuChild
    const menuChild = await Menuchild.findById(menuChildId);
    if (!menuChild) {
        return next(new AppError('Recipe not found', 404));
    }

    // 2. Finde die Kitchentable des Users
    const kitchentable = await Kitchentable.findOne({
        members: { $elemMatch: { user: userId } }
    });

    if (!kitchentable) {
        return next(new AppError('User is not member of any kitchentable', 404));
    }

    // 3. Finde aktive Shopping List
    const shoppingList = await ShoppingList.findOne({
        kitchentable_id: kitchentable._id,
        is_active: true
    });

    if (!shoppingList) {
        return next(new AppError('No active shopping list found', 404));
    }

    // 4. Entferne alle Items des Rezepts
    const deleteResult = await ShoppingListItem.deleteMany({
        shopping_list_id: shoppingList._id,
        recipeId: menuChild.parentId
    });

    // 5. Aktualisiere Shopping List Statistiken
    const totalItems = await ShoppingListItem.countDocuments({
        shopping_list_id: shoppingList._id
    });
    
    const completedItems = await ShoppingListItem.countDocuments({
        shopping_list_id: shoppingList._id,
        is_purchased: true
    });

    await ShoppingList.updateOne(
        { _id: shoppingList._id },
        {
            item_count: totalItems,
            completed_item_count: completedItems
        }
    );

    // 6. Broadcast Update
    if (req.app.locals.broadcastToRoom) {
        try {
            await getListAndBroadcast(shoppingList._id, kitchentable._id, req.app.locals.broadcastToRoom);
        } catch (broadcastError) {
            helper.devConsole(`[removeRecipeFromShoppingList] Broadcast failed: ${broadcastError.message}`);
        }
    }

    helper.devConsole(`[removeRecipeFromShoppingList] Removed ${deleteResult.deletedCount} items`);

    res.status(200).json({
        success: true,
        message: `${deleteResult.deletedCount} Zutaten vom Einkaufszettel entfernt`,
        data: {
            shoppingListId: shoppingList._id,
            removedItems: deleteResult.deletedCount,
            totalItems: totalItems
        }
    });
});

/**
 * Kategorisiert eine Zutat automatisch
 * @param {string} ingredientName - Name der Zutat
 * @returns {string} Kategorie
 */
async function categorizeIngredient(ingredientName) {
    const name = ingredientName.toLowerCase();
    
    // Ordy Kategorie-Farbsystem
    const categories = {
        'Gemüse & Früchte': ['tomate', 'zwiebel', 'karotte', 'apfel', 'banane', 'salat', 'gurke', 'paprika', 'zitrone'],
        'Brotwaren & Backwaren': ['brot', 'brötchen', 'mehl', 'hefe', 'backpulver'],
        'Milchprodukte & Molkereiprodukte': ['milch', 'käse', 'joghurt', 'butter', 'sahne', 'quark'],
        'Fleisch, Wurst & Fisch': ['fleisch', 'wurst', 'fisch', 'hähnchen', 'rind', 'schwein', 'lachs'],
        'Tiefkühlprodukte': ['tiefkühl', 'gefror'],
        'Grundnahrungsmittel': ['reis', 'nudeln', 'pasta', 'öl', 'essig', 'salz', 'pfeffer', 'zucker'],
        'Getränke': ['wasser', 'saft', 'tee', 'kaffee', 'bier', 'wein']
    };

    for (const [category, keywords] of Object.entries(categories)) {
        if (keywords.some(keyword => name.includes(keyword))) {
            return category;
        }
    }

    return 'Sonstiges';
}

/**
 * Helper function für Wochennummer
 */
function getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}

/**
 * Helper function für Broadcasting (falls verfügbar)
 */
async function getListAndBroadcast(listId, kitchentableId, broadcastFunction) {
    try {
        const items = await ShoppingListItem.find({ shopping_list_id: listId })
            .sort({ is_purchased: 1, name: 1 });
        
        const list = await ShoppingList.findById(listId);
        const listData = list.toObject();
        listData.items = items;

        broadcastFunction(`kitchentable_${kitchentableId}`, 'shopping_list_updated', listData);
    } catch (error) {
        helper.devConsole(`Broadcasting failed: ${error.message}`);
    }
}

module.exports = {
    addRecipeToShoppingList,
    removeRecipeFromShoppingList,
    categorizeIngredient
};
