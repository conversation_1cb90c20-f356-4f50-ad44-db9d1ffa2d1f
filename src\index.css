/* Importing fonts from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Yeseva+One&display=swap');

@import "tailwindcss";

/* Globale Stile für bessere mobile Erfahrung */
html {
  -webkit-tap-highlight-color: transparent;
  height: -webkit-fill-available;
  overflow-x: hidden;
}

body {
  min-height: 100vh;
  min-height: -webkit-fill-available;
  overflow-x: hidden;
  touch-action: manipulation;
  -webkit-overflow-scrolling: touch;
}

/* iPhone PWA Safe Area Support */
.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

/* Verbesserte Fokus-Stile für Accessibility - Dezente graue Farben */
:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #9CA3AF, 0 0 0 4px rgba(255, 255, 255, 0.8);
}

/* Verhindern von Text-Selektion bei Buttons und interaktiven Elementen */
button,
.button,
[role="button"],
.interactive {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Verbesserte Touch-Ziele für mobile Geräte */
@media (max-width: 640px) {
  button,
  .button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  a.interactive {
    min-height: 44px;
    min-width: 44px;
  }

  input,
  select,
  textarea {
    font-size: 16px; /* Verhindert Zoom auf iOS */
  }

  /* Entferne nur NEU erscheinende Rahmen bei Focus in Mobile/PWA */
  /* Behalte bestehende border-bottom, entferne nur neue Outline/Box-Shadow */
  input:focus,
  input:active,
  textarea:focus,
  textarea:active,
  select:focus,
  select:active {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Spezifische Behandlung für border-bottom Inputs - behalte border-bottom */
  input.border-b:focus,
  textarea.border-b:focus {
    border-bottom-color: #d1d5db !important; /* Behalte graue border-bottom */
  }

  /* Spezifische Behandlung für Inputs MIT bestehenden Rahmen - behalte sie */
  input.bg-white:focus,
  textarea.bg-white:focus,
  input.rounded-lg:focus,
  textarea.rounded-lg:focus,
  input.rounded-xl:focus,
  textarea.rounded-xl:focus {
    border: 1px solid #d1d5db !important; /* Behalte grauen Rahmen */
  }

  /* Entferne nur NEUE Rahmen bei Focus - nicht bestehende */
  .focus\:border-purple-400:focus:not(.border-b):not(.bg-white),
  .focus\:border-gray-300:focus:not(.border-b):not(.bg-white),
  .focus\:border-blue-500:focus:not(.border-b):not(.bg-white),
  .focus\:border-gray-200:focus:not(.border-b):not(.bg-white),
  .focus\:border-blue-300:focus:not(.border-b):not(.bg-white),
  .focus\:border-indigo-500:focus:not(.border-b):not(.bg-white),
  .focus\:border-green-500:focus:not(.border-b):not(.bg-white) {
    border-color: transparent !important;
  }

  /* Entferne Ring-Effekte */
  .focus\:ring:focus,
  .focus\:ring-2:focus,
  .focus\:ring-blue-500:focus,
  .focus\:ring-purple-500:focus {
    box-shadow: none !important;
  }
}

/* GLOBALE REGEL: Entferne alle purple/pink Focus-Rahmen */
input:focus,
textarea:focus,
select:focus {
  border-color: #D1D5DB !important; /* Grau statt Purple */
  outline: none !important;
  box-shadow: none !important;
}

/* Spezifische Überschreibung für purple Focus-Klassen */
.focus\:border-purple-400:focus,
.focus\:border-purple-500:focus,
.focus\:border-purple-600:focus,
.focus\:border-pink-400:focus,
.focus\:border-pink-500:focus {
  border-color: #D1D5DB !important; /* Grau statt Purple/Pink */
}

/* Animationen für Feedback */
.tap-highlight {
  transition: transform 150ms ease;
}

.tap-highlight:active {
  transform: scale(0.95);
}

/* Portrait-Modus erzwingen - Viewport-Fälschung mit korrekter Rotation */
html.force-portrait-rotation {
  /* HTML-Container bleibt normal */
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

html.force-portrait-rotation body {
  /* Rotiere den Body um 90 Grad */
  transform: rotate(-90deg);
  transform-origin: center center;

  /* WICHTIG: Verwende die kleinere Dimension als Breite (Portrait-Simulation) */
  width: 100vh;  /* Die Höhe des Landscape-Viewports wird zur Portrait-Breite */
  height: 100vw; /* Die Breite des Landscape-Viewports wird zur Portrait-Höhe */

  /* Zentriere den rotierten Body perfekt */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);

  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;

  transition: transform 0.3s ease-in-out;
}

/* App-Container nimmt die volle "gefälschte" Portrait-Größe ein */
html.force-portrait-rotation #app {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  /* Simuliere Portrait-Viewport für alle Child-Elemente */
  --viewport-width: 100vh;   /* Landscape-Höhe = Portrait-Breite */
  --viewport-height: 100vw;  /* Landscape-Breite = Portrait-Höhe */
}

/* GLOBALE MOBILE-SIMULATION: Alle Elemente verhalten sich wie auf schmalem Mobile */
html.force-portrait-rotation * {
  /* Überschreibe alle Breakpoint-basierten Stile */
  box-sizing: border-box !important;
}

/* SUPER EINFACH: Deaktiviere ALLE responsive Breakpoints im Landscape */
html.force-portrait-rotation * {
  /* Überschreibe alle @media Queries - behandle als wären wir immer unter 640px */
}

/* EINFACHER ANSATZ: Nur CSS-Rotation, kein Layout-Chaos */
/* Das Viewport-Meta-Tag erledigt den Rest automatisch */



/* Einfache Basis-Überschreibungen für bessere Kompatibilität */
html.force-portrait-rotation .container {
  max-width: 100% !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Kleine Benachrichtigung, dass die App rotiert wurde */
html.force-portrait-rotation body::before {
  content: '🔄 Auto-rotiert für bessere Ansicht';
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(107, 70, 193, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-family: 'Open Sans', sans-serif;
  z-index: 10000;
  animation: fadeInOut 3s ease-in-out;
  pointer-events: none;
}

/* Normale Darstellung ohne Rotation */
html:not(.force-portrait-rotation) {
  width: 100%;
  height: 100%;
  overflow: visible;
}

html:not(.force-portrait-rotation) body {
  transform: none;
  width: 100%;
  height: 100vh;
  position: relative;
  top: auto;
  left: auto;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
  transition: transform 0.3s ease-in-out;
}

/* Animation für die Rotations-Benachrichtigung */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
  20% { opacity: 1; transform: translateX(-50%) translateY(0); }
  80% { opacity: 1; transform: translateX(-50%) translateY(0); }
  100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
}



/* Define your theme variables */
@theme {
  --spacing: 0.25rem;

  --font-OpenSans: "Open Sans", sans-serif;
  --font-YesevaOne: "Yeseva One", sans-serif;

  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1080px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1620px;
  --breakpoint-3xl: 1890px;

  --spacing-114: 350px;
  --spacing-128: 32rem;

  /* Corrected Color Variables */
  --color-ordypurple-100: rgba(163, 125, 255, 1);
  --color-ordypurple-200: rgba(153, 115, 215, 1);
  --color-ordypink-100: rgba(208, 41, 120, 1);
  --color-ordypink-200: rgba(225, 96, 157, 1);
  --color-alarmred-100: rgba(195, 64, 72, 1);

  --color-green-500: rgba(34, 197, 94, 1);
  --color-gray-25: rgba(250, 250, 250, 1);
  --color-gray-50: rgba(240, 240, 240, 1);
  --color-gray-75: rgba(235, 235, 235, 1);
  --color-gray-90: rgba(225, 225, 225, 1);
  --color-gray-100: rgba(213, 213, 213, 1);
  --color-gray-200: rgba(200, 200, 200, 1);
  --color-black: #000000;
  --color-white: #FFFFFF;
  --color-default-button-bg: #A37DFF;
  --color-default-button-shadow: #E0ADFF;
}

@layer theme {
  :root {
    --font-sans: var(--font-OpenSans);
    --font-serif: var(--font-YesevaOne);
  }
}

@layer base {
  h1 {
    @apply text-[3.2rem] font-serif antialiased break-words;
  }
  h2 {
    @apply text-[2rem] font-serif antialiased;
  }
  h3 {
    @apply font-sans font-bold antialiased;
  }
  p {
    @apply text-[0.95rem] font-sans font-thin antialiased;
  }

  button {
    @apply cursor-pointer;
  }


  /* More customized styles can follow */
}


/* Additional styles or layers for components, utilities, etc. */
@layer utilities {
  .default-button-bg {
    background-color: var(--color-default-button-bg);
  }

  .default-button-shadow {
    box-shadow: -6px 6px 1px 0 var(--color-default-button-shadow);
  }

  .text-tiny {
    font-size: 11px;
  }
  .text-xs {
    font-size: 14px;
  }
  .text-sm {
    font-size: 16px;
  }
  .text-base {
    font-size:19px;
  }
  .text-lg {
    font-size: 25px;
  }
  .text-xl {
    font-size:50px;
  }



  /*fontSize: {
    tiny: ['11px', '13px'],
    xs: ['14px', '15px'],
    sm: ['16px', '18px'],
    base: ['19px', '22px'],
    lg: ['25px', '28px'],
    xl: ['50px', '32px'],
  },*/

  --text-tiny: 11px;
  --text-xs: 14px;
  --text-sm: 16px;
  --text-base: 19px;
  --text-lg: 25px;
  --text-xl: 50px;

  /* Weitere Utilities hier... */
}

/* Prevent iOS auto-zoom on input focus */
input,
textarea,
select {
  font-size: 16px;
}