# BACKEND_DOCUMENTATION.md

## Übersicht

Dieses Dokument enthält die technische Dokumentation für das `ordy-api`-Backend. Es beschreibt den aktuellen Zustand und die Funktionsweise implementierter Features.

## Architektur

*   **Beschreibung:** Standard Express.js-Architektur mit Trennung von Concerns:
    *   `server.js`: Initialisiert den Server.
    *   `app.js`: Definiert die Express-Anwendung, lädt Middleware und bindet Routen ein.
    *   `db.js`: Stellt die Verbindung zur MongoDB-Datenbank her.
    *   `routes/`: Definiert die API-Endpunkte und leitet Anfragen an Controller weiter.
    *   `controllers/`: Enthält die Geschäftslogik für die Verarbeitung von Anfragen.
    *   `models/`: Definiert die Mongoose-Datenbankschemata.
    *   `middleware/`: Enthält Middleware-Funktionen (z.B. für Authentifizierung, Autorisierung, Fehlerbehandlung).
    *   `utils/`: Enthält Hilfsfunktionen.
    *   `config.env`: Konfigurationsdatei für Umgebungsvariablen.
*   [Diagramm (als Link oder eingebettet, falls möglich)] - *Optional*

## API Referenz

### Authentifizierung (`routes/authRoutes.js`, `controllers/authController.js`, `routes/oauthRoutes.js`, `controllers/oauthController.js`)

Dieses System unterstützt zwei primäre Authentifizierungsmechanismen:

1.  **Benutzerauthentifizierung via Stytch:** Für reguläre Benutzer, die sich über OAuth-Anbieter (z.B. Google) anmelden.
2.  **Service-Client-Authentifizierung via JWT:** Für interne oder vertrauenswürdige Drittanbieter-Dienste, die programmatisch auf die API zugreifen.

#### 1. Benutzerauthentifizierung (Stytch)

Dieses System verwendet **Stytch** für die primäre Benutzerauthentifizierung (OAuth, z.B. Google Login) und verwaltet eine lokale Benutzerdatenbank in MongoDB, die mit dem Stytch-Benutzer verknüpft ist.

**Zentraler Token für Benutzer: `session_token` (von Stytch)**

Der wichtigste Token für das Frontend ist der **`session_token`**, der von Stytch generiert wird. Das Frontend erhält diesen Token als **URL-Query-Parameter** nach dem erfolgreichen Login-Redirect vom Backend.

**Verwendung des `session_token` durch das Frontend:**

*   **Für geschützte HTTP API-Anfragen:** Sende den Token im `Authorization`-Header mit dem Präfix `Bearer `.
    *Beispiel:* `Authorization: Bearer <stytch_session_token>`
    *   *Backend-Verifizierung:* Erfolgt durch die Middleware `authController.verify`.
*   **Für WebSocket-Verbindungen:** Sende den Token als **Query-Parameter** `token` in der WebSocket-URL **beim initialen Verbindungsaufbau**.
    *Beispiel:* `wss://www.ordy.ch/ws/assistant/startconversation?token=<stytch_session_token>`
    *   *Backend-Verifizierung:* Erfolgt durch eine spezifische WebSocket-Authentifizierungslogik (z.B. `authController.verifyStytchTokenForWs`).

--- 

**1.1. Login / Registrierung (via Stytch OAuth)**

*   **Zweck:** Authentifiziert einen Benutzer über einen externen Anbieter (z.B. Google) mittels Stytch. Wenn der Benutzer zum ersten Mal über diesen Anbieter kommt, wird automatisch ein lokaler Account erstellt.
*   **Frontend-Trigger:** Benutzer klickt auf einen Login-Button (z.B. "Login mit Google"). Das Frontend initiiert den Stytch OAuth-Flow (z.B. mit `stytch.oauth.google.start()`).
*   **Backend-Endpunkt (Redirect Target):** `/api/v1/auth/reg` (GET)
    *   *Wichtig:* Dieser Endpunkt wird **nicht direkt** vom Frontend aufgerufen. Stytch leitet den Browser des Benutzers nach erfolgreicher Authentifizierung beim Provider (z.B. Google) zu diesem Endpunkt um.
*   **Ablauf:**
    1.  Frontend startet Stytch OAuth Flow.
    2.  Benutzer authentifiziert sich beim OAuth-Provider (z.B. Google).
    3.  OAuth-Provider leitet zu Stytch zurück.
    4.  Stytch leitet den Browser des Benutzers zum Backend-Callback `/api/v1/auth/reg` um und hängt einen temporären Authentifizierungs-Token (`?token=...`) an die URL an.
    5.  Backend (`authController.auth`) empfängt den Redirect und den temporären Token.
    6.  Backend tauscht den temporären Token bei Stytch gegen einen `session_token`, `session_jwt` und Benutzerinformationen (Stytch User ID, Name, Email, etc.) ein (`stytch.oauth.authenticate`).
    7.  Backend sucht in der lokalen MongoDB nach einem Benutzer mit `extId` (Stytch User ID).
    8.  **Fall A: Benutzer existiert (Login):** Das Backend verwendet den gefundenen lokalen Benutzer.
    9.  **Fall B: Benutzer existiert nicht (Registrierung):** Das Backend erstellt einen neuen Benutzer in der lokalen MongoDB und verknüpft ihn mit der Stytch User ID (`extId`).
    10. Backend konstruiert eine **Redirect-URL zurück zum Frontend** (z.B. `https://www.ordy.ch/`, `http://localhost:5173/`).
    11. Backend hängt den erhaltenen **`session_token`**, `session_jwt` sowie weitere Benutzerdaten (lokale `_id`, `firstName`, `lastName`, `img`, `email`, `stytchUserID`, `defaultKitchentable`, etc.) als **Query-Parameter** an diese Frontend-Redirect-URL an.
    12. Backend sendet einen HTTP 302 Redirect (`res.redirect(...)`) an den Browser.
    13. **Frontend empfängt den Redirect:** Der Browser lädt die Frontend-URL mit den angehängten Query-Parametern.
    14. **Frontend muss die Query-Parameter auslesen**, insbesondere den `session_token`, und diesen für die Sitzung speichern. Dieser `session_token` wird für alle weiteren authentifizierten Anfragen benötigt.
*   **Beispielhafte Query-Parameter, die das Frontend nach dem Redirect erhält:**
    `?session_token=stytch_ses_abc...&session_jwt=ey...&user_id=user-test-xyz...&id=65bf6ccbc62e8260e0619055&first_name=Max&last_name=Muster&img=...&email=...&defaultKitchentable=...&explanation_trail=...&externalAccess=...&isNewUser=false&session_expires_at=2024-08-23T10:20:30.123Z`

--- 

**1.2. Logout**

*   **Zweck:** Beendet die aktuelle Benutzersitzung durch Invalidierung des Stytch Session Tokens.
*   **Frontend-Trigger:** Benutzer klickt auf "Logout".
*   **Backend-Endpunkt:** `/api/v1/auth/logout` (POST)
*   **Ablauf:**
    1.  Frontend sendet eine HTTP-Anfrage (z.B. POST) an `/api/v1/auth/logout`.
    2.  Frontend **muss** den gespeicherten `session_token` im `Authorization`-Header senden: `Authorization: Bearer <stytch_session_token>`.
    3.  Backend (`authController.logout`) extrahiert den Token.
    4.  Backend ruft `stytch.sessions.revoke()` mit dem Token auf, um die Session bei Stytch zu beenden.
    5.  Backend sendet eine Erfolgsantwort (z.B. 200 OK) an das Frontend.
    6.  **Frontend muss** bei erfolgreicher Antwort die lokal gespeicherten Tokens (`session_token`, `session_jwt`) und Benutzerdaten löschen und den Benutzer zur Login-Seite leiten oder den UI-Status aktualisieren.

--- 

**1.3. Benutzerdaten abrufen (nach Login/Seiten-Neuladen)**

*   **Zweck:** Abrufen der lokalen Benutzerdaten aus der MongoDB anhand der Stytch Session.
*   **Frontend-Trigger:** Nach dem Login-Redirect oder beim Initialisieren der App, um den eingeloggten Benutzer zu laden.
*   **Backend-Endpunkt:** `/api/v1/auth/userbyid` (POST)
    *   *Hinweis:* Dieser Endpunkt scheint die bevorzugte Methode zu sein, um den *lokalen* Benutzer zu holen, nachdem das Frontend den `session_token` hat.
*   **Ablauf:**
    1.  Frontend sendet eine HTTP-Anfrage (z.B. POST) an `/api/v1/auth/userbyid`.
    2.  Frontend **muss** den gespeicherten `session_token` im `Authorization`-Header senden: `Authorization: Bearer <stytch_session_token>`.
    3.  Backend-Middleware (`authController.verify`) validiert den Token bei Stytch.
    4.  Wenn gültig, wird der zugehörige lokale Benutzer aus der MongoDB geladen (via `extId` Link) und an `req.user` angehängt.
    5.  Der Controller (z.B. `authController.getUserAfterReload`) gibt die Daten des lokalen Benutzers (`req.user`) zurück.
    6.  **Frontend empfängt** die Benutzerdaten und kann den Benutzerstatus in der App setzen.
    7.  *Fehlerfall:* Wenn der Token ungültig ist (von `authController.verify` geprüft), sendet das Backend einen 401-Fehler. **Frontend muss** diesen Fehler behandeln und den Benutzer ausloggen.

--- 

**1.4. Account Löschen**

*   **Zweck:** Löscht den Account des Benutzers sowohl lokal als auch bei Stytch.
*   **Frontend-Trigger:** Benutzer bestätigt die Löschung seines Accounts in den Einstellungen.
*   **Backend-Endpunkt:** `/api/v1/auth/me` (DELETE - *Route in `authRoutes.js` und Controller-Funktion `authController.deleteMe` beachten*)
*   **Ablauf:**
    1.  Frontend sendet eine HTTP DELETE-Anfrage an `/api/v1/auth/me`.
    2.  Frontend **muss** den gespeicherten `session_token` im `Authorization`-Header senden: `Authorization: Bearer <stytch_session_token>`.
    3.  Backend-Middleware (`authController.verify`) validiert den Token und hängt den lokalen Benutzer an `req.user` an.
    4.  Der Controller `authController.deleteMe` wird ausgeführt.
    5.  Backend löscht den Benutzer aus der lokalen MongoDB (`User.findByIdAndDelete(req.user._id)`).
    6.  Backend versucht, den Benutzer bei Stytch zu löschen (`stytch.users.delete({ user_id: req.user.extId })`).
    7.  Backend sendet eine Erfolgsantwort (z.B. 204 No Content).
    8.  **Frontend muss** bei erfolgreicher Antwort lokale Daten löschen und den Benutzer ausloggen/umleiten.

--- 

**1.5. Überprüfung der Session-Gültigkeit (Implizit)**

*   **Zweck:** Sicherstellen, dass der Benutzer noch eingeloggt ist.
*   **Frontend-Trigger:** Regulärer Aufruf eines beliebigen geschützten HTTP API-Endpunkts, der für Benutzer vorgesehen ist.
*   **Backend-Endpunkt:** Jeder durch `authController.verify` geschützte Endpunkt.
*   **Ablauf:**
    1.  Frontend sendet eine Anfrage an einen geschützten Endpunkt mit dem `session_token` im `Authorization`-Header.
    2.  Die Backend-Middleware `authController.verify` prüft den Token bei Stytch.
    3.  **Wenn gültig:** Die Anfrage wird normal bearbeitet, Frontend erhält die erwartete Antwort (z.B. 200 OK mit Daten).
    4.  **Wenn ungültig/abgelaufen:** Das Backend sendet einen 401 Unauthorized Fehler.
    5.  **Frontend muss** auf 401-Fehler reagieren, indem es den Benutzer ausloggt (lokale Tokens löschen, zur Login-Seite leiten).

--- 

**1.6. Token Refresh (Proaktiv durch Frontend)**

*   **Zweck:** Ermöglicht dem Frontend, den `session_token` proaktiv zu erneuern, bevor er abläuft, um eine kontinuierliche Benutzersitzung ohne erzwungenes Neulogin zu gewährleisten.
*   **Frontend-Trigger:** Das Frontend stellt fest, dass der aktuelle `session_token` bald abläuft (basierend auf dem `session_expires_at`-Wert, der beim Login erhalten wurde).
*   **Backend-Endpunkt:** `/api/v1/auth/session/refresh` (POST)
*   **Ablauf:**
    1.  Frontend sendet eine HTTP POST-Anfrage an `/api/v1/auth/session/refresh`.
    2.  Frontend **muss** den aktuellen, noch gültigen `session_token` im `Authorization`-Header senden: `Authorization: Bearer <stytch_session_token>`.
    3.  Das Backend (`authController.refreshSession`) nimmt diesen Token entgegen und ruft die Stytch API (`sessions.authenticate`) auf, um die Session zu validieren und zu erneuern.
    4.  **Erfolgsfall (200 OK):** Das Backend sendet eine JSON-Antwort mit dem (potenziell neuen) `session_token`, `session_jwt` und dem neuen `session_expires_at`:
        ```json
        {
          "status": "success",
          "session_token": "neuer_oder_derselbe_stytch_session_token",
          "session_jwt": "neues_oder_dasselbe_stytch_session_jwt",
          "session_expires_at": "neuer_utc_timestamp_des_ablaufs_als_iso_string"
        }
        ```
    5.  **Frontend muss** bei erfolgreicher Antwort die lokal gespeicherten Tokens und den `session_expires_at`-Wert mit den neuen Werten aktualisieren.
    6.  **Fehlerfall (z.B. 401 Unauthorized):** Wenn der gesendete Token ungültig oder bereits abgelaufen ist. Das Frontend sollte den Benutzer in diesem Fall ausloggen.

#### 2. Service-Client-Authentifizierung (Interne JWTs)

Für interne Backend-Dienste oder vertrauenswürdige Drittanbieter-Services, die programmatisch auf die API zugreifen müssen (z.B. für automatisierte Aufgaben wie die Videoerstellung im Marketing-Workflow), wird ein OAuth 2.0 Client Credentials Grant Flow verwendet.

**Zentraler Token für Services: `access_token` (JWT)**

Service Clients erhalten einen `access_token` (ein JWT), indem sie ihre `clientId` und `clientSecret` austauschen.

**2.1. Service Client Setup (Einmalig pro Service)**

1.  **Erstellung in der Datenbank:** Ein Administrator muss manuell einen Eintrag in der `serviceclients`-Collection der MongoDB anlegen.
    *   **Benötigte Felder:**
        *   `name` (String): Ein beschreibender Name für den Service (z.B. "VideoGenerationService", "ReportingService").
        *   `clientId` (String): Eine eindeutige ID für den Client. Kann z.B. mit `node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"` generiert werden.
        *   `clientSecret` (String): Das **gehashte** Client Secret. Ein Klartext-Secret kann z.B. mit `node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"` generiert und dann mit `bcryptjs` gehasht werden (siehe Kommentare in `controllers/oauthController.js` für den genauen Hash-Befehl).

**2.2. Anfordern eines Access Tokens**

*   **Zweck:** Erlaubt einem registrierten Service Client, seine Credentials gegen einen kurzlebigen `access_token` (JWT) einzutauschen.
*   **Backend-Endpunkt:** `/api/v1/oauth/token` (POST)
    *   Verantwortlicher Controller: `oauthController.createToken`
*   **Anfrage:**
    *   **Header:** `Content-Type: application/x-www-form-urlencoded`
    *   **Body (form-urlencoded):**
        *   `grant_type`: Muss `client_credentials` sein.
        *   `client_id`: Die `clientId` des Service Clients.
        *   `client_secret`: Das **Klartext** `clientSecret` des Service Clients.
*   **Erfolgsantwort (200 OK):**
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // Das JWT
      "token_type": "Bearer",
      "expires_in": 3600 // Gültigkeitsdauer in Sekunden (z.B. 1 Stunde)
    }
    ```
*   **Fehlerantworten:**
    *   `400 Bad Request`: Bei ungültigem `grant_type` oder fehlender `client_id`/`client_secret`.
    *   `401 Unauthorized`: Bei ungültigen `client_id` oder `client_secret`.

**2.3. Verwendung des Access Tokens**

*   **Für geschützte API-Anfragen:** Der Service Client sendet den erhaltenen `access_token` im `Authorization`-Header mit dem Präfix `Bearer `.
    *Beispiel:* `Authorization: Bearer <access_token>`
*   **Backend-Verifizierung:** Erfolgt durch die Middleware `authController.verifyServiceClient`.
    *   Diese Middleware prüft die Gültigkeit des JWT (Signatur, Ablaufdatum).
    *   Bei Erfolg werden die dekodierten Informationen aus dem Token (inkl. `serviceName` und `clientId` als `sub`) an `req.serviceClient` angehängt und die Anfrage an den Controller weitergeleitet.
    *   Bei einem ungültigen oder abgelaufenen Token sendet die Middleware einen `401 Unauthorized`-Fehler.
*   **Caching:** Service Clients sollten den `access_token` cachen und erst bei Ablauf (siehe `expires_in`) einen neuen anfordern.


### Zusammenfassung der Verifizierungslogik für Authentifizierung

Diese Mechanismen stellen sicher, dass nur authentifizierte und autorisierte Entitäten auf die geschützten Ressourcen der API zugreifen können.


### Marketing Workflow (`routes/marketingWorkflowRoutes.js`, `controllers/marketingWorkflowController.js`, `marketingVideoRecorder.js`)

Dieser Workflow ist dafür verantwortlich, automatisiert Marketinginhalte (Texte, Bilder, Videos) zu generieren, zu verarbeiten und zu speichern.

#### 1. Initiierung des Workflows

*   **Endpoint:** `POST /api/v1/marketing-workflow/trigger`
*   **Controller-Funktion:** `marketingWorkflowController.triggerWorkflow`
*   **Zweck:** Startet den gesamten Prozess der Marketing-Content-Generierung.

#### 2. Authentifizierung

*   Der `/trigger` Endpoint ist geschützt.
*   **Mechanismus:** `authController.verifyServiceClient` Middleware.
*   **Anforderung:** Der aufrufende Dienst muss einen gültigen OAuth 2.0 Bearer Token (JWT) im `Authorization`-Header senden. Dieser Token wird über den `POST /api/v1/oauth/token` Endpoint mittels `client_credentials` Grant-Typ bezogen. (Siehe Abschnitt "Service-Client-Authentifizierung (Interne JWTs)" für Details zur Token-Beschaffung).

#### 3. Vordergrund-Verarbeitung (Synchron)

Diese Schritte werden direkt nach dem Aufruf von `/trigger` ausgeführt, bevor eine Antwort an den Client gesendet wird. Verantwortlich ist die `triggerWorkflow` Funktion im `marketingWorkflowController.js`.

*   **Schritt 3.1: Marketing-Story generieren**
    *   Ruft `gptController.generateMarketingStory` auf.
    *   Erzeugt:
        *   `hook`: Ein Aufhänger/Teaser.
        *   `textOne`: Text für das erste Panel/die erste Szene.
        *   `textTwo`: Text für das zweite Panel/die zweite Szene.
        *   `socialMediaTextMitLink`: Social-Media-Post-Text mit einem Platzhalter `{recipeLinkPlaceholder}`.
        *   `socialMediaTextOhneLink`: Social-Media-Post-Text ohne Link.
*   **Schritt 3.2: Zufälliges Rezept abrufen**
    *   Ruft die interne Funktion `_internal_fetchRandomFreeMenuData` (aus `menuController.js`) auf, um ein zufälliges, kostenloses Rezept aus der Datenbank zu laden.
*   **Schritt 3.3: Social-Media-Text finalisieren**
    *   Ersetzt den `{recipeLinkPlaceholder}` im `socialMediaTextMitLink` durch den tatsächlichen Link zum ausgewählten Rezept (z.B. `https://www.ordyapp.com/meinerezepte/menu/<recipeId>`).
*   **Schritt 3.4: Initialen Datenbankeintrag erstellen**
    *   Ein neuer Eintrag wird in der `marketingcontents` MongoDB Collection erstellt (`MarketingContent.create`).
    *   Dieser Eintrag enthält:
        *   Generierungsdatum, Hook, TextOne, TextTwo, die finalisierten Social-Media-Texte.
        *   Details zum Rezept (ID, Name, Bild-URL, Personenanzahl, Kochzeit).
        *   `imgOneUrl`, `imgTwoUrl`, `imgThreeUrl`, `imgFourUrl`, und `videoS3Url` werden initial auf `null` gesetzt.
    *   Die `_id` dieses neuen Eintrags wird als `contentId` für die weiteren Schritte verwendet.
*   **Schritt 3.5: HTTP-Antwort an den Client**
    *   Eine `200 OK` HTTP-Antwort wird an den aufrufenden Client gesendet.
    *   Die Antwort enthält eine Erfolgsmeldung und die `marketingContentId`.
    *   Dies signalisiert, dass der initiale Teil des Workflows erfolgreich war und die ressourcenintensive Bild- und Videoverarbeitung im Hintergrund gestartet wurde.

#### 4. Hintergrund-Verarbeitung (Asynchron)

Unmittelbar nach dem Senden der HTTP-Antwort wird die Funktion `processImagesAndVideoInBackground` (ebenfalls in `marketingWorkflowController.js`) über `setImmediate` aufgerufen. Dies stellt sicher, dass die Antwort an den Client nicht blockiert wird.

*   **Parameter:** Die Funktion erhält `contentId`, `textOne` und `hook` aus dem Vordergrundprozess.
*   **Schritt 4.1: Bilder generieren**
    *   Ruft `gptController.generateImagesFromPrompt` auf.
    *   Generiert basierend auf `hook` und `textOne` vier Bilder.
*   **Schritt 4.2: Bilder auf S3 hochladen**
    *   Die generierten Bild-Buffer werden mittels `uploadImageData` (aus `utils/awsStorage.js`) auf AWS S3 hochgeladen.
    *   Dateinamen enthalten die `contentId` zur Eindeutigkeit.
*   **Schritt 4.3: Datenbank-Update (Bilder)**
    *   Der `MarketingContent`-Eintrag (identifiziert durch `contentId`) wird mit den S3-URLs der hochgeladenen Bilder (`imgOneUrl` bis `imgFourUrl`) aktualisiert.
*   **Schritt 4.4: Marketing-Video aufnehmen**
    *   **URL-Konstruktion:** Die URL für die Videoaufnahme wird dynamisch basierend auf der `NODE_ENV` Umgebungsvariable und der `contentId` zusammengesetzt:
        *   Produktion: `https://ordy.ch/marketing/video1?contentId=<contentId>`
        *   Preview: `https://test.ordy.ch/marketing/video1?contentId=<contentId>`
        *   Development: `http://localhost:5173/marketing/video1?contentId=<contentId>`
    *   **Aufruf:** Die Funktion `recordMarketingVideo` aus `marketingVideoRecorder.js` wird mit dieser `videoPageUrl` aufgerufen.
    *   **Details zu `recordMarketingVideo`:**
        1.  Startet einen headless Chromium-Browser (Playwright).
        2.  **Phase 1 (Initialisierung):**
            *   Navigiert zur `videoPageUrl`.
            *   Wartet 30 Sekunden, damit die Seite (inkl. dynamisch geladenem Content basierend auf `contentId`) vollständig laden kann.
        3.  **Phase 2 (Reload):**
            *   Führt einen "Soft Reload" der Seite durch (Cache bleibt erhalten).
            *   Wartet 5 Sekunden.
        4.  **Phase 3 (Aufnahme):**
            *   Erstellt einen neuen Browser-Kontext mit aktivierter Videoaufnahme.
            *   Das Aufnahmeverzeichnis (`recordVideo.dir`) ist explizit auf das temporäre Verzeichnis des Betriebssystems (`os.tmpdir()`) gesetzt.
            *   Navigiert erneut zur `videoPageUrl`.
            *   Wartet 5 Sekunden.
            *   Nimmt für die Dauer von `RECORDING_DURATION_MS` (aktuell 12300 ms) auf.
            *   Playwright speichert das Video als `.webm`-Datei im temporären Verzeichnis.
        5.  **Konvertierung:**
            *   Konvertiert die `.webm`-Datei mittels `fluent-ffmpeg` (unter Verwendung von `ffmpeg-static`) in eine `.mp4`-Datei.
            *   Die Konvertierung startet mit einem Offset von 5 Sekunden (`.setStartTime('5')`), um eventuelle Ladeanimationen am Anfang zu überspringen.
            *   Die `.mp4`-Datei wird ebenfalls im temporären Verzeichnis des Betriebssystems gespeichert.
        6.  **S3-Upload (Video):**
            *   Der Buffer der `.mp4`-Datei wird mittels `uploadImageData` auf S3 hochgeladen (Bucket: `marketing-videos`).
        7.  **Aufräumen:**
            *   Die temporären lokalen `.webm`- und `.mp4`-Dateien werden gelöscht.
        8.  **Rückgabe:** Die S3-URL des hochgeladenen `.mp4`-Videos wird zurückgegeben.
*   **Schritt 4.5: Datenbank-Update (Video)**
    *   Wenn die Videoaufnahme und der Upload erfolgreich waren, wird der `MarketingContent`-Eintrag mit der `videoS3Url` aktualisiert.

#### 5. Wichtige Komponenten und Dateien

*   **Controller:**
    *   `controllers/marketingWorkflowController.js`: Hauptorchestrierung des Workflows.
    *   `controllers/gptController.js`: Schnittstelle zu OpenAI für Text- und Bildgenerierung.
    *   `controllers/authController.js`: Enthält die `verifyServiceClient` Middleware.
    *   `controllers/oauthController.js`: Stellt den `/oauth/token` Endpoint bereit.
*   **Videoaufnahme:**
    *   `marketingVideoRecorder.js`: Logik für Playwright-Aufnahme und FFmpeg-Konvertierung.
*   **Modelle:**
    *   `models/marketingContentModel.js`: MongoDB Schema für die Marketing-Daten.
    *   `models/serviceClientModel.js`: MongoDB Schema für OAuth Client Credentials.
*   **Routen:**
    *   `routes/marketingWorkflowRoutes.js`: Definiert den `/trigger` Endpoint.
    *   `routes/oauthRoutes.js`: Definiert den `/token` Endpoint.
*   **Utilities:**
    *   `utils/awsStorage.js`: S3 Upload-Funktionen.
    *   `utils/helper.js`: Hilfsfunktionen (z.B. `devConsole`).
*   **Frontend-Seite:**
    *   Die Seite, die unter der `TARGET_VIDEO_PAGE_BASE_URL` (z.B. `http://localhost:5173/marketing/video1`) erreichbar ist. Diese Seite muss den `contentId` Query-Parameter auslesen und den entsprechenden Marketing-Content vom Backend (`/api/v1/marketing-content`) laden und darstellen, damit er aufgenommen werden kann.
*   **Umgebungsvariablen (relevant für diesen Workflow):**
    *   `NODE_ENV`: Beeinflusst `TARGET_VIDEO_PAGE_BASE_URL`.
    *   `OPENAI_API_KEY`: Für GPT-Funktionen.
    *   AWS-Credentials (implizit durch `awsStorage.js` genutzt).
    *   `JWT_SECRET`: Zum Signieren und Verifizieren der OAuth Service-Client-Tokens.

#### 6. Fehlerbehandlung

*   **Vordergrund:** Fehler, die während der Schritte 3.1 bis 3.4 auftreten, werden abgefangen. Eine entsprechende Fehlermeldung wird an den Client gesendet (oft als `AppError` mit Status 500 oder 400).
*   **Hintergrund:** Fehler in `processImagesAndVideoInBackground` werden geloggt.
    *   Bei nicht-kritischen Fehlern (z.B. Fehlschlag beim DB-Update der Bild-URLs) versucht der Prozess oft, mit den nächsten Schritten fortzufahren.
    *   Schwerwiegende Fehler während der Hintergrundverarbeitung führen zum Abbruch des Hintergrundtasks für die jeweilige `contentId` und werden detailliert geloggt.

### Menüs (`routes/menuRoutes.js`)

*   **Endpunkt:** `/api/v1/menus/...`
    *   ...
*   ...

### Einkaufslisten (`routes/shoppingListRoutes.js`, `routes/shoppingListItemRoutes.js`)

*   **Endpunkt:** `/api/v1/shoppinglists/...`
    *   ...
*   ...

### Küchentische (`routes/kitchentableRoutes.js`)

*   **Endpunkt:** `/api/v1/kitchentables/...`
    *   ...
*   ...

### Wochenpläne (`routes/weekplanRoutes.js`)

*   **Endpunkt:** `/api/v1/weekplans/...`
    *   ...
*   ...

### Lebensmittel (`routes/grocerylistRoutes.js`)

*   **Endpunkt:** `/api/v1/grocerylists/...`
    *   ...
*   ...

### GPT (`routes/gptRoutes.js`)

*   **Endpunkt:** `/api/v1/gpt/...`
    *   ...
*   ...

### [Weitere Module hier einfügen...]

## Datenmodelle (MongoDB/Mongoose - `models/`)

*   **User (`userModel.js`):**
    *   `extId: { type: String, required: true, unique: true }` // Stytch User ID
    *   `extAuthService: { type: String }` // z.B. 'Google', 'Stytch'
    *   `firstName: { type: String }`
    *   `lastName: { type: String }`
    *   `email: { type: String, unique: true, sparse: true }` // Eindeutig, aber nicht zwingend erforderlich (falls Stytch keine liefert)
    *   `img: { type: String }` // URL zum Profilbild
    *   `role: { type: String, enum: ['user', 'admin'], default: 'user' }`
    *   `createdAt: { type: Date, default: Date.now }`
    *   `defaultKitchentable: { type: mongoose.Schema.ObjectId, ref: 'Kitchentable' }`
    *   `explanation_trail: { type: Number, default: 0 }`
    *   `externalAccess: { type: Boolean }`
    *   `bookedAbo: { ... }` // Details zum Abo (Stripe IDs etc.)
    *   `bookedAboUsage: { ... }` // Verbrauchsdaten des Abos
    *   ... (Weitere spezifische Felder wie `gdpr`, `weekplanmode`, `install_apple_app` etc.)
*   **ServiceClient (`serviceClientModel.js`):**
    *   ...
*   **ShoppingList (`shoppingListModel.js`):**
    *   ...
*   **ShoppingListItem (`shoppingListItemModel.js`):**
    *   ...
*   **Menu (`menuModel.js`):**
    *   ...
*   **Weekplan (`weekplanModel.js`):**
    *   ...
*   **KitchenTable (`kitchenTableModel.js`):**
    *   ...
*   **GroceryList (`grocerylistModel.js`):**
    *   ...
*   **[Weitere Models hier einfügen...]**

## Setup & Deployment

*   **Lokales Setup:**
    1.  Klone das Repository.
    2.  Installiere Abhängigkeiten: `npm install`
    3.  Erstelle eine `config.env`-Datei im Stammverzeichnis (basierend auf `config.env.example` falls vorhanden, ansonsten notwendige Variablen wie `DATABASE`, `DATABASE_PASSWORD`, `JWT_SECRET` (obwohl Stytch verwendet wird, könnte es noch für anderes genutzt werden?), `STYTCH_PID_DEV`, `STYTCH_PASSWORD_DEV`, `STYTCH_PID_PROD`, `STYTCH_PASSWORD_PROD`, `OPENAI_API_KEY` etc. definieren).
    4.  Starte den Development-Server: `npm run dev`
*   **Deployment:** [Informationen zum Deployment-Prozess, z.B. Vercel, AWS, Heroku? Siehe `vercel.json`] - *Vercel wird verwendet.* 