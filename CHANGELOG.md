# Changelog

Alle wichtigen Änderungen an diesem Projekt werden in dieser Datei dokumentiert.

Das Format basiert auf [Keep a Changelog](https://keepachangelog.com/de/1.0.0/).

## [2.4.3] - 2025-04-17

### ✨ Neue Features

- **Offline-Synchronisation für Einkaufszettel:** Änderungen am Einkaufszettel (aktuell nur "Artikel gekauft" umschalten), die offline getätigt werden, werden nun lokal gespeichert und automatisch mit dem Server synchronisiert, sobald die Verbindung wiederhergestellt ist. Konflikte werden über "Last Write Wins" (basierend auf Zeitstempeln) aufgelöst. (Backend: Neuer Endpunkt `POST /api/shopping-lists/:listId/sync-offline-changes`)
- **Einkaufszettel abschließen:** Wenn das letzte Item auf einem Einkaufszettel als "gekauft" markiert wird, signalisiert das <PERSON>end dies nun dem Frontend (`promptFinishList: true` in der Antwort). Das Frontend kann daraufhin einen Dialog anzeigen, um den Zettel zu beenden. Bei Bestätigung wird der Zettel über den neuen Endpunkt `PUT /api/shopping-lists/:listId/finish` als inaktiv markiert.
- **`isZettel`-Flag für Menüs:** Der Endpunkt `GET /api/v1/menu/complete/allbyuserid/...` gibt jetzt für jedes Menü ein `isZettel`-Flag zurück, das anzeigt, ob dieses Menü bereits auf dem aktiven Einkaufszettel des zugehörigen Küchentischs vorhanden ist.

### 🚀 Verbesserungen

- **WebSocket-Kommunikation (Einkaufszettel):** Die Zuverlässigkeit der Echtzeit-Updates für den Einkaufszettel (`zettel_updated`-Event) wurde verbessert. Probleme, die zu fehlenden oder ungültigen Nachrichten führten, wurden behoben. Sichergestellt, dass das `data`-Feld in der Nachricht immer vorhanden ist.
- **Filterung für Menüliste (`getAllMenusByUserid`):**
  - Der `scope=kitchentable`-Filter zeigt nun korrekt alle Menüs an, die zu Mitgliedern des Küchentischs gehören (nicht nur die des anfragenden Benutzers).
  - Die Paginierung behandelt `page=0` jetzt als erste Seite.
  - Robustere Verarbeitung der Query-Parameter (`cookingTime[lte]`, `sort`, `searchstring`).
  - Verbessertes Logging zur einfacheren Fehlersuche bei Filterproblemen.

### 🐛 Bugfixes

- **Authentifizierung (`/auth/userbyid`):** Mehrere Fehler behoben, die zu einem `500 Internal Server Error` führten, wenn Benutzerdaten nach dem Login/Reload abgerufen wurden. Die JWT-Verifizierung (`authController.verify`) wurde korrigiert (Backend-JWT statt Stytch-Token) und die Datenübergabe an die nachfolgenden Controller (`getUserAfterReload`, `sendanswer`) wurde stabilisiert. Das `populate`-Problem mit `kitchentable` wurde durch gezieltes Laden der `_id` für `defaultKitchentable` umgangen.
- **Menü-Query (`getAllMenusByUserid`):** Der `StrictPopulateError` aufgrund des fälschlicherweise angeforderten `owner`-Feldes wurde behoben. 