/**
 * Intelligent Categorizer - AI-powered ingredient categorization
 * Kombiniert lokale Pattern-Matching mit AI-Kategorisierung
 */

const helper = require('./helper');
const Unit = require('../models/unitModel');
const Grocery = require('../models/groceryModel');

/**
 * <PERSON><PERSON>-Farbsystem (vollständig)
 */
const ORDY_CATEGORIES = {
    'Gemüse & Früchte': {
        color: 'green',
        keywords: [
            'apfel', 'birne', 'banane', 'orange', 'zitrone', 'limette', 'grapefruit',
            'tomate', 'gurke', 'salat', 'karotte', 'möhre', 'zwiebel', 'knoblauch',
            'paprika', 'broccoli', 'blumenkohl', 'spinat', 'kohl', 'pilz', 'champignon',
            'avocado', 'mango', 'ananas', 'erdbeere', 'himbeere', 'blaubeere',
            'kartoffel', 's<PERSON><PERSON><PERSON><PERSON>ffel', 'radieschen', 'rettich', 'lauch', 'porree',
            'sellerie', 'petersilie', 'basilikum', 'oregano', 'thymian', 'rosmarin'
        ]
    },
    'Brotwaren & Backwaren': {
        color: 'yellow',
        keywords: [
            'brot', 'brötchen', 'gipfeli', 'kuchen', 'sandwich', 'toast', 'croissant',
            'bagel', 'wecken', 'zopf', 'mehl', 'hefe', 'backpulver', 'vanillezucker',
            'puderzucker', 'muffin', 'keks', 'zwieback', 'knäckebrot', 'fladenbrot',
            'pita', 'tortilla', 'wrap', 'pizza', 'teig', 'blätterteig', 'hefeteig'
        ]
    },
    'Milchprodukte & Molkereiprodukte': {
        color: 'blue',
        keywords: [
            'milch', 'joghurt', 'butter', 'rahm', 'sahne', 'käse', 'quark', 'schmand',
            'frischkäse', 'mozzarella', 'parmesan', 'gouda', 'emmentaler', 'brie',
            'camembert', 'feta', 'ricotta', 'mascarpone', 'crème fraîche',
            'buttermilch', 'kefir', 'skyr', 'cottage cheese', 'schlagsahne'
        ]
    },
    'Fleisch, Wurst & Fisch': {
        color: 'red',
        keywords: [
            'fleisch', 'wurst', 'fisch', 'hähnchen', 'huhn', 'rind', 'schwein',
            'lamm', 'kalb', 'pute', 'ente', 'gans', 'lachs', 'thunfisch', 'forelle',
            'kabeljau', 'seelachs', 'garnele', 'shrimp', 'muschel', 'tintenfisch',
            'hackfleisch', 'schnitzel', 'steak', 'bratwurst', 'leberwurst', 'salami',
            'schinken', 'speck', 'bacon', 'würstchen', 'mortadella', 'prosciutto'
        ]
    },
    'Tiefkühlprodukte': {
        color: 'cyan',
        keywords: [
            'tiefkühl', 'gefror', 'tk', 'eis', 'eiscreme', 'sorbet', 'frozen',
            'tiefgefroren', 'eisbeutel', 'kühlware', 'gefriergut'
        ]
    },
    'Grundnahrungsmittel': {
        color: 'brown',
        keywords: [
            'reis', 'nudeln', 'pasta', 'spaghetti', 'penne', 'fusilli', 'tagliatelle',
            'öl', 'olivenöl', 'sonnenblumenöl', 'essig', 'balsamico', 'salz', 'pfeffer',
            'zucker', 'honig', 'sirup', 'ahornsirup', 'senf', 'ketchup', 'mayonnaise',
            'sojasauce', 'worcestersauce', 'tabasco', 'curry', 'paprika', 'zimt',
            'muskat', 'ingwer', 'kurkuma', 'koriander', 'kreuzkümmel', 'chili'
        ]
    },
    'Frühstück & Cerealien': {
        color: 'orange',
        keywords: [
            'müsli', 'cornflakes', 'haferflocken', 'granola', 'cereals', 'porridge',
            'marmelade', 'konfitüre', 'nutella', 'honig', 'ahornsirup', 'müsliriegel',
            'croissant', 'pain au chocolat', 'danish', 'gebäck'
        ]
    },
    'Süsswaren & Snacks': {
        color: 'pink',
        keywords: [
            'schokolade', 'bonbon', 'gummibärchen', 'chips', 'kekse', 'plätzchen',
            'nüsse', 'mandeln', 'walnüsse', 'haselnüsse', 'erdnüsse', 'pistazien',
            'rosinen', 'datteln', 'feigen', 'trockenfrüchte', 'studentenfutter',
            'popcorn', 'crackers', 'salzstangen', 'brezel', 'energy drink'
        ]
    },
    'Getränke': {
        color: 'blue-light',
        keywords: [
            'wasser', 'mineralwasser', 'sprudelwasser', 'saft', 'orangensaft', 'apfelsaft',
            'tee', 'kaffee', 'espresso', 'cappuccino', 'latte', 'bier', 'wein', 'sekt',
            'champagner', 'whisky', 'vodka', 'gin', 'rum', 'likör', 'cola', 'limonade',
            'energy drink', 'smoothie', 'milchshake', 'kakao', 'hot chocolate'
        ]
    },
    'Non-Food & Haushaltsartikel': {
        color: 'gray',
        keywords: [
            'spülmittel', 'waschmittel', 'toilettenpapier', 'küchenrolle', 'servietten',
            'müllbeutel', 'alufolie', 'frischhaltefolie', 'backpapier', 'kerzen',
            'batterien', 'glühbirne', 'reiniger', 'schwamm', 'bürste', 'handschuhe'
        ]
    },
    'Sonstiges': {
        color: 'gray-light',
        keywords: []
    }
};

/**
 * Erweiterte lokale Kategorisierung mit Pattern-Matching
 * @param {string} ingredientName - Name der Zutat
 * @returns {Object} Kategorisierungs-Ergebnis
 */
function advancedLocalCategorization(ingredientName) {
    if (!ingredientName || typeof ingredientName !== 'string') {
        return {
            category: 'Sonstiges',
            confidence: 0,
            method: 'fallback'
        };
    }

    const name = ingredientName.toLowerCase().trim();
    
    // Entferne Mengenangaben und Einheiten für bessere Kategorisierung
    const cleanName = name
        .replace(/\d+\s*(g|kg|ml|l|stk|stück|pack|packung|dose|glas|flasche|bund|tüte)\s*/gi, '')
        .replace(/\d+\s*/g, '')
        .trim();

    let bestMatch = {
        category: 'Sonstiges',
        confidence: 0,
        method: 'pattern-matching'
    };

    // Durchsuche alle Kategorien nach Keyword-Matches
    for (const [categoryName, categoryData] of Object.entries(ORDY_CATEGORIES)) {
        if (categoryName === 'Sonstiges') continue;

        for (const keyword of categoryData.keywords) {
            // Exakte Übereinstimmung = höchste Konfidenz
            if (cleanName === keyword) {
                return {
                    category: categoryName,
                    confidence: 1.0,
                    method: 'exact-match',
                    matchedKeyword: keyword
                };
            }

            // Teilstring-Übereinstimmung
            if (cleanName.includes(keyword) || keyword.includes(cleanName)) {
                const confidence = Math.max(
                    keyword.length / cleanName.length,
                    cleanName.length / keyword.length
                ) * 0.8; // Max 0.8 für Teilübereinstimmungen

                if (confidence > bestMatch.confidence) {
                    bestMatch = {
                        category: categoryName,
                        confidence: confidence,
                        method: 'partial-match',
                        matchedKeyword: keyword
                    };
                }
            }
        }
    }

    return bestMatch;
}

/**
 * Intelligente Kategorisierung mit AI-Fallback
 * @param {string} ingredientName - Name der Zutat
 * @param {boolean} useAI - Ob AI verwendet werden soll
 * @returns {Promise<Object>} Kategorisierungs-Ergebnis
 */
async function intelligentCategorization(ingredientName, useAI = true) {
    helper.devConsole(`🤖 Intelligent categorization for: "${ingredientName}"`);

    // 1. Lokale Kategorisierung zuerst
    const localResult = advancedLocalCategorization(ingredientName);
    
    helper.devConsole(`📊 Local categorization result:`, {
        category: localResult.category,
        confidence: localResult.confidence,
        method: localResult.method
    });

    // 2. Wenn lokale Kategorisierung sehr sicher ist, verwende sie
    if (localResult.confidence >= 0.8) {
        helper.devConsole(`✅ High confidence local match, using: ${localResult.category}`);
        return localResult;
    }

    // 3. AI-Kategorisierung für unsichere Fälle (falls verfügbar)
    if (useAI && (localResult.confidence < 0.5 || localResult.category === 'Sonstiges')) {
        try {
            const aiResult = await aiCategorization(ingredientName);
            
            if (aiResult && aiResult.confidence > localResult.confidence) {
                helper.devConsole(`🤖 AI categorization better than local, using: ${aiResult.category}`);
                return aiResult;
            }
        } catch (aiError) {
            helper.devConsole(`⚠️ AI categorization failed: ${aiError.message}`);
        }
    }

    // 4. Fallback auf lokales Ergebnis
    helper.devConsole(`📋 Using local categorization result: ${localResult.category}`);
    return localResult;
}

/**
 * AI-basierte Kategorisierung (Placeholder für zukünftige AI-Integration)
 * @param {string} ingredientName - Name der Zutat
 * @returns {Promise<Object>} AI-Kategorisierungs-Ergebnis
 */
async function aiCategorization(ingredientName) {
    // TODO: Hier könnte eine echte AI-Integration implementiert werden
    // Für jetzt verwenden wir erweiterte Heuristiken
    
    helper.devConsole(`🤖 AI categorization for: "${ingredientName}"`);
    
    // Simuliere AI-Verhalten mit erweiterten Heuristiken
    const name = ingredientName.toLowerCase();
    
    // Spezielle AI-ähnliche Regeln für schwierige Fälle
    const aiRules = [
        {
            pattern: /(bio|organic|natur)/,
            modifier: (category) => category, // Behält Kategorie, aber markiert als bio
            confidence: 0.1
        },
        {
            pattern: /(frisch|fresh)/,
            modifier: (category) => category === 'Sonstiges' ? 'Gemüse & Früchte' : category,
            confidence: 0.3
        },
        {
            pattern: /(getrocknet|dried)/,
            modifier: (category) => category === 'Sonstiges' ? 'Grundnahrungsmittel' : category,
            confidence: 0.4
        }
    ];

    let aiCategory = 'Sonstiges';
    let aiConfidence = 0.2;

    for (const rule of aiRules) {
        if (rule.pattern.test(name)) {
            aiCategory = rule.modifier(aiCategory);
            aiConfidence = Math.max(aiConfidence, rule.confidence);
        }
    }

    return {
        category: aiCategory,
        confidence: aiConfidence,
        method: 'ai-heuristics'
    };
}

/**
 * Kategorisiert eine Zutat und erstellt/findet Grocery-Eintrag
 * @param {string} ingredientName - Name der Zutat
 * @param {boolean} useAI - Ob AI verwendet werden soll
 * @returns {Promise<Object>} Vollständiges Kategorisierungs-Ergebnis mit Grocery-ID
 */
async function categorizeAndCreateGrocery(ingredientName, useAI = true) {
    helper.devConsole(`🔧 Categorizing and creating grocery for: "${ingredientName}"`);

    // 1. Kategorisierung
    const categoryResult = await intelligentCategorization(ingredientName, useAI);

    // 2. Grocery finden oder erstellen
    let grocery = await Grocery.findOne({ name: ingredientName.trim() });
    
    if (!grocery) {
        grocery = await Grocery.create({ 
            name: ingredientName.trim(),
            category: categoryResult.category // Speichere Kategorie in Grocery
        });
        helper.devConsole(`🆕 Created new grocery: ${ingredientName} (${categoryResult.category})`);
    } else if (!grocery.category || grocery.category !== categoryResult.category) {
        // Update Kategorie falls sie sich geändert hat oder fehlte
        grocery.category = categoryResult.category;
        await grocery.save();
        helper.devConsole(`🔄 Updated grocery category: ${ingredientName} → ${categoryResult.category}`);
    }

    return {
        groceryId: grocery._id,
        grocery: grocery,
        category: categoryResult.category,
        confidence: categoryResult.confidence,
        method: categoryResult.method,
        matchedKeyword: categoryResult.matchedKeyword
    };
}

/**
 * Batch-Kategorisierung für mehrere Zutaten
 * @param {Array<string>} ingredientNames - Array von Zutat-Namen
 * @param {boolean} useAI - Ob AI verwendet werden soll
 * @returns {Promise<Array>} Array von Kategorisierungs-Ergebnissen
 */
async function batchCategorization(ingredientNames, useAI = true) {
    helper.devConsole(`📦 Batch categorization for ${ingredientNames.length} ingredients`);

    const results = [];
    
    for (const ingredientName of ingredientNames) {
        try {
            const result = await categorizeAndCreateGrocery(ingredientName, useAI);
            results.push(result);
        } catch (error) {
            helper.devConsole(`❌ Error categorizing "${ingredientName}": ${error.message}`);
            results.push({
                groceryId: null,
                category: 'Sonstiges',
                confidence: 0,
                method: 'error',
                error: error.message
            });
        }
    }

    helper.devConsole(`✅ Batch categorization completed: ${results.length} results`);
    return results;
}

module.exports = {
    ORDY_CATEGORIES,
    advancedLocalCategorization,
    intelligentCategorization,
    categorizeAndCreateGrocery,
    batchCategorization,
    aiCategorization
};
