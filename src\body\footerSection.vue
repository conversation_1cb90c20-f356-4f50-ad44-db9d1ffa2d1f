<template>
    <div
        class="md:block w-full h-52 bg-white rounded-tr-3xl rounded-tl-3xl"
        :class="{ 'hidden': isHidden }">
      <div class="w-8/12 mx-auto md:gap-10 md:grid-cols-4 md:grid">
        <!-- first column -->
        <div>
          <ul class="md:p-10 pt-10 p-3 text-sm leading-relaxed">
            <li class="font-YesevaOne text-base">Rezepte</li>
            <li><a href="/kochbuch">Die beliebtesten Rezepte</a></li>
            <li><a href="/wochenplan/planung">Rezepte erstellen lassen</a></li>
          </ul>
        </div>
        <!-- first column -->
  
        <!-- second column -->
        <div>
          <ul class="md:p-10 md:pt-10 p-3 text-sm leading-relaxed">
            <li class="font-YesevaOne text-base">Mein Account</li>
            <li><a href="/usersettings">Profil</a></li>
            <li><a href="/logout">Logout</a></li>
          </ul>
        </div>
        <!-- second column -->
  
        <!-- third column -->
        <div>
          <ul class="md:p-10 md:pt-10 p-3 text-sm leading-relaxed">
            <li class="font-YesevaOne text-base">Rechtliches</li>
            <li><router-link to="/legal/agb">AGB</router-link></li>
            <li><router-link to="/legal/impressum">Impressum</router-link></li>
            <li><router-link to="/legal/privacy-policy">Datenschutz</router-link></li>
            <li><router-link to="/legal/terms-of-service">Terms of Service</router-link></li>
          </ul>
        </div>
        <!-- third column -->
  
        <!-- fourth column -->
        <div>
          <ul class="md:p-10 md:pt-10 pb-10 p-3 text-sm leading-relaxed">
            <li class="font-YesevaOne text-base">Über uns</li>
            <li><a href="/kuechentisch">Community</a></li>
            <li><a target="_blank" href="mailto:<EMAIL>">Kontakt</a></li>
            <li class="mt-16"><p class="text-[0.4rem]">v. {{ appVersion }}</p></li>
          </ul>
        </div>
        <!-- fourth column -->
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue'
  
  const appVersion = import.meta.env.VITE_APP_VERSION;
  
  const props = defineProps({
    isHidden: Boolean
  })
  </script>