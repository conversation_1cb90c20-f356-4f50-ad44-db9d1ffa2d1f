<template>
  <!-- Column Builder-->
  <div class="flex md:flex-row flex-col min-h-screen">

    <!-- Middle Container -->
    <div class="md:w-3/4 md:pr-12 w-full">
      <!-- Head-->
      <div class="w-full flex flex-row mt-10">
        <h1 class="w-full first-letter:md:w-11/12  h-auto pb-3 leading-[3rem]">Ein Rezept schnell erstellen</h1>
        <div class="invisible w-0 md:visible md:w-1/12">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="w-full md:w-1/2">Lass uns zusammen ein Rezept erstellen, welches am besten zu dir passt.</p>
      <!-- Head-->

      <!-- Select Weekdays
      <div class="flex flex-wrap gap-5 w-full mt-6">
        <div v-for="dayelement in datasetDays" v-bind:key="dayelement.id" >
          <day-card :dayelement="dayelement" :index="index" />  
        </div>
      </div>
      Select Weekdays -->

      <!-- Create Menue -->
      <click-shadow-button 
        @click.prevent="createMenu" 
        :element="{'buttondescription': searchText, 'active': 'false', 'iconneeded': false}"
        :index="1"  
      />
      <!-- Create Menue -->

      <div class="grid gap-5 pr-5 pb-12 mt-12 w-full h-12 place-items-end">
        
      <button @click="tempweekplanStore.clearFoundMenus()" type="button" class="bg-white uppercase text-xs hover:bg-gradient-to-br focus:outline-none 
             shadow-lg shadow-purple dark:shadow-lg dark:shadow-purple font-medium rounded-lg 
             px-5 py-2.5 text-center me-2 mb-2 inline-flex items-center focus:ring-0">
          <img class="w-4 h-4 me-2 -ms-1" src="../../assets/icons/delete.png" />
          <span class="pl-2">Vorschläge löschen</span>
        </button>
      </div>
      

    <div v-if="tempweekplanStore.recieptCreationOnGoing" class="flex flex-nowrap lg:flex-wrap gap-5 pr-5 pb-12 mt-12 w-full h-auto">
      <div class="h-128 w-114 rounded-2xl bg-gradient-to-r from-ordypurple-200 via-ordypink-100 to-ordypurple-200 background-animate">
        <div class="h-20 float-right mt-6 mr-6 w-20 rounded-2xl bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 background-animate">
   
        </div>

        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate">
          
        </div>

        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate">
          
        </div>

        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate">
          
        </div>


      </div>
    </div>

      <!-- Res Value -->
      <div class="flex flex-wrap gap-5 pr-5 pb-12 mt-2 w-full h-auto">
        <div class=" mt-5 rounded-xl h-128 w-114" 
             :class="(tempweekplanStore.isLoading)?'opacity-50 pointer-events-none':''" 
             v-for="(menue, idx) in tempweekplanStore.tempweekplanmenu" 
             v-bind:key="menue.name || idx">
          <menu-card class="bg-sfgyellow-500" 
                     :menuPlanType="{ addToMenu: false, editMenu: false, none: false }" 
                     :weekplanParentData="weekplanParentData" 
                     :element="menue" 
                     :index="idx" />
        </div>
      </div>
      <!-- Res Value -->

      

    </div>


    <!-- Right Container -->
    <div class="md:w-1/4 w-full md:p-10 md:mt-12 pb-32">
      <span class="w-full font-bold font-OpenSans">Deine Präferenzen</span>
      <p>Uns ist es besonders wichtig zu wissen, für wie viele Personen Du planst. Alles andere ist zusätzliche Information.</p>

      <div class="mt-4 space-y-4">
          <div v-for="element in datasetPreferences" v-bind:key="element.boxid">
            <preference-card :element="element" />  
          </div>
      </div>

    </div>

    
  </div>
  
</template>
<script setup>
import { reactive, ref } from 'vue';
import axios from 'axios';
import menuCard from '../../components/menuCard.vue'
import preferenceCard from '../../components/preferenceCard.vue'
import dayCard from '../../components/dayCard.vue'
import useNotification from '../../../modules/notificationInformation';
import { useUserStore } from '@/store/userStore'
import { useTempWeekplanStore } from '@/store/weekplanStore'
import { useHelperStore } from '../../../utils/helper';
import clickShadowButton from '../../components/clickShadowButton.vue'

  const userStore = useUserStore();
  const tempweekplanStore = useTempWeekplanStore();
  const helper = useHelperStore()

  const weekplanParentData = {
    plannedSeats: null
  }

  const { setNotification } = useNotification();

  const searchText = ref("Rezepte suchen") 

  const datasetPreferences =  reactive([
          {
            "boxid": "1",
            "boxdescription": "Wie viele Personen seit ihr?",
            "boxname": "personen",
            "boxvalues": [
              { id: 1, item: '1 Person', selected: true },
              { id: 2, item: '2 Personen', selected: false },
              { id: 3, item: '3 Personen', selected: false },
              { id: 4, item: '4 Personen', selected: false },
              { id: 5, item: '5 Personen', selected: false },
              { id: 6, item: '6 Personen', selected: false }
            ],
          },
          {
            "boxid": "2",
            "boxdescription": "Wie viele Proteine möchtest du zu dir nehmen?",
            "boxname": "proteine",
            "boxvalues": [
              { id: 1, item: 'weniger als normal', selected: false },
              { id: 2, item: 'normal', selected: true },
              { id: 3, item: 'mehr als normal', selected: false },
              { id: 4, item: 'deutlich mehr als normal', selected: false }
            ],
          },
          {
            "boxid": "3",
            "boxdescription": "Hast du spezielle Wünsche zum neuen Menü?",
            "boxname": "praeferenz",
            "boxvalues": [
              { id: 1, item: 'egal', selected: true },
              { id: 2, item: 'low carb', selected: false },
              { id: 3, item: 'keto', selected: false },
              { id: 4, item: 'paleo', selected: false },
              { id: 5, item: 'low suger', selected: false }
            ],
          },
          {
            "boxid": "4",
            "boxdescription": "Gibt es einen Lebensstil, welchen du uns mitgeben möchtest?",
            "boxname": "liefestyle",
            "boxvalues": [
              { id: 1, item: 'egal', selected: true },
              { id: 2, item: 'vegetarisch', selected: false },
              { id: 3, item: 'vegan', selected: false },
              { id: 4, item: 'mixed (vegetarisch, einmal Fleisch)', selected: false },
              { id: 5, item: 'nur Fleisch', selected: false }
            ],
          },
          {
            "boxid": "5",
            "boxdescription": "Gibt es zuberücksichtigende Allergien?",
            "boxname": "allergie",
            "boxvalues": [
              { id: 1, item: 'keine', selected: true },
              { id: 2, item: 'ziebeln', selected: false },
              { id: 3, item: 'knoblauch', selected: false },
              { id: 4, item: 'nüsse', selected: false },
              { id: 5, item: 'huhn', selected: false }
            ],
          },
        ])

  const createMenu = async () => {
    helper.devConsole("createMenu called (SearchMenuView - Local State)");
      
    let selectedPersonenValue = null;
    const anzahlPersonenPref = datasetPreferences.find(p => p.boxid === "1");
    if (anzahlPersonenPref && anzahlPersonenPref.boxvalues) {
        const selectedOption = anzahlPersonenPref.boxvalues.find(option => option.selected === true);
        if (selectedOption && selectedOption.item) {
             const match = selectedOption.item.match(/\d+/);
             if (match) {
                 selectedPersonenValue = parseInt(match[0], 10);
             }
        }
    }

    if (selectedPersonenValue === null || selectedPersonenValue <= 0) {
        setNotification('Bitte gib eine gültige Anzahl Personen an.', 'alert');
        return;
    }

    let preferencesString = "";
    datasetPreferences.forEach(pref => {
        if (pref.boxvalues) {
            const selectedOption = pref.boxvalues.find(option => option.selected === true);
            if (selectedOption && selectedOption.item && selectedOption.item !== 'egal' && selectedOption.item !== 'keine') { 
                 preferencesString += `${pref.boxname}: ${selectedOption.item}, `;
            }
            else if (pref.boxid === "1" && selectedOption && selectedOption.item) {
                 preferencesString += `${pref.boxname}: ${selectedOption.item}, `;
            }
        }
    });
    preferencesString = preferencesString.replace(/,\s*$/, '');

    helper.devConsole("Preferences String (SearchMenuView - Local State):", preferencesString);
    console.log("Payload für /creator/menulist:", { preferences: preferencesString, user_id: userStore.user.id });

    await tempweekplanStore.createWeekplanFromGenerator(preferencesString);
  };
  
</script>
<style scoped>

.background-animate {
    background-size: 400%;

    -webkit-animation: AnimationName 3s ease infinite;
    -moz-animation: AnimationName 3s ease infinite;
    animation: AnimationName 3s ease infinite;
  }

  @keyframes AnimationName {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

.shake {
  animation: shake 3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>