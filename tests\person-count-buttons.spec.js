const { test, expect } = require('@playwright/test');

test.describe('Person Count Buttons Test', () => {
  test('should change person count when clicking + and - buttons', async ({ page }) => {
    console.log('🚀 Starting person count buttons test...');

    // Listen for console logs and errors
    page.on('console', msg => {
      console.log(`🖥️ Browser ${msg.type()}: ${msg.text()}`);
    });

    page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });

    page.on('requestfailed', request => {
      console.log(`📡 Request failed: ${request.url()} - ${request.failure()?.errorText}`);
    });

    // Set authentication cookies manually (copy from your browser)
    console.log('🔑 Setting authentication cookies...');

    // Add session cookies - you need to provide these from your browser
    await page.context().addCookies([
      {
        name: 'sessionToken',
        value: 'your-session-token-here', // Replace with actual token
        domain: 'localhost',
        path: '/'
      }
    ]);

    // Set localStorage items for authentication
    await page.goto('http://localhost:5174');
    await page.evaluate(() => {
      // Set authentication data in localStorage
      localStorage.setItem('sessionToken', 'your-session-token-here'); // Replace with actual token
      localStorage.setItem('refreshToken', 'your-refresh-token-here'); // Replace with actual token
      localStorage.setItem('userId', 'your-user-id-here'); // Replace with actual user ID
    });

    console.log('📍 Navigated to app on port 5174 with auth cookies');

    // Try to navigate to a specific recipe
    try {
      await page.goto('http://localhost:5174/kochbuch');
      console.log('📍 Navigated to kochbuch');
      await page.waitForTimeout(3000);

      // Look for any recipe link or card
      const recipeLinks = await page.locator('a[href*="/kochbuch/"]').all();
      if (recipeLinks.length > 0) {
        console.log(`🔍 Found ${recipeLinks.length} recipe links`);
        await recipeLinks[0].click();
        console.log('📍 Clicked on first recipe');
        await page.waitForTimeout(3000);
      } else {
        // Try direct navigation to a known recipe
        await page.goto('http://localhost:5174/kochbuch/683b5cdf192a97fc76bb4d89');
        console.log('📍 Direct navigation to recipe');
        await page.waitForTimeout(3000);
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error.message);
    }

    // Look for person count display and buttons
    console.log('🔍 Looking for person count elements...');

    // Try different selectors for person count
    const personCountSelectors = [
      '[data-testid="person-count"]',
      '.person-count',
      'text=/\\d+\\s*Person/',
      'text=/\\d+\\s*Personen/',
      '[class*="person"]',
      'button[class*="person"]'
    ];

    let personCountElement = null;
    let plusButton = null;
    let minusButton = null;

    for (const selector of personCountSelectors) {
      try {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          console.log(`✅ Found elements with selector: ${selector} (${elements.length} elements)`);
          personCountElement = elements[0];
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    // Look for + and - buttons
    const buttonSelectors = [
      'button:has-text("+")',
      'button:has-text("-")',
      '[data-testid="plus-button"]',
      '[data-testid="minus-button"]',
      '.plus-button',
      '.minus-button',
      'button[class*="plus"]',
      'button[class*="minus"]'
    ];

    for (const selector of buttonSelectors) {
      try {
        const buttons = await page.locator(selector).all();
        if (buttons.length > 0) {
          console.log(`✅ Found buttons with selector: ${selector} (${buttons.length} buttons)`);
          if (selector.includes('+') || selector.includes('plus')) {
            plusButton = buttons[0];
          } else if (selector.includes('-') || selector.includes('minus')) {
            minusButton = buttons[0];
          }
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    // Log current page content for debugging
    console.log('📄 Current page title:', await page.title());
    console.log('📄 Current URL:', page.url());

    // Check if page has loaded properly
    const bodyContent = await page.locator('body').textContent();
    console.log('📄 Body content length:', bodyContent.length);
    console.log('📄 Body content preview:', bodyContent.substring(0, 200));

    // Check for Vue app mounting
    const vueApp = await page.locator('#app').count();
    console.log('📄 Vue app element found:', vueApp > 0);

    // Check for any error messages
    const errorElements = await page.locator('text=/error|fehler|problem/i').all();
    console.log(`📄 Error elements found: ${errorElements.length}`);

    // Check for loading states
    const loadingElements = await page.locator('text=/loading|laden/i').all();
    console.log(`📄 Loading elements found: ${loadingElements.length}`);

    // Take a screenshot for debugging
    await page.screenshot({ path: '../ordy-at-vite5/test-results/person-count-debug.png', fullPage: true });
    console.log('📸 Screenshot saved');

    // Get all button elements on the page
    const allButtons = await page.locator('button').all();
    console.log(`🔍 Found ${allButtons.length} total buttons on page`);

    // Get all clickable elements
    const allClickable = await page.locator('a, button, [onclick], [role="button"]').all();
    console.log(`🔍 Found ${allClickable.length} total clickable elements on page`);

    for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
      try {
        const buttonText = await allButtons[i].textContent();
        const buttonClass = await allButtons[i].getAttribute('class');
        console.log(`Button ${i}: "${buttonText}" (class: ${buttonClass})`);
      } catch (e) {
        console.log(`Button ${i}: Error reading button`);
      }
    }

    // Test clicking any + or - buttons found
    if (plusButton) {
      console.log('🔄 Testing plus button...');

      // Listen for network requests
      page.on('request', request => {
        if (request.url().includes('createifnotexists')) {
          console.log('📡 Network request:', request.method(), request.url());
        }
      });

      page.on('response', response => {
        if (response.url().includes('createifnotexists')) {
          console.log('📡 Network response:', response.status(), response.url());
        }
      });

      // Listen for console logs
      page.on('console', msg => {
        if (msg.text().includes('Person count change') || msg.text().includes('countPerson')) {
          console.log('🖥️ Browser console:', msg.text());
        }
      });

      await plusButton.click();
      console.log('✅ Clicked plus button');

      // Wait for any network requests or UI updates
      await page.waitForTimeout(5000);

    } else {
      console.log('❌ No plus button found');
    }

    if (minusButton) {
      console.log('🔄 Testing minus button...');
      await minusButton.click();
      console.log('✅ Clicked minus button');
      await page.waitForTimeout(5000);
    } else {
      console.log('❌ No minus button found');
    }

    // Final screenshot
    await page.screenshot({ path: '../ordy-at-vite5/test-results/person-count-final.png', fullPage: true });
    console.log('📸 Final screenshot saved');

    console.log('✅ Test completed');
  });
});
