/**
 * Backend Legacy Migration System
 * Migriert alte Rezept-Formate zu aktueller Datenstruktur
 */

const helper = require('./helper');
const Unit = require('../models/unitModel');
const Grocery = require('../models/groceryModel');

/**
 * Erkennt Legacy-Rezepte anhand verschiedener Indikatoren
 * @param {Object} menuChild - MenuChild-Objekt
 * @returns {Object} Migration-Info
 */
function detectLegacyFormat(menuChild) {
    const migrationInfo = {
        isLegacy: false,
        legacyType: null,
        issues: []
    };

    if (!menuChild) {
        return migrationInfo;
    }

    // 1. Prüfe auf sehr alte "zutaten" Struktur
    if (menuChild.zutaten && Array.isArray(menuChild.zutaten)) {
        migrationInfo.isLegacy = true;
        migrationInfo.legacyType = 'very_old_zutaten';
        migrationInfo.issues.push('Uses old "zutaten" field instead of "ingredients"');
        return migrationInfo;
    }

    // 2. Prüfe auf fehlende StableIDs
    if (menuChild.ingredients && Array.isArray(menuChild.ingredients)) {
        const hasStableIds = menuChild.ingredients.every(ing => 
            ing.stableId && typeof ing.stableId === 'number'
        );
        
        if (!hasStableIds) {
            migrationInfo.isLegacy = true;
            migrationInfo.legacyType = 'missing_stable_ids';
            migrationInfo.issues.push('Missing or invalid stableId fields');
        }

        // 3. Prüfe auf String-Datentypen in Ingredients
        const hasStringAmounts = menuChild.ingredients.some(ing => 
            typeof ing.amount === 'string'
        );
        
        if (hasStringAmounts) {
            migrationInfo.isLegacy = true;
            migrationInfo.legacyType = 'string_amounts';
            migrationInfo.issues.push('Amount fields are strings instead of numbers');
        }

        // 4. Prüfe auf fehlende maxUsedStableId
        if (!menuChild.maxUsedStableId || menuChild.maxUsedStableId === 0) {
            migrationInfo.isLegacy = true;
            migrationInfo.legacyType = 'missing_max_stable_id';
            migrationInfo.issues.push('Missing maxUsedStableId field');
        }
    }

    // 5. Prüfe auf alte "zubereitung" Struktur
    if (menuChild.zubereitung && !menuChild.preperation) {
        migrationInfo.isLegacy = true;
        migrationInfo.legacyType = 'old_preparation';
        migrationInfo.issues.push('Uses old "zubereitung" field instead of "preperation"');
    }

    // 6. Prüfe auf String-Nutritions
    if (menuChild.nutritions && Array.isArray(menuChild.nutritions)) {
        const hasStringNutritions = menuChild.nutritions.some(nut => 
            typeof nut.amount === 'string'
        );
        
        if (hasStringNutritions) {
            migrationInfo.isLegacy = true;
            migrationInfo.legacyType = 'string_nutritions';
            migrationInfo.issues.push('Nutrition amounts are strings instead of numbers');
        }
    }

    return migrationInfo;
}

/**
 * Migriert ein Legacy-MenuChild zu aktueller Struktur
 * @param {Object} menuChild - Legacy MenuChild
 * @returns {Object} Migriertes MenuChild
 */
async function migrateLegacyMenuChild(menuChild) {
    helper.devConsole('🔄 Starting backend legacy migration...');
    
    const migrationInfo = detectLegacyFormat(menuChild);
    
    if (!migrationInfo.isLegacy) {
        helper.devConsole('✅ No migration needed');
        return menuChild;
    }

    helper.devConsole(`🔧 Migrating legacy format: ${migrationInfo.legacyType}`);
    helper.devConsole(`📋 Issues found: ${migrationInfo.issues.join(', ')}`);

    const migratedMenuChild = JSON.parse(JSON.stringify(menuChild)); // Deep clone

    // 1. Migriere sehr alte "zutaten" Struktur
    if (migrationInfo.legacyType === 'very_old_zutaten') {
        migratedMenuChild.ingredients = await migrateOldZutatenStructure(menuChild.zutaten);
        delete migratedMenuChild.zutaten;
        helper.devConsole('✅ Migrated old "zutaten" to "ingredients"');
    }

    // 2. Stelle sicher, dass ingredients existiert
    if (!migratedMenuChild.ingredients) {
        migratedMenuChild.ingredients = [];
    }

    // 3. Migriere Ingredient-Datentypen und StableIDs
    migratedMenuChild.ingredients = await migrateIngredientStructure(migratedMenuChild.ingredients);

    // 4. Setze maxUsedStableId
    const maxStableId = Math.max(0, ...migratedMenuChild.ingredients.map(ing => ing.stableId || 0));
    migratedMenuChild.maxUsedStableId = maxStableId;

    // 5. Migriere alte "zubereitung" zu "preperation"
    if (menuChild.zubereitung && !migratedMenuChild.preperation) {
        migratedMenuChild.preperation = migratePreparationStructure(menuChild.zubereitung);
        delete migratedMenuChild.zubereitung;
        helper.devConsole('✅ Migrated "zubereitung" to "preperation"');
    }

    // 6. Migriere Nutrition-Datentypen
    if (migratedMenuChild.nutritions) {
        migratedMenuChild.nutritions = migrateNutritionStructure(migratedMenuChild.nutritions);
    }

    // 7. Setze Standard-Werte
    migratedMenuChild.seatCount = migratedMenuChild.seatCount || 2;
    migratedMenuChild.cookingTime = migratedMenuChild.cookingTime || 30;
    migratedMenuChild.isStandard = migratedMenuChild.isStandard !== undefined ? migratedMenuChild.isStandard : true;

    helper.devConsole('✅ Backend legacy migration completed');
    return migratedMenuChild;
}

/**
 * Migriert alte "zutaten" Struktur zu "ingredients"
 * @param {Array} zutaten - Alte Zutaten-Struktur
 * @returns {Array} Neue Ingredients-Struktur
 */
async function migrateOldZutatenStructure(zutaten) {
    helper.devConsole('🔧 Migrating old "zutaten" structure...');
    
    const ingredients = [];
    let stableId = 1;

    for (const zutat of zutaten) {
        const ingredient = {
            amount: 1,
            unit: null,
            name: null,
            stableId: stableId++
        };

        // Migriere Menge
        if (zutat.menge) {
            ingredient.amount = typeof zutat.menge === 'string' ? 
                parseFloat(zutat.menge) || 1 : zutat.menge;
        }

        // Migriere Einheit
        if (zutat.einheit) {
            ingredient.unit = await findOrCreateUnit(zutat.einheit);
        } else {
            ingredient.unit = await findOrCreateUnit('-');
        }

        // Migriere Name
        if (zutat.name) {
            ingredient.name = await findOrCreateGrocery(zutat.name);
        } else {
            throw new Error('Ingredient name is required');
        }

        ingredients.push(ingredient);
    }

    helper.devConsole(`✅ Migrated ${ingredients.length} old "zutaten" to ingredients`);
    return ingredients;
}

/**
 * Migriert Ingredient-Struktur und weist StableIDs zu
 * @param {Array} ingredients - Ingredients Array
 * @returns {Array} Migrierte Ingredients
 */
async function migrateIngredientStructure(ingredients) {
    helper.devConsole('🔧 Migrating ingredient structure...');
    
    let stableId = 1;
    const migratedIngredients = [];

    for (const ingredient of ingredients) {
        const migratedIngredient = {
            amount: 1,
            unit: ingredient.unit,
            name: ingredient.name,
            stableId: ingredient.stableId || stableId++
        };

        // Korrigiere Amount-Datentyp
        if (typeof ingredient.amount === 'string') {
            migratedIngredient.amount = parseFloat(ingredient.amount) || 1;
        } else if (typeof ingredient.amount === 'number') {
            migratedIngredient.amount = ingredient.amount;
        }

        // Stelle sicher, dass Unit eine ObjectId ist
        if (!migratedIngredient.unit) {
            migratedIngredient.unit = await findOrCreateUnit('-');
        }

        // Stelle sicher, dass Name eine ObjectId ist
        if (!migratedIngredient.name) {
            throw new Error('Ingredient name is required');
        }

        migratedIngredients.push(migratedIngredient);
    }

    helper.devConsole(`✅ Migrated ${migratedIngredients.length} ingredients with StableIDs`);
    return migratedIngredients;
}

/**
 * Migriert Preparation-Struktur
 * @param {Array} zubereitung - Alte Zubereitung
 * @returns {Array} Neue Preparation
 */
function migratePreparationStructure(zubereitung) {
    if (!Array.isArray(zubereitung)) {
        return [];
    }

    return zubereitung.map((step, index) => ({
        head: step.head || `Schritt ${index + 1}`,
        content: step.content || step
    }));
}

/**
 * Migriert Nutrition-Datentypen
 * @param {Array} nutritions - Nutritions Array
 * @returns {Array} Migrierte Nutritions
 */
function migrateNutritionStructure(nutritions) {
    if (!Array.isArray(nutritions)) {
        return [];
    }

    return nutritions.map(nutrition => ({
        name: nutrition.name || 'Unbekannt',
        amount: typeof nutrition.amount === 'string' ? 
            parseFloat(nutrition.amount) || 0 : nutrition.amount || 0,
        unit: nutrition.unit || 'g'
    }));
}

/**
 * Findet oder erstellt eine Unit
 * @param {string} unitName - Name der Einheit
 * @returns {ObjectId} Unit ObjectId
 */
async function findOrCreateUnit(unitName) {
    if (!unitName || unitName.trim() === '') {
        unitName = '-';
    }

    let unit = await Unit.findOne({ name: unitName.trim() });
    
    if (!unit) {
        unit = await Unit.create({ name: unitName.trim() });
        helper.devConsole(`🆕 Created new unit: ${unitName}`);
    }

    return unit._id;
}

/**
 * Findet oder erstellt eine Grocery
 * @param {string} groceryName - Name der Zutat
 * @returns {ObjectId} Grocery ObjectId
 */
async function findOrCreateGrocery(groceryName) {
    if (!groceryName || groceryName.trim() === '') {
        throw new Error('Grocery name cannot be empty');
    }

    let grocery = await Grocery.findOne({ name: groceryName.trim() });
    
    if (!grocery) {
        grocery = await Grocery.create({ name: groceryName.trim() });
        helper.devConsole(`🆕 Created new grocery: ${groceryName}`);
    }

    return grocery._id;
}

module.exports = {
    detectLegacyFormat,
    migrateLegacyMenuChild,
    migrateOldZutatenStructure,
    migrateIngredientStructure
};
