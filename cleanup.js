'use strict';

/**
 * Cleanup-Skript zur Bereinigung des Speicherplatzes auf Azure App Service
 * Dieses Skript wird vor dem Start der App ausgeführt
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Logging-Funktionen
function log(message) {
  console.log(`[CLEANUP] ${message}`);
}

function logError(message) {
  console.error(`[CLEANUP ERROR] ${message}`);
}

// Verzeichnisse, die bereinigt werden sollen
const directoriesToClean = [
  path.join(process.env.HOME || '.', 'LogFiles'),
  path.join(process.env.TEMP || '/tmp'),
  path.join(process.env.TMP || '/tmp'),
  path.join(__dirname, 'logs')
];

// Dateiendungen, die entfernt werden sollen
const fileExtensionsToDelete = [
  '.log', '.zip', '.tar.gz', '.tgz'
];

// Spezifische Dateien, die entfernt werden sollen
const filesToDelete = [
  'node_modules.tar.gz'
];

// Verzeichnisse bereinigen
log('Starte Bereinigung des Speicherplatzes...');

// Aktuelle Speichernutzung anzeigen (falls möglich)
try {
  const diskUsage = execSync('df -h').toString();
  log(`Aktuelle Speichernutzung:\n${diskUsage}`);
} catch (error) {
  log('Konnte Speichernutzung nicht anzeigen: ' + error.message);
}

// Temporäre Dateien in den angegebenen Verzeichnissen löschen
directoriesToClean.forEach(dir => {
  try {
    if (fs.existsSync(dir)) {
      log(`Bereinige Verzeichnis: ${dir}`);
      
      // Alle Dateien im Verzeichnis durchgehen
      const files = fs.readdirSync(dir);
      
      // Zähler für gelöschte Dateien und freigegebenen Speicherplatz
      let deletedFiles = 0;
      let freedSpace = 0;
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        
        try {
          const stats = fs.statSync(filePath);
          
          // Datei löschen, wenn es eine reguläre Datei mit einer der zu löschenden Erweiterungen ist
          if (stats.isFile() && (
            fileExtensionsToDelete.some(ext => file.endsWith(ext)) || 
            filesToDelete.includes(file)
          )) {
            const fileSize = stats.size;
            fs.unlinkSync(filePath);
            deletedFiles++;
            freedSpace += fileSize;
            log(`Gelöschte Datei: ${filePath} (${Math.round(fileSize / 1024)} KB)`);
          }
        } catch (err) {
          logError(`Fehler beim Löschen von ${filePath}: ${err.message}`);
        }
      });
      
      log(`${deletedFiles} Dateien gelöscht, ${Math.round(freedSpace / 1024 / 1024)} MB freigegeben in ${dir}`);
    } else {
      log(`Verzeichnis existiert nicht: ${dir}`);
    }
  } catch (error) {
    logError(`Fehler beim Bereinigen von ${dir}: ${error.message}`);
  }
});

// Alte Deployment-Verzeichnisse bereinigen (falls auf Azure)
if (process.env.WEBSITE_SITE_NAME) {
  try {
    const deploymentsDir = path.join(process.env.HOME || '.', 'site', 'deployments');
    
    if (fs.existsSync(deploymentsDir)) {
      log(`Bereinige alte Deployments in: ${deploymentsDir}`);
      
      // Alle Unterverzeichnisse auflisten
      const deployments = fs.readdirSync(deploymentsDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory() && dirent.name !== 'tools')
        .map(dirent => ({
          name: dirent.name,
          path: path.join(deploymentsDir, dirent.name),
          stats: fs.statSync(path.join(deploymentsDir, dirent.name))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime()); // Nach Änderungsdatum sortieren (neuste zuerst)
      
      // Behalte die neuesten 3 Deployments, lösche den Rest
      if (deployments.length > 3) {
        log(`${deployments.length} Deployments gefunden, behalte die neuesten 3`);
        
        for (let i = 3; i < deployments.length; i++) {
          try {
            log(`Lösche altes Deployment: ${deployments[i].name}`);
            execSync(`rm -rf "${deployments[i].path}"`);
          } catch (err) {
            logError(`Fehler beim Löschen des Deployments ${deployments[i].name}: ${err.message}`);
          }
        }
      } else {
        log(`Nur ${deployments.length} Deployments gefunden, keine Bereinigung notwendig`);
      }
    }
  } catch (error) {
    logError(`Fehler beim Bereinigen alter Deployments: ${error.message}`);
  }
}

log('Bereinigung abgeschlossen.');

// Aktuelle Speichernutzung nach der Bereinigung anzeigen (falls möglich)
try {
  const diskUsage = execSync('df -h').toString();
  log(`Speichernutzung nach Bereinigung:\n${diskUsage}`);
} catch (error) {
  log('Konnte Speichernutzung nicht anzeigen: ' + error.message);
}

module.exports = { log, logError }; 