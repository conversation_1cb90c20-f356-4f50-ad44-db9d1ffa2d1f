<template>
    <div v-if="!userStore.user.id && userStore.noSessionUserInformation" class="w-full md:w-96 px-5 mb-10 z-50 rounded-3xl fixed h-94 bottom-0 mx-auto right-0 md:mr-12 md:mb-12 delay-fade-in">
        <!-- Head -->
         <div class="w-full relative rounded-3xl bg-black h-16"></div>
         <div class="w-full relative -mt-14 rounded-3xl bg-ordypink-200 h-36 pr-4 md:pr-2 pl-8 md:pl-4">
            <button @click="userStore.noSessionUserInformation = !userStore.noSessionUserInformation" class="h-8 w-8 mt-2 float-right">
              <img src="../assets/icons/close_cancel.svg" class="h-6 w-6" alt="closet" />
            </button>
            <h1 class="w-full text-left align-bottom text-[2.5rem] text-white leading-3 mt-3 font-YesevaOne pt-12 pb-6 pr-3">Willkommen</h1>
            <p class="w-full text-left align-bottom text-[1.1rem] text-white leading-3 font-YesevaOne">Entfessele den Chefkoch in dir</p>
         </div>
        <!-- Head -->
         <div class="h-auto p-10 pt-8 bg-white mx-1 -mt-4 border-4 border-black">
          <p class="font-YesevaOne text-tiny">Mach deine Küche zur Kreativwerkstatt! Stell dir vor, du könntest jedes Gericht in ein kulinarisches Meisterwerk verwandeln und deine Gäste immer wieder aufs Neue begeistern. Genau das bietet dir unsere innovative Plattform! Mit uns an deiner Seite wirst du zum Spitzenkoch, egal ob du gerade erst anfängst oder deine Kochkünste perfektionieren möchtest. 🚀</p>
          <div class="h-4 pb-16 flex flex-row pt-4 gap-6">
            <img class="text-black h-8" src="../assets/icons/createnewreciept.svg" />
            <img class="text-black h-8" src="../assets/icons/editreciept.svg" />
            <img class="text-black h-8" src="../assets/icons/addimage.svg" />
          </div>
          </div>
         <!-- Footer Button -->
        <div class="w-full relative mb-0 -mt-16">
            <div class="relative bg-ordypurple-200 w-12 ml-3 -mb-8 h-12 rounded-full pt-3">
                <img src="../assets/icons/info.png" class="mx-auto my-auto" />
            </div>
            <button @click="$router.push('/login'), userStore.noSessionUserInformation = !userStore.noSessionUserInformation" class="rounded-3xl bg-black w-full h-16 text-right text-white">
                <p class="align-bottom mb-0 pb-0 mt-3 font-YesevaOne text-xl pt-5 pr-3"><span>Los gehts</span></p>
            </button>
        </div>
        <!-- Footer Button -->
    </div>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../store/userStore'

  const router = useRouter();
  const route = useRoute();

  const { setNotification } = useNotification();
  const userStore = useUserStore()

  
</script>
<style>
@keyframes delayedFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.delay-fade-in {
  opacity: 0; /* Startet unsichtbar */
  animation: delayedFadeIn 1s ease-in-out 2s forwards; /* 10s Verzögerung, 2s Animationsdauer */
}
</style>