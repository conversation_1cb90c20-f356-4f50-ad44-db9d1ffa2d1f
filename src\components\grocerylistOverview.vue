<template>
  <button @click="groceryliststore.getGrocerysByKitchentableId(index3)" class="flex flex-row gap-3 mt-4 w-full" v-for="(item3,index3) in groceryliststore.formattedGroceryList" :key="index3">
    <div class="w-2/12 h-auto pl-1 pt-1">
      <img class="w-auto" src="./../assets/icons/grocerylist_ordpurple.png" />
    </div>
    <div class="flex flex-col w-10/12">
      <span class="w-full font-semibold text-xs text-left">Einkaufszettel für </span>
      <span class="w-full text-xs text-left">{{ item3.formattedStartDate  }} bis {{ item3.formattedEndDate  }}</span>
    </div>
  </button>
</template>
<script setup>
import useNotification from '../../modules/notificationInformation';
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMenuStore } from '../store/menuStore'
import { useUserStore } from '../store/userStore'
import { useGrocerylistStore } from '../store/grocerylistStore'
import { useWeekplanStore } from '../store/weekplanStore'


///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const groceryliststore = useGrocerylistStore();
  const weekplanstore = useWeekplanStore();

///////////////////// SETUP /////////////////////////////////

</script>