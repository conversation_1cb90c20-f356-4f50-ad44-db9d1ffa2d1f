const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper')
const Menu = require('../models/menuModel')
//const MenueRelation = require('../models/menuRelationModel')
const { uploadFile, getFileStream } = require('../utils/awsStorage')

/////////////////////////////////////// ONE MENU /////////////////////////////////////////
// @POST /menu/one/:id
// empty Menü/Rezept/Reciept is created
exports.createOneMenue = catchAsync(async (req, res, next) => {
  try{
    const newMenue = await Menu.create(
      req.body.data
    );

    helper.devConsole("drin2")

    res.data = newMenue;

    next()

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

// @GET /menu/one/:id
exports.getOneMenue = catchAsync(async (req, res, next) => {
  try{
    // find one menu and automaticly return
    const menue = await Menu.findOne({_id: req.params.id}).populate('zutaten.einheit').populate('zutaten.name');
    //.populate('zutaten.unit')
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        menue
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

// @ UPDATE 
// /menu/one/:id
exports.patchOneMenue = catchAsync(async (req, res, next) => {
  try{
    
    helper.devConsole("inPatchOneMenu")
    helper.devConsole(req.body.data)

    const updateObject = req.body.data;
    const menue = await Menu.updateOne({_id: req.params.id}, {$set: updateObject});
    helper.devConsole(menue)
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        menue
      }
    });
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(403).json({
      status: 'error',
      data: error
    });
  }

});

/////////////////////////////////////// //////////// /////////////////////////////////////////

// getOneMenueByPersons
// @GET /menu/one/:id/:persons
/*
exports.checkAndAddOneMenueByPersons = catchAsync(async (req, res, next) => {
  helper.devConsole(req.params.id)
  helper.devConsole(req.params.persons)
  ////////// SETTINGS //////////
  //
  /////////////////////////////
  try{
    // find one menu and with correct number of persons and automaticly return
    const menue = await Menu.findOne({_id: req.params.id}, {plannedSeats: req.params.persons})
    //.populate('zutaten.unit')
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        "menue" : "menu"
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});
*/


//
// POST @ /menu/images/upload
exports.uploadOneImage = catchAsync(async (req, res, next) => {
  try{

    helper.devConsole(req.body.image)
    helper.devConsole(req.file)

    // upload image
    const res = await uploadFile(req.file)

    helper.devConsole(res)
    
    // send object id (req.body.id) for next() -> patchOne
    req.params.id = req.body.id
    req.body = { "imagelinkv": res.key }
    
    // next()
    next();

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

//
exports.downloadOneImage = catchAsync(async (req, res, next) => {
  
  try{
    // load file stream from aws bucket
    const readStream = getFileStream(req.params.id)
    readStream.pipe(res)

    
  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});


// @GET /menu/one/byIngridient/:ingridient
exports.getOneMenuByIngridient = catchAsync(async (req, res, next) => {
  try{
    // find one menu and automaticly return
    helper.devConsole(req.params.ingridient)
    let menue = await Menu.findOne({
      $or: [
        { "zutaten.name": { $regex: req.params.ingridient, $options: "i" }},
        { "zubereitung.content": { $regex: req.params.ingridient, $options: "i" }}
      ]
    })

    
    helper.devConsole(menue)

    if(menue == null){
      menue.data.data.imagelink = ''
    }

    helper.devConsole(menue)

    /***
    const searchstring = req.params.ingridient;
    const pipeline = [
      {
        $lookup: {
          from: 'menus', // Der Name der Collection, in der die Menü-Dokumente gespeichert sind.
          localField: 'menu', // Der Feldname in 'MenuesRelations', der auf 'menu' verweist.
          foreignField: '_id', // Der Schlüsselfeldname in der 'menu'-Collection.
          as: 'menu'
        }
      },
      { $unwind: '$menu' },
      { $match: { 'menu.Name': { $regex: new RegExp(searchstring, 'i') } } },
      { $limit: 1 }
    ];

    const result = await Menu.aggregate(pipeline).exec();
 */
    

    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        menue
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(500).json({
      status: 'error',
      data: error
    });
  }

});

