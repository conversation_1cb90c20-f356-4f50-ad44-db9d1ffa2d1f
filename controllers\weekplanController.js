const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const Weekplan = require('../models/weekplanModel');
const Menu = require('../models/menuModel');
const Menuchild = require('../models/menuchildModel');
const AppError = require('../utils/appError');
const dayjs = require('dayjs');
require('dayjs/locale/de')

exports.addOneMenu = catchAsync(async (req, res, next) => {
  helper.devConsole("addOneMenu in weekplanController")
  helper.devConsole(req.body.weekplan)
  if( 
    !req.body.menuchild.parentId,
    !req.body.menuchild.seatCount,
    !req.body.weekplan.kitchentableid,
    !req.body.weekplan.date,
    !req.body.weekplan.plannedSeats,
    !req.body.weekplan.phase
  ){
      req.body.answerobject = "finished"
  }

  //load relations from the user
  const weekplan = await Weekplan.create(
      {
        menuId: req.body.menuchild.parentId, 
        kitchentableId: req.body.weekplan.kitchentableid, 
        date: req.body.weekplan.date, 
        daytime: req.body.weekplan.phase, 
        numberOfPersons: req.body.weekplan.plannedSeats
      }
    );
  helper.devConsole(weekplan)

  //load all menues by id
  // Send success response
  next()
});

/*OLD CNTROLLER*/
exports.createWeekplan = catchAsync(async (req, res, next) => {
  helper.devConsole("createWeekplan in weekplanController")
  try{
    helper.devConsole()
    //load relations from the user
    const weekplan = await Weekplan.create({menuId: req.body.menuid, kitchentableId: req.body.kitchentableid, date: req.body.date, daytime: req.body.phase});
    helper.devConsole(weekplan)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: weekplan
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

// @POST api/v1/weekplan/many
exports.searchWeekplans = catchAsync(async (req, res, next) => {
  helper.devConsole("searchWeekplans in weekplanController")
  helper.devConsole(req.body)
  if( 
    !req.body.weekplan.kitchentableid,
    !req.body.weekplan.dates,
    !req.body.settings.outputtype
  ){
      req.body.answerobject = "finished"
  }

  // Schritt 1: Finde alle Weekplans, die den Kriterien entsprechen
  const weekplans = await Weekplan.find({
    kitchentableId: req.body.weekplan.kitchentableid,
    date: {
      $gte: req.body.weekplan.dates[0],
      $lte: req.body.weekplan.dates[1]
    }
  })
  .sort({ date: 1 })
  .populate({
    path: 'menuId',
    populate: {
      path: 'menuchilds.menuChildId',
      model: 'MenuChild' // Stellt sicher, dass 'MenuChild' korrekt verwendet wird.
    }
  });

  // Schritt 2: Extrahiere alle menuIds und numberOfPersons
  const numberOfPersonsMap = {};
  weekplans.forEach(wp => {
    numberOfPersonsMap[wp._id.toString()] = wp.numberOfPersons;
  });

  // Schritt 3 & 4: Filtere alle Menus und ihre `menuchilds`
  const results = weekplans.map(wp => {
    const menu = wp.menuId.toObject();
    const numberOfPersons = numberOfPersonsMap[wp._id.toString()];

    const filteredMenuchilds = menu.menuchilds.filter(mc => {
      // Überprüft, ob das Menü kind existiert und ob die Sitzplatzanzahl der Anzahl der Personen entspricht
      return mc.menuChildId && mc.menuChildId.seatCount === numberOfPersons;
    });

     // Aktualisiere das Menü im jeweiligen Weekplan
    const updatedMenu = { ...menu, menuchilds: filteredMenuchilds };

    // Formatiere das Datum
    
    dayjs.locale('de')
    const formattedDate = dayjs(wp.date).format('dddd, D. MMMM');

    // Update `wp`-Objekt mit dem formatierten Datum und aktualisiertem Menü
    return { ...wp.toObject(), date: formattedDate, menuId: updatedMenu };
  });

  //console.log(results)

  if(req.body.settings.outputtype === "complete_array"){
    helper.devConsole("---- complete array was called -------")
  }

  req.body.answerobject = results


  // next
  next()

});

// @POST api/v1/weekplan/one
exports.getWeekplan = catchAsync(async (req, res, next) => {
  helper.devConsole("getWeekplan in weekplanController")
  //console.log(req.body)
  if( 
    !req.params.weekplanid
  ){
    next(new AppError('Not every data was given at weekplanController.getWeekplan', 500))
  }

  //console.log(req.params.weekplanid)
  //console.log("req.params.weekplanid")

  // Schritt 1: Finde alle Weekplans, die den Kriterien entsprechen
  const weekplan = await Weekplan.findOne({
    '_id': req.params.weekplanid
  }).populate({
    path: 'menuId',
    populate: [
      {
        path: 'menuchilds.menuChildId',
        model: 'MenuChild',
        populate: [
          {
            path: 'ingredients.unit',
            model: 'Unit'
          },
          {
            path: 'ingredients.name',
            model: 'Grocery'
          }
        ]
      },
      {
        path: 'users.userId',
        model: 'User'
      }
    ]
  });

  helper.devConsole(weekplan)
  helper.devConsole("--------")

  helper.devConsole(weekplan.numberOfPersons)

  // Schritt 2: Filtere `menuchilds`
  let newChilds = weekplan.menuId.menuchilds.filter(
    menuchild => {
      //console.log("Checking seatCount:", menuchild.menuChildId.seatCount);
      //console.log("Checking numberOfPersons:", weekplan.numberOfPersons);
      return menuchild.menuChildId.seatCount === weekplan.numberOfPersons;
    }
  );
  //console.log("Filtered MenuChilds:", newChilds);
  weekplan.menuId.menuchilds = newChilds;

  // Formatiere das Datum
  dayjs.locale('de')
  const formattedDate = dayjs(weekplan.date).format('dddd, D. MMMM');
  helper.devConsole(formattedDate)
  weekplan.date = formattedDate

  req.body.answerobject = weekplan


  // next
  next()

});
    

exports.deleteWeekplan = catchAsync(async (req, res, next) => {
  helper.devConsole("deleteWeekplan in weekplanController")
  helper.devConsole(req.params)
  //load relations from the user
  const weekplanDelete = await Weekplan.deleteOne({ "_id" : req.params.weekplanid });
  helper.devConsole(weekplanDelete)
  
  req.body.answerobject = "done"
  // next
  next()
});
