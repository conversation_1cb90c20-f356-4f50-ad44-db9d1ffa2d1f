const catchAsync = require('../utils/catchAsync');
var helper = require('../utils/helper');
const Menu = require('../models/menuModel');
const Menuchild = require('../models/menuchildModel')
//const MenuRelations = require('../models/menuRelationModel');
const authController = require('./authController');
const unitController = require('./unitController');
const Jimp = require("jimp");
const { uploadFileByLink, getFileStream } = require('../utils/awsStorage');
const fs = require('fs')
const axios = require('axios')
const cheerio = require('cheerio');  // new addition
const { validateAndFixAIRecipe } = require('../utils/aiRecipeValidator');
const { Buffer } = require('buffer');
const WebSocket = require('ws');
const atob = require('atob');
const { reset } = require('nodemon');
const MarketingContent = require('../models/marketingContentModel');
const AppError = require('../utils/appError');
const User = require('../models/userModel');
const multer = require('multer');


/////////////////////////////////// BASE STRING //////////////////////////////////////////

const ORDY_MENU_TEMPLATE = `
  Versuche Zutaten mit folgenden Abkürzungen anzugeben: l, g, kg, lbs, ml, dl, cl, tl, el, Scheibe, stk, Prise, Becher, Dose, Stange. Gib das Ergebnis genau strukturiert wiefolgt zurück, damit sie später mit JSON.parse() verwendet werden können:

  [
    {
      "menu": {
          "name": "", // string, Name des Rezeptes
          "description": "", // string, bildliche Beschreibung des Rezeptes
          "menuchilds": [], // empty array
          "users": [], // empty array
          "imagelink": " ", // string with one space
          "freeAccess": false // Boolean false
        },
      "menuchild": {
          "seatCount": 0, // number, Zum Beispiel 4, wenn das Rezept für 4 Personen gedacht ist
          "cookingTime": 0, // number, zum Beispiel 82. Gibt die Zubereitungszeit in Minuten des Rezeptes an
          "preperation":[ // array
            {
              "head":"schritt1", // string, eine Überschrift für jeden Zubereitungsschritt
              "content": "Inhalt von Schritt 1" // string, die Beschreibung wie dieser Rezeptschritt ausgeführt werden soll
            }
          ],
          "nutritions": [ // array
            { // object
              "name": "Protein", // string, zum Beispiel Protein
              "amount": 20, // number, zum Beispiel 20 (als Zahl, nicht String)
              "unit": "g" // string, zum Beispiel Gramm (Abkürzung verwenden) g
            }
          ],
          "versions": [] // empty array
      },
      "ingredients": [ // array
        {
          "unit": { "name": "Pack" }, // object mit key name und value zum Beispiel Liter (Abkürzung verwenden) l, falls keine gefunden wird folgendes Zeichen verwenden: -
          "amount": 1, // number nur in Dezimalzahlen oder Ganzzahlen, zum Beispiel 1 oder 0.5 oder 1.5 aber nicht 1 1/2 oder 1½ oder ½
          "name": { "name": "Milch" }, // object mit key name und value zum Beispiel Milch
          "stableId": 1 // KRITISCH: Permanente ID für Platzhalter-System, startet bei 1 und erhöht sich für jede weitere Zutat

        }
      ]
    }
  ]
`;
////////////////////////////////////////////////////////////////////////////////////////////



/////////////////////////////////// CREATE IMAGES //////////////////////////////////////////

//@POST /functions/createimagebasedonotherimage
exports.createImageByOtherImage = catchAsync(async (req, res, next) => {
  helper.devConsole("createImageByOtherImage in gptController")

  try{
    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const response = await openai.images.createVariation({
      image: fs.createReadStream("test2.png"),
      model: "dall-e-2",
      n: 1,
      size: "1024x1024"
    });
    helper.devConsole(response)
    image_url = response.data.data[0].url;
    helper.devConsole(image_url)
  } catch(err) {
    helper.devConsole(err)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

});
// creates one image based on following parameters
exports.createImage = catchAsync(async (req, res, next) => {
  helper.devConsole("createImage in gptController")
  helper.devConsole(req.body.menu.name)
  helper.devConsole(req.body.menu.description)
  if(
    !req.body.menu.name ||
    !req.body.menu.description
  ){
      next(new AppError('Not every data was given at menuController.createImage', 500))
  }
  const { OpenAI } = require("openai");

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  const response = await openai.images.generate({
    model: "dall-e-3",
    prompt: `A cooked an beautiful prepared dish: ${req.body.menu.name} ${req.body.menu.description} in closeup on random pastel color plate, fotographed with Sigma 85 mm f/8.`,
    n: 1,
    size: "1024x1024",
  });

  image_url = response.data[0].url;

  let file = {}
  file.name = (Math.random() + 1).toString(36).substring(7) + ".png"

  // grab image by url from chatgtp api and send it to s3
  const imageType = "image/png";
  const image = await Jimp.read(image_url);
  const buffer = await image.getBufferAsync(imageType);
  file.imageType = imageType
  file.body = buffer

  // upload image
  const resp = await uploadFileByLink(file)

  //req.body.menu.imagelink = "https://ordy-images.s3.amazonaws.com/" + file.name
  req.body.menu.imagelink = file.name

  helper.devConsole(req.body)

  next()

});

// Last element in chain
//@POST /creator/createRecieptImage #createRecieptImage #old: createDetailList
exports.createRecieptAndImage = catchAsync(async (req, res, next) => {
  const functionName = 'gptController.createRecieptAndImage'; // Added for logging
  helper.devConsole(functionName, "Starting...");

  // Data validation (Ensure req.body.data exists)
  if (!req.body.data) {
    helper.devConsole(functionName, "ERROR: req.body.data is missing.");
    return next(new AppError('Recipe data is missing in request body.', 400));
  }
  if (!req.body.data.Name) {
       helper.devConsole(functionName, "ERROR: req.body.data.Name is missing.");
       return next(new AppError('Recipe name is missing in request data.', 400));
  }

  try{
        // --- 1. Generate Image --- START
        helper.devConsole(functionName, `Generating image for recipe: ${req.body.data.Name}`);
        const { OpenAI } = require("openai");
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });

        let image_url;
        try {
            const response = await openai.images.generate({
              model: "dall-e-3", // Or your desired model
              prompt: `${req.body.data.Name} in closeup on random pastel color plate, fotographed with Sigma 85 mm f/8.`,
              n: 1,
              size: "1024x1024", // Consider using the standard size from video recording if needed
            });
            image_url = response?.data?.[0]?.url;
            if (!image_url) {
                 helper.devConsole(functionName, "ERROR: OpenAI response missing image URL.", response);
                 throw new Error('Failed to get image URL from OpenAI response.');
            }
             helper.devConsole(functionName, `Image generated, URL: ${image_url.substring(0,50)}...`);
        } catch (imageGenError) {
             helper.devConsole(functionName, "ERROR generating image via OpenAI:", imageGenError);
             return next(new AppError('Failed to generate recipe image.', 500)); // Forward error
        }
        // --- 1. Generate Image --- END

        // --- 2. Process and Upload Image --- START
        let file = {};
        let generatedFileName;
        try {
            generatedFileName = (Math.random() + 1).toString(36).substring(7) + ".png";
            file.name = generatedFileName;
            const imageType = "image/png";
            const image = await Jimp.read(image_url);
            const buffer = await image.getBufferAsync(imageType);
            file.imageType = imageType;
            file.body = buffer;

            helper.devConsole(functionName, `Uploading image ${file.name} to S3...`);
            const resp = await uploadFileByLink(file);
            if (!resp || !resp.Location) {
                 helper.devConsole(functionName, "ERROR: S3 upload response missing Location.", resp);
                 throw new Error('Failed to get upload location from S3 response.');
            }
             helper.devConsole(functionName, `Image uploaded successfully: ${resp.Location}`);
        } catch (uploadError) {
             helper.devConsole(functionName, "ERROR processing or uploading image:", uploadError);
             return next(new AppError('Failed to process or upload recipe image.', 500)); // Forward error
        }
         // --- 2. Process and Upload Image --- END

        // --- 3. Prepare Data for DB --- START
        // Add the generated image filename (not the full S3 URL) to the data object
        const recipeDataToSave = {
             ...req.body.data,
             imagelink: generatedFileName // Store only the filename
         };
        helper.devConsole(functionName, "Prepared recipe data for saving:", recipeDataToSave);
        // --- 3. Prepare Data for DB --- END

        // --- 4. Create Recipe in DB (ONLY after image success) --- START
        helper.devConsole(functionName, "Saving recipe to database...");
        const newMenu = await Menu.create(recipeDataToSave);
        helper.devConsole(functionName, `Recipe saved successfully with ID: ${newMenu._id}`);
        // --- 4. Create Recipe in DB --- END

        // --- 5. Prepare and Send Response --- START
        // Construct the full S3 URL for the response only
        const fullImageUrl = `https://ordy-images.s3.amazonaws.com/${generatedFileName}`;
        // Create a response object based on the saved menu, but with the full image URL
        const responseData = newMenu.toObject(); // Convert Mongoose doc to plain object
        responseData.imagelink = fullImageUrl;

        helper.devConsole(functionName, "Sending success response.");
        res.status(200).json({
            status: 'success',
            data: responseData // Send the modified data with full URL
        });
        // --- 5. Prepare and Send Response --- END

  } catch(err) {
    // Catch unexpected errors during the process (e.g., DB connection issues after image upload)
    helper.devConsole(functionName, "UNEXPECTED ERROR in createRecieptAndImage:", err);
    // Use next to pass the error to the global error handler
    return next(err);
  }

});

////////////////////////////////////////////////////////////////////////////////////////////



////////////////////////////// CREATE RECIEPT TEXT, OPEN AI ////////////////////////////////

//@POST /creator/functions/reciept/createbytext
exports.createRecieptByText = catchAsync(async (req, res, next) => {
  helper.devConsole("createRecieptByText at gptController")
  helper.devConsole(req.body)

  let incomingString = req.body.data;

  if(!req.body.settings){
    res.status(500).json({
      status: 'error',
      'message': 'no settings parameter set (createRecieptByText)'
    });
  }

  if(!req.body.data){
    res.status(500).json({
      status: 'error',
      'message': 'no body.data parameter found (createRecieptByText)'
    });
  }

  /*  !!!   SETTINGS !!!
      Switch to either end the function by calling 200 response (req.body.settings.switch = 'end')
      or
      switch forward to image creation function (req.body.settings.switch = 'forward')
  */

  try{
    helper.devConsole("Start Creating")

    // 2) Create phrase
    const questionQuery = `
      Erstelle ein vollständig, gut erklärtes Rezept bestehend aus Hauptgericht und Beilagen basierend auf diesem Input:
      ${incomingString}

      ${ORDY_MENU_TEMPLATE}
    `;

      const { OpenAI } = require("openai");

      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      //console.log(questionQuery)

      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `Du erstellst normale bis originielle Rezepte. Diese Rezepte bestehen aus einer Hauptzutat und Beilagen. Erkläre in deinen Rezepten den Kochprozess gut und einfach aber ausführlich, damit jeder Mensch einfach das Rezept kochen kann. Das Rezept sollte schön angerichtet werden können und einen guten Geschmack beim Essen aufweisen.

KRITISCH - Dynamische Mengenangaben in Zubereitungsschritten:
Verwende in den Zubereitungsschritten AUSSCHLIESSLICH ID-basierte Platzhalter für Mengenangaben, damit sich diese automatisch an die Personenanzahl anpassen können.

VERWENDE NUR ID-BASIERTE PLATZHALTER:
Verwende ID-Nummern für Zutaten (1, 2, 3, etc. in der Reihenfolge der Zutatenliste):
- \${ID:1} = Komplette Angabe der ersten Zutat (z.B. "250g Mehl")

WICHTIG: Verwende NIEMALS \${MENGE:X} oder \${EINHEIT:X} - diese sind veraltet!
Verwende NIEMALS namens-basierte Platzhalter wie \${MEHL} oder \${OLIVENOEL}!
Verwende IMMER nur \${ID:X} mit der entsprechenden Nummer!

Beispiele für korrekte Zubereitungsschritte:
- "Geben Sie \${ID:1} in eine Schüssel." (erste Zutat komplett)
- "Erhitzen Sie \${ID:2} in einer Pfanne." (zweite Zutat komplett)
- "Fügen Sie \${ID:3} hinzu und braten Sie sie an." (dritte Zutat komplett)
- "Mit \${ID:4} würzen." (vierte Zutat komplett)

FALSCH: "Schneiden Sie \${MENGE:2} \${EINHEIT:2} Gurken"
RICHTIG: "Schneiden Sie \${ID:2} Gurken"

Die ID entspricht der Position in der Zutatenliste (1 = erste Zutat, 2 = zweite Zutat, etc.)
Das System ersetzt \${ID:2} automatisch mit "2 Stück" wenn das die zweite Zutat ist.`,
          },
          {
            role: "user",
            content: questionQuery
          }
        ],
        response_format: { type: "json_object" },
      });

      // get output from gpt object
      //```json
      //console.log(completion.choices[0].message.content)

      //completion.choices[0].message.content = completion.choices[0].message.content.replaceAll("`","")

      let output = await JSON.parse(completion.choices[0].message.content)
      //console.log("---------")
      //console.log("output")
      //console.log(output)
      //console.log("---------")


      //console.log(req.body.settings)


      /// See Settings at the top of this route (forward, end)
      if(req.body.settings.switch === "forward"){

        // 1. Get user ID correctly and safely from the authenticated user object
        let userIdFromAuth;
        // Check common locations for the user ID attached by auth middleware
        if (req.user && req.user._id) {
            userIdFromAuth = req.user._id.toString(); // Ensure it's a string if it's an ObjectId
        } else if (req.user && req.user.id) {
            userIdFromAuth = req.user.id;
        } else {
            // If user information is missing, pass an authentication error
            return next(new AppError('Benutzer nicht authentifiziert oder Benutzer-ID fehlt.', 401));
        }

        // Preserve the original output from GPT before overwriting req.body
        const gptOutput = output;

        // 🔧 KRITISCH: Validiere und korrigiere AI-generiertes Rezept
        helper.devConsole('🔍 Validating AI-generated recipe from text...');
        try {
          const validatedRecipe = await validateAndFixAIRecipe(gptOutput);
          helper.devConsole('✅ AI recipe validation successful');

          // Reset req.body with the validated GPT output
          req.body = validatedRecipe;
        } catch (validationError) {
          helper.devConsole('❌ AI recipe validation failed:', validationError.message);
          return next(new AppError(`Rezept-Validierung fehlgeschlagen: ${validationError.message}`, 400));
        }

        // Re-apply settings object if needed by the next middleware
        req.body.settings = {
          switch: "forward"
        };

        // 2. Attach the authenticated user ID directly to the req object, not req.body
        //    This prevents it from being lost if req.body is further modified.
        req.userId = userIdFromAuth;

        // Pass control to the next middleware in the chain
        next();
      }


      if(req.body.settings.switch === "end"){
        res.status(200).json({
          status: 'success',
          'data': output
        });
      }

    } catch(err) {
      helper.devConsole(err)
      res.status(500).json({
        status: 'error',
        'message': err.message
      });
    }



});

////////////////////////////////////////////////////////////////////////////////////////////

exports.getDetails = catchAsync(async (req, res, next) => {

  // 1) Create wording für details
  const questionQuery = `
    Erstelle für das Menü ${menuname} für ${personen} eine Zutatenliste.
    Erstelle eine Übersicht wie viele Proteine, Eiweisse, Fette, Vitamine und Mineralstoffe die Mahlzeit hat.
    Erstelle zusätzlich eine Anleitung für die Zubereitung des Menüs.
  `

  const completion = "hi"

  // 4) Create data as response
  res.status(200).json({
    status: 'success',
    'data': completion
  });
});


///////// CREATE RECIEPT IDEAS BY SETTINGS
//@POST /creator/menulist
exports.createOverview = catchAsync(async (req, res, next) => {
    if(
      !req.body.data.user_id ||
      !req.body.data.days ||
      !req.body.data.personen
    ){
        next(new AppError('Not every data was given at gptController.createOverview', 500))
    }

    //console.log(req.body)

    ///////// 0) Get all reciepts from this user
    // prepare object for getAllMenusByUserid
    req.params.userid = req.body.data.user_id
    const foundMenus = await Menu.find({'users.userId': req.params.userid})
    const menues = []

    let menuesToIgnore = ""

    for (let i = 0; i < foundMenus.length; i++){
      helper.devConsole(foundMenus[i].name)
      menuesToIgnore = menuesToIgnore + foundMenus[i].name + ", "
    }
    //console.log(menuesToIgnore)


    //////// 1) Create wording
    //If content is coming from client, set the value otherwise no value is choose
    helper.devConsole(req.body.data)

    const days = req.body.data.days ? req.body.data.days : '1';
    const personen = req.body.data.personen ? req.body.data.personen : '';
    const allergie = req.body.data.allergie ? `Achte auf eine Allergie gegen. ${req.body.data.allergie}. ` : '';
    const praeferenz = req.body.data.praeferenz ? `Jede Mahlzeit soll ${req.body.data.praeferenz}. ` : '';
    const liefestyle = req.body.data.liefestyle ? `Ich möchte mich ${req.body.data.liefestyle} ernähren. ` : '';
    const proteine = req.body.data.proteine ? `Ich möchte mich mit ${req.body.data.proteine} Portion Proteine ernähren. ` : '';
    const custom_wunsch = req.body.data.custom_wunsch ? `Ich möchte ${req.body.data.custom_wunsch}. ` : '';
    const date = new Date();

    helper.devConsole("Start Creating")

    // 2) Create phrase - 🔧 KRITISCH: Verwende das gleiche Template wie die anderen Generatoren
    const questionQuery = `
      Erstelle ein vollständig, gut erklärtes Rezept bestehend aus Hauptgericht und Beilagen für ${personen} Personen.
      ${allergie} ${praeferenz} ${liefestyle} ${proteine} ${custom_wunsch}
      Beachte den speziellen Wunsch: ${custom_wunsch}.

      Das Rezept sollte eine ausgewogene Mischung aus Proteinen, Kohlenhydraten, Fetten, Vitaminen und Mineralstoffen bieten.
      Berücksichtige saisonale Produkte für frische und nachhaltige Rezepte. Versuche aktuelle Trends zu berücksichtigen.

      Es sollen neue und möglichst diverse Menüvorschläge entstehen. Folgende Menüvorschläge existieren bereits und
      sollten nicht noch einmal von dir als Vorschlag gebracht werden: ${menuesToIgnore}.

      ${ORDY_MENU_TEMPLATE}
    `;

    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    helper.devConsole(questionQuery)


    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `Du erstellst normale bis originielle Rezepte. Diese Rezepte bestehen aus einer Hauptzutat und Beilagen. Erkläre in deinen Rezepten den Kochprozess gut und einfach aber ausführlich, damit jeder Mensch einfach das Rezept kochen kann. Das Rezept sollte schön angerichtet werden können und einen guten Geschmack beim Essen aufweisen.

KRITISCH - Dynamische Mengenangaben in Zubereitungsschritten:
Verwende in den Zubereitungsschritten AUSSCHLIESSLICH ID-basierte Platzhalter für Mengenangaben, damit sich diese automatisch an die Personenanzahl anpassen können.

VERWENDE NUR ID-BASIERTE PLATZHALTER:
Verwende ID-Nummern für Zutaten (1, 2, 3, etc. in der Reihenfolge der Zutatenliste):
- \${ID:1} = Komplette Angabe der ersten Zutat (z.B. "250g Mehl")

WICHTIG: Verwende NIEMALS \${MENGE:X} oder \${EINHEIT:X} - diese sind veraltet!
Verwende NIEMALS namens-basierte Platzhalter wie \${MEHL} oder \${OLIVENOEL}!
Verwende IMMER nur \${ID:X} mit der entsprechenden Nummer!

Beispiele für korrekte Zubereitungsschritte:
- "Geben Sie \${ID:1} in eine Schüssel." (erste Zutat komplett)
- "Erhitzen Sie \${ID:2} in einer Pfanne." (zweite Zutat komplett)
- "Fügen Sie \${ID:3} hinzu und braten Sie sie an." (dritte Zutat komplett)
- "Mit \${ID:4} würzen." (vierte Zutat komplett)

FALSCH: "Schneiden Sie \${MENGE:2} \${EINHEIT:2} Gurken"
RICHTIG: "Schneiden Sie \${ID:2} Gurken"

Die ID entspricht der Position in der Zutatenliste (1 = erste Zutat, 2 = zweite Zutat, etc.)
Das System ersetzt \${ID:2} automatisch mit "2 Stück" wenn das die zweite Zutat ist.`,
        },
        {
          role: "user",
          content: questionQuery
        }
      ],
      response_format: { type: "json_object" },
    });

    let output = await JSON.parse(completion.choices[0].message.content)
    helper.devConsole("🔍 Generator raw output:", output);

    // 🔧 KRITISCH: Verwende das gleiche Validierungssystem wie die anderen Generatoren
    try {
      // Validiere und korrigiere das AI-generierte Rezept
      const validatedRecipe = await validateAndFixAIRecipe(output);
      helper.devConsole('✅ Generator recipe validation successful');

      res.status(200).json({
        status: 'success',
        'data': [validatedRecipe] // Als Array zurückgeben für Kompatibilität mit Frontend
      });
    } catch (validationError) {
      helper.devConsole('❌ Generator recipe validation failed:', validationError.message);

      // Fallback: Return original output
      let flattenedOutput;
      if(output.output){
        helper.devConsole("output.output")
        flattenedOutput = output.output
      } else {
        helper.devConsole("output")
        flattenedOutput = output
      }

      res.status(200).json({
        status: 'success',
        'data': flattenedOutput
      });
    }


});

// @POST /functions/basic
exports.onlyChat = catchAsync(async (req, res, next) => {

  helper.devConsole("chat in gptController")

  helper.devConsole(req.body.data)

  try{

    const tools = [{
            type: "function",
            function: {
              name: "get_ordy_menus",
              description: "Search for ingredients",
              parameters: {
                  type: "object",
                  properties: {
                    ingridient: {
                        type: "string",
                        description: "Random ingridient or ingredients, such as tomato or cheese",
                    },
                  },
                  required: ["ingridient"],
              },
            }
        }];


    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });


    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: req.body.data,
      tools: tools,
      tool_choice: "auto"
    });

    helper.devConsole("test durch?")
    helper.devConsole(completion.choices[0])

    /*

    let responseMessage = await JSON.parse(completion.choices[0].message)

    const toolCalls = responseMessage.tool_calls;
    helper.devConsole(responseMessage.tool_calls)

    if(responseMessage.tool_calls){
      helper.devConsole("toolCalls true")
      helper.devConsole(toolCalls)
    }
    */


    res.status(200).json({
      success: true,
      data: completion.choices[0].message
    });

  } catch(err){
    res.status(500).json({
      success: false,
      error: err.message
    });
  }

});

// @POST
exports.createReciepsByIngridients = catchAsync(async (req, res, next) => {

  helper.devConsole("/////////////// POST gptController.createReciepsByIngridients /////////////////////")
  //helper.devConsole("drin")
  //helper.devConsole(req.body)

  try{

    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: req.body.chat,
      response_format: { type: "json_object" }
    });

    helper.devConsole(completion.choices)

    //data: completion.choises[0].message
    req.body.answerobject = {}
    req.body.answerobject.data = completion.choices[0].message
    next()
    /*res.status(200).json({
      status: 'success',
      'data': completion.choices[0].message
    });*/

  } catch(err) {
    helper.devConsole(err)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

});

// @POST
exports.proofIngridient = catchAsync(async (req, res, next) => {
  //console.log("loadImages arrives")

  helper.devConsole("proofIngridient in gptController")

  helper.devConsole(req.params.ingridient)

    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `
          Du bist ein Spezialist mit Einkaufszettel im Supermarkt. Du kennst alle Produkte im Supermarkt sehr gut und kannst basierend von Abkürzungen ziemlich genau erraten welches Produkt der Kunde möchte. Korrigiere also Fehler in der Rechtschreibung und schreibe Abkürzungen aus. Erstelle ein struktieriertes JSON Objekt.
          {
          amount // string, abgekürzte Einheit, zB g für Gramm oder l für Liter etc
          unit // number, Menge als Nummer, zB 1,2,3,300,0.1 etc
          name // string, Der Namen des Produktes ausgeschrieben
          date // today
          numberOfPersons // Anzahl Personen
          type // manual
          }
          For Example if someone gives you: Hello my name is Susane, I need a structured output: 1 Liter Milch, you give back the following:
          {
          unit: l
          amount: 1
          name: Milch
          date: 2024-05-28T22:00:00.000Z
          numberOfPersons: 0
          type: manual
          }
          Wenn keine Menge angegeben wurde, kannst du davon ausgehen, dass Stück oder Bund oder Pack gemeint ist.
          `,
        },
        {
          role: "user",
          content: req.params.ingridient
        }
      ],
      response_format: { type: "json_object" },
    });

      helper.devConsole(completion.choices[0].message.content)


    // get output from gpt object
    let output = await JSON.parse(completion.choices[0].message.content)

     // next
     req.body.answerobject = output
     next()

});

// @POST /functions/ingredients/nutritionalcalc
exports.createCalculationOfNutrions = catchAsync(async (req, res, next) => {
  console.log("[createCalculationOfNutrions] Starting...")
  console.log("[createCalculationOfNutrions] menuchild_created:", req.body.settings.menuchild_created)
  if(
    !req.body.settings.menuchild_created
  ){
      console.log("[createCalculationOfNutrions] Skipping - menuchild not created")
      next()
      return;
  } else {
    console.log("[createCalculationOfNutrions] Processing nutrition calculation")
    // check if object exists
    if(!req.body.ingredients){
      req.body.ingredients = req.body.menuchild.ingredients
    }

  }
  console.log("[createCalculationOfNutrions] Request body processed")

  let messageString = '';

  //Create GPT string
  for (let i = 0; i < req.body.ingredients.length; i++){
    messageString = `${messageString} ${req.body.ingredients[i].amount} ${req.body.ingredients[i].unit.name} ${req.body.ingredients[i].name.name}, `
  }


    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `
          "Bitte gib mir Daten über die Nährstoffzusammensetzung in einem spezifischen Format. Ich benötige ein JSON-Array mit Objekten für Protein, Kohlenhydrate und Fett. Jedes Objekt soll 'name', 'unit' und 'amount' enthalten. 'name' soll den Nährstoffnamen als String, 'unit' die Einheit als 'g' und 'amount' die Menge als Nummer enthalten.
          {
             nutritions: [
              {"name": "Protein", "unit": "g", "amount": 3.5},
              {"name": "Kohlenhydrate", "unit": "g", "amount": 4.9},
              {"name": "Fett", "unit": "g", "amount": 0.5}
            ]
          }
          `,
        },
        {
          role: "user",
          content: messageString
        }
      ],
      response_format: { type: "json_object" },
    });

    /*
    [
            {
              "name": "Protein", // string
              "unit": "g", // string
              "amount": x // number
            },
            {
              "name": "Kohlenhydrate", // string
              "unit": "g", // string
              "amount": x // number
            },
            {
              "name": "Fett", // string
              "unit": "g", // string
              "amount": x // number
            }
          ]
    */

      //helper.devConsole(completion.choices[0].message.content)

    // nex7
    //let output = await JSON.parse(completion.choices[0].message.content)
    if(
      !req.body.settings.menuchild_created
    ){
      // if req.body.settings.menuchild_created = false
      // process already ended at the end iside this function
    } else {
      // if req.body.settings.menuchild_created = true
      console.log("[createCalculationOfNutrions] Processing nutrition data")
      let newobject = await JSON.parse(completion.choices[0].message.content)
      req.body.answerobject = newobject.nutritions
      console.log("[createCalculationOfNutrions] Nutrition data:", req.body.answerobject)

      const updateChoosenMenuchildToTrue = await Menuchild.updateOne(
        {
          _id: req.body.menuchild._id
        },
        {
          $set: { nutritions: req.body.answerobject }
        }
      );
      console.log("[createCalculationOfNutrions] Database update result:", updateChoosenMenuchildToTrue)
      next()
      return;
    }

    req.body.answerobject = await JSON.parse(completion.choices[0].message.content)
    next()

});

////////////////////////// FRIST TRANSFORMATION STEP /////////////////////////////

//////// PARSE URL
// @POST /functions/reciept/createbyurl
exports.createRecieptByURL = catchAsync(async (req, res, next) => {
  helper.devConsole("createRecieptByURL in gptController")
  helper.devConsole(req.body.data)

  let testresult = ""
  req.body.settings.switch = 'forward'

  async function scrapeSite(url) {
    const { data } = await axios.get(url);
    const $ = cheerio.load(data);
    const result = $.root().text()
    // elim html
    testresult = $.root().text()
    // elim two or more spaces next to each other and add it to body
    req.body.reciepttext = testresult.replace(/\s{2,}/g, ' ');
    return result;
  }

  //console.log(req.body.zutaten[0].einheit)


  try{
    //Grab URL Content
    const newResult = await scrapeSite(req.body.data).then(result => {
        //console.log(result)
        helper.devConsole("drin")
    }).catch(err => helper.devConsole(err));

    /*
    helper.devConsole("------ 656 ----------")
    helper.devConsole(newResult)
    helper.devConsole(testresult)
    */

    // forward data to Open AI
   next()

  } catch(err) {
    helper.devConsole(err)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

});


//////// IMAGE TO TEXT
// @POST /functions/reciept/createbyimage
exports.createRecieptByImage = catchAsync(async (req, res, next) => {
  helper.devConsole("createRecieptByImage in gptController")
  //helper.devConsole(req.file)
  //helper.devConsole(req.file.mimetype)

  // Sicherstellen, dass eine Datei hochgeladen wurde
  if (!req.file) {
      // kein Menü erkannt
      res.status(201).json({
        success: false,
        'message': "Es wurde kein Bild erkannt."
      });
  }

  // Zugriff auf den Buffer der hochgeladenen Datei
  const fileBuffer = req.file.buffer;

  // Umwandlung des Buffers in einen Base64-String
  const base64Image = fileBuffer.toString('base64');
  const base64String = `data:image/jpeg;base64,${base64Image}`;
  helper.devConsole(base64Image)

  const { OpenAI } = require("openai");

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  try {
    const image = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Scanne das Bild nach Rezepten oder Menüs. Diese können als angerichtetes Gericht oder als Rezept in Textform im Bild auftreten. Notiere alle Informationen die zu finden sind (Alle Zutaten, Detailierte Zubereitung, Tipps und Tricks) und überführe alles in ein Rezept. Achte darauf alle Zutaten seperat auszuweisen und nicht zusammenzufassen. Falls du keine Zutaten in Textform erkennen kannst, melde 'Leider konnte kein Rezept erkannt werden'.",
            },
            {
              type: "image_url",
              image_url: {
                url: base64String,
              },
            },
          ],
        },
      ],
    });
    //helper.devConsole(image)

    if(image){
      // Den zurückgegebenen Text an den Client senden

      helper.devConsole("createRecieptByImage in gptController || image was send")
      helper.devConsole(image.choices[0]?.message.content)
      const resultText = image.choices[0]?.message.content || 'Leider konnte kein Rezept erkannt werden.';

      req.body.data = ''
      req.body.data = resultText

      // Set settings if not defined
      if(!req.body.settings){
        req.body.settings = {}
      }
      req.body.settings.switch = 'forward'

      // check if information (text) was found on the image
      helper.devConsole(resultText)
      helper.devConsole(resultText.includes("kein Rezept erkannt"))
      if(resultText.includes("kein Rezept erkannt")){
        // kein Menü erkannt
        helper.devConsole("kein Rezept erkannt")
        res.status(201).json({
          success: false,
          'message': "Leider konnte kein Menü auf dem Bild erkannt werden."
        });
      } else {
        // Next
        helper.devConsole("ein Rezept erkannt")
        next()
      }
    }



  } catch (err) {
    helper.devConsole(err);
    res.status(500).json({
      status: 'error',
      message: err.message,
    });
  }

});


////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////// CREATE MARKETING POST ////////////////////////////////

// Helper function to select random examples
const _selectRandomExamples = (examples, count) => {
  const shuffled = examples.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Function to generate a marketing post based on examples
exports.generateMarketingPostFromExamples = catchAsync(async (req, res, next) => {
  const functionName = 'gptController.generateMarketingPostFromExamples';
  helper.devConsole(functionName, 'Starting marketing post generation...');

  // 1. Fetch previous marketing texts (implement this based on your model)
  let previousTexts = [];
  try {
    // Example: Fetch last 10 hooks, panel1Texts, panel2Texts
    previousTexts = await MarketingContent.find({})
      .sort({ generationDate: -1 })
      .limit(10)
      .select('hook textOne textTwo -_id') // Select relevant fields
      .lean();
    helper.devConsole(functionName, `Found ${previousTexts.length} previous text sets.`);
  } catch (dbError) {
    helper.devConsole(functionName, `Error fetching previous texts: ${dbError}`);
    // Decide if you want to proceed without previous texts or return an error
  }

  // Format previous texts for the prompt
  const previousTextsString = previousTexts
    .map(t => `- Hook: ${t.hook}\n  Text1: ${t.textOne}\n  Text2: ${t.textTwo}`)
    .join('\n');
  helper.devConsole(functionName, 'Formatted previous texts for prompt:', previousTextsString);


  // 2. Get Recipe Name (passed via req.body or fetched)
  let recipeName = req.body.recipeName; // Assume it's passed in the request body for now
  if (!recipeName) {
     // If not passed, try fetching a random one as a fallback
     helper.devConsole(functionName, 'No recipeName in request body, fetching random recipe as fallback...');
      try {
          const randomRecipe = await _internal_fetchRandomFreeMenuData(); // Use the existing internal function
          if (randomRecipe && randomRecipe.name) {
              recipeName = randomRecipe.name;
              helper.devConsole(functionName, `Using fallback recipe: ${recipeName}`);
          } else {
               helper.devConsole(functionName, 'Fallback recipe fetching failed.');
               return next(new AppError('Recipe name is required and fallback failed.', 400));
          }
      } catch (fetchError) {
           helper.devConsole(functionName, `Error fetching fallback recipe: ${fetchError}`);
           return next(new AppError('Error fetching fallback recipe.', 500));
      }
  }

  // 3. Prepare the prompt using the template
  const prompt = MARKETING_POST_TEMPLATE
                    .replace('{recipeName}', recipeName)
                    .replace('{previousTextsString}', previousTextsString || 'Keine'); // Handle case with no previous texts

  helper.devConsole(functionName, 'Generated Prompt:', prompt);


  // 4. Call OpenAI API
  let rawCompletion;
  try {
    rawCompletion = await callOpenAIWithRetry(prompt); // Using retry mechanism
    helper.devConsole(functionName, 'Raw completion received from OpenAI:', rawCompletion);
  } catch (error) {
     helper.devConsole(functionName, `Error calling OpenAI: ${error}`);
     return next(new AppError('Failed to generate marketing text from OpenAI.', 500));
  }


  // 5. Parse the result
  let parsedResult;
  try {
    parsedResult = parseMarketingPostOutput(rawCompletion);
    helper.devConsole(functionName, 'Parsed Result:', parsedResult);
  } catch (error) {
     helper.devConsole(functionName, `Error parsing OpenAI response: ${error}`);
     // Consider how to handle parsing errors - maybe retry? Or return error?
     return next(new AppError(`Failed to parse marketing text response: ${error.message}`, 500));
  }

  // 6. Respond (if called as an API endpoint) or return (if called internally)
  // Check if res object exists and has methods like status/json
  if (res && typeof res.status === 'function' && typeof res.json === 'function') {
       helper.devConsole(functionName, 'Sending HTTP response.');
       res.status(200).json({
          status: 'success',
          data: parsedResult
      });
  } else {
       helper.devConsole(functionName, 'Returning parsed result (called internally).');
       return parsedResult; // Return the object if called by another function
  }

});

// Parses the raw string output from OpenAI for the marketing post
const parseMarketingPostOutput = (rawOutput) => {
  const functionName = 'gptController.parseMarketingPostOutput';
  helper.devConsole(functionName, 'Raw Output:', rawOutput);

  try {
    // Basic cleanup: Remove potential markdown code blocks and trim whitespace
    let cleanedOutput = rawOutput.replace(/```json\n?|\n?```/g, '').trim();
    helper.devConsole(functionName, 'Cleaned Output:', cleanedOutput);

    // Attempt to parse the cleaned string as JSON
    const parsed = JSON.parse(cleanedOutput);
    helper.devConsole(functionName, 'Parsed JSON:', parsed);

    // Validate the expected structure including new fields
    if (typeof parsed.hook === 'string' &&
        typeof parsed.textOne === 'string' &&
        typeof parsed.textTwo === 'string' &&
        typeof parsed.socialMediaTextMitLink === 'string' &&
        typeof parsed.socialMediaTextOhneLink === 'string') {
      helper.devConsole(functionName, 'Validation successful.');
      return {
        hook: parsed.hook.trim(),
        textOne: parsed.textOne.trim(),
        textTwo: parsed.textTwo.trim(),
        socialMediaTextMitLink: parsed.socialMediaTextMitLink.trim(),
        socialMediaTextOhneLink: parsed.socialMediaTextOhneLink.trim()
      };
    } else {
      helper.devConsole(functionName, 'Validation failed: Missing or invalid fields in parsed JSON (expected hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink).');
      throw new Error('Parsed JSON does not match the expected structure (hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink).');
    }
  } catch (error) {
    helper.devConsole(functionName, `Error parsing marketing post output: ${error.message}`);
    helper.devConsole(functionName, 'Attempting fallback extraction...');

    // Fallback: Regex to extract fields (less reliable)
    const hookMatch = rawOutput.match(/\"hook\":\\s*\"([^\"]*)\"/);
    const textOneMatch = rawOutput.match(/\"textOne\":\\s*\"([^\"]*)\"/);
    const textTwoMatch = rawOutput.match(/\"textTwo\":\\s*\"([^\"]*)\"/);
    const mitLinkMatch = rawOutput.match(/\"socialMediaTextMitLink\":\\s*\"([^\"]*)\"/);
    const ohneLinkMatch = rawOutput.match(/\"socialMediaTextOhneLink\":\\s*\"([^\"]*)\"/);

    if (hookMatch && hookMatch[1] &&
        textOneMatch && textOneMatch[1] &&
        textTwoMatch && textTwoMatch[1] &&
        mitLinkMatch && mitLinkMatch[1] &&
        ohneLinkMatch && ohneLinkMatch[1]) {
      helper.devConsole(functionName, 'Fallback extraction successful.');
      return {
        hook: hookMatch[1].trim(),
        textOne: textOneMatch[1].trim(),
        textTwo: textTwoMatch[1].trim(),
        socialMediaTextMitLink: mitLinkMatch[1].trim(),
        socialMediaTextOhneLink: ohneLinkMatch[1].trim()
      };
    } else {
      helper.devConsole(functionName, 'Fallback extraction failed.');
      throw new Error(`Failed to parse or extract marketing post data. Raw output: ${rawOutput}`);
    }
  }
};

// --- Prompts for OpenAI ---
const GENERATE_MARKETING_STORY_PROMPT_SYSTEM = `
Du bist ein kreativer Marketing-Assistent für Ordy, eine Rezept-App. Deine Aufgabe ist es, kurze, ansprechende Marketing-Texte zu generieren.
Die Texte sollen aus einem "Hook" (max. 5 Wörter, neugierig machend), "TextTeil1" (max. 15 Wörter, Problem/Situation beschreibend) und "TextTeil2" (max. 15 Wörter, Lösung mit Ordy andeutend) bestehen.
Zusätzlich erstelle zwei Social-Media-Texte:
1. "socialMediaTextMitLink": Ein Text (max. 25 Wörter), der einen Platzhalter "{recipeLinkPlaceholder}" enthält, der später durch einen echten Link ersetzt wird.
2. "socialMediaTextOhneLink": Ein kurzer, allgemeiner Text (max. 15 Wörter) ohne Link.
WICHTIG: Achte auf eine geschlechtsneutrale Sprache und sprich gezielt sowohl Frauen als auch Männer an. Verwende Formulierungen, die alle Geschlechter gleichermassen einschliessen.
Antworte IMMER im JSON-Format, genau so:
{"hook": "TEXT", "textOne": "TEXT", "textTwo": "TEXT", "socialMediaTextMitLink": "TEXT {recipeLinkPlaceholder}", "socialMediaTextOhneLink": "TEXT"}
`;

const GENERATE_MARKETING_STORY_PROMPT_USER_TEMPLATE = (lastStorys) => `
ERSTELLE EINE ABSOLUT NEUE UND EINZIGARTIGE MARKETING-STORY FÜR ORDY.
Sei maximal kreativ und weiche STARK von vorherigen Mustern ab.

Folgende Texte wurden bereits verwendet (DIESE NICHT WIEDERHOLEN ODER ÄHNLICH GESTALTEN!):
${lastStorys}

Deine neue Story muss sich thematisch und stilistisch deutlich von den obigen Beispielen unterscheiden.
Überrasche mit einer frischen Idee!
`;

// Updated prompt with stronger negative constraints at the end
const GENERATE_IMAGES_PROMPT_TEMPLATE = (hook, panel1Text) => `
Fotorealistisches Bild einer modernen, hellen Küche oder eines Essbereichs.
Der Fokus liegt auf alltäglichen Küchengegenständen, Lebensmitteln, Zutaten oder fertig zubereiteten Gerichten, die zum Thema "${hook}" und zur Szene "${panel1Text}" passen.
Stil: Natürlich, hochwertig, Food-Blog-Ästhetik, leicht unscharfer Hintergrund (Bokeh), exzellente Ausleuchtung.
KEINE Menschen, KEINE Tiere, KEINE Comic-Elemente, KEINE übertriebenen Darstellungen, KEINE Fantasie-Elemente.
Das Bild soll authentisch und einladend wirken, wie aus einem modernen Kochbuch oder Food-Magazin.
Atmosphäre: Warm, gemütlich, appetitlich.
`;

// --- End Prompts ---

////////////////////////////// CREATE MARKETING STORY ////////////////////////////////

// Removed catchAsync wrapper as this is called internally
exports.generateMarketingStory = async () => {
  const functionName = 'gptController.generateMarketingStory';
  helper.devConsole(functionName, "Generating new marketing story...");

  // --- Fetch last 10 stories (weniger als vorher) --- START
  let lastStorysString = 'Keine'; // Default value if no stories are found
  try {
    const previousStories = await MarketingContent.find({})
      .sort({ generationDate: -1 })
      .limit(10) // Nur die letzten 10 als Kontext
      .select('textOne textTwo -_id') // Select only textOne and textTwo
      .lean();

    if (previousStories && previousStories.length > 0) {
      lastStorysString = previousStories
        .map((story, index) => `- Story ${index + 1}: ${story.textOne || ''} ${story.textTwo || ''}`)
        .join('\n');
       helper.devConsole(functionName, `Formatted last ${previousStories.length} stories for prompt:\n${lastStorysString}`);
    } else {
       helper.devConsole(functionName, "No previous stories found in the database.");
    }
  } catch (dbError) {
    helper.devConsole(functionName, `WARN: Error fetching previous stories: ${dbError.message}. Proceeding without them.`);
    // Keep default 'Keine'
  }
  // --- Fetch last 15 stories --- END

  const { OpenAI } = require("openai");
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  const userPrompt = GENERATE_MARKETING_STORY_PROMPT_USER_TEMPLATE(lastStorysString);
  let rawGptResponseContent = null; // Variable to store raw response

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Or preferred model
      messages: [
        {
          role: "system",
          content: GENERATE_MARKETING_STORY_PROMPT_SYSTEM,
        },
        {
          role: "user",
          content: userPrompt,
        },
      ],
      response_format: { type: "json_object" },
      temperature: 1.1, // Erhöhte Temperatur für mehr Kreativität
      max_tokens: 250, // Etwas mehr Tokens für potenziell längere, aber immer noch kurze Variationen
    });

    // Store the raw content before parsing
    rawGptResponseContent = completion?.choices[0]?.message?.content;
    if (!rawGptResponseContent) {
        helper.devConsole(functionName, "ERROR: Received empty or invalid content from OpenAI", completion);
        throw new Error("Received empty or invalid content from OpenAI.");
    }
    helper.devConsole(functionName, "Raw GPT Response Content:", rawGptResponseContent);

    const resultJson = JSON.parse(rawGptResponseContent);
    helper.devConsole(functionName, "Parsed Story JSON:", resultJson);

    // Basic validation - CORRECTED TO CHECK FOR textOne, textTwo and new fields
    if (!resultJson ||
        !resultJson.hook ||
        !resultJson.textOne || // Check for textOne
        !resultJson.textTwo || // Check for textTwo
        !resultJson.socialMediaTextMitLink || // Check for new field
        !resultJson.socialMediaTextOhneLink   // Check for new field
       ) {
      helper.devConsole(functionName, "ERROR: Generated JSON is missing required keys (hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink).", resultJson);
      // Corrected error message to reflect actual checked keys
      throw new Error(`Generated JSON is missing required keys (hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink). Received: ${JSON.stringify(resultJson)}`);
    }

    return resultJson; // Return { hook, textOne, textTwo, socialMediaTextMitLink, socialMediaTextOhneLink }

  } catch (error) {
    helper.devConsole(functionName, "ERROR during marketing story generation:", error);
    if (error instanceof SyntaxError) { // JSON parsing error
         helper.devConsole(functionName, "ERROR: Failed to parse GPT response as JSON. Raw Content:", rawGptResponseContent);
         throw new Error(`Failed to parse GPT response as JSON. Raw Response: ${rawGptResponseContent || '{No content received}'}`);
    } else if (error.message.includes("Received empty or invalid content")) {
         // Rethrow error from the check above
         throw error;
    } else if (error.message.includes("missing required keys")) {
         // Rethrow validation error from above
         throw error;
    }
    // General API or other errors
    throw new Error(`Failed to generate marketing story due to API or other error: ${error.message}`);
  }
};


////////////////////////////// CREATE IMAGES //////////////////////////////////////////

// Removed catchAsync wrapper as this is called internally
// Modified to accept hook, call API multiple times with prompt variations
exports.generateImagesFromPrompt = async (hook, promptText, numberOfImages = 1) => {
    // --- Robust Logging Start ---
    helper.devConsole(`[gptController.generateImagesFromPrompt] ENTERED. Hook: "${hook}", Prompt: "${promptText.substring(0,30)}..." Requesting ${numberOfImages} images.`);
    // --- Robust Logging End ---
    const functionName = 'gptController.generateImagesFromPrompt';
    // Log using helper.devConsole too
    helper.devConsole(functionName, `Generating ${numberOfImages} images individually. Hook="${hook}", BasePrompt="${promptText.substring(0, 50)}..."`);

    if (!promptText) {
        console.error("[gptController.generateImagesFromPrompt] ERROR: No prompt text (panel1Text) provided.");
        throw new Error('No prompt text (panel1Text) provided for image generation.');
    }
    // Hook is optional context, don't throw error if missing, but log
    if (!hook) {
        console.warn("[gptController.generateImagesFromPrompt] WARN: No hook text provided, generating images based on promptText only.");
    }
    if (numberOfImages <= 0) {
        chelper.devConsole("[gptController.generateImagesFromPrompt] INFO: Requested 0 images, returning empty array.");
        return []; // Return empty array if 0 images requested
    }

    const { OpenAI } = require("openai");
    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    // --- Generate images one by one using Promise.all for concurrency ---
    const imagePromises = [];
    // Stärkere Variation Hints für realistischere Bilder
    const variationHints = [
        ", aus leicht anderer Perspektive",
        ", mit anderem Lichteinfall, natürliche Beleuchtung",
        ", anderer Bildausschnitt, Fokus auf Details",
        ", sanfte Schatten, weiches Licht"
    ];

    for (let i = 0; i < numberOfImages; i++) {
        // Use the template for the base prompt
        const basePrompt = GENERATE_IMAGES_PROMPT_TEMPLATE(hook || "", promptText); // Use hook if available
        // Add variation hint (cycle through hints if more than 4 images)
        const variationHint = variationHints[i % variationHints.length];
        const finalPrompt = `${basePrompt}${variationHint}`;

        helper.devConsole(functionName, `Using image generation prompt [${i+1}/${numberOfImages}]: "${finalPrompt.substring(0, 150)}..."`);

        const promise = openai.images.generate({
            model: "dall-e-3",
            prompt: finalPrompt,
            n: 1,
            size: "1024x1792",
            response_format: "b64_json",
            quality: "hd", // Fordert höhere Qualität an, falls vom Modell unterstützt
            style: "natural" // Fordert einen natürlicheren Stil an, falls vom Modell unterstützt
        }).catch(err => {
             // Catch individual errors
             helper.devConsole(functionName, `ERROR generating image ${i + 1} with prompt "${finalPrompt.substring(0,50)}...":`, err);
             console.error(`[gptController.generateImagesFromPrompt] ERROR generating image ${i + 1}:`, err);
             return null; // Return null on error for this specific image
        });
        imagePromises.push(promise);
    }

    try {
        const results = await Promise.all(imagePromises);
        helper.devConsole(functionName, `Received ${results.length} responses from API calls.`);

        // Process results: Convert b64_json to buffer and structure output
        const imageResults = results.map((response, index) => {
             // Check if the individual promise failed (returned null)
             if (!response) {
                 helper.devConsole(functionName, `WARN: Skipping image ${index + 1} due to generation error.`);
                 return null;
             }
             // Check the structure of the successful response
             if (!response.data || response.data.length !== 1 || !response.data[0].b64_json) {
                 helper.devConsole(functionName, `WARN: Missing or invalid b64_json for image ${index + 1}. Response:`, response);
                 return null; // Indicate failure for this image
             }

             try {
                 const buffer = Buffer.from(response.data[0].b64_json, 'base64');
                 return { data: buffer, contentType: 'image/png' }; // Assuming PNG
             } catch (bufferError) {
                 helper.devConsole(functionName, `WARN: Error converting b64_json to buffer for image ${index + 1}:`, bufferError);
                 return null;
             }
        }).filter(result => result !== null); // Filter out any failures

        helper.devConsole(functionName, `Successfully processed ${imageResults.length} out of ${numberOfImages} requested images.`);

        // --- Robust Logging Start ---
        console.log(`[gptController.generateImagesFromPrompt] RETURNING ${imageResults.length} results. Result array:`, imageResults);
        // --- Robust Logging End ---

        return imageResults; // Returns array like [{data: Buffer, contentType: 'image/png'}, ...]

    } catch (error) {
        // This catch block might be less likely to be hit now due to individual catches,
        // but keep it for safety (e.g., Promise.all internal errors?)
        console.error("[gptController.generateImagesFromPrompt] ERROR processing results:", error);
        helper.devConsole(functionName, "Error processing image generation results:", error);
        throw new Error(`Failed to process image generation results: ${error.message}`);
    }
};

////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////// PROFILE IMAGE MANAGEMENT ////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////

// Configure multer for profile image upload
const profileImageStorage = multer.memoryStorage();
const profileImageUpload = multer({
    storage: profileImageStorage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new AppError('Nur Bilddateien sind erlaubt!', 400), false);
        }
    }
}).single('profileImage');

//@POST /profile/image/upload
exports.uploadProfileImage = catchAsync(async (req, res, next) => {
    const functionName = 'gptController.uploadProfileImage';
    helper.devConsole(functionName, "Starting profile image upload...");

    // Handle multer upload
    profileImageUpload(req, res, async (err) => {
        if (err) {
            helper.devConsole(functionName, "Multer error:", err);
            return next(new AppError('Fehler beim Datei-Upload: ' + err.message, 400));
        }

        if (!req.file) {
            return next(new AppError('Keine Datei hochgeladen!', 400));
        }

        try {
            // Get user ID from auth middleware
            const userId = req.user._id || req.user.id;
            if (!userId) {
                return next(new AppError('Benutzer nicht authentifiziert.', 401));
            }

            // Process image with Jimp (resize, optimize)
            const image = await Jimp.read(req.file.buffer);
            const processedImageBuffer = await image
                .resize(400, 400)
                .quality(85)
                .getBufferAsync(Jimp.MIME_JPEG);

            // Generate unique filename
            const fileName = `profile_${userId}_${Date.now()}.jpg`;

            // Prepare file object for S3 upload
            const file = {
                name: fileName,
                body: processedImageBuffer,
                imageType: 'image/jpeg'
            };

            // Upload to S3
            helper.devConsole(functionName, `Uploading ${fileName} to S3...`);
            const uploadResult = await uploadFileByLink(file);

            if (!uploadResult || (!uploadResult.Location && !uploadResult.ETag)) {
                throw new Error('S3 Upload fehlgeschlagen');
            }

            // Update user profile with new image
            const updatedUser = await User.findByIdAndUpdate(
                userId,
                {
                    img: fileName,
                    profileImageUpdatedAt: new Date()
                },
                { new: true, select: 'img firstName lastName email' }
            );

            if (!updatedUser) {
                return next(new AppError('Benutzer nicht gefunden.', 404));
            }

            helper.devConsole(functionName, "Profile image uploaded successfully");

            // Send response
            res.status(200).json({
                status: 'success',
                message: 'Profilbild erfolgreich hochgeladen',
                data: {
                    user: updatedUser,
                    imageUrl: `https://ordy-images.s3.amazonaws.com/${fileName}`
                }
            });

        } catch (error) {
            helper.devConsole(functionName, "Error:", error);
            return next(new AppError('Fehler beim Verarbeiten des Profilbildes: ' + error.message, 500));
        }
    });
});

//@POST /profile/image/generate
exports.generateProfileImage = catchAsync(async (req, res, next) => {
    const functionName = 'gptController.generateProfileImage';
    helper.devConsole(functionName, "Starting AI profile image generation...");

    try {
        // Get user ID from auth middleware
        const userId = req.user._id || req.user.id;
        if (!userId) {
            return next(new AppError('Benutzer nicht authentifiziert.', 401));
        }

        // Get user data for personalized prompt
        const user = await User.findById(userId).select('firstName lastName');
        if (!user) {
            return next(new AppError('Benutzer nicht gefunden.', 404));
        }

        // Extract prompt and style from request
        const { prompt, style = 'professional' } = req.body;

        // Create personalized prompt
        let aiPrompt;
        if (prompt) {
            aiPrompt = `${prompt}, ${style} style, high quality portrait`;
        } else {
            // Default prompt based on user name
            aiPrompt = `Professional portrait of a person named ${user.firstName}, ${style} style, friendly expression, high quality, clean background`;
        }

        helper.devConsole(functionName, `Generating image with prompt: ${aiPrompt}`);

        // Generate image with OpenAI DALL-E
        const { OpenAI } = require("openai");
        const openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });

        const response = await openai.images.generate({
            model: "dall-e-3",
            prompt: aiPrompt,
            n: 1,
            size: "1024x1024",
            quality: "standard"
        });

        const imageUrl = response.data[0].url;
        if (!imageUrl) {
            throw new Error('Keine Bild-URL von OpenAI erhalten');
        }

        helper.devConsole(functionName, "Image generated successfully");

        // Download and process the generated image
        const image = await Jimp.read(imageUrl);

        // Resize to profile image size
        const processedImage = await image
            .resize(400, 400)
            .quality(85)
            .getBufferAsync(Jimp.MIME_JPEG);

        // Generate unique filename
        const fileName = `profile_ai_${userId}_${Date.now()}.jpg`;

        // Prepare file object for S3 upload
        const file = {
            name: fileName,
            body: processedImage,
            imageType: 'image/jpeg'
        };

        // Upload to S3
        helper.devConsole(functionName, `Uploading ${fileName} to S3...`);
        const uploadResult = await uploadFileByLink(file);

        if (!uploadResult || (!uploadResult.Location && !uploadResult.ETag)) {
            throw new Error('S3 Upload fehlgeschlagen');
        }

        // Update user profile with new AI-generated image
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            {
                img: fileName,
                profileImageUpdatedAt: new Date(),
                profileImageSource: 'ai_generated'
            },
            { new: true, select: 'img firstName lastName email' }
        );

        helper.devConsole(functionName, "AI profile image generated and saved successfully");

        // Send response
        res.status(200).json({
            status: 'success',
            message: 'KI-Profilbild erfolgreich generiert',
            data: {
                user: updatedUser,
                imageUrl: `https://ordy-images.s3.amazonaws.com/${fileName}`,
                prompt: aiPrompt
            }
        });

    } catch (error) {
        helper.devConsole(functionName, "Error:", error);
        return next(new AppError('Fehler beim Generieren des KI-Profilbildes: ' + error.message, 500));
    }
});

// Automatic profile image generation for new users
exports.generateProfileImageForNewUser = async (userId, firstName) => {
    const functionName = 'gptController.generateProfileImageForNewUser';
    helper.devConsole(functionName, `Generating profile image for new user: ${userId}`);

    try {
        // Create a generic avatar prompt without using the actual name
        const aiPrompt = `Professional avatar portrait, friendly expression, clean background, modern style, high quality, suitable for profile picture`;

        helper.devConsole(functionName, `Generating image with prompt: ${aiPrompt}`);

        // Generate image with OpenAI DALL-E
        const { OpenAI } = require("openai");
        const openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });

        const response = await openai.images.generate({
            model: "dall-e-3",
            prompt: aiPrompt,
            n: 1,
            size: "1024x1024",
            quality: "standard"
        });

        const imageUrl = response.data[0].url;
        if (!imageUrl) {
            throw new Error('Keine Bild-URL von OpenAI erhalten');
        }

        helper.devConsole(functionName, "Image generated successfully");

        // Download and process the generated image
        const image = await Jimp.read(imageUrl);

        // Resize to profile image size
        const processedImage = await image
            .resize(400, 400)
            .quality(85)
            .getBufferAsync(Jimp.MIME_JPEG);

        // Generate unique filename
        const fileName = `profile_auto_${userId}_${Date.now()}.jpg`;

        // Prepare file object for S3 upload
        const file = {
            name: fileName,
            body: processedImage,
            imageType: 'image/jpeg'
        };

        // Upload to S3
        helper.devConsole(functionName, `Uploading ${fileName} to S3...`);
        const uploadResult = await uploadFileByLink(file);

        if (!uploadResult || (!uploadResult.Location && !uploadResult.ETag)) {
            throw new Error('S3 Upload fehlgeschlagen');
        }

        // Update user profile with new AI-generated image
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            {
                img: fileName,
                profileImageUpdatedAt: new Date(),
                profileImageSource: 'ai_generated'
            },
            { new: true, select: 'img firstName lastName email' }
        );

        helper.devConsole(functionName, "Auto profile image generated and saved successfully");

        return {
            success: true,
            fileName: fileName,
            imageUrl: `https://ordy-images.s3.amazonaws.com/${fileName}`
        };

    } catch (error) {
        helper.devConsole(functionName, "Error:", error);
        // Don't throw error for automatic generation - just log it
        return {
            success: false,
            error: error.message
        };
    }
};

///////// CATEGORIZE INGREDIENT WITH AI
//@POST /creator/functions/ingredients/categorize
exports.categorizeIngredientWithAI = catchAsync(async (req, res, next) => {
    const functionName = 'categorizeIngredientWithAI';
    helper.devConsole(functionName, 'Starting AI categorization');

    const { input, name, quantity, unit } = req.body;

    if (!input && !name) {
        return next(new AppError('Input text or ingredient name is required', 400));
    }

    try {
        // Available categories from the shopping list model
        const categories = [
            'Gemüse & Früchte',
            'Brotwaren & Backwaren',
            'Milchprodukte & Molkereiprodukte',
            'Fleisch, Wurst & Fisch',
            'Tiefkühlprodukte',
            'Grundnahrungsmittel',
            'Frühstück & Cerealien',
            'Süsswaren & Snacks',
            'Getränke',
            'Non-Food & Haushaltsartikel',
            'Sonstiges'
        ];

        const { OpenAI } = require("openai");
        const openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });

        const prompt = `Du bist ein Experte für Lebensmittelkategorisierung.

Analysiere die folgende Eingabe und extrahiere:
1. Den Zutatennamen (ohne Mengenangaben)
2. Die Menge (falls vorhanden)
3. Die Einheit (falls vorhanden)
4. Die passende Kategorie

Eingabe: "${input || name}"

Verfügbare Kategorien:
${categories.map((cat, index) => `${index + 1}. ${cat}`).join('\n')}

WICHTIG: Wähle IMMER eine passende Kategorie aus der Liste aus. Verwende "Sonstiges" nur als allerletzte Option.

Antworte AUSSCHLIESSLICH im folgenden JSON-Format:
{
  "name": "Zutatennamen hier",
  "quantity": "Menge hier oder null",
  "unit": "Einheit hier oder null",
  "category": "Exakte Kategorie aus der Liste"
}

Beispiele:
- "500ml Rahm" → {"name": "Rahm", "quantity": "500", "unit": "ml", "category": "Milchprodukte & Molkereiprodukte"}
- "2 Äpfel" → {"name": "Äpfel", "quantity": "2", "unit": "Stk", "category": "Gemüse & Früchte"}
- "100g Zucker" → {"name": "Zucker", "quantity": "100", "unit": "g", "category": "Grundnahrungsmittel"}
- "Brot" → {"name": "Brot", "quantity": null, "unit": null, "category": "Brotwaren & Backwaren"}
- "Mehl" → {"name": "Mehl", "quantity": null, "unit": null, "category": "Grundnahrungsmittel"}`;

        const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo", // Cheapest model
            messages: [
                {
                    role: "system",
                    content: "Du bist ein Experte für deutsche Lebensmittelkategorisierung. Antworte immer im exakten JSON-Format ohne zusätzlichen Text."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.1, // Low temperature for consistent results
            max_tokens: 200
        });

        const aiResponse = completion.choices[0].message.content.trim();
        helper.devConsole(functionName, `AI Response: ${aiResponse}`);

        // Parse the JSON response
        let parsedResult;
        try {
            parsedResult = JSON.parse(aiResponse);
        } catch (parseError) {
            helper.devConsole(functionName, `Failed to parse AI response: ${parseError}`);
            // Fallback to default categorization
            parsedResult = {
                name: name || input,
                quantity: quantity || null,
                unit: unit || null,
                category: 'Sonstiges'
            };
        }

        // Validate category exists in our enum
        if (!categories.includes(parsedResult.category)) {
            helper.devConsole(functionName, `Invalid category from AI: ${parsedResult.category}, defaulting to Sonstiges`);
            parsedResult.category = 'Sonstiges';
        }

        helper.devConsole(functionName, `Categorization result: ${JSON.stringify(parsedResult)}`);

        req.body.data = {
            success: true,
            result: parsedResult,
            originalInput: input || name
        };

        next();

    } catch (error) {
        helper.devConsole(functionName, `Error: ${error.message}`);
        return next(new AppError(`AI categorization failed: ${error.message}`, 500));
    }
});