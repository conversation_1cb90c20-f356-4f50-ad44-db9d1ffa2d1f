'use strict';

const path = require('path');
const nodeExternals = require('webpack-node-externals');

module.exports = {
  target: 'node',
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  entry: {
    server: './server.js',
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
  },
  node: {
    // Ziel ist eine korrekte NodeJS-Bundle-Ausgabe
    __dirname: false,
    __filename: false,
  },
  externals: [nodeExternals()], // Um alle node_modules in node_externals zu bündeln
  module: {
    rules: [
      {
        // JS-Dateien mit Babel transpilieren (falls nötig)
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      }
    ]
  },
  resolve: {
    extensions: ['.js'],
  }
}; 