<template>
    <div 
        class="rounded-2xl bg-white h-auto w-full"
    >
        <!-- up -->
        <div class="w-full h-28 mt-8 p-6 flex flex-row font-bold text-xs">
            {{ element.boxdescription }}
        </div>

        <!-- select -->
        <div class="w-full rounded-2xl border-solid border-black h-12 mb-0 pt-3 pb-0 border-2 flex flex-row">
            <!-- selected values -->
            <span class="text-xs uppercase px-4 font-bold font-OpenSans align-middle text-center w-10/12 flex flex-row gap-1">
                <div v-for="selectedvalues in element.boxvalues" v-bind:key="selectedvalues.id" class="">
                    <span v-if="selectedvalues.selected == true" class="w-1/12">{{ selectedvalues.item }}</span>
                </div>
            </span>
            
            <div class="w-2/12" @click="openMenu">
                <img src="../assets/icons/select.png" alt="open" />
            </div>
        </div>

        <div v-if="stateMenu" class="text-xs mt-4 uppercase pb-3 px-4 h-auto font-bold font-OpenSans align-middle text-center w-10/12 flex flex-col">
            <div v-for="selectelement in element.boxvalues" v-bind:key="selectelement.id" class="inline-flex items-center py-1" >
                <label class="inline-flex items-center py-1">
                    <input type="checkbox" class="form-checkbox h-4 w-4 text-gray-600" v-model="selectelement.selected" :value='selectelement.item'>
                    <span class="ml-2 text-gray-700 text-left text-xs">{{ selectelement.item }}</span>
                </label>
            </div>
        </div>

    </div>
</template>
<script setup>
    import { ref } from 'vue';

    defineProps({
        element: Object,
        index: Number,
    })

    const stateMenu = ref(false);

    const openMenu = () => {
        if(import.meta.env.VITE_ENV == "development"){ console.log("open up menu") }
        stateMenu.value = !stateMenu.value;
    }
</script>
<style>
/*
    .imglinkcss {
        background-image: v-bind('element.imagelink');
    }
*/
</style>

