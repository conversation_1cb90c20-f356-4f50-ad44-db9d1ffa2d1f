case "session.update":
    // 1. Loggen der empfangenen Nachricht vom Client (für Debugging)
    console.log('Backend: Received session.update from client:', JSON.stringify(clientMessage, null, 2));

    // 2. Extrahieren des session-Objekts und optional des payloads vom Client
    const clientSessionConfig = clientMessage.session;
    const clientPayload = clientMessage.payload; // Kann recipe_data enthalten

    if (!clientSessionConfig) {
        console.error('Backend: session.update from client is missing the session object.');
        // Ggf. Fehler an Client senden
        if (wsToClient && wsToClient.readyState === WebSocket.OPEN) {
            wsToClient.send(JSON.stringify({ event: "backend_error", data: { message: "session.update is missing session object" } }));
        }
        return; // Oder break, je nach Struktur der Fehlerbehandlung im Switch
    }

    // 3. Vorbereiten des session-Objekts für OpenAI
    // Grundsätzlich die Konfiguration vom Client übernehmen.
    const openAISessionConfig = { ...clientSessionConfig };

    // 3a. (Optional) Beispielhafte Anreicherung der 'instructions' durch das Backend,
    // falls clientPayload und clientPayload.recipe_data vorhanden sind.
    // Diese Logik sollte an die spezifischen Bedürfnisse angepasst werden,
    // z.B. ob clientseitige Instructions priorisiert oder immer ergänzt werden sollen.
    if (clientPayload && clientPayload.recipe_data && clientPayload.recipe_data.name) {
        const recipeContext = `Zusätzlicher Kontext aus dem Backend für Rezept: '${clientPayload.recipe_data.name}'. Beschreibung: ${clientPayload.recipe_data.description || 'Keine Beschreibung vorhanden'}. `;
        // Variante 1: Backend-Kontext voranstellen (wenn Client-Instructions vorhanden)
        openAISessionConfig.instructions = recipeContext + (openAISessionConfig.instructions || "Du bist ein hilfreicher Assistent.");
        // Variante 2: Nur wenn keine Client-Instructions da sind (als Fallback)
        // if (!openAISessionConfig.instructions) {
        //    openAISessionConfig.instructions = recipeContext + "Du bist ein hilfreicher Assistent.";
        // }
        console.log('Backend: Enriched instructions with recipe data from payload.');
    }

    // WICHTIG: Sicherstellen, dass 'instructions' vorhanden ist.
    // Das Frontend sollte es immer senden, aber als Sicherheitsnetz:
    if (!openAISessionConfig.instructions) {
        openAISessionConfig.instructions = "Du bist ein hilfreicher Assistent."; // Fallback
        console.warn("Backend: 'instructions' was missing in client session.update and not enriched. Using default.");
    }
    
    // 4. Erstellen der Nachricht für OpenAI
    const messageToOpenAI = {
        type: "session.update",
        session: openAISessionConfig
        // Das 'payload' Feld vom Client wird i.d.R. nicht direkt an OpenAI weitergeleitet.
        // Die relevanten Infos (wie Rezeptdaten) sollten in 'openAISessionConfig.instructions' verarbeitet sein.
    };

    // 5. Senden an OpenAI (nur wenn die Verbindung zu OpenAI besteht und bereit ist)
    // Es wird angenommen, dass eine Variable wie 'isOpenAIReady' den Status der OpenAI-Verbindung widerspiegelt,
    // insbesondere ob die initiale OpenAI-Session bereits erfolgreich mit 'session.created' oder 'session.updated' bestätigt wurde.
    // Ohne diese Bestätigung könnte 'session.update' fehlschlagen oder ignoriert werden.
    if (wsToOpenAI && wsToOpenAI.readyState === WebSocket.OPEN /* && isOpenAIReady (oder ähnliche Prüfung) */) {
        console.log('Backend: Sending session.update to OpenAI:', JSON.stringify(messageToOpenAI, null, 2));
        wsToOpenAI.send(JSON.stringify(messageToOpenAI));
    } else {
        console.error('Backend: OpenAI WebSocket not open or not ready. Cannot send session.update.');
        // Optional: Nachricht in eine Queue für spätere Sendung legen, wenn die Verbindung wiederhergestellt ist.
        // Für 'session.update' ist dies jedoch oft zeitkritisch.
        if (wsToClient && wsToClient.readyState === WebSocket.OPEN) {
            wsToClient.send(JSON.stringify({ event: "backend_error", data: { message: "OpenAI connection not ready for session update." } }));
        }
    }
    break; 