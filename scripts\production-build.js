#!/usr/bin/env node

/**
 * Production Build Script
 * 
 * This script prepares the application for production deployment by:
 * 1. Removing console.log statements from production builds
 * 2. Validating environment variables
 * 3. Running security checks
 * 4. Building optimized bundles
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting production build process...');

// Check if we're in production mode
const NODE_ENV = process.env.NODE_ENV || process.env.VITE_ENV;
if (NODE_ENV !== 'production') {
  console.warn('⚠️  Warning: NODE_ENV is not set to "production"');
  console.warn(`   Current environment: ${NODE_ENV}`);
}

// Function to remove console.log statements from files
function removeConsoleLogs(filePath) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Remove console.log, console.warn, console.info (but keep console.error)
  content = content.replace(/console\.(log|warn|info)\([^)]*\);?\s*/g, '');
  
  // Remove devConsole calls
  content = content.replace(/helper\.devConsole\([^)]*\);?\s*/g, '');
  content = content.replace(/devConsole\([^)]*\);?\s*/g, '');
  
  fs.writeFileSync(filePath, content, 'utf8');
}

// Function to recursively process files
function processDirectory(dirPath, extensions = ['.js', '.vue', '.ts']) {
  if (!fs.existsSync(dirPath)) {
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', 'dist', '.git', '.vscode'].includes(item)) {
        processDirectory(fullPath, extensions);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(fullPath);
      if (extensions.includes(ext)) {
        console.log(`Processing: ${fullPath}`);
        removeConsoleLogs(fullPath);
      }
    }
  }
}

// Only run console.log removal in production
if (NODE_ENV === 'production') {
  console.log('🧹 Removing console.log statements for production...');
  
  // Process source files
  processDirectory('./src');
  processDirectory('./utils');
  
  console.log('✅ Console.log statements removed');
} else {
  console.log('ℹ️  Skipping console.log removal (not in production mode)');
}

// Validate critical environment variables
console.log('🔍 Validating environment variables...');

const requiredEnvVars = [
  'VITE_API_BASE_URL',
  'VITE_API_BASE_WS_URL',
  'VITE_ENV'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  process.exit(1);
}

console.log('✅ Environment variables validated');

// Run the build
console.log('📦 Building application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Security check
console.log('🔒 Running security audit...');
try {
  execSync('npm audit --audit-level=high', { stdio: 'inherit' });
  console.log('✅ Security audit passed');
} catch (error) {
  console.warn('⚠️  Security audit found issues. Please review.');
}

console.log('🎉 Production build process completed!');
console.log('');
console.log('📋 Deployment checklist:');
console.log('   ✅ Console.log statements removed');
console.log('   ✅ Environment variables validated');
console.log('   ✅ Application built');
console.log('   ✅ Security audit completed');
console.log('');
console.log('🚀 Ready for deployment!');
