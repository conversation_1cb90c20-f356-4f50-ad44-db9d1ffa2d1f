import axios from 'axios';
import { useUserStore } from '../store/userStore';
import { useHelperStore } from '../../utils/helper';

// Konstanten für Session-Management
const SESSION_TOKEN_KEY = 'session_token';
const USER_ID_KEY = 'id';
const LAST_ROUTE_KEY = 'last_route';
const TOKEN_REFRESH_INTERVAL = 12 * 60 * 60 * 1000; // 12 Stunden in Millisekunden
const TOKEN_REFRESH_THRESHOLD = 24 * 60 * 60 * 1000; // 24 Stunden in Millisekunden

// Variablen für Intervall-Management
let refreshInterval = null;
let lastRefreshTimestamp = null;

/**
 * Initialisiert das Session-Management
 */
export function initSessionManagement() {
  const helper = useHelperStore();
  helper.devConsole('Initializing session management');

  // Letzte Aktualisierungszeit aus localStorage laden
  lastRefreshTimestamp = localStorage.getItem('last_token_refresh');

  // Prüfen, ob ein Token-Refresh nötig ist
  checkAndRefreshToken();

  // Intervall für regelmäßige Token-Aktualisierung einrichten
  if (!refreshInterval) {
    refreshInterval = setInterval(() => {
      checkAndRefreshToken();
    }, TOKEN_REFRESH_INTERVAL);

    helper.devConsole('Token refresh interval set up');
  }

  // Event-Listener für App-Aktivierung hinzufügen
  document.addEventListener('visibilitychange', handleVisibilityChange);
}

/**
 * Beendet das Session-Management
 */
export function cleanupSessionManagement() {
  const helper = useHelperStore();

  // Intervall löschen
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
    helper.devConsole('Token refresh interval cleared');
  }

  // Event-Listener entfernen
  document.removeEventListener('visibilitychange', handleVisibilityChange);
}

/**
 * Behandelt Änderungen der Sichtbarkeit der App
 */
function handleVisibilityChange() {
  const helper = useHelperStore();

  if (document.visibilityState === 'visible') {
    helper.devConsole('App became visible, checking token');
    checkAndRefreshToken();
  }
}

/**
 * Prüft, ob ein Token-Refresh nötig ist und führt ihn ggf. durch
 */
async function checkAndRefreshToken() {
  const helper = useHelperStore();
  const userStore = useUserStore();

  // Prüfen, ob ein Session-Token existiert
  const sessionToken = localStorage.getItem(SESSION_TOKEN_KEY);
  if (!sessionToken) {
    helper.devConsole('No session token found, skipping refresh');
    return;
  }

  // Prüfen, ob ein Token-Refresh nötig ist
  const now = Date.now();
  const lastRefresh = lastRefreshTimestamp ? parseInt(lastRefreshTimestamp, 10) : 0;
  const timeSinceLastRefresh = now - lastRefresh;

  if (timeSinceLastRefresh < TOKEN_REFRESH_THRESHOLD) {
    helper.devConsole(`Token was refreshed ${Math.round(timeSinceLastRefresh / (60 * 60 * 1000))} hours ago, skipping refresh`);
    return;
  }

  // Token aktualisieren
  try {
    helper.devConsole('Refreshing session token');

    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/session/refresh`,
      {},
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`
        }
      }
    );

    if (response.data.status === 'success') {
      // Neuen Token speichern
      localStorage.setItem(SESSION_TOKEN_KEY, response.data.session_token);

      // Zeitstempel der letzten Aktualisierung speichern
      lastRefreshTimestamp = now.toString();
      localStorage.setItem('last_token_refresh', lastRefreshTimestamp);

      helper.devConsole('Session token refreshed successfully');
    }
  } catch (error) {
    helper.devConsole('Error refreshing session token:', error);

    // Bei 401-Fehler (Unauthorized) ist der Token ungültig oder abgelaufen
    if (error.response && error.response.status === 401) {
      helper.devConsole('Session token expired, clearing session data');
      clearSessionData();
    }
  }
}

/**
 * Löscht alle Session-Daten
 */
export function clearSessionData() {
  localStorage.removeItem(SESSION_TOKEN_KEY);
  localStorage.removeItem(USER_ID_KEY);
  localStorage.removeItem('last_token_refresh');

  const userStore = useUserStore();
  userStore.user.id = null;
  userStore.user.img = null;
  userStore.user.firstName = null;
  userStore.user.lastName = null;
  userStore.user.email = null;
}

/**
 * Speichert die aktuelle Route
 * @param {Object} route - Das Route-Objekt
 */
export function saveCurrentRoute(route) {
  // Routen, die nicht gespeichert werden sollen
  const excludedRoutes = ['login', 'logout', 'home'];

  if (!route || excludedRoutes.includes(route.name)) {
    return;
  }

  // Nur Routen mit requiresAuth speichern (authentifizierte Bereiche)
  if (!route.meta || !route.meta.requiresAuth) {
    return;
  }

  const routeData = {
    name: route.name,
    path: route.path,
    params: route.params,
    query: route.query,
    timestamp: Date.now(),
    meta: {
      requiresAuth: route.meta.requiresAuth,
      layout: route.meta.layout
    }
  };

  const helper = useHelperStore();
  helper.devConsole(`[Session] Saving current route: ${route.path}`);

  localStorage.setItem(LAST_ROUTE_KEY, JSON.stringify(routeData));
}

/**
 * Gibt die zuletzt besuchte Route zurück
 * @returns {Object|null} Das Route-Objekt oder null
 */
export function getLastRoute() {
  const routeData = localStorage.getItem(LAST_ROUTE_KEY);

  if (!routeData) {
    return null;
  }

  try {
    const route = JSON.parse(routeData);

    // Validiere, dass die Route noch gültig ist (nicht älter als 24 Stunden)
    if (route.timestamp && (Date.now() - route.timestamp) > (24 * 60 * 60 * 1000)) {
      const helper = useHelperStore();
      helper.devConsole('[Session] Last route is older than 24 hours, ignoring');
      return null;
    }

    return route;
  } catch (error) {
    console.error('Error parsing last route data:', error);
    return null;
  }
}

/**
 * Löscht die gespeicherte Route (z.B. nach erfolgreichem Logout)
 */
export function clearLastRoute() {
  localStorage.removeItem(LAST_ROUTE_KEY);
  const helper = useHelperStore();
  helper.devConsole('[Session] Cleared last route');
}

/**
 * Prüft, ob eine Route für die Wiederherstellung geeignet ist
 * @param {Object} route - Das Route-Objekt
 * @returns {boolean} True wenn die Route wiederhergestellt werden kann
 */
export function isRouteRestorable(route) {
  if (!route || !route.name) {
    return false;
  }

  // Ausgeschlossene Routen
  const excludedRoutes = ['login', 'logout', 'home', 'kochbuch'];
  if (excludedRoutes.includes(route.name)) {
    return false;
  }

  // Prüfe, ob die Route Authentifizierung erfordert
  if (!route.meta || !route.meta.requiresAuth) {
    return false;
  }

  // Prüfe Zeitstempel (nicht älter als 24 Stunden)
  if (!route.timestamp || (Date.now() - route.timestamp) > (24 * 60 * 60 * 1000)) {
    return false;
  }

  return true;
}
