const User = require('../models/userModel');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const authController = require('../controllers/authController');

// Middleware to check if user has admin permissions
exports.requireAdmin = catchAsync(async (req, res, next) => {
    helper.devConsole('[adminMiddleware.requireAdmin] Checking admin permissions...');
    
    // First verify the user is authenticated
    await authController.verify(req, res, async () => {
        try {
            // Get user from database with admin permissions
            const user = await User.findById(req.user.id).select('+adminPermissions');
            
            if (!user) {
                return next(new AppError('User not found', 404));
            }
            
            // Check if user has admin permissions
            if (!user.adminPermissions || !user.adminPermissions.isAdmin) {
                helper.devConsole(`[adminMiddleware.requireAdmin] Access denied for user: ${user.email}`);
                return next(new AppError('Access denied. Admin permissions required.', 403));
            }
            
            helper.devConsole(`[adminMiddleware.requireAdmin] Admin access granted for user: ${user.email}`);
            req.adminUser = user;
            next();
        } catch (error) {
            helper.devConsole('[adminMiddleware.requireAdmin] Error:', error.message);
            return next(new AppError('Authentication error', 500));
        }
    });
});

// Middleware to check specific admin permission
exports.requirePermission = (permission) => {
    return catchAsync(async (req, res, next) => {
        helper.devConsole(`[adminMiddleware.requirePermission] Checking permission: ${permission}`);
        
        if (!req.adminUser) {
            return next(new AppError('Admin authentication required', 401));
        }
        
        const user = req.adminUser;
        const adminPerms = user.adminPermissions;
        
        // Check specific permission
        let hasPermission = false;
        
        switch (permission) {
            case 'backend_access':
                hasPermission = adminPerms.canAccessBackend;
                break;
            case 'pinterest_management':
                hasPermission = adminPerms.canManagePinterest;
                break;
            case 'marketing_management':
                hasPermission = adminPerms.canManageMarketing;
                break;
            case 'analytics_view':
                hasPermission = adminPerms.canViewAnalytics;
                break;
            default:
                hasPermission = adminPerms.permissions && adminPerms.permissions.includes(permission);
        }
        
        if (!hasPermission) {
            helper.devConsole(`[adminMiddleware.requirePermission] Permission denied: ${permission} for user: ${user.email}`);
            return next(new AppError(`Access denied. Permission '${permission}' required.`, 403));
        }
        
        helper.devConsole(`[adminMiddleware.requirePermission] Permission granted: ${permission} for user: ${user.email}`);
        next();
    });
};

// Middleware for backend access specifically
exports.requireBackendAccess = [
    exports.requireAdmin,
    exports.requirePermission('backend_access')
];

// Middleware for Pinterest management
exports.requirePinterestAccess = [
    exports.requireAdmin,
    exports.requirePermission('pinterest_management')
];

// Middleware for marketing management
exports.requireMarketingAccess = [
    exports.requireAdmin,
    exports.requirePermission('marketing_management')
];
