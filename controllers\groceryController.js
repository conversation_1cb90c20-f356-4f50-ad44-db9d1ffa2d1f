const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const Grocerylist = require('../models/grocerylistModel');
const AppError = require('../utils/appError');

exports.loadGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("appAuth in grocerylistController")
  try{
    
    
    

    //use axios as you normally would, but specify httpsAgent in the config

    res.status(201).json({
      status: 'success',
      data: {
        data: "data will come"
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

//@POST /grocerylist/one/bykitchentableid/:userid
exports.loadGroceryListItemByUserId = catchAsync(async (req, res, next) => {
  helper.devConsole("grocerylist by userid in grocerylistController")
  try{

    helper.devConsole(req.params.id)

    const grocerylist = await Grocerylist.find({
      creatorId: req.params.id
      })
      .sort({ createdAt : -1})

    //console.log(grocerylist)

    res.status(201).json({
      status: 'success',
      data: {
        data: grocerylist
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});



//@POST /grocerylist/one/:id
exports.createGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("createGroceryListItem in grocerylistController")
  try{
    helper.devConsole()
    //load relations from the user
    const weekplan = await Grocerylist.create({relatedMenus: req.body.menuid, creatorUserId: req.body.userid, date: req.body.date});
    helper.devConsole(weekplan)
    //load all menues by id
    
    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: weekplan
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});


// @POST /grocerylist/many
exports.saveManyGroceryListItems = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("drin")
    //console.log(req.body.menus)

    const groceryListObject = {}
    groceryListObject.relatedMenus = []
    groceryListObject.groceryListActive = []
    
    groceryListObject.creatorId = req.body.id
    groceryListObject.startDate = req.body.dates[0]
    groceryListObject.endDate = req.body.dates[1]

    for (let i = 0; i < req.body.menus.length; i++){
      for (let o = 0; o < req.body.menus[i].length; o++){
        // menu (weekplan)
        groceryListObject.relatedMenus.push(req.body.menus[i][o]._id)

        // grocerylist
        for (let u = 0; u < req.body.menus[i][o].menu.zutaten.length; u++){
          helper.devConsole("---------- hi ----------")
          helper.devConsole(req.body.menus[i][o].plannedSeats)
          helper.devConsole(req.body.menus[i][o].menu.zutaten[u].menge)
          helper.devConsole(req.body.menus[i][o].menu.Personen)

          // recalculate the amount of food which is to buy
          //1 / 2 = 0.5
          // plannedSeats / reciept Personen = factor
          req.body.menus[i][o].menu.zutaten[u].menge = req.body.menus[i][o].menu.zutaten[u].menge * (req.body.menus[i][o].plannedSeats / req.body.menus[i][o].menu.Personen)
          //req.body.menus[i][o].menu.zutaten[u].menge = req.body.menus[i][o].menu.zutaten[u].menge * req.body.menus[i][o].plannedSeats
          helper.devConsole(req.body.menus[i][o].menu.zutaten[u].menge)
          groceryListObject.groceryListActive.push(req.body.menus[i][o].menu.zutaten[u])
        }
      }
    }

    //console.log(groceryListObject)

    const grocerylist = await Grocerylist.create(groceryListObject);

    res.status(201).json({
      status: 'success',
      data: grocerylist
    });
  
  } catch(err) {
    helper.devConsole(err)

    res.status(401).json({
      status: 'error',
      data: err
    });
  }

})

// @POST /grocerylist/create


// @POST /search/many
exports.searchGroceryListItems = catchAsync(async (req, res, next) => {
  helper.devConsole("searchWeekplans in weekplanController")
  //if(req.body.userid == '' || req.body.date == ''){AppError('Bitte userid und date objekt mitliefern', 500)}
  try{
    //load relations from the user
    const weekplan = await Grocerylist.find({
      date: {
          $gte: req.body.dates[0],
          $lte: req.body.dates[1]
      },
      user: req.body.userid
    }).sort({ date : 1});

    helper.devConsole(weekplan)

    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        weekplan
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

// @patch /grocerylist/one/:id
exports.updateGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("updateGroceryListItem in grocerylistController")
  //helper.devConsole(req.params)
  try{
    if(req.params.id){
      //load relations from the user
      const updateObject = req.body;
      const groceryListItem = await Grocerylist.updateOne({_id: req.params.id}, {$set: updateObject});
      helper.devConsole(groceryListItem)
      //load all menues by id
      
      // Send success response
      res.status(201).json({
        status: 'success',
        data: {
          data: groceryListItem
        }
      });

    }

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});
    

exports.deleteGroceryList = catchAsync(async (req, res, next) => {
  helper.devConsole("deleteWeekplan in weekplanController")
  helper.devConsole(req.params)
  try{
    if(req.params.id){
      //load relations from the user
      const grocerylistDelete = await Grocerylist.deleteOne({ "_id" : req.params.id });
      //console.log(grocerylistDelete)
      //load all menues by id
      
      // Send success response
      res.status(201).json({
        status: 'success',
        data: {
          data: grocerylistDelete
        }
      });

    }

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});