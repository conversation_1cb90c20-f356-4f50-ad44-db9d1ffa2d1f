/**
 * Debug-Test für Personenanzahl-Buttons
 * Überwacht Backend-Logs und Frontend-Requests detailliert
 */

import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5173';

test.describe('Personenanzahl Debug Test', () => {
  
  test('Debug Personenanzahl-Buttons mit detailliertem Logging', async ({ page }) => {
    console.log('🧪 Starte Debug-Test für Personenanzahl-Buttons...');
    
    // Überwache alle Console-Logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('🔍') || text.includes('📦') || text.includes('⚠️') || text.includes('❌')) {
        console.log(`[FRONTEND] ${text}`);
      }
    });
    
    // Überwache Netzwerk-Requests
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('createifnotexists')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          timestamp: Date.now()
        });
        console.log(`[REQUEST] ${request.method()} ${request.url()}`);
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('createifnotexists')) {
        console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
      }
    });
    
    // Navigiere zur Startseite
    console.log('📍 Navigiere zur Startseite...');
    await page.goto(`${BASE_URL}`);
    await page.waitForLoadState('networkidle');
    
    // Prüfe ob Login erforderlich ist
    const currentUrl = page.url();
    console.log(`📍 Aktuelle URL: ${currentUrl}`);
    
    if (currentUrl.includes('/login')) {
      console.log('🔐 Login erforderlich - überspringe Test');
      test.skip('Login erforderlich');
      return;
    }
    
    // Navigiere zu Kochbuch
    console.log('📍 Navigiere zu Kochbuch...');
    await page.goto(`${BASE_URL}/kochbuch`);
    await page.waitForLoadState('networkidle');
    
    // Finde erstes Rezept
    const firstRecipe = page.locator('[data-testid="recipe-card"], .recipe-card, a[href*="/kochbuch/menu/"]').first();
    
    if (await firstRecipe.count() === 0) {
      console.log('❌ Keine Rezepte gefunden');
      test.skip('Keine Rezepte verfügbar');
      return;
    }
    
    // Klicke auf erstes Rezept
    console.log('📍 Öffne erstes Rezept...');
    await firstRecipe.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const recipeUrl = page.url();
    console.log(`📍 Rezept-URL: ${recipeUrl}`);
    
    // Suche Personenanzahl-Buttons (verschiedene Selektoren)
    const buttonSelectors = [
      'button:has-text("+")',
      'button[data-testid="increase-persons"]',
      '.person-count button:has-text("+")',
      '[class*="person"] button:has-text("+")',
      'button:near(:text("Person"))'
    ];
    
    let increaseButton = null;
    for (const selector of buttonSelectors) {
      const button = page.locator(selector).first();
      if (await button.count() > 0) {
        increaseButton = button;
        console.log(`✅ + Button gefunden mit Selektor: ${selector}`);
        break;
      }
    }
    
    if (!increaseButton) {
      console.log('❌ Keine Personenanzahl-Buttons gefunden');
      console.log('🔍 Verfügbare Buttons:');
      const allButtons = await page.locator('button').all();
      for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
        const text = await allButtons[i].textContent();
        console.log(`  - Button ${i}: "${text}"`);
      }
      test.skip('Personenanzahl-Buttons nicht gefunden');
      return;
    }
    
    // Teste + Button
    console.log('🔼 Klicke + Button...');
    await increaseButton.click();
    
    // Warte auf Backend-Response und Logs
    await page.waitForTimeout(5000);
    
    console.log(`📊 Anzahl Requests: ${requests.length}`);
    
    if (requests.length > 0) {
      console.log('✅ Request wurde gesendet');
      
      // Prüfe ob Frontend-Logs die Datenstruktur zeigen
      await page.waitForTimeout(2000);
      
    } else {
      console.log('❌ Kein Request gesendet - Button funktioniert nicht');
    }
    
    console.log('🎯 Debug-Test abgeschlossen');
  });
});
