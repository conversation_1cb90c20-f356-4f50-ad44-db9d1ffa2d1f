const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const marketingContentSchema = new mongoose.Schema({
  generationDate: {
    type: Date,
    default: Date.now,
    required: true,
    index: true // Index für schnelles Sortieren/Filtern nach Datum
  },
  // Felder aus n8n Workflow 1 - Generierung
  marketingHook: {
    type: String, // Optional, falls der Hook selbst gespeichert werden soll
    trim: true
  },
  textOne: { // Annahme: Teil des Marketing-Textes/Hooks
    type: String,
    trim: true
  },
  textTwo: { // Annahme: Teil des Marketing-Textes/Hooks
    type: String,
    trim: true
  },
  imgOneUrl: {
    type: String,
    trim: true
  },
  imgTwoUrl: {
    type: String,
    trim: true
  },
  imgThreeUrl: {
    type: String,
    trim: true
  },
  imgFourUrl: {
    type: String,
    trim: true
  },
  // Zugehörige Rezept-Infos (aus random-free call)
  recipeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu', // Referenz zum Menu-Modell
    required: true
  },
  recipeName: {
    type: String,
    required: true,
    trim: true
  },
  recipeImageUrl: {
    type: String, // Original-Bild vom Rezept
    trim: true
  },
  recipeNumberOfPersons: { // Aus dem MenuChild
    type: Number 
  },
  recipeCookingTime: { // Aus dem MenuChild
    type: Number
  },
  // Felder aus n8n Workflow 2 - Video & Post
  videoS3Url: { // Wird nach dem Upload hinzugefügt
    type: String,
    trim: true
  },
  socialMediaDescription: { // Wird nach AI-Generierung hinzugefügt
    type: String,
    trim: true
  },
  // NEU: Zwei Varianten für den Social Media Text
  socialMediaTextMitLink: {
    type: String,
    trim: true
  },
  socialMediaTextOhneLink: {
    type: String,
    trim: true
  },
  // NEU: Zähler für Veröffentlichungen pro Plattform
  published_tiktok: {
    type: Number,
    default: 0
  },
  published_fb: {
    type: Number,
    default: 0
  },
  published_insta: {
    type: Number,
    default: 0
  },
  published_pinterest: {
    type: Number,
    default: 0
  },
  // NEU: Instagram Stories Zähler
  published_insta_story: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true // Fügt createdAt und updatedAt automatisch hinzu
});

// Use the specific connection (connection1) to define the model
const MarketingContent = connection1.model('MarketingContent', marketingContentSchema);

module.exports = MarketingContent; 