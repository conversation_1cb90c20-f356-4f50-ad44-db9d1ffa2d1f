<template>
    <!-- Innerer Container -->
    <div class="w-full flex flex-row relative z-40">
      <div class="w-2/6 md:w-1/6 h-auto relative">
        <img
          @click="$router.push('/')"
          src="../assets/ordy_logo.svg"
          alt="Logo"
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-w-full p-7 mt-10 md:mt-16 safe-area-inset-top"
        >
      </div>
      <div class="w-4/6 md:w-5/6 pt-16 z-30">

        <div class="md:w-2/6 w-4/6 flex flex-row justify-right float-right relative z-30">
          <p v-if="!userStore.user.firstName" class="pt-2 text-xs">
            <button @click="$router.push('/login')">Login</button>
          </p>
          <p @click.prevent="userStore.openSettings = !userStore.openSettings" v-if="userStore.user.firstName" class="pt-2 text-xs">
            {{ userStore.user.firstName }}
          </p>
          <div @click.prevent="userStore.openSettings = !userStore.openSettings"
               class="rounded-[3rem] bg-ordypurple-200 hover:border-2 hover:border-white w-9 h-9 antialiased ml-4 z-10 flex items-center justify-center overflow-hidden">
            <img v-if="hasValidImage" :src="currentImageUrl" :alt="userStore.user.firstName"
                 class="w-full h-full object-cover" />
            <div v-else class="text-white font-bold text-sm">
              {{ userInitials }}
            </div>
          </div>

          <!--- subnavigation -->
          <div v-if="userStore.openSettings && userStore.user.id"
               class="w-48 flex flex-col z-40 absolute right-[40%] sm:right-[70%] top-full mt-2 p-2 rounded-2xl bg-white shadow-lg">
            <div class="w-full flex justify-end mb-1">
              <button class="text-gray-500 hover:text-gray-700 p-1" @click.prevent="userStore.openSettings = !userStore.openSettings">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
            <div class="w-full font-YesevaOne text-tiny flex flex-col md:space-y-1">
              <button @click.prevent="goToSettings(), userStore.closeTopNavigation()"
                      class="w-full md:py-1 px-2 text-left hover:bg-gray-100 rounded-lg transition-colors">
                Einstellungen
              </button>
              <button @click.prevent="goToKuechentisch(), userStore.closeTopNavigation()"
                      class="w-full md:py-1 px-2 text-left hover:bg-gray-100 rounded-lg transition-colors">
                Küchentisch
              </button>
              <!-- Admin Button -->
              <button v-if="userStore.user?.adminPermissions?.isAdmin === true"
                      @click.prevent="goToAdmin(), userStore.closeTopNavigation()"
                      class="w-full md:py-1 px-2 text-left hover:bg-purple-100 rounded-lg transition-colors text-purple-700 font-semibold">
                🛠️ Admin
              </button>
              <button @click.prevent="userStore.logoutSession(), userStore.closeTopNavigation()"
                      class="w-full md:py-1 px-2 text-left hover:bg-gray-100 rounded-lg transition-colors">
                Logout
              </button>
            </div>
          </div>
          <!--- subnavigation -->

        </div>
      </div>
    </div>
</template>
<script setup>
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '../store/userStore';

////////////////// SETUP //////////////
  const router = useRouter()
  const userStore = useUserStore()

  // Debug entfernt für Performance
////////////////// SETUP //////////////

// Computed properties for profile image
const currentImageUrl = computed(() => {
    if (userStore.user.img && userStore.user.img !== 'default.png' && userStore.user.img !== '') {
        // Check if it's already a full URL (from OAuth providers)
        if (userStore.user.img.startsWith('http')) {
            return userStore.user.img;
        }
        // Otherwise, it's a filename from our S3 bucket
        return `https://ordy-images.s3.amazonaws.com/${userStore.user.img}`;
    }
    return '/default-avatar.png'; // Fallback image
});

const hasValidImage = computed(() => {
    return userStore.user.img &&
           userStore.user.img !== 'default.png' &&
           userStore.user.img !== '';
});

const userInitials = computed(() => {
    const firstName = userStore.user.firstName || '';
    const lastName = userStore.user.lastName || '';
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U';
});


  const goToSettings = () => {
    router.push({ name: 'usersettings'})
  }

  const goToKuechentisch = () => {
    router.push({ name: 'kuechentisch'})
  }

  const goToAdmin = () => {
    router.push('/admin')
  }

</script>
<style>

</style>