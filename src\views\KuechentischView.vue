<template>
  <!-- Modern Container with UserSettings-style layout -->
  <div class="flex flex-col md:flex-row min-h-screen px-6">

    <!-- Main Content Area -->
    <div class="w-full md:w-3/4 md:pr-12">
      <GoBack />

      <!-- Modern Header Section -->
      <div class="w-full mt-20">
        <h1 class="font-YesevaOne text-[3.2rem] leading-tight">Küchentisch</h1>
        <p class="text-[0.95rem] font-sans font-thin antialiased">Verwalte deinen Küchentisch und lade Familienmitglieder ein</p>
      </div>

      <!-- Loading State or No-Table Message -->
      <div v-if="isLoading">Lade Küchentische...</div>
      <div v-else-if="!isLoading && kitchenstablestore.availableKitchenTableList.length === 0" class="text-center p-6 bg-white rounded-lg shadow-md mt-4 max-w-lg mx-auto">
          <h3 class="text-lg mb-7">Ke<PERSON>üchentisch zugeordnet</h3>
          <p class="text-gray-600 mb-4">Du bist aktuell keinem Küchentisch zugewiesen oder hast noch keinen eigenen erstellt.</p>
          <p class="text-gray-600 mb-4">Erstelle auf dieser Seite bei der Übersicht einen neuen Küchentisch.</p>
          <p class="text-xs text-gray-500 italic">Ein Küchentisch ermöglicht das Teilen von Wochenplänen und Einkaufszetteln.</p>
      </div>

      <!-- Existing Content (only shown if tables are loaded) -->
      <!-- Modern Content Sections -->
      <div v-else class="max-w-4xl mx-auto">
          <!-- Current Kitchentable Section -->
          <div class="bg-white rounded-3xl p-6 mt-12 shadow-custom shadow-gray-200/50 border border-gray-100 mb-8">
            <div class="flex items-center gap-4 mb-6">
              <div class="w-12 h-12 bg-gradient-to-r from-ordypurple-100 to-ordypurple-200 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
              </div>
              <div>
                <h3 class="font-YesevaOne text-lg text-gray-800">Aktueller Küchentisch</h3>
                <p class="text-sm text-gray-500">Wähle deinen aktiven Küchentisch aus</p>
              </div>
            </div>

            <div class="space-y-4">
              <button
                @click.prevent="kitchenstablestore.openKitchentableSelecter = !kitchenstablestore.openKitchentableSelecter"
                class="w-full p-4 bg-gradient-to-r from-ordypurple-100 to-ordypurple-200 text-white rounded-2xl font-YesevaOne text-sm flex justify-between items-center hover:from-ordypurple-200 hover:to-ordypurple-100 transition-all duration-200 shadow-custom default-button-shadow"
              >
                <span>
                  {{ kitchenstablestore.kitchentable.tableAddress_street ? `${kitchenstablestore.kitchentable.tableAddress_street}, ${kitchenstablestore.kitchentable.tableAddress_plztown}` : 'Bitte wähle einen Tisch' }}
                </span>
                <svg class="w-4 h-4 text-white transform transition-transform duration-200" :class="kitchenstablestore.openKitchentableSelecter ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </button>

              <!-- Modern Dropdown -->
              <div v-if="kitchenstablestore.openKitchentableSelecter" class="bg-gray-50 rounded-2xl overflow-hidden shadow-inner">
                <button
                  @click="kitchenstablestore.setKitchenTable(index)"
                  class="w-full px-4 py-3 text-left hover:bg-white transition-colors duration-200 border-b border-gray-200 last:border-b-0"
                  v-for="(item,index) in kitchenstablestore.availableKitchenTableList" :key="item._id"
                >
                  <span class="font-medium text-sm text-gray-800">{{ item.tableAddress_street }} - {{ item.tableAddress_plztown }}</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Members Section -->
          <div class="bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 mb-8">
            <div class="flex items-center gap-4 mb-6">
              <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <div>
                <h3 class="font-YesevaOne text-lg text-gray-800">Mitglieder</h3>
                <p class="text-sm text-gray-500">{{ kitchenstablestore.kitchentable.members ? kitchenstablestore.kitchentable.members.length : 0 }} Personen am Küchentisch</p>
              </div>
            </div>

            <div class="space-y-3">
              <div
                v-for="(item3,index3) in kitchenstablestore.kitchentable.members"
                :key="index3"
                class="flex items-center p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors duration-200"
              >
                <!-- Modern Avatar -->
                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-ordypurple-100 to-ordypurple-200 mr-4 flex-shrink-0 flex items-center justify-center text-white font-YesevaOne text-sm"
                     :style="item3.userId && item3.userId.img ? { 'background-image': 'url(' + item3.userId.img + ')', 'background-size': 'cover', 'background-position': 'center' } : {}">
                  {{ !item3.userId?.img && item3.userId ? (item3.userId.firstName?.[0] || '') + (item3.userId.lastName?.[0] || '') : '' }}
                </div>
                <!-- Name and Email -->
                <div class="flex-grow">
                  <p class="font-medium text-gray-800">{{ item3.userId ? (item3.userId.firstName + ' ' + (item3.userId.lastName || '')).trim() : 'Unbekannt' }}</p>
                  <p class="text-sm text-gray-500 truncate" :title="item3.userId ? item3.userId.email : '-'">{{ item3.userId ? item3.userId.email : '-' }}</p>
                </div>
                <!-- Modern Delete Button -->
                <button
                  v-if="item3.roleId && item3.roleId.name == 'member'"
                  @click="kitchenstablestore.deletePerson(index3)"
                  class="w-8 h-8 rounded-full bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 transition-colors duration-200 flex items-center justify-center"
                  title="Mitglied entfernen"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <!-- Delete Kitchentable Link -->
          <div class="w-full max-w-md mt-4 text-right"> <!-- Limit width -->
             <button
                @click.prevent="kitchenstablestore.deleteKitchentable(kitchenstablestore.kitchentable.id)"
                class="inline-flex items-center text-black font-['Open_Sans'] text-[8px] hover:text-red-600"
                v-if="kitchenstablestore.kitchentable && kitchenstablestore.kitchentable.members && kitchenstablestore.kitchentable.members.some(m => m.userId && m.userId.email === userstore.user.email && m.roleId.name === 'owner')"
                >
                Diesen Küchentisch jetzt löschen
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
              </button>
          </div>
          <!-- End Members Section -->

          <!-- Add Person Section -->
          <p class="w-full mt-8 font-['Open_Sans'] font-bold text-[12px]">
                Person zum Tisch hinzufügen
          </p>
          <div v-if="kitchenstablestore.kitchentable.id" class="flex items-center bg-white rounded-lg mt-1 p-1 shadow-sm max-w-md"> <!-- Limit width -->
            <input
              autocomplete="new-password"
              type="text"
              @input="kitchenstablestore.searchForPersons()"
              v-model="kitchenstablestore.kitchentablePerson"
              class="p-2 w-full border-0 outline-none text-[8px] font-['Open_Sans']"
              placeholder="Namen suchen..." />
          </div>
          <!-- Search Results -->
          <div class="flex flex-wrap gap-2 mt-2 max-w-md"> <!-- Limit width -->
            <div v-if="kitchenstablestore.availablePersons">
              <div
                v-for="(item4,index4) in kitchenstablestore.availablePersons"
                :key="index4"
              >
                <button @click.prevent="kitchenstablestore.setPerson(index4)" class="p-2 bg-white rounded-lg text-xs font-['Open_Sans'] hover:bg-gray-200 shadow-sm">{{ item4.email }}</button>
              </div>
            </div>
          </div>
          <!-- End Add Person Section -->

          <!-- Invite Person Section -->
           <p class="w-full mt-8 font-['Open_Sans'] font-bold text-[12px]">
                Neue Person zu Ordy einladen
          </p>
          <button
              class="bg-[#a37dff] rounded-lg p-3 mt-1 font-['Yeseva_One'] text-[12px] text-white text-center w-full max-w-md hover:bg-purple-700 transition-colors duration-200"
              @click="sendInviteLink"
              >
              Jetzt Link zusenden
            </button>
          <!-- End Invite Person Section -->
      </div> <!-- End v-else for existing content -->

    </div>
    <!-- End Main Content Area -->

    <!-- Right Sidebar (Overview) -->
    <div class="w-full md:w-1/3 lg:w-1/4 p-4 md:p-8 md:border-l md:border-gray-300/50">
      <h2 class="w-full text-lg mb-2">Übersicht</h2>
      <p class="mb-4 text-sm">Neuen Küchentisch erstellen:</p>

      <div>
        <kitchentableOverview></kitchentableOverview>
      </div>

    </div>
    <!-- End Right Sidebar -->

  </div>
  <!-- End Main Container -->
</template>

<script setup>
///////////////////// IMPORT /////////////////////////////////
import { reactive, ref, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
// Removed unused imports: menuCard, dayCard, MenuCard
import kitchentableOverview from '../components/kitchentableOverview.vue' // Keep if needed elsewhere, but not used in template now
import useNotification from '../../modules/notificationInformation';
import { useMenuStore } from '../store/menuStore'
import { useUserStore } from '../store/userStore'
import { useGrocerylistStore } from '../store/grocerylistStore'
import { useWeekplanStore } from '../store/weekplanStore'
import { useKitchentableStore } from '../store/kitchentableStore'
import GoBack from '../body/goBack.vue';
import { useHelperStore } from '../../utils/helper';
// Removed ShareLinkModal import

  ///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const groceryliststore = useGrocerylistStore();
  const weekplanstore = useWeekplanStore();
  const userstore = useUserStore(); // Duplicate userStore
  const kitchenstablestore = useKitchentableStore();
  const helper = useHelperStore()

  // Add a loading state
  const isLoading = ref(true);

  ////////////////////// LOGIC ////////////////////////////////

  // Watch for User ID availability and load the list
  watch(() => userStore.user.id, async (newUserId) => { // Make watcher async
    isLoading.value = true; // Set loading true when user ID changes
    if (newUserId) {
      helper.devConsole('(Watcher) User ID available:', newUserId, '- Loading kitchentable list.');
      await kitchenstablestore.getKitchenTableList(); // Wait for the list to load
    } else {
       helper.devConsole('(Watcher) User ID not available, cannot load kitchentable list.');
       kitchenstablestore.availableKitchenTableList = []; // Clear list if no user
    }
    // isLoading.value = false; // Set loading false after attempting to load
  }, { immediate: true });

  // Watch for Default Kitchentable availability/change and load details
  watch(() => userStore.user.defaultKitchentable, async (newTableId) => { // Make watcher async
    // isLoading.value = true; // Optionally set loading here too, or rely on list loading
    if (newTableId) {
      helper.devConsole('(Watcher) Default Kitchentable available:', newTableId, '- Loading table details.');
      await kitchenstablestore.getKitchenTable(); // Wait for details to load
    } else {
       helper.devConsole('(Watcher) Default Kitchentable not available, cannot load details.');
    }
    // isLoading.value = false; // Set loading false after attempting to load details
  }, { immediate: true });

  // NEW: Watch the list loading status to set isLoading definitively
  watch(() => kitchenstablestore.availableKitchenTableList, () => {
      isLoading.value = false;
  }, { deep: true }); // Use deep watch if needed, or adjust based on how loading is tracked

  // Function to handle sending invite link (Now copies to clipboard)
  const sendInviteLink = async () => { // Added async
    console.log("sendInviteLink started.");
    console.log("Current kitchentable:", kitchenstablestore.kitchentable);
    console.log("Kitchentable ID:", kitchenstablestore.kitchentable?.id);

    if (!kitchenstablestore.kitchentable || !kitchenstablestore.kitchentable.id) {
      console.log("Condition failed: No kitchentable ID found.");
      setNotification('Bitte wähle zuerst einen Küchentisch aus.', 'warn');
      return;
    }

    const placeholderBaseUrl = window.location.origin;
    const inviteToken = kitchenstablestore.kitchentable.id;
    const shareUrlToCopy = `${placeholderBaseUrl}/invite/${inviteToken}`;

    console.log("URL to copy:", shareUrlToCopy);

    // Copy to clipboard logic
    if (!navigator.clipboard) {
        // Fallback for older browsers or insecure contexts (http)
        try {
            const textArea = document.createElement("textarea");
            textArea.value = shareUrlToCopy;
            textArea.style.position = "fixed"; // Prevent scrolling to bottom
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            setNotification('Link in Zwischenablage kopiert (Fallback)', 'success');
            console.log('Link copied to clipboard (Fallback)');
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            setNotification('Fehler beim Kopieren des Links', 'error');
        }
        return;
    }

    try {
        await navigator.clipboard.writeText(shareUrlToCopy);
        setNotification('Link in Zwischenablage kopiert', 'success');
        console.log('Link copied to clipboard');
    } catch (err) {
        console.error('Failed to copy: ', err);
        setNotification('Fehler beim Kopieren des Links', 'error');
    }
  }

  // Removed unused reloadData function

</script>

<style scoped>
/* Removed unused styles: .box-shadow, .move keyframes */

/* Add fallback fonts in case Yeseva One or Open Sans are not loaded */
.font-\[\'Yeseva_One\'\] {
  font-family: 'Yeseva One', cursive; /* Assuming cursive as a fallback */
}

.font-\[\'Open_Sans\'\] {
  font-family: 'Open Sans', sans-serif;
}
</style>