/**
 * Test für Shopping List Merge-Logik
 * 
 * Diese Tests prüfen, ob die Merge-Logik korrekt funktioniert und bereits
 * erledigte Items nicht reaktiviert werden.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { mergeShoppingListData } from '../services/conflictResolution.js';

describe('Shopping List Merge Logic', () => {
  let localItems, serverItems, offlineActions;

  beforeEach(() => {
    // Reset test data before each test
    localItems = [];
    serverItems = [];
    offlineActions = [];
  });

  it('should preserve locally purchased items when server shows them as unpurchased', () => {
    // Arrange
    localItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: true,
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ];

    serverItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: false,
        updatedAt: '2024-01-15T10:25:00Z'
      }
    ];

    offlineActions = [
      {
        type: 'updateItemPurchasedStatus',
        payload: { itemId: 'item1', isPurchased: true },
        synced: false,
        timestamp: '2024-01-15T10:30:00Z'
      }
    ];

    // Act
    const result = mergeShoppingListData(localItems, serverItems, offlineActions);

    // Assert
    expect(result.mergedItems).toHaveLength(1);
    expect(result.mergedItems[0].is_purchased).toBe(true);
    expect(result.mergedItems[0].isPurchased).toBe(true);
    expect(result.conflicts).toHaveLength(1);
    expect(result.conflicts[0].resolution).toBe('purchased_wins');
  });

  it('should not override server purchased status when no local offline action exists', () => {
    // Arrange
    localItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: false,
        updatedAt: '2024-01-15T10:25:00Z'
      }
    ];

    serverItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: true,
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ];

    offlineActions = []; // No offline actions

    // Act
    const result = mergeShoppingListData(localItems, serverItems, offlineActions);

    // Assert
    expect(result.mergedItems).toHaveLength(1);
    expect(result.mergedItems[0].is_purchased).toBe(true);
    expect(result.mergedItems[0].isPurchased).toBe(true);
  });

  it('should handle mixed field names (isPurchased vs is_purchased)', () => {
    // Arrange
    localItems = [
      {
        _id: 'item1',
        name: 'Milch',
        isPurchased: true, // Frontend format
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ];

    serverItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: false, // Backend format
        updatedAt: '2024-01-15T10:25:00Z'
      }
    ];

    offlineActions = [
      {
        type: 'updateItemPurchasedStatus',
        payload: { itemId: 'item1', isPurchased: true },
        synced: false
      }
    ];

    // Act
    const result = mergeShoppingListData(localItems, serverItems, offlineActions);

    // Assert
    expect(result.mergedItems).toHaveLength(1);
    expect(result.mergedItems[0].is_purchased).toBe(true);
    expect(result.mergedItems[0].isPurchased).toBe(true);
  });

  it('should handle multiple items with different conflict scenarios', () => {
    // Arrange
    localItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: true,
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        _id: 'item2',
        name: 'Brot',
        is_purchased: false,
        updatedAt: '2024-01-15T10:25:00Z'
      }
    ];

    serverItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: false,
        updatedAt: '2024-01-15T10:25:00Z'
      },
      {
        _id: 'item2',
        name: 'Brot',
        is_purchased: true,
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ];

    offlineActions = [
      {
        type: 'updateItemPurchasedStatus',
        payload: { itemId: 'item1', isPurchased: true },
        synced: false
      }
      // No offline action for item2
    ];

    // Act
    const result = mergeShoppingListData(localItems, serverItems, offlineActions);

    // Assert
    expect(result.mergedItems).toHaveLength(2);
    
    // Item1: Local purchase should be preserved
    const item1 = result.mergedItems.find(item => item._id === 'item1');
    expect(item1.is_purchased).toBe(true);
    
    // Item2: Server purchase should be accepted
    const item2 = result.mergedItems.find(item => item._id === 'item2');
    expect(item2.is_purchased).toBe(true);
  });

  it('should ignore synced offline actions', () => {
    // Arrange
    localItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: true,
        updatedAt: '2024-01-15T10:30:00Z'
      }
    ];

    serverItems = [
      {
        _id: 'item1',
        name: 'Milch',
        is_purchased: false,
        updatedAt: '2024-01-15T10:35:00Z' // Newer than local
      }
    ];

    offlineActions = [
      {
        type: 'updateItemPurchasedStatus',
        payload: { itemId: 'item1', isPurchased: true },
        synced: true // Already synced
      }
    ];

    // Act
    const result = mergeShoppingListData(localItems, serverItems, offlineActions);

    // Assert
    expect(result.mergedItems).toHaveLength(1);
    // Should use server status since offline action was already synced
    expect(result.mergedItems[0].is_purchased).toBe(false);
  });
});
