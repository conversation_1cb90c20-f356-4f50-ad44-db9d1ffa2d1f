# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: ordy-tst | PREVIEW

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  NODE_ENV: preview

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checout Source
        uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'

      - name: npm install, build, and test
        run: npm install

      - name: Install Playwright Browsers
        run: npm run install:browsers

      - name: Check for playwright-browsers
        run: ls -l playwright-browsers || echo "Ordner nicht gefunden"

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'ordy-tst'
          publish-profile: ${{ secrets.AZURE_WEBAPP_TST_PUBLISH_PROFILE }}
          package: .