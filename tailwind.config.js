module.exports = {
  content: [
    // Example content paths...
    './public/**/*.html',
    './src/**/*.{js,jsx,ts,tsx,vue}',
  ],
  darkMode: 'media', // or 'media' or 'class'
  theme: {
    boxShadow: {
      custom: '-6px 6px 1px 0 rgba(0, 0, 0, 0.2)',
    },
    extend: {
      spacing: {
        '114': '350px',
        '128': '32rem'
      },
      screens: {
        'sm': '640px',
        // => @media (min-width: 640px) { ... }

        'md': '768px',
        // => @media (min-width: 768px) { ... }

        'lg': '1080px',
        // => @media (min-width: 1024px) { ... }

        'xl': '1280px',
        // => @media (min-width: 1280px) { ... }

        '2xl': '1620px',
        // => @media (min-width: 1536px) { ... }

        '3xl': '1890px',
        // => @media (min-width: 1536px) { ... }
      },
      fontFamily: {
        OpenSans: ['Open Sans', 'sans-serif'],
        YesevaOne: ['Yeseva One', 'sans-serif'],
      },
      fontSize: {
        tiny: ['11px', '13px'],
        xs: ['14px', '15px'],
        sm: ['16px', '18px'],
        base: ['19px', '22px'],
        lg: ['25px', '28px'],
        xl: ['50px', '32px'],
      },
      gridTemplateColumns: {
        13: 'repeat(13, minmax(0, 1fr))',
        14: 'repeat(14, minmax(0, 1fr))',
        15: 'repeat(15, minmax(0, 1fr))',
        16: 'repeat(16, minmax(0, 1fr))',
        17: 'repeat(17, minmax(0, 1fr))',
        18: 'repeat(18, minmax(0, 1fr))',
        19: 'repeat(19, minmax(0, 1fr))',
        20: 'repeat(20, minmax(0, 1fr))',
        21: 'repeat(21, minmax(0, 1fr))',
        22: 'repeat(22, minmax(0, 1fr))',
        23: 'repeat(23, minmax(0, 1fr))',
        24: 'repeat(24, minmax(0, 1fr))',
        25: 'repeat(25, minmax(0, 1fr))',
        26: 'repeat(26, minmax(0, 1fr))',
        27: 'repeat(27, minmax(0, 1fr))',
        28: 'repeat(28, minmax(0, 1fr))',
        29: 'repeat(29, minmax(0, 1fr))',
        30: 'repeat(30, minmax(0, 1fr))',
        45: 'repeat(45, minmax(0, 1fr))',
      },
      gridColumn: {
        'span-13': 'span 13 / span 13',
        'span-14': 'span 14 / span 14',
        'span-15': 'span 15 / span 15',
        'span-16': 'span 16 / span 16',
        'span-17': 'span 17 / span 17',
        'span-18': 'span 18 / span 18',
        'span-19': 'span 19 / span 19',
      },
      gridColumnStart: {
        '13': '13',
        '14': '14',
        '15': '15',
        '16': '16',
        '17': '17',
      }
    },
    colors: {
      transparent: 'transparent',
      current: 'currentColor',
      green:{
        500:'rgba(34 197 94)'
      },
      ordypurple: {
        100:'rgba(163, 125, 255, 1)',
        200:'rgba(153, 115, 215, 1)'
      },
      ordypink:{
        100:'rgba(208, 41, 120, 1)',
        200:'rgba(225, 96, 157, 1)'
      },
      white: {
        DEFAULT: '#FFFFFF',
      },
      gray: {
        200: 'rgba(200, 200, 200, 1)',
        100: 'rgba(213, 213, 213, 1)',
        90: 'rgba(225, 225, 225, 1)',
        75: 'rgba(235, 235, 235, 1)',
        50: 'rgba(240, 240, 240, 1)',
        25: 'rgba(250, 250, 250, 1)'
      },
      black: {
        DEFAULT: '#000000',
      },
      alarmred: {
        100: 'rgba(195, 64, 72, 1)'
      }
    },
  },
  variants: {
    extend: {},
  },
  plugins: [],
};
