/**
 * Azure Environment Variables Validator
 * 
 * Dieses Skript überprüft, ob alle erforderlichen Umgebungsvariablen 
 * für den Azure-Deployment vorhanden sind.
 */

'use strict';

const dotenv = require('dotenv');
const fs = require('fs');

// Laden der Umgebungsvariablen
dotenv.config({ path: './config.env' });

// Definition der erforderlichen Umgebungsvariablen pro Umgebung
const requiredVariables = {
  development: [
    'PORT',
    'DATABASE_DEV',
    'DATABASE_DEV_PASSWORD'
  ],
  preview: [
    'PORT',
    'DATABASE_DEV', 
    'DATABASE_DEV_PASSWORD',
    'JWT_SECRET',
    'JWT_EXPIRES_IN'
  ],
  production: [
    'PORT',
    'DATABASE_PRD',
    'DATABASE_PRD_PASSWORD',
    'JWT_SECRET',
    'JWT_EXPIRES_IN'
  ]
};

// Aktuelle Umgebung ermitteln
const env = process.env.NODE_ENV || 'development';
console.log(`Checking environment variables for: ${env}`);

// Überprüfen der Umgebungsvariablen
const missingVariables = [];
const requiredForEnv = requiredVariables[env] || [];

requiredForEnv.forEach(variable => {
  if (!process.env[variable]) {
    missingVariables.push(variable);
  }
});

// Azure-spezifische Variablen überprüfen
const azureSpecificVars = [
  'WEBSITE_SITE_NAME',
  'WEBSITE_HOSTNAME',
  'WEBSITE_INSTANCE_ID'
];

const missingAzureVars = [];
azureSpecificVars.forEach(variable => {
  if (!process.env[variable]) {
    missingAzureVars.push(variable);
  }
});

// Ausgabe der Ergebnisse
if (missingVariables.length > 0) {
  console.error(`⚠️ Missing required environment variables for ${env}:`);
  missingVariables.forEach(variable => {
    console.error(`  - ${variable}`);
  });
  console.error('\nThese variables must be set in your config.env file.');
} else {
  console.log(`✅ All required environment variables for ${env} are set.`);
}

if (missingAzureVars.length > 0) {
  console.warn('\n⚠️ Azure-specific environment variables not found:');
  missingAzureVars.forEach(variable => {
    console.warn(`  - ${variable}`);
  });
  console.warn('\nThis is expected when running locally but will be available in Azure Web App environment.');
}

// Lokale Azure-Simulation definieren
const localAzureVars = {
  WEBSITE_SITE_NAME: env === 'production' ? 'ordy-prd' : 'ordy-tst',
  WEBSITE_HOSTNAME: env === 'production' ? 'ordy-prd.azurewebsites.net' : 'ordy-tst-d8cfc0bzchbqd9ha.switzerlandnorth-01.azurewebsites.net',
  WEBSITE_INSTANCE_ID: 'local-simulation'
};

console.log('\n📋 Local Azure environment simulation values:');
Object.entries(localAzureVars).forEach(([key, value]) => {
  console.log(`  - ${key}: ${value}`);
});

// Überprüfen, ob die Hauptdateien existieren
const requiredFiles = ['server.js', 'app.js', 'package.json'];
const missingFiles = [];

requiredFiles.forEach(file => {
  if (!fs.existsSync(`./${file}`)) {
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.error('\n❌ Missing required files for Azure deployment:');
  missingFiles.forEach(file => {
    console.error(`  - ${file}`);
  });
} else {
  console.log('\n✅ All required files for Azure deployment are present.');
}

module.exports = {
  isValid: missingVariables.length === 0 && missingFiles.length === 0,
  missingVariables,
  missingFiles,
  localAzureVars
}; 