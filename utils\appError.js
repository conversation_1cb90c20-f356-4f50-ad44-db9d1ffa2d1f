class AppError extends Error {
    constructor(message, statusCode) {
      super(message);
      // Only log in development/preview environments
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview') {
        console.log(message);
        console.log(statusCode);
      }
      this.statusCode = statusCode;
      this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
      this.isOperational = true;

      Error.captureStackTrace(this, this.constructor);
    }
  }

  module.exports = AppError;