/**
 * Conflict-Resolution-Service für Ordy
 * 
 * Dieser Service löst Konflikte zwischen lokalen Offline-Änderungen und Server-Zustand
 * beim Wiederherstellen der Internetverbindung.
 */

import { useHelperStore } from '../../utils/helper';

/**
 * Merge-Strategien für verschiedene Konflikte
 */
const MERGE_STRATEGIES = {
  // Wenn ein Item lokal als "gekauft" markiert wurde, hat das Priorität
  PURCHASED_WINS: 'purchased_wins',
  // Neuere Timestamps haben Priorität
  TIMESTAMP_WINS: 'timestamp_wins',
  // Lokale Änderungen haben Priorität
  LOCAL_WINS: 'local_wins',
  // Server-Änderungen haben Priorität
  SERVER_WINS: 'server_wins'
};

/**
 * Führt einen intelligenten Merge zwischen lokalen und Server-Daten durch
 * @param {Array} localItems - Lokale Items (mit Offline-Änderungen)
 * @param {Array} serverItems - Server-Items (aktueller Online-Zustand)
 * @param {Array} offlineActions - Liste der Offline-Aktionen
 * @returns {Object} - Merge-Ergebnis mit finalen Items und Konflikten
 */
export function mergeShoppingListData(localItems, serverItems, offlineActions) {
  const helper = useHelperStore();
  helper.devConsole('Starte intelligenten Merge von Shopping-List-Daten...');
  helper.devConsole('Lokale Items:', localItems.length);
  helper.devConsole('Server Items:', serverItems.length);
  helper.devConsole('Offline-Aktionen:', offlineActions.length);

  const mergedItems = [];
  const conflicts = [];
  const processedItemIds = new Set();

  // Erstelle Maps für schnelleren Zugriff
  const localItemsMap = new Map(localItems.map(item => [item._id, item]));
  const serverItemsMap = new Map(serverItems.map(item => [item._id, item]));

  // Erstelle eine Map der Offline-Aktionen nach Item-ID
  const offlineActionsMap = new Map();
  offlineActions.forEach(action => {
    if (action.payload && action.payload.itemId) {
      if (!offlineActionsMap.has(action.payload.itemId)) {
        offlineActionsMap.set(action.payload.itemId, []);
      }
      offlineActionsMap.get(action.payload.itemId).push(action);
    }
  });

  // 1. Verarbeite alle Items, die sowohl lokal als auch auf dem Server existieren
  for (const [itemId, localItem] of localItemsMap) {
    if (serverItemsMap.has(itemId)) {
      const serverItem = serverItemsMap.get(itemId);
      const itemOfflineActions = offlineActionsMap.get(itemId) || [];
      
      const mergeResult = mergeItem(localItem, serverItem, itemOfflineActions);
      mergedItems.push(mergeResult.item);
      
      if (mergeResult.conflict) {
        conflicts.push(mergeResult.conflict);
      }
      
      processedItemIds.add(itemId);
    }
  }

  // 2. Füge Items hinzu, die nur lokal existieren (neue Items, die offline hinzugefügt wurden)
  for (const [itemId, localItem] of localItemsMap) {
    if (!processedItemIds.has(itemId)) {
      // Prüfe, ob es sich um ein temporäres Item handelt
      if (localItem.isTemporary) {
        helper.devConsole(`Lokales temporäres Item gefunden: ${localItem.name}`);
        // Entferne das isTemporary-Flag, da es jetzt synchronisiert wird
        const cleanedItem = { ...localItem };
        delete cleanedItem.isTemporary;
        mergedItems.push(cleanedItem);
      } else {
        mergedItems.push(localItem);
      }
      processedItemIds.add(itemId);
    }
  }

  // 3. Füge Items hinzu, die nur auf dem Server existieren (von anderen Geräten hinzugefügt)
  for (const [itemId, serverItem] of serverItemsMap) {
    if (!processedItemIds.has(itemId)) {
      helper.devConsole(`Server-Item gefunden, das lokal nicht existiert: ${serverItem.name}`);
      mergedItems.push(serverItem);
      processedItemIds.add(itemId);
    }
  }

  helper.devConsole(`Merge abgeschlossen. ${mergedItems.length} Items, ${conflicts.length} Konflikte`);

  return {
    items: mergedItems,
    conflicts: conflicts,
    stats: {
      totalItems: mergedItems.length,
      conflictsResolved: conflicts.length,
      localOnlyItems: localItems.filter(item => !serverItemsMap.has(item._id)).length,
      serverOnlyItems: serverItems.filter(item => !localItemsMap.has(item._id)).length
    }
  };
}

/**
 * Führt einen Merge für ein einzelnes Item durch
 * @param {Object} localItem - Lokales Item
 * @param {Object} serverItem - Server-Item
 * @param {Array} offlineActions - Offline-Aktionen für dieses Item
 * @returns {Object} - Merge-Ergebnis
 */
function mergeItem(localItem, serverItem, offlineActions) {
  const helper = useHelperStore();
  
  // Wenn beide Items identisch sind, keine Konflikte
  if (areItemsEqual(localItem, serverItem)) {
    return { item: serverItem, conflict: null };
  }

  helper.devConsole(`Merge-Konflikt für Item: ${localItem.name}`);
  
  const mergedItem = { ...serverItem }; // Starte mit Server-Item als Basis
  const conflict = {
    itemId: localItem._id,
    itemName: localItem.name,
    type: 'item_conflict',
    localValue: localItem,
    serverValue: serverItem,
    resolution: null
  };

  // KRITISCHER FIX: Normalisiere Feldnamen zwischen Frontend (isPurchased) und Backend (is_purchased)
  const localPurchased = localItem.isPurchased || localItem.is_purchased || false;
  const serverPurchased = serverItem.isPurchased || serverItem.is_purchased || false;

  // Wichtigste Regel: Wenn ein Item lokal als "gekauft" markiert wurde, behält das Priorität
  if (localPurchased && !serverPurchased) {
    helper.devConsole(`Item "${localItem.name}" wurde offline als gekauft markiert - behält gekauft-Status`);
    mergedItem.is_purchased = true;
    mergedItem.isPurchased = true; // Für Frontend-Kompatibilität
    mergedItem.purchasedAt = localItem.purchasedAt || new Date().toISOString();
    conflict.resolution = MERGE_STRATEGIES.PURCHASED_WINS;
  }
  // Wenn das Item auf dem Server als gekauft markiert wurde, aber lokal nicht, prüfe Timestamps
  else if (!localPurchased && serverPurchased) {
    // Prüfe, ob es eine lokale Aktion gab, die das Item als "nicht gekauft" markiert hat
    const unpurchaseAction = offlineActions.find(action =>
      action.type === 'updateItemPurchasedStatus' &&
      action.payload.itemId === localItem._id &&
      action.payload.isPurchased === false &&
      !action.synced
    );

    if (unpurchaseAction) {
      helper.devConsole(`Item "${localItem.name}" wurde offline als nicht-gekauft markiert - behält nicht-gekauft-Status`);
      mergedItem.is_purchased = false;
      mergedItem.isPurchased = false; // Für Frontend-Kompatibilität
      mergedItem.purchasedAt = null;
      conflict.resolution = MERGE_STRATEGIES.LOCAL_WINS;
    } else {
      // Keine lokale Änderung, Server-Status beibehalten
      mergedItem.is_purchased = serverPurchased;
      mergedItem.isPurchased = serverPurchased; // Für Frontend-Kompatibilität
      conflict.resolution = MERGE_STRATEGIES.SERVER_WINS;
    }
  }
  // Wenn beide den gleichen Status haben, behalte ihn bei
  else {
    mergedItem.is_purchased = serverPurchased;
    mergedItem.isPurchased = serverPurchased; // Für Frontend-Kompatibilität
  }

  // Merge andere Eigenschaften basierend auf Timestamps
  if (localItem.updatedAt && serverItem.updatedAt) {
    const localTime = new Date(localItem.updatedAt).getTime();
    const serverTime = new Date(serverItem.updatedAt).getTime();
    
    if (localTime > serverTime) {
      // Lokale Änderungen sind neuer
      mergedItem.quantity = localItem.quantity;
      mergedItem.unit = localItem.unit;
      mergedItem.category = localItem.category;
      if (!conflict.resolution) {
        conflict.resolution = MERGE_STRATEGIES.TIMESTAMP_WINS;
      }
    }
  }

  return { item: mergedItem, conflict: conflict };
}

/**
 * Prüft, ob zwei Items identisch sind
 * @param {Object} item1 - Erstes Item
 * @param {Object} item2 - Zweites Item
 * @returns {boolean} - true, wenn identisch
 */
function areItemsEqual(item1, item2) {
  return (
    item1._id === item2._id &&
    item1.name === item2.name &&
    item1.quantity === item2.quantity &&
    item1.unit === item2.unit &&
    item1.category === item2.category &&
    item1.isPurchased === item2.isPurchased &&
    item1.addedManually === item2.addedManually
  );
}

/**
 * Erstellt einen Snapshot des aktuellen Zustands für Rollback-Zwecke
 * @param {Array} items - Aktuelle Items
 * @param {Array} offlineActions - Aktuelle Offline-Aktionen
 * @returns {Object} - Snapshot
 */
export function createStateSnapshot(items, offlineActions) {
  return {
    timestamp: Date.now(),
    items: JSON.parse(JSON.stringify(items)),
    offlineActions: JSON.parse(JSON.stringify(offlineActions))
  };
}

/**
 * Stellt einen vorherigen Zustand wieder her
 * @param {Object} snapshot - Snapshot zum Wiederherstellen
 * @returns {Object} - Wiederhergestellter Zustand
 */
export function restoreFromSnapshot(snapshot) {
  return {
    items: snapshot.items,
    offlineActions: snapshot.offlineActions
  };
}
