<template>
  <!-- Column Builder-->
  <div class="px-6 flex md:flex-row flex-col min-h-screen">
  
    <!-- Middle Container -->
    <div class="w-full md:w-3/4 md:pr-12 mt-5 md:mt-0">
      <!-- Head-->
      <div class="w-full flex flex-col md:flex-row mt-10">
        <h1 class="w-10/12 md:w-10/12 h-auto break-all">Ein<PERSON>ufszettel</h1>
        <div class="w-full md:w-1/12 mt-4 my-auto flex flex-row">
          <img v-if="groceryliststore.groceryObject._id" @click="groceryliststore.deleteGroceryObject" src="../assets/icons/delete.png" class="w-1/3 mt-5 md:w-1/3 min-w-[20px] max-w-[21px] min-h-[20px] max-h-[21px]" />
        </div>
        <div class="w-full md:w-1/12 h-auto mt-9">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="md:w-3/5 w-full mt-4">Gemäss Planung wurde deine Einkaufsliste zusammengestellt. Du kannst manuell immer noch Elemente zur Liste hinzufügen</p>
      <!-- Head-->
      
      <!-- Information no Grocery Selected-->

      <h2 v-if="!groceryliststore.groceryObject._id" class="mt-12 text-gray-200">Wähle einen Einkaufszettel</h2>
       <!-- Search Button -->
       <button v-if="!groceryliststore.groceryObject.locked" class="w-full flex-initial md:w-1/3 bg-white rounded-xl flex mt-8">
        <input v-model="groceryliststore.groceryManualInput" class="p-3 ml-2 w-10/12 border-0 outline-none" placeholder="Hinzufügen.." />
        <div class="w-2/12 h-12 justify-end" @click.prevent="groceryliststore.createOneNewItemToTheList()"><img class="w-6 pt-3 ml-3 pr-0" src="../assets/icons/add.png" /></div>
      </button>
      
      <!-- Recieptitems Active -->
      <div class="mt-12 flex flex-wrap gap-6 pb-12 w-full" v-if="groceryliststore.groceryObject">
        <div class="move text-ellipsis w-24 h-24 bg-ordypurple-100 rounded-xl text-white box-shadow" v-for="(item,index) in groceryliststore.formattedActiveGroceryList" :key="index">
          
          <button class="w-full h-full p-1 text-center" @click="groceryliststore.changeList(index, 'activeToPassive')">
            <p class="text-[0.65em] pb-1">für {{ item.numberOfPersons }} Personen</p>
            <h3 class="text-sm break-words text-ellipsis">{{ item.name }}</h3>
            <p class="text-[0.65em] pt-1">{{ item.amount }}{{ item.unit }} {{ item.dateformatted }}</p>
          </button>

        </div>
      </div>
      <!-- Recieptitems Active-->

      <p class="mt-12 font-OpenSans" v-if="groceryliststore.groceryObject">Eingekauft</p>
      <!-- Recieptitems Passive -->
      <div class="mt-4 flex flex-wrap gap-6 pb-12 w-full z-10" v-if="groceryliststore.groceryObject">
        <div class="move text-ellipsis w-24 h-24 bg-gray-200 z-10 rounded-xl text-white box-shadow" v-for="(item2,index2) in groceryliststore.formattedPassiveGroceryList" :key="index2">
          
          <button class="w-full h-full p-1 text-center" @click="groceryliststore.changeList(index2, 'passiveToActive')">
            <p class="text-[0.65em] pb-1">für {{ item2.numberOfPersons }} Personen</p>
            <h3 class="text-sm break-words text-ellipsis">{{ item2.name }}</h3>
            <p class="text-[0.65em] pt-1">{{ item2.amount }}{{ item2.unit }} {{ item2.dateformatted }}</p>
          </button>

        </div>
      </div>
      <!-- Recieptitems Passive -->

    </div>


    <!-- Right Container -->
    <div class="w-full md:w-1/4 md:p-10">
      <h2 class="w-full">Übersicht</h2>
      <p class="">Alle vergangenen Einkaufszettel:</p>

      <div>
        <grocerylistOverview></grocerylistOverview>
      </div>
      
    </div>
    
  </div>
  
</template>
<script setup>

///////////////////// IMPORT /////////////////////////////////
import { reactive, ref, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../components/menuCard.vue'
import grocerylistOverview from '../components/grocerylistOverview.vue'
import dayCard from '../components/dayCard.vue'
import useNotification from '../../modules/notificationInformation';
import { useMenuStore } from '../store/menuStore'
import { useUserStore } from '../store/userStore'
import { useGrocerylistStore } from '../store/grocerylistStore'
import { useWeekplanStore } from '../store/weekplanStore'
import MenuCard from '../components/menuCard.vue';

  ///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userstore = useUserStore();
  const groceryliststore = useGrocerylistStore();
  const weekplanstore = useWeekplanStore();

  ////////////////////// SETUP ////////////////////////////////

  // LOAD All GroceryList By Kitchentable Id
  watch(() => userstore.user.defaultKitchentable, (newVal, oldVal) => {
      //console.log('defaultKitchentable changed from', oldVal, 'to', newVal);
      if (newVal) {
        //console.log(newVal)
        //console.log("set value")
        groceryliststore.getGrocerylistOverviewByKitchentableId(newVal);
        /*if(groceryliststore.groceryObject.endDate != null){
          groceryliststore.getGrocerysByKitchentableId()
        }*/
      }
  });

  
</script>
<style scoped>

.box-shadow {
  box-shadow: -6px 7px 0px 0px rgba(217,169,234,100);
}
.move {
  animation: move 2s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform: translate3d(0, 0, 0);
}

@keyframes move {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>