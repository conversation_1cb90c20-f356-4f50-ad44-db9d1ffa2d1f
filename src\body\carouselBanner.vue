<template>
    <div class="w-full flex flex-col md:flex-row bg-white text-black rounded-lg p-2 md:p-8 z-10 mt-20">
      <div class="md:w-1/2 md:p-8 md:px-12 w-full z-10">
        <div class="w-full font-bold text-ordypurple-100 text-center z-10 uppercase mt-5">Zu viel versprochen?</div>
        <h2 class="font-YesevaOne text-[3em] h-auto font-normal md:text-[3.5em] md:leading-[4rem] leading-[3rem] pt-5 md:mb-0 md:pt-12 w-full text-center z-10">Jetzt überzeugen wir Sie</h2>
        <ul class="w-full z-10 mt-5 md:px-20 flex flex-row text-tiny md:flex-col md:mt-12 md:space-y-6 md:text-base">
          <li
            v-for="(item, index) in industries"
            :key="index"
            @click="selectIndustry(index)"
            class="flex p-2 bg-gray-25 rounded-2xl flex-col md:flex-row text-center items-center cursor-pointer z-10 w-full md:text-base justify-center"
          >

            <!-- Beispiel-Icon -->
            <div v-if="item.svg == 1">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm4.5-11.5l-4.5 4.5L7.5 9 6 10.5l3.5 3.5 5.5-5.5L14.5 6.5z" clip-rule="evenodd"></path>
                </svg>
            </div>

            <div v-if="item.svg == 2">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M14 8V6a4 4 0 00-8 0v2H4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2v-8a2 2 0 00-2-2h-2zm-6 0V6a2 2 0 114 0v2H8z" clip-rule="evenodd"></path>
                </svg>
            </div>

            <div v-if="item.svg == 3">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M9 18a8 8 0 100-16 8 8 0 000 16zm.293-12.707a1 1 0 011.414 0l2 2a1 1 0 010 1.414l-2 2a1 1 0 01-1.414-1.414L10.586 9 9.293 7.707a1 1 0 010-1.414zM11 13v2H9v-2h2z" clip-rule="evenodd"></path>
                </svg>
            </div>

            <div v-if="item.svg == 4">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M9 3a1 1 0 011-1h6a1 1 0 011 1v10a1 1 0 01-1 1h-6a1 1 0 01-1-1V9H7a1 1 0 010-2l2-2V3z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <!-- Beispiel-Icon -->

            {{ item.name }}
          </li>
        </ul>
      </div>
      <div class="md:w-1/2 w-full flex flex-col justify-center items-center p-2 md:p-16 z-10">
        <img
          :src="currentIndustry.image"
          :alt="currentIndustry.name"
          class="rounded-lg shadow-lg max-w-full mb-4"
        />
        <p class="text-black text-tiny ">
          <strong>{{ currentIndustry.name }}</strong>: {{ currentIndustry.description }}
        </p>
        <button @click="goLogin()" class="bg-ordypurple-100 px-6 py-3 rounded-xl text-white 
        uppercase font-semibold shadow-lg hover:bg-ordypurple-200 transition block md:inline-block z-10 mx-auto mt-6 mb-6 md:mb-0">
          <span v-if="!userStore.user.id">registrieren</span>
          <span v-if="userStore.user.id">starten</span>
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { reactive, toRefs } from 'vue';
    import { useRouter } from 'vue-router'
    import { useUserStore } from '../store/userStore'

    const router = useRouter();
    const userStore = useUserStore();
  
  const industries = [
    {
      name: 'Kompliziert zu bedienen',
      image: '/_zweifen_1.png',  // Verweis auf das Bild im public-Ordner
      description: 'Unsere Assistenten unterstützen Sie beim kochen auf Schritt und Tritt. So etwas haben Sie noch nie erlebt: Glauben Sie uns nicht?',
      svg: 1
    },
    {
      name: 'Datensicherheit gefährdet',
      image: './datasecurity.jpg',
      description: 'Tatsächlich werden die Verbindungen verschlüsselt und keine Audio oder Chatverläufe werden bei Ordy gespeichert. Dies ist auch in der Datenschutzerklärung und AGBs vermerkt.',
      svg: 2
    },
    {
      name: 'Unklarer Mehrwert',
      image: '../src/assets/img/login_img1.png',
      description: 'Kochen wir für sie durch uns zur Nebensache. Wir unterstützen und planen - Sie konzentrieren sich darauf was Spass macht.',
      svg: 3
    },
    {
      name: 'Leider viel zu teuer',
      image: '../src/assets/img/_aboübersicht.png',
      description: 'Nutzen Sie Ordy in der Gratisversion. Sind Sie sich aber bewusst, dass Technologien auf diesem ausserordentlichen Niveau teuer sind. Datensicherheit und Technologievorsprung haben in der Schweiz ihren Preis.',
      svg: 4
    }
  ];

  const goLogin = () => {
    router.push({ name: 'kochbuch'})
    };
  
  const state = reactive({
    currentIndustry: industries[0],
  });
  
  function selectIndustry(index) {
    state.currentIndustry = industries[index];
  }
  
  const { currentIndustry } = toRefs(state);
  </script>
  
  <style>
  /* Optional: Zusätzliche CSS-Styles */
  </style>