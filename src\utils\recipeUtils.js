/**
 * Utility functions for recipe processing and dynamic ingredient quantities
 */

/**
 * <PERSON>rse<PERSON><PERSON> Platzhalter in Rezeptanweisungen mit aktuellen Mengenangaben
 * @param {string} instructionText - Der Anweisungstext mit Platzhaltern
 * @param {Array} ingredients - Array der Zutaten mit Mengen
 * @param {number} currentPersons - Aktuelle Personenanzahl
 * @param {number} originalPersons - Original Personenanzahl des Rezepts
 * @returns {string} - Verarbeiteter Text mit eingesetzten Mengen
 */
export function processInstructionText(instructionText, ingredients, currentPersons, originalPersons) {
  if (!instructionText || !ingredients) {
    return instructionText;
  }

  // Skalierungsfaktor berechnen
  const scaleFactor = currentPersons / originalPersons;

  let processedText = instructionText;

  // ID-basierte Platzhalter: ${ID:1}, ${MENGE:1}, ${EINHEIT:1} mit kompaktem, farbkodiertem Styling
  // WICHTIG: IDs sind stabil und ändern sich nicht beim <PERSON> von Zutaten
  processedText = processedText.replace(/\$\{ID:(\d+)\}/g, (match, ingredientId) => {
    const stableId = parseInt(ingredientId);

    // DEBUG: Detaillierte Informationen
    console.log(`🔍 Processing placeholder ${match}:`, {
      searchingForStableId: stableId,
      totalIngredients: ingredients.length,
      ingredientsWithStableIds: ingredients.map((ing, idx) => ({
        index: idx,
        name: ing.name?.name || 'Unknown',
        stableId: ing.stableId,
        stableIdType: typeof ing.stableId
      })),
      availableStableIds: ingredients.map(ing => ing.stableId).filter(id => id !== undefined),
      actualStableIds: ingredients.map(ing => ing.stableId),
      missingId: stableId,
      PROBLEM: `Searching for ID ${stableId} but available IDs are: [${ingredients.map(ing => ing.stableId).join(', ')}]`
    });

    // Suche Zutat mit stabiler numerischer ID (nicht Array-Index!)
    const ingredient = ingredients.find(ing =>
      (typeof ing.stableId === 'number' && ing.stableId === stableId) ||
      (ing.stableId === undefined && ingredients.indexOf(ing) + 1 === stableId)
    );

    if (ingredient) {
      const scaledAmount = Math.round((ingredient.amount * scaleFactor) * 10) / 10;
      const content = `${scaledAmount} ${ingredient.unit.name} ${ingredient.name.name}`;

      // Kategorie-basierte Farben für Platzhalter
      const categoryColor = getCategoryColorForPlaceholder(ingredient);

      console.log(`✅ Found ingredient for ${match}:`, ingredient.name?.name);
      return `<span class="ingredient-placeholder ${categoryColor} px-1.5 py-0.5 rounded-md text-xs font-medium shadow-sm hover:shadow-md transition-all duration-200 inline-block mx-1 whitespace-nowrap">${content}</span>`;
    }
    console.warn(`❌ ID-Platzhalter ${match} konnte nicht ersetzt werden - Stabile ID ${stableId} nicht gefunden`);
    return `<span class="ingredient-placeholder-missing bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded-md text-xs mx-1">[Zutat ${stableId} nicht gefunden]</span>`;
  });

  // ENTFERNT: ${MENGE:X} und ${EINHEIT:X} Platzhalter sind veraltet
  // Nur noch ${ID:X} Platzhalter werden unterstützt

  // Warnung für veraltete Platzhalter
  const deprecatedPlaceholders = processedText.match(/\$\{(MENGE|EINHEIT):\d+\}/g);
  if (deprecatedPlaceholders) {
    console.warn('⚠️ VERALTETE PLATZHALTER GEFUNDEN:', deprecatedPlaceholders);
    console.warn('Bitte verwenden Sie nur ${ID:X} Platzhalter. ${MENGE:X} und ${EINHEIT:X} sind veraltet.');

    // Entferne veraltete Platzhalter aus dem Text
    processedText = processedText.replace(/\$\{(MENGE|EINHEIT):\d+\}/g, '');
  }

  // ENTFERNT: Namens-basierte Platzhalter (${MEHL}, ${MENGE:OLIVENOEL}, etc.)
  // Nur noch ID-basierte Platzhalter werden unterstützt: ${ID:1}, ${MENGE:1}, ${EINHEIT:1}

  return processedText;
}

// Kategorie-basierte Farben für Platzhalter im Fließtext
const getCategoryColorForPlaceholder = (ingredient) => {
  const ingredientName = ingredient.name?.name?.toLowerCase() || '';

  // Gemüse & Früchte - Grün
  if (ingredientName.includes('brokkoli') || ingredientName.includes('tomate') || ingredientName.includes('zwiebel') ||
      ingredientName.includes('karotte') || ingredientName.includes('paprika') || ingredientName.includes('salat') ||
      ingredientName.includes('apfel') || ingredientName.includes('banane') || ingredientName.includes('orange')) {
    return 'bg-green-100 text-green-800';
  }

  // Milchprodukte - Blau
  if (ingredientName.includes('milch') || ingredientName.includes('käse') || ingredientName.includes('joghurt') ||
      ingredientName.includes('butter') || ingredientName.includes('sahne') || ingredientName.includes('quark') ||
      ingredientName.includes('parmesan') || ingredientName.includes('frischkäse')) {
    return 'bg-blue-100 text-blue-800';
  }

  // Fleisch & Fisch - Rot
  if (ingredientName.includes('fleisch') || ingredientName.includes('hähnchen') || ingredientName.includes('rind') ||
      ingredientName.includes('schwein') || ingredientName.includes('fisch') || ingredientName.includes('lachs') ||
      ingredientName.includes('thunfisch') || ingredientName.includes('garnele')) {
    return 'bg-red-100 text-red-800';
  }

  // Getreide & Pasta - Orange
  if (ingredientName.includes('pasta') || ingredientName.includes('nudel') || ingredientName.includes('reis') ||
      ingredientName.includes('brot') || ingredientName.includes('mehl') || ingredientName.includes('haferflocken')) {
    return 'bg-orange-100 text-orange-800';
  }

  // Gewürze & Kräuter - Gelb (statt Violett wegen App-Hintergrund)
  if (ingredientName.includes('salz') || ingredientName.includes('pfeffer') || ingredientName.includes('petersilie') ||
      ingredientName.includes('basilikum') || ingredientName.includes('oregano') || ingredientName.includes('kräuter')) {
    return 'bg-yellow-100 text-yellow-800';
  }

  // Standard - Grau
  return 'bg-gray-100 text-gray-800';
};

// ENTFERNT: findIngredientByKey - Nicht mehr benötigt für ID-basiertes System

/**
 * Generiert einen Schlüssel für eine Zutat (für KI-Prompts)
 * @param {string} ingredientName - Name der Zutat
 * @returns {string} - Normalisierter Schlüssel
 */
export function generateIngredientKey(ingredientName) {
  if (!ingredientName) return '';

  return ingredientName
    .toUpperCase()
    .replace(/[ÄÖÜSS]/g, (match) => {
      const replacements = { 'Ä': 'AE', 'Ö': 'OE', 'Ü': 'UE', 'ß': 'SS' };
      return replacements[match] || match;
    })
    .replace(/[^A-Z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
}

/**
 * Erstellt eine Zutatenliste für KI-Prompts mit Schlüsseln
 * @param {Array} ingredients - Array der Zutaten
 * @returns {string} - Formatierte Zutatenliste für KI
 */
export function createIngredientListForAI(ingredients) {
  if (!ingredients || !Array.isArray(ingredients)) return '';

  return ingredients.map(ingredient => {
    if (!ingredient.name || !ingredient.name.name) return '';

    const key = generateIngredientKey(ingredient.name.name);
    const amount = ingredient.amount || 0;
    const unit = ingredient.unit?.name || '';
    const name = ingredient.name.name;

    return `- ${name}: ${amount} ${unit} (Platzhalter: \${${key}} oder \${MENGE:${key}} für nur die Menge)`;
  }).filter(line => line).join('\n');
}

/**
 * Validiert und bereinigt Rezeptanweisungen für dynamische Platzhalter
 * @param {Array} preparationSteps - Array der Zubereitungsschritte
 * @param {Array} ingredients - Array der Zutaten
 * @returns {Array} - Bereinigte Zubereitungsschritte
 */
export function validateAndCleanInstructions(preparationSteps, ingredients) {
  if (!preparationSteps || !Array.isArray(preparationSteps)) return [];

  const availableKeys = ingredients.map(ing =>
    ing.name?.name ? generateIngredientKey(ing.name.name) : ''
  ).filter(key => key);

  return preparationSteps.map(step => {
    if (!step.content) return step;

    let cleanedContent = step.content;

    // Finde alle Platzhalter im Text
    const placeholders = cleanedContent.match(/\$\{[^}]+\}/g) || [];

    placeholders.forEach(placeholder => {
      const key = placeholder.replace(/\$\{(MENGE:|EINHEIT:)?([^}]+)\}/, '$2');

      // Prüfe ob der Schlüssel zu einer verfügbaren Zutat passt
      const hasMatchingIngredient = availableKeys.some(availableKey =>
        availableKey === key ||
        availableKey.includes(key) ||
        key.includes(availableKey)
      );

      if (!hasMatchingIngredient) {
        console.warn(`Platzhalter ${placeholder} hat keine passende Zutat. Verfügbare Schlüssel:`, availableKeys);
      }
    });

    return {
      ...step,
      content: cleanedContent
    };
  });
}

/**
 * Erstellt einen erweiterten KI-Prompt für Rezepterstellung mit dynamischen Platzhaltern
 * @param {Object} basePrompt - Basis-Prompt-Objekt
 * @param {Array} ingredients - Array der Zutaten
 * @returns {string} - Erweiterter Prompt
 */
export function createEnhancedRecipePrompt(basePrompt, ingredients) {
  const ingredientList = createIngredientListForAI(ingredients);

  const dynamicInstructionsGuide = `
WICHTIG - Dynamische Mengenangaben in Anweisungen:
Verwende in den Zubereitungsschritten Platzhalter für Mengenangaben, damit sich diese automatisch an die Personenanzahl anpassen.

Verfügbare Zutaten und ihre Platzhalter:
${ingredientList}

Platzhalter-Formate:
- \${ZUTAT_SCHLUESSEL} = Komplette Angabe (z.B. "250g Mehl")
- \${MENGE:ZUTAT_SCHLUESSEL} = Nur die Menge (z.B. "250")
- \${EINHEIT:ZUTAT_SCHLUESSEL} = Nur die Einheit (z.B. "g")

Beispiele für gute Anweisungen:
- "Geben Sie \${MEHL} in eine Schüssel."
- "Erhitzen Sie \${MENGE:OEL} EL Öl in einer Pfanne."
- "Fügen Sie \${ZWIEBELN} hinzu und braten Sie sie an."

WICHTIG: Verwende diese Platzhalter NUR für Zutaten, die in der obigen Liste stehen!
`;

  return `${basePrompt}\n\n${dynamicInstructionsGuide}`;
}

/**
 * KRITISCH: Korrigiert stableId-Zuweisungen nach KI-Generierung (Stabile IDs)
 * Stellt sicher, dass ${ID:1} tatsächlich auf die erste Zutat zeigt, etc.
 * @param {Object} recipe - Das generierte Rezept-Objekt
 * @returns {Object} - Korrigiertes Rezept mit richtigen stableIds
 */
export function fixStableIdsAfterAIGeneration(recipe) {
  console.log('🔧 Fixing stable IDs after AI generation (PERMANENT IDs)...');

  if (!recipe?.menuchilds?.menuChildId?.ingredients) {
    console.warn('No ingredients found in recipe');
    return recipe;
  }

  const ingredients = recipe.menuchilds.menuChildId.ingredients;

  // KRITISCH: Weise stabile IDs zu (1-basiert) - diese ändern sich NIE mehr
  ingredients.forEach((ingredient, index) => {
    const stableId = index + 1; // 1-basiert: erste Zutat = ID 1
    ingredient.stableId = stableId;
    console.log(`🆔 PERMANENT stable ID: Ingredient "${ingredient.name?.name || 'Unknown'}" at position ${index} → stableId ${stableId}`);
  });

  // Setze das Maximum für dieses Rezept
  const maxStableId = ingredients.length;
  recipe.menuchilds.menuChildId.maxUsedStableId = maxStableId;
  console.log(`🔢 Set recipe max stable ID to: ${maxStableId} (per recipe)`);

  // ERWEITERT: Repariere falsche Platzhalter im Rezepttext
  recipe = fixPlaceholdersInRecipeText(recipe);

  console.log('✅ PERMANENT stable ID fix completed');
  return recipe;
}

/**
 * KRITISCH: Repariert falsche Platzhalter im Rezepttext
 * Ersetzt ${ID:X} mit hohen IDs durch korrekte IDs basierend auf verfügbaren Zutaten
 * @param {Object} recipe - Das Rezept-Objekt
 * @returns {Object} - Rezept mit korrigierten Platzhaltern
 */
export function fixPlaceholdersInRecipeText(recipe) {
  console.log('🔧 Fixing placeholders in recipe text...');

  if (!recipe?.menuchilds?.menuChildId) {
    return recipe;
  }

  const ingredients = recipe.menuchilds.menuChildId.ingredients || [];
  const availableIds = ingredients.map(ing => ing.stableId).filter(id => typeof id === 'number');
  const maxAvailableId = Math.max(...availableIds, 0);

  console.log(`📋 Available ingredient IDs: [${availableIds.join(', ')}], max: ${maxAvailableId}`);

  // Funktion zum Reparieren von Text
  const fixTextPlaceholders = (text) => {
    if (!text || typeof text !== 'string') return text;

    return text.replace(/\$\{ID:(\d+)\}/g, (match, idStr) => {
      const requestedId = parseInt(idStr);

      // Wenn die ID verfügbar ist, behalte sie
      if (availableIds.includes(requestedId)) {
        return match; // Keine Änderung nötig
      }

      // Wenn die ID zu hoch ist, mappe sie auf eine verfügbare ID
      if (requestedId > maxAvailableId && maxAvailableId > 0) {
        // Zyklische Zuordnung: ID 11 → ID 1, ID 12 → ID 2, etc.
        const mappedId = ((requestedId - 1) % maxAvailableId) + 1;
        console.log(`🔄 Mapping placeholder ${match} → \${ID:${mappedId}}`);
        return `\${ID:${mappedId}}`;
      }

      // Fallback: Verwende ID 1 wenn verfügbar
      if (availableIds.includes(1)) {
        console.log(`🔄 Fallback mapping ${match} → \${ID:1}`);
        return '${ID:1}';
      }

      // Letzter Fallback: Behalte Original (wird später als "nicht gefunden" behandelt)
      console.warn(`⚠️ Cannot fix placeholder ${match} - no suitable replacement found`);
      return match;
    });
  };

  // Repariere instruction Text
  if (recipe.menuchilds.menuChildId.instruction) {
    const originalInstruction = recipe.menuchilds.menuChildId.instruction;
    const fixedInstruction = fixTextPlaceholders(originalInstruction);
    if (fixedInstruction !== originalInstruction) {
      recipe.menuchilds.menuChildId.instruction = fixedInstruction;
      console.log('✅ Fixed placeholders in instruction text');
    }
  }

  // Repariere preperation Steps
  if (recipe.menuchilds.menuChildId.preperation && Array.isArray(recipe.menuchilds.menuChildId.preperation)) {
    recipe.menuchilds.menuChildId.preperation.forEach((step, index) => {
      if (step.content) {
        const originalContent = step.content;
        const fixedContent = fixTextPlaceholders(originalContent);
        if (fixedContent !== originalContent) {
          step.content = fixedContent;
          console.log(`✅ Fixed placeholders in preparation step ${index + 1}`);
        }
      }
    });
  }

  console.log('✅ Placeholder fix completed');
  return recipe;
}
