<template>
    <div class="bg-ordy h-auto z-0">
        <!-- Cookies -->
        <noSessionUserInformation></noSessionUserInformation>
        <!-- Cookies -->
        <!-- Cookies -->
        <cookieBanner></cookieBanner>
        <!-- Cookies -->
         <!-- confirmation -->
        <basic-confirmation></basic-confirmation>
        <!-- confirmation -->
         <!-- notification -->
        <basic-notification></basic-notification>
        <!-- notification -->
        <!-- appInstallCard -->
        <appInstallCard></appInstallCard>
        <!-- appInstallCard -->
        <!-- reloadsite
        <div v-if="reload" class="w-full relative h-12 bg-green-500">
            <img class="w-6 animate-spin" src="../assets/icons/reload.png" alt="searching" />
        </div> -->
         <!-- reloadsite -->
        <div class="flex flex-col h-auto">
            <div class="w-full h-auto relative z-60">
                <top-navigation></top-navigation>
            </div>
        </div>
        <div class="flex flex-col md:flex-row mt-6 md:mt-2 pb-12">

            <!-- Menü Spalte -->
            <!-- if min-w- is more than 300px, mobile devices have problems (white border on the right) -->
            <div class="
            w-full
            md:w-1/12 md:min-w-[250px]
            lg:w-1/12 lg:min-w-[220px]
            xl:w-1/12 xl:min-w-[220px]
            2xl:w-3/12 2xl:min-w-[250px]
            3xl:w-3/12 3xl:min-w-[250px]
            px-5 md:relative md:order-1 flow order-2 fixed bottom-0 z-40">
                <!-- Menü Inhalt hier -->
                <left-sidebar></left-sidebar>
            </div>

            <!-- Inhalt Spalte -->
            <div class="w-full md:w-4/5 static order-1 md:order-2 h-auto">

                <!-- Inhalt hier -->
                <div class="w-full min-h-screen pb-24 md:pl-0 md:pr-0">
                        <router-view></router-view>
                </div>
            </div>

        </div>
        <footerSection :isHidden="true"></footerSection>
    </div>
</template>
<script setup>
    import { ref, onMounted, onBeforeUnmount } from 'vue'

    // Importiere Komponenten aus dem Ordner body
    import cookieBanner from './cookieBanner.vue'
    import footerSection from './footerSection.vue'
    import basicNotification from './basicNotification.vue'
    import leftSidebar from './leftSidebar.vue'
    import rightSidebar from './rightSidebar.vue'
    import topNavigation from './topNavigation.vue'
    import appInstallCard from '../components/appInstallCard.vue'

    let wakeLock = null;
    const wakeLockActive = ref(false);

    // Funktion zum Anfordern des Wake Locks
    async function requestWakeLock() {
      // Only request wake lock if document is visible
      if (document.visibilityState !== 'visible') {
        console.log('Wake Lock not requested - page not visible');
        return;
      }

      try {
        wakeLock = await navigator.wakeLock.request('screen');
        wakeLockActive.value = true;
        console.log('Wake Lock active');
      } catch (err) {
        // Only log as info for NotAllowedError when page is not visible
        if (err.name === 'NotAllowedError' && document.visibilityState !== 'visible') {
          console.info(`Wake Lock not allowed - page not visible`);
        } else {
          console.error(`Could not obtain wake lock: ${err.name}, ${err.message}`);
        }
      }
    }

    // Funktion zum Freigeben des Wake Locks
    async function releaseWakeLock() {
      if (wakeLock !== null && wakeLockActive.value) {
        try {
          await wakeLock.release();
          wakeLock = null;
          wakeLockActive.value = false;
          console.log('Wake Lock released');
        } catch (err) {
          console.error(`Could not release wake lock: ${err.name}, ${err.message}`);
        }
      }
    }

    // Setze das Wake Lock bei der Initialisierung
    onMounted(() => {
      requestWakeLock();

      // Überwacht den Sichtbarkeitsstatus der Seite
      document.addEventListener('visibilitychange', () => {
        // Only try to request wake lock when page becomes visible
        if (document.visibilityState === 'visible') {
          // Small delay to ensure the page is fully visible before requesting
          setTimeout(() => {
            requestWakeLock();
          }, 300);
        } else if (document.visibilityState === 'hidden') {
          // Wake lock is automatically released by the browser when hidden
          // Just update our internal state
          if (wakeLockActive.value) {
            console.log('Page hidden, wake lock likely released by browser');
            wakeLockActive.value = false;
          }
        }
      });
    });

    // Stelle sicher, dass das Wake Lock freigegeben wird, bevor die Komponente zerstört wird
    onBeforeUnmount(() => {
      releaseWakeLock();
    });

</script>
<style>
    .text-xl{
        line-height: 0.86em;
    }
    .bg-ordy{
        background: linear-gradient(152.67deg, #FFFFFF -4.31%, #EBDFFF 55.19%, #F8E5F3 117.2%, #FFFFFF 117.21%);
    }



</style>