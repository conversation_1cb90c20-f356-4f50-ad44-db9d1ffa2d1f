[2025-06-03T08:29:58.469Z] === AZURE DEPLOYMENT STARTUP LOGS ===
[2025-06-03T08:29:58.470Z] Deployment gestartet: 2025-06-03T08:29:58.470Z
[2025-06-03T08:29:58.470Z] 
=== SYSTEM INFORMATION ===
[2025-06-03T08:29:58.473Z] Hostname: DESKTOP-S93FA43
[2025-06-03T08:29:58.473Z] Platform: win32
[2025-06-03T08:29:58.474Z] Architecture: x64
[2025-06-03T08:29:58.474Z] Node.js-Version: v22.15.0
[2025-06-03T08:29:58.474Z] Total Memory: 32656 MB
[2025-06-03T08:29:58.474Z] Free Memory: 19071 MB
[2025-06-03T08:29:58.475Z] CPU Count: 24
[2025-06-03T08:29:58.475Z] 
=== AZURE ENVIRONMENT VARIABLES ===
[2025-06-03T08:29:58.476Z] WEBSITE_SITE_NAME: (nicht gesetzt)
[2025-06-03T08:29:58.476Z] WEBSITE_HOSTNAME: (nicht gesetzt)
[2025-06-03T08:29:58.476Z] WEBSITE_INSTANCE_ID: (nicht gesetzt)
[2025-06-03T08:29:58.477Z] WEBSITE_NODE_DEFAULT_VERSION: (nicht gesetzt)
[2025-06-03T08:29:58.477Z] WEBSITE_OWNER_NAME: (nicht gesetzt)
[2025-06-03T08:29:58.477Z] WEBSITE_RESOURCE_GROUP: (nicht gesetzt)
[2025-06-03T08:29:58.477Z] 
=== APPLICATION ENVIRONMENT VARIABLES ===
[2025-06-03T08:29:58.478Z] NODE_ENV: (nicht gesetzt)
[2025-06-03T08:29:58.478Z] PORT: (nicht gesetzt)
[2025-06-03T08:29:58.478Z] 
=== FILE SYSTEM CHECK ===
[2025-06-03T08:29:58.478Z] server.js: Vorhanden
[2025-06-03T08:29:58.479Z] app.js: Vorhanden
[2025-06-03T08:29:58.479Z] package.json: Vorhanden
[2025-06-03T08:29:58.479Z] web.config: Vorhanden
[2025-06-03T08:29:58.480Z] .deployment: FEHLT!
[2025-06-03T08:29:58.480Z] 
App-Version: orderapi-tst@2.3.4
[2025-06-03T08:29:58.481Z] 
=== MONGODB CONNECTION CHECK ===
[2025-06-03T08:29:58.481Z] MongoDB-Verbindung wird überprüft für: Entwicklung/Test
[2025-06-03T08:29:58.481Z] Verbindungsstring vorhanden: false
[2025-06-03T08:29:58.482Z] Passwort vorhanden: false
[2025-06-03T08:29:58.482Z] 
=== NPM PACKAGE CHECK ===
[2025-06-03T08:29:59.649Z] Fehler bei der npm-Paketüberprüfung: Command failed: npm list --depth=0
npm ERR! max depth reached: @aws-sdk/credential-providers@^3.188.0, required by mongodb@6.16.0
npm ERR! max depth reached: @mongodb-js/zstd@^1.1.0 || ^2.0.0, required by mongodb@6.16.0
npm ERR! max depth reached: gcp-metadata@^5.2.0, required by mongodb@6.16.0
npm ERR! max depth reached: kerberos@^2.0.1, required by mongodb@6.16.0
npm ERR! max depth reached: mongodb-client-encryption@>=6.0.0 <7, required by mongodb@6.16.0
npm ERR! max depth reached: snappy@^7.2.2, required by mongodb@6.16.0
npm ERR! max depth reached: zod@^3.23.8, required by openai@4.98.0
npm ERR! max depth reached: aws-crt@>=1.0.0, required by @aws-sdk/util-user-agent-node@3.806.0
npm ERR! extraneous: fast-json-stable-stringify@2.1.0 C:\src\ordy\ordy-api\node_modules\fast-json-stable-stringify
npm ERR! extraneous: p-try@2.2.0 C:\src\ordy\ordy-api\node_modules\p-try
npm ERR! extraneous: type-fest@0.20.2 C:\src\ordy\ordy-api\node_modules\type-fest
npm ERR! extraneous: uri-js@4.4.1 C:\src\ordy\ordy-api\node_modules\uri-js
npm ERR! max depth reached: less@*, required by vite@6.3.5
npm ERR! max depth reached: sass@*, required by vite@6.3.5
npm ERR! max depth reached: sass-embedded@*, required by vite@6.3.5
npm ERR! max depth reached: stylus@*, required by vite@6.3.5
npm ERR! max depth reached: sugarss@*, required by vite@6.3.5
npm ERR! max depth reached: tsx@^4.8.1, required by vite@6.3.5

[2025-06-03T08:29:59.650Z] 
=== STARTUP COMPLETED ===
[2025-06-03T08:29:59.650Z] Startup-Überprüfung abgeschlossen: 2025-06-03T08:29:59.650Z
[2025-06-03T08:29:59.650Z] Starte jetzt den Hauptserver...

[2025-06-03T08:29:59.651Z] Starting in local environment...
[2025-06-03T08:30:02.026Z] Server successfully started.
