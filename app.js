const path = require('path');
const express = require('express');
const expressWs = require('express-ws');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const cors = require('cors');
const helpers = require('./utils/helper');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');
const mongoose = require('mongoose');
const WebSocket = require('ws');
const Buffer = require('buffer').Buffer;

// Webhook Controller
const paymentController = require('./controllers/paymentController');
const authController = require('./controllers/authController'); // For WS auth
const Kitchentable = require('./models/kitchentableModel'); // For WS auth
// Import the controller responsible for shopping list items - USE shoppingListController instead
// const shoppingListItemController = require('./controllers/shoppingListItemController'); // Removed import
const shoppingListController = require('./controllers/shoppingListController'); // Ensure this is imported
// Import models needed for fetching list data in WS handler
const ShoppingList = require('./models/shoppingListModel');
const ShoppingListItem = require('./models/shoppingListItemModel');
const User = require('./models/userModel'); // <<< MODIFIED: Added User model import
// const gptController = require('./controllers/gptController'); // Will be removed as logic moves here

// Start express app
const appInstance = express();
const wsInstance = expressWs(appInstance);
const app = wsInstance.app;

const AppError = require('./utils/appError');
const globalErrorHandler = require('./controllers/errorController');
const gptRouter = require('./routes/gptRoutes');
const menuRouter = require('./routes/menuRoutes');
const authRouter = require('./routes/authRoutes');
const weekplanRoutes = require('./routes/weekplanRoutes');
const grocerylistRoutes = require('./routes/grocerylistRoutes');
const kitchentableRoutes = require('./routes/kitchentableRoutes');
const peaxRoutes = require('./routes/peax/peaxRoutes');
// Require the new routers (CommonJS style)
const shoppingListRouter = require('./routes/shoppingListRoutes');
const shoppingListItemRouter = require('./routes/shoppingListItemRoutes');
const oauthRouter = require('./routes/oauthRoutes');
// const marketingContentRouter = require('./routes/marketingContentRoutes'); // Removed
// const recorderRouter = require('./routes/recorderRoutes'); // Removed
const marketingWorkflowRouter = require('./routes/marketingWorkflowRoutes');
const marketingContentRouter = require('./routes/marketingContentRoutes'); // Add the new router require
const publishRouter = require('./routes/publishRoutes'); // <<< ADD THIS LINE
const pinterestOAuthRouter = require('./routes/pinterestOAuthRoutes'); // Pinterest OAuth routes
const legalRouter = require('./routes/legalRoutes'); // Legal pages (AGB, Impressum, Terms, Privacy)

// Sicherere Konfiguration für 'trust proxy'
// Option 1: Vertraue nur bestimmten Proxy-Servern (z.B. AWS LoadBalancer oder Cloudflare)
app.set('trust proxy', ['loopback', 'linklocal', 'uniquelocal']);

// 1) GLOBAL MIDDLEWARES

// Development logging
let envMode = process.env.NODE_ENV; // process is global in CommonJS
let corsOptions = {};
if (envMode === 'development' || envMode === 'preview') {
  helpers.devConsole("--------------- dev morgan running ----------------")
  app.use(morgan('dev'));

  corsOptions = {
    origin: '*', // Erlaube diese Domains
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Erlaube diese HTTP-Methoden
    credentials: true, // Erlaube Cookies und Autorisierung
  };
} else {
  corsOptions = {
    origin: ['https://peaxmicrosoft.kinnovations.ch', 'https://www.ordy.ch', 'https://www.ordyapp.com', 'https://test.ordy.ch'], // Erlaube diese Domains
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Erlaube diese HTTP-Methoden
    credentials: true, // Erlaube Cookies und Autorisierung
  };
}

helpers.devConsole(corsOptions)

// Enable CORS for all origins in development, configure specific origins for production
const allowedOrigins =
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview'
    ? ['http://localhost:5173', 'http://localhost:8080', 'https://test.ordy.ch', 'https://www.ordy.ch', 'https://www.ordyapp.com']
    : ['https://www.ordy.ch', 'https://test.ordy.ch', 'https://www.ordyapp.com'];

app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        if (allowedOrigins.indexOf(origin) === -1) {
            const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
            return callback(new Error(msg), false);
        }
        return callback(null, true);
    },
    credentials: true // Allow cookies and authorization headers
}));

// Serving static files
app.use(express.static(path.join(__dirname, 'public')));

// Set security HTTP headers
app.use(helmet());

// Sessions
/*app.use(session({
  secret: 'keyCaterySnap1234',
  resave: false,
  saveUninitialized: false
}))*/

// Passport middleware
//app.use(passport.initialize())
//app.use(passport.session())

// Limit requests from same API
const limiter = rateLimit({
  max: 100,
  windowMs: 60 * 60 * 1000,
  message: 'Too many requests from this IP, please try again in an hour!',
  // Explizite Konfiguration für Rate-Limiter hinzufügen
  trustProxy: false, // Explizit 'trust proxy' Einstellung für Rate-Limiter deaktivieren
  // Eigener Key-Generator, um nur die direkte IP zu verwenden
  keyGenerator: (req) => {
    return req.ip; // Nutzt die von Express interpretierte IP
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api', limiter);

// Stripe webhook, BEFORE body-parser, because stripe needs the body as stream
app.post(
  '/api/v1/auth/payment/stripe_webhook',
  express.raw({ type: 'application/json' }),
  paymentController.stripewebhook
  //bookingController.webhookCheckout
);

// Body parser, reading data from body into req.body
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use(cookieParser());

// Create Access Controll Allow Headers
// alle akzeptierten headers
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Headers', 'Origin, x-authorization-update, Content-Type, Accept, Authorization')
  res.setHeader('Cache-Control', 'no-store');
  next()
})

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Data sanitization against XSS
app.use(xss());

// Prevent parameter pollution
app.use(
  hpp({
    whitelist: [
      'duration',
      'ratingsQuantity',
      'ratingsAverage',
      'maxGroupSize',
      'difficulty',
      'price'
    ]
  })
);

app.use(compression());

// Test middleware
app.use((req, res, next) => {
  req.requestTime = new Date().toISOString();
  // console.log(req.cookies);
  next();
});

// 2) SWAGGER API DOCUMENTATION (Now available in all environments)
// Removed environment check to make docs available in production
// WARNING: Exposes API structure publicly. Consider adding auth (Option B) if needed.
helpers.devConsole('Setting up Swagger documentation...');

  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Ordy API Documentation',
        version: '1.0.0',
        description: 'API Dokumentation für die Ordy Backend Anwendung',
      },
      servers: [
        {
        // Adjust URL based on environment if necessary, or use relative paths
        url: `/`, // Using relative path might be more robust
        description: 'Current server',
        },
      ],
      components: { // Beispiel für JWT Auth
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          }
        }
      },
      security: [{ // Global JWT Auth anwenden
        bearerAuth: []
      }]
    },
    apis: [
        path.join(__dirname, './routes/*.js'), // Routen-Dateien
        path.join(__dirname, './controllers/**/*.js') // Controller-Dateien (optional, falls dort Defs sind)
    ],
  };

  try {
    const swaggerSpec = swaggerJsdoc(options);
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
    app.get('/api-docs.json', (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });
  // Log might show incorrect port in production if PORT env var isn't set there
  helpers.devConsole(`API documentation available at /api-docs`);
  } catch (err) {
      console.error("Error setting up Swagger documentation:", err);
}

// VORGEHEN
/*
reactivate auth
reactivate kitchentable
reactivate menu
reactivate creator/gpt
*/

// 3) ROUTES
app.use('/api/v1/auth', authRouter);
app.use('/api/v1/creator', gptRouter);
app.use('/api/v1/menu', menuRouter);
app.use('/api/v1/weekplan', weekplanRoutes);
app.use('/api/v1/grocerylist', grocerylistRoutes);
app.use('/api/v1/kitchentable', kitchentableRoutes);
app.use('/api/shopping-lists/:listId', shoppingListRouter);
app.use('/api/shopping-list-items', shoppingListItemRouter);
app.use('/api/v1/peax', peaxRoutes);
app.use('/api/v1/oauth', oauthRouter);
// app.use('/api/v1/marketing-content', marketingContentRouter); // Removed
// app.use('/api/v1/recorder', recorderRouter); // Removed
app.use('/api/v1/marketing-workflow', marketingWorkflowRouter);
app.use('/api/v1/marketing-content', marketingContentRouter); // Mount the new router
app.use('/api/v1/marketing/publish', publishRouter); // <<< ADD THIS LINE
app.use('/api/v1/pinterest/oauth', pinterestOAuthRouter); // Pinterest OAuth routes
app.use('/api/v1/admin', require('./routes/adminRoutes')); // Admin backend routes
app.use('/api/v1/categorization', require('./routes/categorizationRoutes')); // 🤖 Intelligent categorization routes
app.use('/api/v1/legal', legalRouter); // Legal pages (public, no auth required)

// KRITISCH: Pinterest OAuth Callback Route hinzufügen (für /auth/pinterest/callback)
app.use('/auth/pinterest', oauthRouter); // Pinterest callback route

// Facebook OAuth Callback Route hinzufügen (für /auth/facebook/callback)
app.use('/auth/facebook', require('./routes/facebookOAuthRoutes')); // Facebook callback route

// --- WebSocket Setup ---

// Store active WebSocket connections per Kitchentable room
// TODO: Consider scalability for large numbers of connections/rooms.
//       A dedicated solution like Redis pub/sub might be better for production.
const kitchentableRooms = {}; // e.g., { 'kitchentable_123': [ws1, ws2], 'kitchentable_456': [ws3] }

// WebSocket handler for GPT Assistant
app.ws('/ws/assist-ant/startconversation', async (ws, req) => {
    helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] Entered WebSocket handler. Req URL: ${req.url}`);
    // --- AUTHENTICATION --- START
    helpers.devConsole(`[${new Date().toISOString()}] [App.js WS] User ${ws.userId || 'N/A'}: Authentication process started.`);
    let authResult;
    let directAppendCounter = 0; // MODIFIED: Initialize counter here
    try {
        const token = req.query.token;
        helpers.devConsole('[App.js WS /ws/assist-ant/startconversation] Connection attempt...');

        if (!token) {
            helpers.devConsole('[App.js WS /ws/assist-ant/startconversation] Auth Error: No token provided.');
            ws.send(JSON.stringify({ event: 'auth_error', data: { message: 'Authentication token required.' }}));
            return ws.close(1008, "Authentication token required.");
        }
        authResult = await authController.verifyStytchTokenForWs(token);
        if (!authResult || !authResult.isValid) {
             const errorMessage = authResult?.error || 'Invalid or expired session token.';
             helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] Auth Error: Token verification failed - ${errorMessage}`);
             ws.send(JSON.stringify({ event: 'auth_error', data: { message: errorMessage }}));
             return ws.close(1008, errorMessage);
        }
        ws.userId = authResult.userId;
        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId} AUTH SUCCESS.`);
        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId} authenticated.`);
        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS] User ${ws.userId}: Authentication process completed. UserId: ${ws.userId}.`);
        // --- AUTHENTICATION --- END

        // ---> SEND BACKEND_READY MESSAGE TO CLIENT -- START <---
        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS] User ${ws.userId}: Sending 'backend_ready_for_messages'.`);
        try {
            if (ws.readyState === WebSocket.OPEN) { // Ensure WebSocket is open
                const backendReadyMsg = JSON.stringify({ event: 'backend_ready_for_messages', data: { userId: ws.userId } });
                ws.send(backendReadyMsg);
                helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId}: Sent 'backend_ready_for_messages' to client.`);
            } else {
                helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId}: Client WebSocket not open when trying to send 'backend_ready_for_messages'. State: ${ws.readyState}`);
                // Optionally close if not open, or decide how to handle this unlikely scenario
            }
        } catch (sendError) {
            helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId}: Error sending 'backend_ready_for_messages': ${sendError.message}`);
            // Decide how to handle, maybe close connection if this critical first message fails
        }
        // ---> SEND BACKEND_READY MESSAGE TO CLIENT -- END <---

        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS /ws/assist-ant/startconversation] User ${ws.userId}: Authentication successful. Starting GPT Conversation Logic INLINED.`);

        // INLINED startGPTConversation LOGIC STARTS HERE
        // Original parameters: (clientConnection, req, res) - clientConnection is now 'ws', req is available
        const clientConnection = ws; // Alias for clarity within the pasted code
        const userId = ws.userId; // Get userId from the authenticated ws object

        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: ENTERED startGPTConversation logic. Client readyState: ${clientConnection.readyState}`);
        const tempUserIdForEarlyLog = userId || "UNKNOWN_USER_YET"; // Already have userId here
        console.log(`[GPT_LOGIC_IN_APP_JS_EARLY] startGPTConversation called. User: ${tempUserIdForEarlyLog}. Request URL: ${req ? req.url : 'N/A'}`);
        helpers.devConsole("[GPT_LOGIC_IN_APP_JS] START of startGPTConversation function-equivalent");
        helpers.devConsole("[GPT_LOGIC_IN_APP_JS] startGPTConversation logic in app.js");

        const apiKey = process.env.OPENAI_API_KEY;
        const messageQueue = [];
        let createdSessionId;
        let isOpenAIReady = false;
        let isSemanticVadConfirmedByOpenAI = false;
        let ongoingToolCalls = {}; // MODIFIED: Added object to track streaming tool calls
        // let currentActiveKitchentableIdForOpenAISession = null; // MODIFIED: REMOVED variable - unreliable

        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: About to attach 'message' listener to clientConnection.`);
        helpers.devConsole(`[GPT_LOGIC_IN_APP_JS] User ${userId}: Initializing OpenAI WebSocket connection...`);
        const wsOpenAi = new WebSocket('wss://api.openai.com/v1/realtime?model=gpt-4o-mini-realtime-preview&modalities=audio&input_audio_format=pcm16&output_audio_format=pcm16', {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            "OpenAI-Beta": "realtime=v1"
          }
        });

        wsOpenAi.on("open", () => {
          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WebSocket is open. isOpenAIReady (before set): ${isOpenAIReady}`); // Log original state
          isOpenAIReady = true;
          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: isOpenAIReady is now true. Looking for 'session.update' in queue to send immediately...`);

          // MODIFIED: Only send session.update from queue on open. Audio waits for VAD confirmation.
          let sessionUpdateSentFromOpen = false;
          for (let i = 0; i < messageQueue.length; i++) {
              try {
                  const parsedQueuedMessage = JSON.parse(messageQueue[i]);
                  if (parsedQueuedMessage.type === "session.update") {
                      helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sending queued 'session.update' (from onOpen) to OpenAI.`);
                      wsOpenAi.send(messageQueue[i]);
                      messageQueue.splice(i, 1); // Remove from queue
                      sessionUpdateSentFromOpen = true;
                      break;
                  }
              } catch (e) { /* Ignore non-JSON or assume not session.update */ }
          }
          if (sessionUpdateSentFromOpen) {
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: 'session.update' sent from queue during onOpen. Other messages (e.g. audio) will wait for VAD confirmation.`);
          } else {
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: No 'session.update' found in queue during onOpen, or queue was empty. Waiting for client messages or VAD confirmation to process queue.`);
          }
          // Audio messages in queue will be processed once isSemanticVadConfirmedByOpenAI is true, via wsOpenAi.on('message')
        });

        wsOpenAi.on('message', async (data) => {
            let rawDataString = data.toString();
            let isAudioDeltaRaw = false;
            try {
                // Attempt to parse to check type for raw logging, without altering main flow
                if (JSON.parse(rawDataString).type === 'response.audio.delta') {
                    isAudioDeltaRaw = true;
                }
            } catch(e) { /* ignore parse error for this specific log optimization */ }

            if (isAudioDeltaRaw) {
                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: RAW DATA received from OpenAI (length: ${data.length}, type: response.audio.delta) - audio data content truncated in this log.`);
            } else {
                // MODIFIED: Increased substring length for raw non-audio data
                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: RAW DATA received from OpenAI (length: ${data.length}): ${rawDataString.substring(0, 2000)}...`);
            }

            try {
                const response = JSON.parse(data);

                if (response.type === 'response.audio.delta') {
                    const loggedResponse = { ...response, delta: `[AUDIO_DATA_TRUNCATED - Original Length: ${response.delta ? response.delta.length : 'N/A'}]` };
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received from OpenAI - Type: ${response.type}, Full Data (audio.delta truncated): ${JSON.stringify(loggedResponse).substring(0, 500)}...`);
                } else {
                    // MODIFIED: Removed substring limit for full parsed non-audio data
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received from OpenAI - Type: ${response.type}, Full Data: ${JSON.stringify(response)}`);
                }

                if (response.type === 'session.created'){
                  createdSessionId = response.session.id;
                  helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI Session created with ID: ${createdSessionId}`);
                }

                // --- MODIFIED: Start of new logic for streamed tool calls ---
                if (response.type === 'response.output_item.added' && response.item && response.item.type === 'function_call') {
                    const toolCallId = response.item.call_id;
                    const name = response.item.name;
                    if (toolCallId && name) {
                        // MODIFIED: Removed retrieval/storage of kitchentableId here. Will be fetched later.
                        // const ktIdForTool = currentActiveKitchentableIdForOpenAISession;
                        ongoingToolCalls[toolCallId] = {
                            name: name,
                            arguments: "",
                            responseId: response.response_id
                            // kitchentableId: ktIdForTool // REMOVED - Will fetch from DB just before tool execution
                        };
                        // MODIFIED: Simplified log
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI initiated function call. Call ID: ${toolCallId}, Name: ${name}.`);

                    } else {
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received response.output_item.added for function_call but missing call_id or name. Item: ${JSON.stringify(response.item)}`);
                    }
                }

                if (response.type === 'response.function_call_arguments.delta' && response.call_id && ongoingToolCalls[response.call_id]) {
                    ongoingToolCalls[response.call_id].arguments += response.delta;
                    // helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received arguments delta for call ID: ${response.call_id}`); // Can be verbose
                }

                if (response.type === 'response.function_call_arguments.done' && response.call_id && ongoingToolCalls[response.call_id]) {
                    const toolCallId = response.call_id;
                    const callDetails = ongoingToolCalls[toolCallId];
                    const functionName = callDetails.name;
                    const fullArguments = callDetails.arguments;
                    // MODIFIED: Removed retrieval of unreliable kitchentableId from callDetails
                    // const currentKitchentableId = callDetails.kitchentableId;

                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Completed receiving arguments for call ID: ${toolCallId}. Name: ${functionName}, Args: ${fullArguments}.`); // Removed KT_ID log here

                    let functionArgs = {};
                    try {
                        functionArgs = JSON.parse(fullArguments);
                    } catch (e) {
                        console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error parsing accumulated tool arguments for ${functionName} (Call ID: ${toolCallId}): ${e.message}. Arguments string: ${fullArguments}`);
                        // Optionally send error back to OpenAI?
                        delete ongoingToolCalls[toolCallId]; // Clean up failed call
                        // Consider sending a tool_result with error here
                        const errorResultMsg = {
                            type: "tool_result",
                            tool_call_id: toolCallId,
                            result: JSON.stringify({ status: "error", message: "Failed to parse arguments: " + e.message })
                        };
                        if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                           wsOpenAi.send(JSON.stringify(errorResultMsg));
                           helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sent TOOL_RESULT (Error parsing args) to OpenAI for ID ${toolCallId}.`);
                        }
                    }

                    let toolResultContent = JSON.stringify({ status: "error", message: "An internal error occurred during tool execution." }); // Default error JSON
                    let kitchentableIdForTool = null; // Initialize

                    try {
                        // --- MODIFIED: Fetch kitchentableId from User DB ---
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Fetching user data to get defaultKitchentable for tool call ID: ${toolCallId}`);
                        const user = await User.findById(userId).select('defaultKitchentable').lean();
                        if (user && user.defaultKitchentable) {
                            kitchentableIdForTool = user.defaultKitchentable.toString(); // Ensure it's a string
                            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Found defaultKitchentable ID: ${kitchentableIdForTool}`);
                        } else {
                            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: User found, but no defaultKitchentable set or user not found (User: ${user ? 'found' : 'not found'}).`);
                            // Set specific error if KT ID is missing
                             toolResultContent = JSON.stringify({ status: "error", message: "Default kitchentable ID not found for the user." });
                            // Skip tool execution if ID is missing
                        }
                        // --- END MODIFIED ---

                        // Only execute tool if kitchentableId was found
                        if (kitchentableIdForTool) {
                             helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Proceeding with tool execution using KT_ID: ${kitchentableIdForTool}`);
                            if (functionName === "add_ingredient_to_shopping_list") {
                                const { ingredient_name, quantity, unit } = functionArgs;
                                if (!ingredient_name) {
                                    toolResultContent = JSON.stringify({ status: "error", message: "Missing required parameter 'ingredient_name' for add_ingredient_to_shopping_list." });
                                } else {
                                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: AWAITING tool function callAddManualItemTool for ID: ${toolCallId}`);
                                    // MODIFIED: Pass the fetched kitchentableIdForTool
                                    toolResultContent = await callAddManualItemTool(userId, kitchentableIdForTool, ingredient_name, quantity, unit);
                                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: COMPLETED tool function callAddManualItemTool for ID: ${toolCallId}. Raw result: ${toolResultContent}`);
                                }
                            } else if (functionName === "get_shopping_list_details") {
                                const { ingredient_name } = functionArgs; // Optional
                                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: AWAITING tool function callGetShoppingListDetailsTool for ID: ${toolCallId}`);
                                // MODIFIED: Pass the fetched kitchentableIdForTool
                                toolResultContent = await callGetShoppingListDetailsTool(userId, kitchentableIdForTool, ingredient_name);
                                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: COMPLETED tool function callGetShoppingListDetailsTool for ID: ${toolCallId}. Raw result: ${toolResultContent}`);
                            } else {
                                toolResultContent = JSON.stringify({ status: "error", message: `Unknown tool function requested: ${functionName}` });
                                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Unknown tool function requested: ${functionName} (Call ID: ${toolCallId})`);
                            }
                        } // else: toolResultContent already contains the error about missing kitchentableId

                    } catch (e) {
                        console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Exception during user fetch or tool execution for ${functionName} (Call ID: ${toolCallId}): ${e.message}`, e);
                        toolResultContent = JSON.stringify({ status: "error", message: `System error during user fetch or tool execution: ${e.message}` });
                    }

                    // MODIFIED: Removed the old check (!currentKitchentableId) as it's handled within the try block now
                    /*
                    if (!currentKitchentableId) {
                        toolResultContent = JSON.stringify({ status: "error", message: "Kitchentable context (kitchentableId) is missing or was not available when tool call was initiated." }); // MODIFIED: Updated error message
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error - KitchentableId NOT FOUND in callDetails for tool call ID: ${toolCallId}.`);
                    } else {
                        try {
                            // ... original tool execution logic ...
                        } catch (e) {
                           // ... original catch ...
                        }
                    }*/

                    // LOGIK ZUM SENDEN DES TOOL-ERGEBNISSES AN OPENAI - VERSUCH MIT ROLE 'ASSISTANT' UND TYPE 'TEXT'
                    const toolResponseMessage = {
                        type: "conversation.item.create",
                        item: {
                            type: "message",
                            role: "assistant", // Beibehalten von "assistant"
                            content: [
                                {
                                    type: "text", // GEÄNDERT von "tool_result" zu "text"
                                    text: toolResultContent // Das stringifizierte JSON-Ergebnis unseres Tools als Text
                                    // tool_call_id hier nicht mehr vorhanden, da Typ jetzt "text"
                                }
                            ]
                        }
                    };

                    const messageToSendStr = JSON.stringify(toolResponseMessage);
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: PREPARING to send TOOL_RESULT (role:assistant, type:text) for Call ID ${toolCallId}. Message: ${messageToSendStr.substring(0, 500)}...`);

                    if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                        // 1. Send the result of the tool as an assistant message
                        wsOpenAi.send(messageToSendStr);
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sent TOOL_RESULT (role:assistant, type:text) for Call ID ${toolCallId}. Result snippet: ${toolResultContent.substring(0, 100)}...`);

                        // --- NEU: Explizit eine Antwort von OpenAI anfordern (basierend auf Forum-Info) ---
                        const triggerResponseMessage = {
                            type: "response.create",
                            response: {
                                // Re-sending the tool result as instructions for the response, as per forum example
                                instructions: toolResultContent
                            }
                        };
                        const triggerResponseStr = JSON.stringify(triggerResponseMessage);
                        wsOpenAi.send(triggerResponseStr);
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sent response.create trigger to OpenAI for Call ID ${toolCallId} with instructions: ${toolResultContent.substring(0,100)}...`);
                        // --- ENDE NEU ---

                    } else {
                        console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS not ready when attempting to send tool result or trigger response for Call ID ${toolCallId}. State: ${wsOpenAi.readyState}, isOpenAIReady: ${isOpenAIReady}`);
                    }

                    delete ongoingToolCalls[toolCallId];
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Cleaned up ongoingToolCall for ID ${toolCallId} after attempting to send result.`);
                    // --- ENDE DES ANGEPASSTEN BLOCKS ---

                }
                // --- MODIFIED: End of new logic for streamed tool calls ---

                // MODIFIED: Check for semantic_vad confirmation and process audio queue
                if (response.type === 'session.updated' && response.session && response.session.turn_detection && response.session.turn_detection.type === "semantic_vad") {
                    if (!isSemanticVadConfirmedByOpenAI) { // Check if it's the first time we confirm
                        isSemanticVadConfirmedByOpenAI = true;
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI CONFIRMED session.updated with semantic_vad. isSemanticVadConfirmedByOpenAI is now TRUE.`);

                        // Now process any queued audio messages
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Processing audio message queue (length ${messageQueue.length}) after VAD confirmation...`);
                        let queuedAudioAppendCounter = 0;
                        const remainingMessagesInQueue = []; // To hold non-audio messages or messages that fail
                        while (messageQueue.length > 0) {
                            const queuedMessageString = messageQueue.shift();
                            try {
                                const parsedQueuedMessage = JSON.parse(queuedMessageString);
                                if (parsedQueuedMessage.type === "input_audio_buffer.append") {
                                    if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                                        wsOpenAi.send(queuedMessageString);
                                        queuedAudioAppendCounter++;
                                    } else {
                                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS not ready while trying to send queued audio. Re-queuing.`);
                                        remainingMessagesInQueue.push(queuedMessageString); // Re-queue if WS not ready
                                    }
                                } else {
                                     helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Non-audio message '${parsedQueuedMessage.type}' found in queue during audio processing. Re-queuing.`);
                                    remainingMessagesInQueue.push(queuedMessageString); // Re-queue other message types
                                }
                            } catch (e) {
                                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error parsing message from queue during audio processing or unparsable (possibly already stringified audio). Assuming audio and attempting to send if WS open.`);
                                // If it's not parsable, it might be a pre-stringified audio message. Risky, but attempt send.
                                if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                                    wsOpenAi.send(queuedMessageString); // Send as is
                                    queuedAudioAppendCounter++; // Assume it was audio
                                } else {
                                   remainingMessagesInQueue.push(queuedMessageString);
                                }
                            }
                        }
                        // Restore any messages that were not processed or re-queued
                        if (remainingMessagesInQueue.length > 0) {
                            messageQueue.unshift(...remainingMessagesInQueue.reverse()); // Add them back to the front
                             helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Re-queued ${remainingMessagesInQueue.length} messages after VAD confirmation audio processing.`);
                        }

                        if (queuedAudioAppendCounter > 0) {
                            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sent ${queuedAudioAppendCounter} queued 'input_audio_buffer.append' message(s) to OpenAI after VAD confirmation.`);
                        }
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Finished processing audio message queue after VAD confirmation.`);
                    } else {
                        // helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received session.updated (semantic_vad already confirmed).`);
                    }
                }
                if (response.type === 'input_audio_buffer.speech_started') {
                  helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI DETECTED speech_started. Forwarding to client.`); // Changed log slightly
                  if(clientConnection.readyState === WebSocket.OPEN) {
                    clientConnection.send(JSON.stringify({ event: 'input_audio_buffer.speech_started' }));
                  }
                }
                if (response.type === 'response.audio.delta' && response.delta) {
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI SENT response.audio.delta. Payload length: ${response.delta.length}. Forwarding to client.`);
                    if(clientConnection.readyState === WebSocket.OPEN) {
                      clientConnection.send(JSON.stringify({ event: 'media', media: { payload: response.delta } }));
                    }
                }
                if (response.type === 'response.text.delta' && response.delta) {
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI SENT response.text.delta: "${response.delta.substring(0, 100)}...". Forwarding to client.`);
                     if(clientConnection.readyState === WebSocket.OPEN) {
                        clientConnection.send(JSON.stringify({ event: 'text_delta', text: response.delta }));
                    }
                }
                if (response.type === 'response.text.final' && response.text) {
                     helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI SENT response.text.final: "${response.text.substring(0,100)}...". Forwarding to client.`);
                     if(clientConnection.readyState === WebSocket.OPEN) {
                        clientConnection.send(JSON.stringify({ event: 'text_final', text: response.text }));
                    }
                }
                if (response.type === 'error') {
                    console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Full error object from OpenAI:`, JSON.stringify(response, null, 2));
                    helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI SENT error (raw message was ${response.message === undefined ? 'undefined' : response.message}). Full error object logged to console.error. Forwarding to client.`); // MODIFIED Log

                    if(clientConnection.readyState === WebSocket.OPEN) {
                      clientConnection.send(JSON.stringify({ event: 'openai_error', data: { message: response.message || "Unknown error from OpenAI" }}));
                    }
                }
            } catch (error) {
                console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error processing message from OpenAI:`, error, "Raw data as string:", data.toString().substring(0,100));
                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error processing OpenAI message: ${error.message}. Original raw data logged above.`);
            }
        });

        wsOpenAi.on('close', (code, reason) => {
            isOpenAIReady = false;
            isSemanticVadConfirmedByOpenAI = false; // MODIFIED: Reset flag
            const reasonString = reason ? reason.toString().substring(0,100) : 'N/A';
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: WebSocket with OpenAI closed. Code: ${code}, Reason: ${reasonString}`);
            if(clientConnection.readyState === WebSocket.OPEN) {
              clientConnection.send(JSON.stringify({ event: 'openai_connection_closed', data: { code, reason: reasonString }}));
            }
        });

        wsOpenAi.on('error', (error) => {
           isOpenAIReady = false;
           isSemanticVadConfirmedByOpenAI = false; // MODIFIED: Reset flag
           console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error in OpenAI WebSocket:`, JSON.stringify(error, Object.getOwnPropertyNames(error)));
           helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WebSocket ERROR: ${error.message}`);
           if(clientConnection.readyState === WebSocket.OPEN) {
             clientConnection.send(JSON.stringify({ event: 'openai_error', data: { message: `OpenAI WebSocket error: ${error.message}` }}));
           }
        });

        wsOpenAi.on('unexpected-response', (request, response) => {
            isOpenAIReady = false;
            isSemanticVadConfirmedByOpenAI = false; // MODIFIED: Reset flag
            let responseBody = [];
            response.on('data', chunk => responseBody.push(chunk));
            response.on('end', () => {
                console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Unexpected response from OpenAI. Status: ${response.statusCode}, Message: ${response.statusMessage}, Headers: ${JSON.stringify(response.headers)}, Body: ${Buffer.concat(responseBody).toString()}`);
            });
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WebSocket UNEXPECTED RESPONSE: Status ${response.statusCode}`);
            if(clientConnection.readyState === WebSocket.OPEN) {
                clientConnection.send(JSON.stringify({ event: 'openai_error', data: { message: `OpenAI unexpected response: ${response.statusCode} ${response.statusMessage}` }}));
            }
        });

        clientConnection.on('message', async (message) => {
          if (!message.toString().includes('"type":"input_audio_buffer.append"')) {
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: VERY_EARLY_CLIENT_MESSAGE_RECEIVED (Non-AudioAppend): ${message.toString().substring(0, 300)}`);
          }
          try {
              const dataFromClient = JSON.parse(message);
              let messageToProcess = { ...dataFromClient }; // messageToProcess is used for audio forwarding
              let stringifiedMessageForOpenAI; // Declare here for broader scope

              // Conditional logging for parsed message type
              if (dataFromClient.type !== "input_audio_buffer.append" && dataFromClient.type !== "input_audio_buffer.flush") {
                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Parsed client message. Type: ${dataFromClient?.type || 'N/A'}`);
              }

              // Token removal logic (moved up, as it was inside the old messageToProcess block)
              if (messageToProcess.token) {
                  helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Removed 'token' property from client message of type ${dataFromClient.type}.`);
                  delete messageToProcess.token;
              }

              switch (dataFromClient.type) {
                  case "session.update": {
                      helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Handling client 'session.update'. Raw client data: ${message.toString().substring(0,300)}`);

                      let clientSessionConfig = {};
                      if (dataFromClient.session) {
                          clientSessionConfig = dataFromClient.session;
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received session config from client: ${JSON.stringify(clientSessionConfig).substring(0,200)}`);
                      }

                      // MODIFIED: Define finalInstructions FIRST, before potential modifications/reads
                      let finalInstructions = clientSessionConfig.instructions || "You are a helpful assistant."; // Default or from client

                      // Log KT_ID value right before potential reset/set
                      // helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Handling 'session.update'. Value of currentActiveKitchentableIdForOpenAISession BEFORE reset/set: ${currentActiveKitchentableIdForOpenAISession}`); // REMOVED redundant log

                      // Extract kitchentableId (for context string generation, not tool execution)
                      // currentActiveKitchentableIdForOpenAISession = null; // REMOVED - Not needed anymore
                      const ktIdMatch = finalInstructions.match(/ID: ([a-f0-9]{24})/i);
                      let extractedKtId = null;
                      if (ktIdMatch && ktIdMatch[1]) {
                          extractedKtId = ktIdMatch[1];
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Extracted kitchentableId '${extractedKtId}' from instructions (will be used for initial context, not stored for tools).`);
                      } else {
                           // Use already defined finalInstructions here
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: No kitchentableId found in instructions for session scope. Instructions: ${finalInstructions.substring(0,100)}...`);
                      }

                      // Modify instructions ONLY if recipe_data is primary source (unlikely if client sends full instructions)
                      if (!clientSessionConfig.instructions && dataFromClient.payload && dataFromClient.payload.recipe_data) {
                          const { id, name, description } = dataFromClient.payload.recipe_data;
                          if (id && name && description) {
                               // Re-assign finalInstructions if using recipe data
                              finalInstructions = `Kontext: Der Benutzer betrachtet das Rezept "${name}" (ID: ${id}). Beschreibung: "${description}". Bitte berücksichtige dies in deinen Antworten. ${extractedKtId ? `Der zugehörige Küchentisch hat die ID: ${extractedKtId}.` : '' }`;
                              helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Using recipe_data for OpenAI instructions: ${finalInstructions.substring(0, 100)}...`);
                          } else {
                              helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Recipe_data present but incomplete. Using default instructions.`);
                              finalInstructions = "You are a helpful assistant."; // Fallback to default if recipe data invalid
                          }
                      } else if (clientSessionConfig.instructions) {
                            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Using 'instructions' directly from client: ${finalInstructions.substring(0,100)}...`);
                      } else {
                            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: No recipe_data and no client instructions. Using default instructions: ${finalInstructions.substring(0,100)}...`);
                      }

                      const openAIConfigMessage = {
                          type: "session.update",
                          session: {
                              modalities: clientSessionConfig.modalities || ["audio", "text"],
                              voice: clientSessionConfig.voice || "alloy",
                              temperature: clientSessionConfig.temperature === undefined ? 0.8 : clientSessionConfig.temperature,
                              turn_detection: clientSessionConfig.turn_detection || {
                                  type: "semantic_vad",
                                  eagerness: "medium",
                                  create_response: true
                              },
                              instructions: finalInstructions,
                              tools: [
                                {
                                  "type": "function",
                                  "name": "add_ingredient_to_shopping_list",
                                  "description": "Fügt eine Zutat mit optionaler Menge und Einheit zur aktuellen Einkaufsliste des Benutzers hinzu. Bestätige die Aktion danach.",
                                  "parameters": {
                                    "type": "object",
                                    "properties": {
                                      "ingredient_name": {
                                        "type": "string",
                                        "description": "Der Name der Zutat, z.B. 'Milch', 'Tomaten'."
                                      },
                                      "quantity": {
                                        "type": "number",
                                        "description": "Die Menge der Zutat, z.B. 2, 500."
                                      },
                                      "unit": {
                                        "type": "string",
                                        "description": "Die Einheit für die Menge, z.B. 'Liter', 'Gramm', 'Stück'."
                                      }
                                    },
                                    "required": ["ingredient_name"]
                                  }
                                },
                                {
                                  "type": "function",
                                  "name": "get_shopping_list_details",
                                  "description": "Ruft Details von der aktuellen Einkaufsliste ab. Wenn ein 'ingredient_name' angegeben wird, wird geprüft, ob diese spezifische Zutat vorhanden ist und Details dazu zurückgegeben. Ohne 'ingredient_name' werden allgemeine Informationen oder die ersten Einträge der Liste zurückgegeben.",
                                  "parameters": {
                                    "type": "object",
                                    "properties": {
                                      "ingredient_name": {
                                        "type": "string",
                                        "description": "Optional. Der Name der Zutat, die auf der Liste gesucht werden soll, z.B. 'Milch', 'Tomaten'."
                                      }
                                    }
                                  }
                                }
                              ]
                          }
                      };

                      if (clientSessionConfig.language) {
                        openAIConfigMessage.session.language = clientSessionConfig.language;
                        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Using 'language' from client: ${clientSessionConfig.language}`);
                      }

                      helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI session.update message prepared. Instructions: ${openAIConfigMessage.session.instructions.substring(0,100)}... Tools defined: ${openAIConfigMessage.session.tools.length}. Full config: ${JSON.stringify(openAIConfigMessage).substring(0,350)}`);

                      stringifiedMessageForOpenAI = JSON.stringify(openAIConfigMessage);

                      if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS open, sending constructed 'session.update' to OpenAI.`);
                          wsOpenAi.send(stringifiedMessageForOpenAI);
                      } else {
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS not ready, queueing constructed 'session.update' for OpenAI.`);
                          // Stelle sicher, dass die session.update immer als erstes in der Queue landet oder Priorität hat
                          const existingSessionUpdate = messageQueue.findIndex(msg => JSON.parse(msg).type === 'session.update');
                          if (existingSessionUpdate !== -1) {
                            messageQueue.splice(existingSessionUpdate, 1); // Entferne alte session.update
                          }
                          messageQueue.unshift(stringifiedMessageForOpenAI); // Füge neue session.update am Anfang ein
                      }
                      break;
                  } // End of case "session.update"
                  case "input_audio_buffer.append":
                  case "input_audio_buffer.flush":
                      stringifiedMessageForOpenAI = JSON.stringify(messageToProcess);
                      // MODIFIED: Condition for sending audio directly now includes isSemanticVadConfirmedByOpenAI
                      if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN && isSemanticVadConfirmedByOpenAI) {
                          if (dataFromClient.type === "input_audio_buffer.append") {
                              directAppendCounter++;
                          }
                          wsOpenAi.send(stringifiedMessageForOpenAI);
                      } else {
                          if (!isSemanticVadConfirmedByOpenAI) {
                              // MODIFIED: Commented out verbose log for queuing due to unconfirmed VAD
                              // helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS open but VAD not confirmed by OpenAI yet. Queuing '${dataFromClient.type}'.`);
                          } else {
                              helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS not ready/open (State: ${wsOpenAi.readyState}, isOpenAIReady: ${isOpenAIReady}, VAD Confirmed: ${isSemanticVadConfirmedByOpenAI}). Queuing '${dataFromClient.type}'.`);
                          }
                          messageQueue.push(stringifiedMessageForOpenAI);
                      }
                      break;
                  case "input_audio_buffer.speech_started":
                      helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Client sent speech_started (to interrupt OpenAI).`);
                      if (isOpenAIReady && wsOpenAi.readyState === WebSocket.OPEN) {
                          helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Sending 'response.cancel' to OpenAI.`);
                          wsOpenAi.send(JSON.stringify({ "type": "response.cancel" }));
                      } else {
                           helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: OpenAI WS not ready, cannot send 'response.cancel'. Message not queued.`);
                      }
                      break;
                  default:
                      helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received unhandled message type from client: ${dataFromClient.type}. This message will not be forwarded to OpenAI.`);
                      console.warn(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Received unhandled message type from client: ${dataFromClient.type}`);
              }
          } catch (parseError) {
              console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error parsing message from client:`, parseError, "Original message:", message.toString().substring(0,100));
              helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Error parsing message from client: ${parseError.message}`);
              if(clientConnection.readyState === WebSocket.OPEN) {
                clientConnection.send(JSON.stringify({ event: 'client_error', data: { message: 'Invalid JSON format from client.'}}));
              }
          }
        });

        clientConnection.on('close', (code, reason) => {
            const reasonString = reason ? reason.toString().substring(0,100) : 'N/A';
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Client connection closed. Code: ${code}, Reason: ${reasonString}. Sent ${directAppendCounter} direct 'input_audio_buffer.append' messages to OpenAI during this session.`);
            directAppendCounter = 0;
            isSemanticVadConfirmedByOpenAI = false; // MODIFIED: Reset flag

            if (wsOpenAi && (wsOpenAi.readyState === WebSocket.OPEN || wsOpenAi.readyState === WebSocket.CONNECTING)) {
                helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Closing OpenAI WebSocket due to client disconnection.`);
                wsOpenAi.close();
            }
            isOpenAIReady = false;
        });

        clientConnection.on('error', (error) => {
            console.error(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Client connection error for User ${userId}:`, error);
            helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Client connection ERROR: ${error.message}`);
            isSemanticVadConfirmedByOpenAI = false; // MODIFIED: Reset flag
            if (wsOpenAi && (wsOpenAi.readyState === WebSocket.OPEN || wsOpenAi.readyState === WebSocket.CONNECTING)) {
                 helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Closing OpenAI WebSocket due to client error.`);
                wsOpenAi.close();
            }
            isOpenAIReady = false;
        });

        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Successfully ATTACHED 'message' listener to clientConnection.`);
        helpers.devConsole(`[${new Date().toISOString()}] [GPT_LOGIC_IN_APP_JS] User ${userId}: Finished setting up startGPTConversation logic in app.js. Listening for client messages.`);
        // INLINED startGPTConversation LOGIC ENDS HERE

    } catch (error) {
        // Catch any unexpected errors during authentication (this block is from the original app.ws)
        const errorMessage = error.message || 'Internal server error during authentication.';
        helpers.devConsole(`[App.js WS /ws/assist-ant/startconversation] UNEXPECTED Auth Error: ${errorMessage}`);
        helpers.devConsole(`[${new Date().toISOString()}] [App.js WS] User ${ws.userId || 'N/A'}: Authentication error: ${errorMessage}.`);
        try {
            ws.send(JSON.stringify({ event: 'auth_error', data: { message: 'Authentication failed.' }}));
        } catch (sendError) {
             helpers.devConsole(`[App.js WS /ws/assist-ant/startconversation] Error sending error message before closing: ${sendError}`);
        }
        ws.close(1011, "Authentication failed."); // 1011: Internal Server Error
    }
});

// Function to broadcast messages to a specific room
const broadcastToRoom = (roomId, event, data) => {
    const functionName = 'broadcastToRoom'; // For logging
    helpers.devConsole(`[${functionName}] Received broadcast request for Room: ${roomId}, Event: ${event}`);

    if (!kitchentableRooms[roomId]) {
        helpers.devConsole(`[${functionName}] Room ${roomId} not found or empty in kitchentableRooms. Skipping broadcast.`);
        return;
    }

    let message; // Variable for the message string
    try {
        // Attempt to serialize the data payload
        // Ensure the 'data' field is always present, defaulting to null if input data is undefined
        message = JSON.stringify({ event, data: data === undefined ? null : data });
    } catch (error) {
        // Log the serialization error and the problematic data
        helpers.devConsole(`[${functionName}] ERROR serializing data for event ${event} in room ${roomId}: ${error}`);
        console.error(`[${functionName}] Failed to serialize payload for ${roomId}, event ${event}. Payload was:`, data);
        // Do not proceed with broadcasting if serialization fails
        return;
    }

    const socketsInRoom = kitchentableRooms[roomId];
    let closedSocketIndices = [];

    helpers.devConsole(`[${functionName}] Found ${socketsInRoom.length} socket(s) in room ${roomId}. Preparing message: ${message.substring(0,150)}...`);

    socketsInRoom.forEach((ws, index) => {
        // helpers.devConsole(`[${functionName}] Attempting to send to socket index ${index} in room ${roomId}. ReadyState: ${ws.readyState}`);
        if (ws.readyState === 1) { // WebSocket.OPEN
            // ---> ADD PRE-SEND CHECK LOG <---
            helpers.devConsole(`[${functionName}] PRE-SEND CHECK for event '${event}'. Message length: ${message.length}. Message: ${message.substring(0, 300)}...`);
            ws.send(message);
            // helpers.devConsole(`[${functionName}]   -> Sent successfully to socket index ${index}.`);
        } else {
            helpers.devConsole(`[${functionName}]   -> Socket index ${index} not open (state: ${ws.readyState}). Marking for removal.`);
            closedSocketIndices.push(index);
        }
    });

    // Clean up closed connections
    if (closedSocketIndices.length > 0) {
        for (let i = closedSocketIndices.length - 1; i >= 0; i--) {
            socketsInRoom.splice(closedSocketIndices[i], 1);
        }
        if (socketsInRoom.length === 0) {
             helpers.devConsole(`[${functionName}] Room ${roomId} became empty after cleanup, removing room.`);
            delete kitchentableRooms[roomId];
        }
    }
    helpers.devConsole(`[${functionName}] Finished broadcast processing for room ${roomId}.`);
};

// Make broadcast function available
app.locals.broadcastToRoom = broadcastToRoom;
app.locals.kitchentableRooms = kitchentableRooms;

// WebSocket connection endpoint
app.ws('/ws/shopping-list/:kitchentableId', async (ws, req) => {
    let localUserId = null; // Keep localUserId scoped here
    const { kitchentableId } = req.params;
    const roomId = `kitchentable_${kitchentableId}`;

    // helpers.devConsole(`WS Connection attempt for room ${roomId}`);

    try {
        // --- Authentication & Authorization ---
        const token = req.query.token; // Expect Stytch session_token
        // helpers.devConsole(`WS Auth: Received token via query param: [${token}]`);

        if (!token) {
            // helpers.devConsole(`WS Auth Error: No token provided for room ${roomId}`);
            ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Authentication token required.' }}));
            // Close with 1008 (Policy Violation) as per WebSocket spec for auth issues
            return ws.close(1008, "Authentication token required.");
        }

        // Verify Stytch token and get LOCAL user ID using the new controller function
        const authResult = await authController.verifyStytchTokenForWs(token);

        if (!authResult || !authResult.isValid) {
            const errorMessage = authResult?.error || 'Authentication failed.';
            helpers.devConsole(`WS Auth Error for shopping-list (${roomId}): ${errorMessage}`);
            ws.send(JSON.stringify({ event: 'zettel_error', data: { message: errorMessage }}));
            return ws.close(1008, errorMessage);
        }

        localUserId = authResult.userId; // Get the user ID from the authResult object

        // helpers.devConsole(`WS Auth Success: User ${localUserId} authenticated via Stytch Token for room ${roomId}`);

        // Check if user is member of the kitchentable using the LOCAL user ID
        const kitchentable = await Kitchentable.findOne({ _id: kitchentableId, 'members.userId': localUserId });
        if (!kitchentable) {
            // helpers.devConsole(`WS Auth Error: User ${localUserId} not authorized for kitchentable ${kitchentableId}`);
            ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Not authorized for this kitchentable.' }}));
            // Use 1008 (Policy Violation) for authorization failures
            return ws.close(1008, "Not authorized for this kitchentable.");
        }
        // helpers.devConsole(`WS Auth Success: User ${localUserId} authorized for room ${roomId}`);

        // --- Add WebSocket to Room ---
        if (!kitchentableRooms[roomId]) {
            kitchentableRooms[roomId] = [];
        }
        kitchentableRooms[roomId].push(ws);
        // helpers.devConsole(`WS User ${localUserId} joined room ${roomId}. Room size: ${kitchentableRooms[roomId].length}`);

        // --- Handle WebSocket Events ---
        ws.on('message', (msg) => {
            // Add console log here to indicate message reception
            helpers.devConsole(`[WebSocket] Received message from User ${localUserId || 'UNKNOWN'} in room ${roomId}. Message: ${msg.toString().substring(0, 100)}...`); // Existing log

            try {
                const parsedMsg = JSON.parse(msg);

                // Handle item purchase status updates
                if (parsedMsg.event === 'update_item_purchase_status') {
                    const { itemId, isPurchased } = parsedMsg.payload || {}; // Destructure safely

                    // Validate payload
                    if (itemId === undefined || isPurchased === undefined) {
                        helpers.devConsole(`[WebSocket] Invalid payload for update_item_purchase_status from User ${localUserId || 'UNKNOWN'} in room ${roomId}. Payload: ${JSON.stringify(parsedMsg.payload)}`);
                        // Optional: Send error back to the specific client
                        ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Invalid payload for update_item_purchase_status: Missing itemId or isPurchased.' }}));
                        return; // Stop processing this message
                    }

                    helpers.devConsole(`[WebSocket] Processing update_item_purchase_status for Item ${itemId} to ${isPurchased} by User ${localUserId} in room ${roomId}`);

                    // Call controller to update the database (async)
                    (async () => { // Use an async IIFE to handle await
                        try {
                            // Call controller function to update the item
                            const result = await shoppingListController.updateItemPurchaseStatus(itemId, isPurchased, localUserId, kitchentableId);

                            // Prüfen ob es das letzte Item ist
                            if (result && result.isLastItem === true) {
                                helpers.devConsole(`[WebSocket] Last item detected! Sending confirmation request to client for item ${itemId}`);

                                // Sende eine Bestätigungsanfrage nur an den Client, der das Update ausgelöst hat
                                ws.send(JSON.stringify({
                                    event: 'last_item_confirmation_needed',
                                    data: {
                                        itemId: result.itemId,
                                        listId: result.listId,
                                        message: 'Dies ist das letzte Item. Möchten Sie die Liste abschließen?'
                                    }
                                }));

                                // Kein Broadcast, da der Benutzer erst bestätigen muss
                                return;
                            } else {
                                // Broadcast the update to all clients in the room
                                const broadcastPayload = {
                                    itemId: result.itemId,
                                    isPurchased: result.isPurchased
                                };
                                broadcastToRoom(roomId, 'item_status_updated', broadcastPayload);
                                helpers.devConsole(`[WebSocket] Broadcasted 'item_status_updated' for item ${itemId} in room ${roomId}. Payload: ${JSON.stringify(broadcastPayload)}`);
                            }
                        } catch (error) {
                            helpers.devConsole(`[WebSocket] Error updating item purchase status: ${error.message}`);
                            // Send error back to the specific client
                             try {
                                ws.send(JSON.stringify({ event: 'zettel_error', data: { message: `Failed to update item ${itemId}: ${error.message || 'Internal server error'}` }}));
                             } catch (sendErr) {
                                 helpers.devConsole("[WebSocket] Failed to send error message back to client after update error.");
                             }
                        }
                    })(); // Immediately invoke the async function
                }
                // Handle confirmation and finish list
                if (parsedMsg.event === 'confirm_and_finish_list') {
                    const { itemId, confirm } = parsedMsg.payload || {};

                    // Validate payload
                    if (itemId === undefined || confirm === undefined) {
                        helpers.devConsole(`[WebSocket] Invalid payload for confirm_and_finish_list from User ${localUserId || 'UNKNOWN'} in room ${roomId}`);
                        ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Invalid payload for confirm_and_finish_list' }}));
                        return;
                    }

                    helpers.devConsole(`[WebSocket] Processing confirm_and_finish_list for Item ${itemId}, confirmation=${confirm}`);

                    // Async-Funktion für die Verarbeitung
                    (async () => {
                        try {
                            if (confirm === true) {
                                // Benutzer hat bestätigt - Item kaufen und Liste abschließen
                                const item = await ShoppingListItem.findById(itemId).populate('shopping_list_id');
                                if (!item) {
                                    throw new Error('Item not found');
                                }

                                // Item als gekauft markieren
                                item.is_purchased = true;
                                await item.save();

                                // Liste als inaktiv und abgeschlossen markieren
                                const list = item.shopping_list_id;
                                list.is_active = false;
                                list.is_completed = true;
                                await list.save();

                                // Broadcast an alle Clients
                                const listItems = await ShoppingListItem.find({ shopping_list_id: list._id })
                                    .sort({ is_purchased: 1, name: 1 });

                                // Alle Clients über die Änderung informieren
                                broadcastToRoom(roomId, 'zettel_updated', { ...list.toObject(), items: listItems });
                                broadcastToRoom(roomId, 'zettel_finished', { listId: list._id });

                                helpers.devConsole(`[WebSocket] List ${list._id} marked as finished`);
                            } else {
                                // Benutzer hat abgelehnt - nur das Item nicht kaufen
                                helpers.devConsole(`[WebSocket] User declined to finish list, leaving item ${itemId} unchecked`);

                                // Sende nur dem anfragenden Client eine Bestätigung
                                ws.send(JSON.stringify({
                                    event: 'finish_list_cancelled',
                                    data: { itemId: itemId }
                                }));
                            }
                        } catch (error) {
                            helpers.devConsole(`[WebSocket] Error processing confirm_and_finish_list: ${error.message}`);
                            ws.send(JSON.stringify({ event: 'zettel_error', data: { message: error.message }}));
                        }
                    })();
                }
                // Handle ping/pong
                else if (parsedMsg.event === 'ping') {
                    ws.send(JSON.stringify({ event: 'pong' }));

                // ---> ADD HANDLING FOR MANUAL ITEMS <---
                } else if (parsedMsg.event === 'add_manual_item') {
                    const { name, quantity, unit } = parsedMsg.payload || {};

                    // Validate payload
                    if (!name || quantity === undefined) { // Unit is optional
                        helpers.devConsole(`[WebSocket] Invalid payload for add_manual_item from User ${localUserId || 'UNKNOWN'} in room ${roomId}. Payload: ${JSON.stringify(parsedMsg.payload)}`);
                        ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Invalid payload for add_manual_item: Missing name or quantity.' }}));
                        return; // Stop processing
                    }

                    helpers.devConsole(`[WebSocket] Processing add_manual_item Name: ${name}, Qty: ${quantity}, Unit: ${unit} by User ${localUserId} in room ${roomId}`);

                    // Call controller to add the item (async)
                    (async () => {
                        try {
                            // The controller function needs kitchentableId to find the active list
                            const newItem = await shoppingListController.addManualItemViaWebSocket(kitchentableId, localUserId, name, quantity, unit);

                            if (newItem) {
                                helpers.devConsole(`[WebSocket] Successfully added manual item ${newItem._id} via WS by User ${localUserId}.`);
                                // Broadcast the entire updated list after adding an item
                                // Fetch the list and items directly here
                                try {
                                    const listId = newItem.shopping_list_id;
                                    const list = await ShoppingList.findById(listId);
                                    const items = await ShoppingListItem.find({ shopping_list_id: listId }).sort({ is_purchased: 1, name: 1 });

                                    if (list) {
                                        const listData = { ...list.toObject(), items: items };
                                        broadcastToRoom(roomId, 'zettel_updated', listData);
                                        helpers.devConsole(`[WebSocket] Broadcasted 'zettel_updated' after adding manual item in room ${roomId}.`);
                                    } else {
                                        helpers.devConsole(`[WebSocket] Warning: Could not find list ${listId} after adding item ${newItem._id} to broadcast.`);
                                    }
                                } catch (fetchError) {
                                     helpers.devConsole(`[WebSocket] Error fetching list/items for broadcast after adding manual item: ${fetchError}`);
                                     // Decide if we should send an error to the client?
                                     // Might be confusing if the item was added but broadcast failed.
                                }
                            } else {
                                helpers.devConsole(`[WebSocket] addManualItemViaWebSocket for User ${localUserId} did not return a new item (check controller).`);
                                ws.send(JSON.stringify({ event: 'zettel_error', data: { message: 'Could not add manual item.' }}));
                            }
                        } catch (addError) {
                            helpers.devConsole(`[WebSocket] Error calling addManualItemViaWebSocket by User ${localUserId}: ${addError}`);
                             try {
                                 ws.send(JSON.stringify({ event: 'zettel_error', data: { message: `Failed to add manual item: ${addError.message || 'Internal server error'}` }}));
                             } catch (sendErr) {
                                 helpers.devConsole("[WebSocket] Failed to send error message back to client after add error.");
                             }
                        }
                    })();
                }
                // ---> END HANDLING FOR MANUAL ITEMS <---

                // ---> ADD HANDLING FOR RECIPE ADD/REMOVE <---
                else if (parsedMsg.event === 'add_recipe' || parsedMsg.event === 'remove_recipe') {
                    const { recipeId } = parsedMsg.payload || {};

                    if (!recipeId) {
                        helpers.devConsole(`[WebSocket] Invalid payload for ${parsedMsg.event} from User ${localUserId || 'UNKNOWN'} in room ${roomId}. Missing recipeId.`);
                        ws.send(JSON.stringify({ event: 'zettel_error', data: { message: `Invalid payload for ${parsedMsg.event}: Missing recipeId.` }}));
                        return; // Stop processing
                    }

                    helpers.devConsole(`[WebSocket] Processing ${parsedMsg.event} for Recipe ${recipeId} by User ${localUserId} in room ${roomId}`);

                    // Find the active shopping list for this kitchentable
                    (async () => {
                        try {
                            const activeList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true }).select('_id');
                            if (!activeList) {
                                throw new Error(`No active shopping list found for kitchentable ${kitchentableId}`);
                            }
                            const listId = activeList._id;

                            // Construct mock request object suitable for the Impl functions
                            const mockReq = {
                                params: { listId: listId.toString(), recipeId: recipeId }, // remove_recipe needs recipeId in params
                                body: { recipeId: recipeId }, // add_recipe needs recipeId in body
                                user: { id: localUserId }, // User ID for authorization
                                // No need for req.app or other Express-specific properties
                                // The broadcastToRoom function will be passed directly
                            };
                            // Define a simple mock next function to catch errors if needed within the Impl
                            // Although the Impl functions now throw directly or handle HTTP responses
                            const mockNext = (err) => { if (err) { throw err; } };

                            // Call the appropriate Impl function
                            let listData;
                            if (parsedMsg.event === 'add_recipe') {
                                helpers.devConsole(`[WebSocket] Calling shoppingListController.addRecipeToListImpl for list ${listId}, recipe ${recipeId}...`);
                                listData = await shoppingListController.addRecipeToListImpl(mockReq, null, mockNext, broadcastToRoom);
                                helpers.devConsole(`[WebSocket] addRecipeToListImpl finished.`);
                            } else { // remove_recipe
                                helpers.devConsole(`[WebSocket] Calling shoppingListController.removeRecipeFromListImpl for list ${listId}, recipe ${recipeId}...`);
                                listData = await shoppingListController.removeRecipeFromListImpl(mockReq, null, mockNext, broadcastToRoom);
                                helpers.devConsole(`[WebSocket] removeRecipeFromListImpl finished.`);
                            }

                            // Note: The ...Impl functions now call broadcastToRoom internally on success.
                            // We don't need to explicitly broadcast here anymore.
                             if (listData) {
                                helpers.devConsole(`[WebSocket] ${parsedMsg.event} processed successfully by Impl function. Broadcasting was handled internally.`);
                             } else {
                                 helpers.devConsole(`[WebSocket] Warning: ${parsedMsg.event} Impl function did not return list data. Broadcast might not have occurred.`);
                             }

                        } catch (recipeError) {
                            helpers.devConsole(`[WebSocket] Error processing ${parsedMsg.event} for Recipe ${recipeId} by User ${localUserId}: ${recipeError}`);
                             try {
                                 ws.send(JSON.stringify({ event: 'zettel_error', data: { message: `Failed to ${parsedMsg.event}: ${recipeError.message || 'Internal server error'}` }}));
                             } catch (sendErr) {
                                 helpers.devConsole("[WebSocket] Failed to send error message back to client after recipe processing error.");
                             }
                        }
                    })();
                }
                // ---> END HANDLING FOR RECIPE ADD/REMOVE <---

                // Handle other message types or ignore
                else {
                    helpers.devConsole(`[WebSocket] Received unhandled event type: ${parsedMsg.event} from User ${localUserId || 'UNKNOWN'} in room ${roomId}`);
                }
            } catch (error) {
                 helpers.devConsole(`[WebSocket] Error parsing message or unknown error in message handler from User ${localUserId || 'UNKNOWN'} in room ${roomId}: ${error}. Original message: ${msg.toString().substring(0, 100)}...`);
                // Ignore non-JSON messages or handle differently
            }
        });

        ws.on('close', (code, reason) => {
            helpers.devConsole(`WS Connection closed for User ${localUserId} in room ${roomId}. Code: ${code}, Reason: ${reason ? reason.toString().substring(0,100) : 'N/A'}`);
            if (kitchentableRooms[roomId]) {
                const index = kitchentableRooms[roomId].indexOf(ws);
                if (index > -1) {
                    kitchentableRooms[roomId].splice(index, 1);
                    if (kitchentableRooms[roomId].length === 0) {
                        delete kitchentableRooms[roomId];
                    }
                }
            }
        });

        ws.on('error', (error) => {
            helpers.devConsole(`WS Error for connection likely associated with User ${localUserId || 'UNKNOWN'} in room ${roomId}: ${error}`); // Log errors
            try {
                 ws.close(1011, "Internal server error");
            } catch (closeError) {
                 helpers.devConsole(`WS Error: Failed to close socket after error: ${closeError}`);
            }
            // Cleanup on error too
            if (kitchentableRooms[roomId]) {
                const index = kitchentableRooms[roomId].indexOf(ws);
                if (index > -1) {
                    kitchentableRooms[roomId].splice(index, 1);
                    if (kitchentableRooms[roomId].length === 0) {
                        delete kitchentableRooms[roomId];
                    }
                }
            }
        });

    } catch (error) {
        // Handle errors during auth or kitchentable check
        helpers.devConsole(`WS Connection Error for KT ${kitchentableId}: ${error.message || error}`);
        let closeCode = 1011; // Internal Server Error default
        let closeMessage = 'Connection setup failed.';
        if (error instanceof AppError) {
            // Use status code to potentially map to WebSocket close codes
            if (error.statusCode === 401) {
                closeCode = 1008; // Policy Violation for auth failure
                closeMessage = 'Authentication failed.';
            } else if (error.statusCode === 403) {
                closeCode = 1008; // Policy Violation for authz failure
                closeMessage = 'Authorization failed.';
            } else if (error.statusCode === 400) {
                closeCode = 1007; // Invalid frame payload data (e.g., bad token format)
                closeMessage = 'Invalid token format.';
            }
            // Keep generic message for 500 or others
        }
        // Send error before closing if possible
        try {
             ws.send(JSON.stringify({ event: 'zettel_error', data: { message: closeMessage }}));
        } catch (sendError) {
             helpers.devConsole(`WS Error sending close message: ${sendError}`);
        }
        // Close the connection with appropriate code
        ws.close(closeCode, closeMessage);
    }
}); // <<< END of app.ws handler

app.all('*', (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

app.use(globalErrorHandler);

// --- Helper Functions for Tools ---
async function callAddManualItemTool(assistantUserId, kitchentableId, ingredientName, quantity, unit) {
    helpers.devConsole(`[callAddManualItemTool] Called for User: ${assistantUserId}, KT_ID: ${kitchentableId}, Ing: ${ingredientName}, Qty: ${quantity}, Unit: ${unit}`);
    try {
        const activeList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true });
        if (!activeList) {
            return `Fehler: Keine aktive Einkaufsliste für Küchentisch-ID ${kitchentableId} gefunden.`;
        }
        const newItemData = {
            shopping_list_id: activeList._id,
            added_by_user_id: assistantUserId,
            name: ingredientName,
            quantity: quantity || 1,
            unit: unit || 'Stk.',
            is_recipe_item: false,
            is_manual_item: true
        };
        const newItem = await ShoppingListItem.create(newItemData);
        helpers.devConsole(`[callAddManualItemTool] Successfully created new manual item ${newItem._id} on list ${activeList._id}`);
        if (app && app.locals && app.locals.broadcastToRoom) {
            const listDataForBroadcast = await fetchShoppingListDataForBroadcast(kitchentableId);
            if (listDataForBroadcast) {
                app.locals.broadcastToRoom(`kitchentable_${kitchentableId}`, 'zettel_updated', listDataForBroadcast);
                helpers.devConsole(`[callAddManualItemTool] Broadcasted 'zettel_updated' for KT_ID ${kitchentableId}.`);
            }
        } else {
            helpers.devConsole(`[callAddManualItemTool] broadcastToRoom not available or not passed. Skipping broadcast.`);
        }
        // MODIFIED: Return natural language confirmation instead of JSON
        return `OK, ${ingredientName} wurde zur Liste hinzugefügt.`;

    } catch (error) {
        console.error(`[callAddManualItemTool] Error: ${error.message}`, error);
        return `Fehler beim Hinzufügen von "${ingredientName}" zur Liste: ${error.message}`;
    }
}

async function callGetShoppingListDetailsTool(assistantUserId, kitchentableId, ingredientName) {
    helpers.devConsole(`[callGetShoppingListDetailsTool] Called for User: ${assistantUserId}, KT_ID: ${kitchentableId}, Ing: ${ingredientName || 'N/A'}`);
    try {
        const activeList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true });
        if (!activeList) {
            // MODIFIED: More user-friendly error message
            return `Entschuldigung, ich konnte die aktive Einkaufsliste gerade nicht finden.`;
        }
        const items = await ShoppingListItem.find({ shopping_list_id: activeList._id }).sort({ is_purchased: 1, name: 1 });
        if (ingredientName) {
            const normalizedSearchName = ingredientName.trim().toLowerCase();
            const foundItems = items.filter(item => item.name.toLowerCase().includes(normalizedSearchName));
            if (foundItems.length > 0) {
                const itemDetails = foundItems.map(item => `${item.name} (${item.quantity} ${item.unit || ''})${item.is_purchased ? ' (erledigt)':''}`).join(', ');
                return `Ja, Folgendes wurde auf der Liste gefunden für "${ingredientName}": ${itemDetails}.`;
            } else {
                return `Nein, "${ingredientName}" wurde nicht auf der aktuellen Einkaufsliste gefunden.`;
            }
        } else {
            if (items.length === 0) {
                return "Die Einkaufsliste ist derzeit leer.";
            }
            const summary = items.slice(0, 10).map(item => `${item.name} (${item.quantity} ${item.unit || ''})${item.is_purchased ? ' (erledigt)':''}`).join('; ');
            let resultString = `Hier sind die ersten Einträge der Einkaufsliste: ${summary}.`;
            if (items.length > 10) {
                resultString += ` Insgesamt sind ${items.length} Einträge vorhanden.`;
            }
            return resultString;
        }
    } catch (error) {
        console.error(`[callGetShoppingListDetailsTool] Error: ${error.message}`, error);
        // MODIFIED: More user-friendly error message
        return `Entschuldigung, es gab ein technisches Problem beim Abrufen der Liste.`;
    }
}

async function fetchShoppingListDataForBroadcast(kitchentableId) {
    helpers.devConsole(`[fetchShoppingListDataForBroadcast] Called for KT_ID: ${kitchentableId}`);
    try {
        const activeList = await ShoppingList.findOne({ kitchentable_id: kitchentableId, is_active: true });
        if (!activeList) {
            helpers.devConsole(`[fetchShoppingListDataForBroadcast] No active list found for KT_ID ${kitchentableId}`);
            return null;
        }
        const items = await ShoppingListItem.find({ shopping_list_id: activeList._id }).sort({ is_purchased: 1, name: 1 });
        const listData = { ...activeList.toObject(), items: items.map(item => item.toObject()) };
        return listData;
    } catch (error) {
        console.error(`[fetchShoppingListDataForBroadcast] Error fetching list data for KT_ID ${kitchentableId}: ${error.message}`, error);
        return null;
    }
}

module.exports = app;