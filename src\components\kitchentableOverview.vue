<template>
  <!-- street -->
  <input v-model="kitchenstablestore.kitchentableForm.tableAddress_street" class="p-3 mt-4 ml-2 w-full border-0 outline-none rounded-2xl z-10 bg-white text-xs" autocomplete="off" placeholder="Strasse" />
  <!-- street -->
  <!-- street -->
  <input v-model="kitchenstablestore.kitchentableForm.tableAddress_plztown" class="p-3 mt-4 ml-2 w-full border-0 outline-none rounded-2xl bg-white text-xs" autocomplete="off" placeholder="Ort" />
  <!-- street -->
  <!-- street -->
  <input v-model="kitchenstablestore.kitchentableForm.tableAddress_country" class="p-3 mt-4 ml-2 w-full border-0 outline-none rounded-2xl bg-white text-xs" autocomplete="off" placeholder="Land" />
  <!-- street -->
  <button @click="kitchenstablestore.createKitchenTable()" class="z-10 ml-2 rounded-2xl text-xs mt-4 p-3 h-auto w-full font-OpenSans font-base border-solid border-2 shadow-custom default-button-shadow bg-white text-black border-black">
    <span class="font-semibold">Erstellen</span>
  </button>


</template>
<script setup>
import useNotification from '../../modules/notificationInformation';
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMenuStore } from '../store/menuStore'
import { useUserStore } from '../store/userStore'
import { useGrocerylistStore } from '../store/grocerylistStore'
import { useKitchentableStore } from '../store/kitchentableStore'
import { useWeekplanStore } from '../store/weekplanStore'


///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const kitchenstablestore = useKitchentableStore();
  const weekplanstore = useWeekplanStore();

///////////////////// SETUP /////////////////////////////////

</script>