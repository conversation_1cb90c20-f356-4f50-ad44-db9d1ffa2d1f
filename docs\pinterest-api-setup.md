# Pinterest API Setup Guide

This guide explains how to set up the Pinterest API for the Ordy marketing automation system.

## Prerequisites

- A Pinterest business account
- Access to the Pinterest Developer Portal

## Step 1: Create a Pinterest App

1. Go to the [Pinterest Developer Portal](https://developers.pinterest.com/)
2. Log in with your Pinterest business account
3. Click on "Apps" in the top navigation
4. Click "Create app"
5. Fill in the required information:
   - App name: "Ordy Marketing"
   - Description: "Ordy recipe marketing automation"
   - Website URL: Your website URL (e.g., https://www.ordyapp.com)
6. Click "Create"

## Step 2: Configure App Settings

1. In your newly created app, go to the "Settings" tab
2. Under "App settings", make sure the following scopes are enabled:
   - `boards:read`
   - `pins:read`
   - `pins:write`
3. Add your redirect URI (e.g., https://www.ordyapp.com/auth/pinterest/callback)
4. Save changes

## Step 3: Generate Access Token

For testing and development, you can generate an access token directly:

1. Go to the "Tools" tab in your Pinterest app
2. Under "Access token", click "Generate token"
3. Select all the required scopes
4. Click "Generate token"
5. Copy the generated token

For production, you should implement the OAuth 2.0 flow to get user authorization.

## Step 4: Create a Board for Marketing Content

1. Log in to your Pinterest business account
2. Create a new board specifically for Ordy marketing content
3. Note the board ID (you can find it in the URL when viewing the board)

## Step 5: Configure Environment Variables

Add the following environment variables to your `.env` file:

```
PINTEREST_ACCESS_TOKEN=your_access_token
PINTEREST_BOARD_ID=your_board_id
```

## Testing the Integration

You can test the Pinterest integration by running the test script:

```
node test-pinterest-publish.js
```

This will attempt to publish the most recent marketing content to Pinterest.

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Make sure your access token is valid and has not expired.
2. **Permission Errors**: Ensure your app has the necessary scopes enabled.
3. **Media Upload Errors**: Check that the video format is supported by Pinterest (MP4 is recommended).

### Debugging

The system logs detailed information about the Pinterest API calls. Check the logs for any error messages or issues.

## Pinterest API Documentation

For more information, refer to the official Pinterest API documentation:

- [Pinterest API v5 Documentation](https://developers.pinterest.com/docs/api/v5/)
- [Media Upload Guide](https://developers.pinterest.com/docs/api/v5/media-create/)
- [Pin Creation Guide](https://developers.pinterest.com/docs/api/v5/pins-create/)
