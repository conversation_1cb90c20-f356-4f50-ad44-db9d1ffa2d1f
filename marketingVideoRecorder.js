const { chromium } = require('playwright');
// const cron = require('node-cron'); // Removed
const path = require('path');
const fs = require('fs');
// const axios = require('axios'); // Removed
// const FormData = require('form-data'); // Removed
const { uploadFile, uploadImageData } = require('./utils/awsStorage'); 
// const helper = require('./utils/helper'); // Removed, using console.log
// const { exec } = require('child_process'); // Removed exec 
// const util = require('util');          // Removed util
// const execPromise = util.promisify(exec); // Removed execPromise
const ffmpegPath = require('ffmpeg-static');
const ffmpeg = require('fluent-ffmpeg');
const os = require('os'); // Import os module

// --- FFmpeg.wasm imports ---
// Removed FFmpeg.wasm imports from top level

// --- Konfiguration ---
// Constants like TARGET_URL, API_UPLOAD_URL, API_FILE_FIELD_NAME, CRON_SCHEDULE removed
// const OUTPUT_DIR = path.join(__dirname, 'marketing_recordings'); // Entfernt
const RECORDING_DURATION_MS = 12300; 
const VIDEO_WIDTH = 1080;
const VIDEO_HEIGHT = 1920;

// --- Hilfsfunktionen ---
function log(message, data = null) {
    const logMessage = `[${new Date().toISOString()}] [MarketingRecorder] ${message}`;
    if (data) {
        console.log(logMessage, data);
    } else {
        console.log(logMessage);
    }
}

// Sicherstellen, dass das Ausgabe-Verzeichnis existiert - Nicht mehr nötig, da wir os.tmpdir() verwenden
// if (!fs.existsSync(OUTPUT_DIR)){
//     log(`Creating output directory: ${OUTPUT_DIR}`);
//     fs.mkdirSync(OUTPUT_DIR);
// }

// --- Hauptfunktion für die Aufnahme (jetzt exportiert) ---
// Akzeptiert die Basis-URL und optional eine contentId
async function recordMarketingVideo(targetUrl, contentId = null) { 
    const finalUrl = contentId ? `${targetUrl}?contentId=${contentId}` : targetUrl;
    log(`Starting marketing video recording process for URL: ${finalUrl}`);
    
    let browser, initialContext, initialPage;
    let recordingContext, recordingPage;
    let recordedVideoPath = null; // Pfad zur .webm Datei
    let outputMp4Path = null;   // Pfad zur .mp4 Datei
    let s3Url;

    try {
        log('Launching browser...');
        browser = await chromium.launch({ headless: true });
        
        // --- PHASE 1: Seite laden und 30 Sekunden warten (ohne Aufnahme) ---
        log('Creating initial browser context (without recording)...');
        initialContext = await browser.newContext({
            viewport: { width: VIDEO_WIDTH, height: VIDEO_HEIGHT }
        });
        
        initialPage = await initialContext.newPage();
        log(`Navigating to ${finalUrl} (initial load)...`);
        try {
            // Ändere 'load' zu 'domcontentloaded', um schneller fortzufahren
            await initialPage.goto(finalUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
            log('DOM content loaded. Waiting for page to stabilize...');
            
            // Warte auf ein Element, das einen erfolgreichen Seitenaufbau signalisiert
            // Verwende einen allgemeinen Selektor, der auf den meisten Websites existieren sollte
            try {
                await initialPage.waitForSelector('body', { timeout: 10000 });
                log('Body element found. Page seems to be stable.');
            } catch (selectorError) {
                log(`Warning: Could not find body element: ${selectorError.message}`);
                // Fahre trotzdem fort
            }
        } catch (navigationError) {
            log(`Warning: Initial page navigation encountered issues: ${navigationError.message}`);
            log('Continuing anyway, as page might still be partially loaded');
            // Fahre trotzdem fort, auch wenn die Navigation Probleme hatte
        }
        
        log('Page initialized. Waiting 30 seconds to load all resources without recording...');
        await initialPage.waitForTimeout(30000);
        
        // --- PHASE 2: Soft-Reload durchführen (Cache bleibt erhalten) ---
        log('Performing soft reload to ensure optimal page state...');
        await initialPage.reload({ waitUntil: 'load' });
        
        log('Waiting 5 seconds after reload...');
        await initialPage.waitForTimeout(5000);
        
        // --- PHASE 3: Neuen Kontext mit Video-Recording öffnen ---
        log('Closing initial context...');
        await initialContext.close();
        
        log('Creating recording context with video capture...');
        const tempVideoDir = os.tmpdir(); // Get OS temporary directory
        log(`Setting video recording directory to: ${tempVideoDir}`);
        recordingContext = await browser.newContext({
            recordVideo: {
                dir: tempVideoDir, // Explicitly set the directory for .webm recording
                size: { width: VIDEO_WIDTH, height: VIDEO_HEIGHT }
            },
            viewport: { width: VIDEO_WIDTH, height: VIDEO_HEIGHT }
        });
        
        recordingPage = await recordingContext.newPage();
        log(`Navigating to ${finalUrl} for recording (with cache)...`);
        try {
            // Auch hier 'load' zu 'domcontentloaded' ändern
            await recordingPage.goto(finalUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
            log('DOM content loaded for recording. Waiting for page to stabilize...');
            
            // Erneut auf ein Element warten
            try {
                await recordingPage.waitForSelector('body', { timeout: 10000 });
                log('Body element found in recording page. Ready to capture.');
            } catch (selectorError) {
                log(`Warning: Could not find body element in recording page: ${selectorError.message}`);
                // Fahre trotzdem fort
            }
        } catch (navigationError) {
            log(`Warning: Recording page navigation encountered issues: ${navigationError.message}`);
            log('Continuing with recording anyway, as page might still be partially loaded');
            // Fahre trotzdem mit der Aufnahme fort
        }
        
        log('Starting recording. Waiting for content to display properly...');
        await recordingPage.waitForTimeout(5000);
        
        log(`Recording for ${RECORDING_DURATION_MS} ms...`);
        await recordingPage.waitForTimeout(RECORDING_DURATION_MS);
        
        log('Recording finished. Obtaining video path...');
        // Playwright gibt den Pfad zur temporären .webm Datei zurück
        recordedVideoPath = await recordingPage.video().path(); 
        log(`Video path obtained from Playwright (temp .webm): ${recordedVideoPath}`);
        
        log('Closing recording context to finalize video saving...');
        await recordingContext.close();
        log('Recording context closed, video saving finalized.');

        if (!fs.existsSync(recordedVideoPath)) {
             throw new Error(`Recorded video file (.webm) not found at path: ${recordedVideoPath}`);
        }
        log(`Verified .webm file exists at: ${recordedVideoPath}`);

        // --- WebM zu MP4 konvertieren mit fluent-ffmpeg ---
        // .mp4 Datei im OS temp Verzeichnis erstellen
        const tempMp4FileName = `ordy_video_${new Date().getTime()}.mp4`;
        outputMp4Path = path.join(os.tmpdir(), tempMp4FileName);
        log(`Starting WebM to MP4 conversion. Outputting to temp file: ${outputMp4Path}`);
        
        await new Promise((resolve, reject) => {
            ffmpeg(recordedVideoPath) 
                .setFfmpegPath(ffmpegPath)
                .setStartTime('5') // Beibehaltung des Start-Offsets
                .outputOptions(['-c:v libx264', '-c:a aac', '-pix_fmt yuv420p'])
                .on('end', () => {
                    log('FFmpeg conversion successful.');
                    resolve();
                })
                .on('error', (err) => {
                    log('ERROR during FFmpeg conversion:', err);
                    reject(new Error(`FFmpeg conversion failed: ${err.message}`));
                })
                .save(outputMp4Path);
        });

        if (!fs.existsSync(outputMp4Path)) {
            throw new Error(`Converted MP4 file not found at temp path: ${outputMp4Path}`);
        }
        log(`MP4 file created at temp path: ${outputMp4Path}`);

        const mp4Buffer = fs.readFileSync(outputMp4Path);
        log(`MP4 buffer read (${mp4Buffer.length} bytes) from temp file.`);

        // --- S3 Upload (Upload the MP4 Buffer) ---
        const s3FileName = path.basename(outputMp4Path); // Behalte den generierten Dateinamen für S3
        log(`Uploading MP4 buffer to S3 as ${s3FileName}...`);
        
        const uploadResult = await uploadImageData(mp4Buffer, s3FileName, 'video/mp4', 'marketing-videos');

        if (!uploadResult || !uploadResult.Location) {
            throw new Error('S3 Upload failed or did not return a location URL.');
        }
        s3Url = uploadResult.Location;
        log(`S3 Upload successful! URL: ${s3Url}`);

    } catch (error) {
        log(`ERROR in recordMarketingVideo: ${error.message}`, error.stack);
        // Stelle sicher, dass temporäre Dateien auch im Fehlerfall versucht werden zu löschen
        // Das Löschen passiert aber primär in der `finally`-Klausel
        throw error; 

    } finally {
        if (browser) {
            log('Closing browser...');
            await browser.close();
            log('Browser closed.');
        }
        // Lokale temporäre Dateien nach Abschluss löschen
        if (recordedVideoPath && fs.existsSync(recordedVideoPath)) {
            try {
                fs.unlinkSync(recordedVideoPath);
                log(`Deleted local temp WebM file: ${recordedVideoPath}`);
            } catch (unlinkWebmError) {
                log(`WARN: Failed to delete local temp WebM file ${recordedVideoPath}: ${unlinkWebmError.message}`);
            }
        }
        if (outputMp4Path && fs.existsSync(outputMp4Path)) {
            try {
                fs.unlinkSync(outputMp4Path);
                log(`Deleted local temp MP4 file: ${outputMp4Path}`);
            } catch (unlinkMp4Error) {
                log(`WARN: Failed to delete local temp MP4 file ${outputMp4Path}: ${unlinkMp4Error.message}`);
            }
        }
        log('Recording process finished.');
    }
    return s3Url;
}

// Exportiere die Hauptfunktion
module.exports = {
    recordMarketingVideo
};

log('Marketing video recorder script loaded and ready.'); 