const express = require('express');
const authController = require('../controllers/authController');
const shoppingListController = require('../controllers/shoppingListController');

const router = express.Router();

// Protect all routes after this middleware
router.use(authController.verify);

// Routes for operating on a specific shopping list item 
router.route('/:itemId')
    .put(shoppingListController.updateShoppingListItem)    // PUT /api/shopping-list-items/:itemId
    .delete(shoppingListController.deleteShoppingListItem); // DELETE /api/shopping-list-items/:itemId

// Route für das Bestätigen des letzten Items und Abschließen der Liste
router.route('/:itemId/confirm-and-finish')
    .post(shoppingListController.confirmPurchaseAndFinishList);

module.exports = router; 