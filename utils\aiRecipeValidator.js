/**
 * AI Recipe Validator & Post-Processor
 * Validiert und korrigiert AI-generierte Rezepte für Ordy-Kompatibilität
 */

const helper = require('./helper');
const Unit = require('../models/unitModel');
const Grocery = require('../models/groceryModel');

/**
 * Validiert und korrigiert ein AI-generiertes Rezept
 * @param {Object} aiRecipe - Das von der AI generierte Rezept
 * @returns {Object} Korrigiertes und validiertes Rezept
 */
async function validateAndFixAIRecipe(aiRecipe) {
    helper.devConsole('🔍 AI Recipe Validator: Starting validation...');
    
    if (!aiRecipe) {
        throw new Error('AI Recipe is null or undefined');
    }

    const fixedRecipe = JSON.parse(JSON.stringify(aiRecipe)); // Deep clone

    // 1. Validiere und korrigiere Menu-Struktur
    if (!fixedRecipe.menu) {
        fixedRecipe.menu = {};
    }
    
    // Pflichtfelder für Menu
    if (!fixedRecipe.menu.name || fixedRecipe.menu.name.trim() === '') {
        throw new Error('Recipe name is required');
    }
    
    if (!fixedRecipe.menu.description || fixedRecipe.menu.description.trim() === '') {
        fixedRecipe.menu.description = `Leckeres Rezept: ${fixedRecipe.menu.name}`;
        helper.devConsole('⚠️ Generated default description');
    }

    // Standard-Werte für Menu
    fixedRecipe.menu.menuchilds = fixedRecipe.menu.menuchilds || [];
    fixedRecipe.menu.users = fixedRecipe.menu.users || [];
    fixedRecipe.menu.imagelink = fixedRecipe.menu.imagelink || ' ';
    fixedRecipe.menu.freeAccess = fixedRecipe.menu.freeAccess || false;

    // 2. Validiere und korrigiere MenuChild-Struktur
    if (!fixedRecipe.menuchild) {
        fixedRecipe.menuchild = {};
    }

    // Pflichtfelder für MenuChild
    if (!fixedRecipe.menuchild.seatCount || fixedRecipe.menuchild.seatCount < 1) {
        fixedRecipe.menuchild.seatCount = 2; // Standard: 2 Personen
        helper.devConsole('⚠️ Set default seatCount to 2');
    }

    if (!fixedRecipe.menuchild.cookingTime || fixedRecipe.menuchild.cookingTime < 1) {
        fixedRecipe.menuchild.cookingTime = 30; // Standard: 30 Minuten
        helper.devConsole('⚠️ Set default cookingTime to 30 minutes');
    }

    // Validiere Preparation
    if (!fixedRecipe.menuchild.preperation || !Array.isArray(fixedRecipe.menuchild.preperation)) {
        fixedRecipe.menuchild.preperation = [
            {
                head: 'Zubereitung',
                content: 'Bereiten Sie das Rezept nach Ihren Wünschen zu.'
            }
        ];
        helper.devConsole('⚠️ Generated default preparation steps');
    }

    // Validiere Nutritions
    if (!fixedRecipe.menuchild.nutritions || !Array.isArray(fixedRecipe.menuchild.nutritions)) {
        fixedRecipe.menuchild.nutritions = [
            { name: 'Protein', amount: 20, unit: 'g' },
            { name: 'Kohlenhydrate', amount: 45, unit: 'g' },
            { name: 'Fett', amount: 15, unit: 'g' }
        ];
        helper.devConsole('⚠️ Generated default nutritions');
    }

    // Korrigiere Nutrition-Datentypen
    fixedRecipe.menuchild.nutritions = fixedRecipe.menuchild.nutritions.map(nutrition => ({
        name: nutrition.name || 'Unbekannt',
        amount: typeof nutrition.amount === 'string' ? parseFloat(nutrition.amount) || 0 : nutrition.amount || 0,
        unit: nutrition.unit || 'g'
    }));

    fixedRecipe.menuchild.versions = fixedRecipe.menuchild.versions || [];

    // 3. Validiere und korrigiere Ingredients
    if (!fixedRecipe.ingredients || !Array.isArray(fixedRecipe.ingredients)) {
        throw new Error('Recipe must have ingredients array');
    }

    if (fixedRecipe.ingredients.length === 0) {
        throw new Error('Recipe must have at least one ingredient');
    }

    // Korrigiere Ingredient-Struktur und weise StableIDs zu
    fixedRecipe.ingredients = await fixIngredientStructure(fixedRecipe.ingredients);

    helper.devConsole('✅ AI Recipe validation completed successfully');
    return fixedRecipe;
}

/**
 * Korrigiert die Ingredient-Struktur und weist StableIDs zu
 * @param {Array} ingredients - Array der Zutaten
 * @returns {Array} Korrigierte Zutaten mit StableIDs
 */
async function fixIngredientStructure(ingredients) {
    helper.devConsole('🔧 Fixing ingredient structure...');
    
    const fixedIngredients = [];
    let stableId = 1;

    for (const ingredient of ingredients) {
        const fixedIngredient = {
            amount: 1,
            unit: null,
            name: null,
            stableId: stableId++
        };

        // Korrigiere Amount (muss Number sein)
        if (typeof ingredient.amount === 'string') {
            fixedIngredient.amount = parseFloat(ingredient.amount) || 1;
        } else if (typeof ingredient.amount === 'number') {
            fixedIngredient.amount = ingredient.amount;
        }

        // Korrigiere Unit (muss ObjectId sein)
        if (ingredient.unit) {
            if (typeof ingredient.unit === 'string') {
                // String zu Unit-ObjectId konvertieren
                fixedIngredient.unit = await findOrCreateUnit(ingredient.unit);
            } else if (ingredient.unit.name) {
                // {name: "..."} Format zu ObjectId konvertieren
                fixedIngredient.unit = await findOrCreateUnit(ingredient.unit.name);
            }
        }

        if (!fixedIngredient.unit) {
            // Fallback: Standard-Unit "-"
            fixedIngredient.unit = await findOrCreateUnit('-');
        }

        // Korrigiere Name (muss ObjectId sein)
        if (ingredient.name) {
            if (typeof ingredient.name === 'string') {
                // String zu Grocery-ObjectId konvertieren
                fixedIngredient.name = await findOrCreateGrocery(ingredient.name);
            } else if (ingredient.name.name) {
                // {name: "..."} Format zu ObjectId konvertieren
                fixedIngredient.name = await findOrCreateGrocery(ingredient.name.name);
            }
        }

        if (!fixedIngredient.name) {
            throw new Error(`Invalid ingredient name: ${JSON.stringify(ingredient)}`);
        }

        fixedIngredients.push(fixedIngredient);
        helper.devConsole(`✅ Fixed ingredient ${stableId-1}: ${ingredient.name?.name || ingredient.name} (${fixedIngredient.amount} ${ingredient.unit?.name || ingredient.unit})`);
    }

    return fixedIngredients;
}

/**
 * Findet oder erstellt eine Unit
 * @param {string} unitName - Name der Einheit
 * @returns {ObjectId} Unit ObjectId
 */
async function findOrCreateUnit(unitName) {
    if (!unitName || unitName.trim() === '') {
        unitName = '-';
    }

    let unit = await Unit.findOne({ name: unitName.trim() });
    
    if (!unit) {
        unit = await Unit.create({ name: unitName.trim() });
        helper.devConsole(`🆕 Created new unit: ${unitName}`);
    }

    return unit._id;
}

/**
 * Findet oder erstellt eine Grocery
 * @param {string} groceryName - Name der Zutat
 * @returns {ObjectId} Grocery ObjectId
 */
async function findOrCreateGrocery(groceryName) {
    if (!groceryName || groceryName.trim() === '') {
        throw new Error('Grocery name cannot be empty');
    }

    let grocery = await Grocery.findOne({ name: groceryName.trim() });
    
    if (!grocery) {
        grocery = await Grocery.create({ name: groceryName.trim() });
        helper.devConsole(`🆕 Created new grocery: ${groceryName}`);
    }

    return grocery._id;
}

/**
 * Validiert Platzhalter in Zubereitungsschritten
 * @param {Array} preparationSteps - Zubereitungsschritte
 * @param {number} maxStableId - Höchste verfügbare StableID
 * @returns {Array} Validierte Zubereitungsschritte
 */
function validatePreparationPlaceholders(preparationSteps, maxStableId) {
    helper.devConsole('🔍 Validating preparation placeholders...');
    
    return preparationSteps.map(step => {
        let content = step.content || '';
        
        // Finde alle ${ID:X} Platzhalter
        const placeholderRegex = /\$\{ID:(\d+)\}/g;
        let match;
        
        while ((match = placeholderRegex.exec(content)) !== null) {
            const placeholderId = parseInt(match[1]);
            
            if (placeholderId > maxStableId) {
                helper.devConsole(`⚠️ Invalid placeholder ${match[0]} - max ID is ${maxStableId}`);
                // Entferne ungültige Platzhalter
                content = content.replace(match[0], '[Zutat]');
            }
        }
        
        return {
            ...step,
            content: content
        };
    });
}

module.exports = {
    validateAndFixAIRecipe,
    fixIngredientStructure,
    validatePreparationPlaceholders
};
