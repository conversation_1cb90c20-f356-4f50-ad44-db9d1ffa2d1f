const express = require('express');
const marketingWorkflowController = require('../controllers/marketingWorkflowController');
const authController = require('../controllers/authController'); // For authentication

const router = express.Router();

// Protect the workflow trigger route
router.use(authController.verifyServiceClient);

// Route to trigger the full workflow
router.post('/trigger', marketingWorkflowController.triggerWorkflow);

module.exports = router; 