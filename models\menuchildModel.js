const mongoose = require('mongoose');
const { connection1 } = require('./../db.js');
//const MenuesRelations = require('../models/menuRelationModel')
const Unit = require('./unitModel')
const Grocery = require('./groceryModel')
const helper = require('./../utils/helper.js')

const menuchildSchema = new mongoose.Schema({
    parentId: {
        type: String
    },
    seatCount: {
        type: Number
    },
    cookingTime: {
        type: Number
    },
    isStandard: {
        type: Boolean,
        default: true,
    },
    ingredients: [
        {
            amount: Number,
            unit: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Unit'
            },
            name: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Grocery'
            },
            stableId: {
                type: Number,
                default: null
            }
        }
    ],
    // KRITISCH: Globales Maximum für stabile IDs - verhindert Wiederverwendung gelöschter IDs
    maxUsedStableId: {
        type: Number,
        default: 0
    },
    preperation: {
        type: Array,
    },
    nutritions: {
        type: Array,
    },
    versions: {
        type: Array,
    },
    createdAt: {
        type: Date,
        default: Date.now()
    }
})

// Middleware, um sicherzustellen, dass jedes Unit-Feld einen Wert hat
menuchildSchema.pre('save', async function(next) {
    try {
        if (this.ingredients && Array.isArray(this.ingredients)) {
            const unitToAdd = await Unit.findOne({"name": "-"});
            for (const ingredient of this.ingredients) {
                if (ingredient.unit == null) {
                    // Füge das Standard-Unit-Objekt hinzu, wenn unit null ist
                    console.log("----------------- add unit pre hock ---------------")
                    console.log(unitToAdd._id)
                    console.log("----------------- add unit pre hock ---------------")
                    ingredient.unit = unitToAdd._id
                }
            }
        }
        next(); // Nächster Middleware oder Speichern
    } catch (error) {
        next(error); // Fehlerbehandlung
    }
});

// KRITISCH: StableID-Validierung vor dem Speichern (nur bei direkten save() Operationen)
menuchildSchema.pre('save', function(next) {
    try {
        // Überspringe Validierung bei updateOne/updateMany Operationen
        if (this.isNew === false && this.$op === undefined) {
            helper.devConsole('⚠️ Skipping StableID validation for updateOne operation');
            return next();
        }

        if (this.ingredients && Array.isArray(this.ingredients)) {
            const stableIdManager = require('../utils/stableIdManager');

            // Validiere StableID-Konsistenz nur bei neuen Dokumenten oder expliziten save() Aufrufen
            const validation = stableIdManager.validateStableIds({
                ingredients: this.ingredients,
                maxUsedStableId: this.maxUsedStableId
            });

            if (!validation.isValid) {
                helper.devConsole('❌ StableID validation failed:', validation.errors);
                // Logge Warnung aber blockiere nicht die Speicherung bei Updates
                console.warn('⚠️ StableID validation failed but allowing update:', validation.errors);
            }

            if (validation.warnings.length > 0) {
                helper.devConsole('⚠️ StableID warnings during save:', validation.warnings);
            }
        }
        next();
    } catch (error) {
        helper.devConsole('❌ Error in StableID pre-save middleware:', error);
        // Logge Fehler aber blockiere nicht die Speicherung
        console.warn('⚠️ StableID middleware error but allowing save:', error.message);
        next();
    }
});

module.exports = connection1.model('MenuChild', menuchildSchema)