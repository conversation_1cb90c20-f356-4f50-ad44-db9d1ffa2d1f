on:
  push:
    branches:
      - main

env:
  AZURE_WEBAPP_NAME: ordy-api-prod
  AZURE_WEBAPP_PACKAGE_PATH: '.'
  NODE_VERSION: '20.x'
  DATABASE: ${{ secrets.DATABASE }}
  DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
  JWT_EXPIRES_IN: ${{ secrets.JWT_EXPIRES_IN }}
  JWT_SECRET: ${{ secrets.JWT_SECRET }}
  NODE_ENV: ${{ secrets.NODE_ENV }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  OPENAI_API_ORG: ${{ secrets.OPENAI_API_ORG }}
  PORT: ${{ secrets.PORT }}
  S3_ACCESSKEY: ${{ secrets.S3_ACCESSKEY }}
  S3_BUCKETNAME: ${{ secrets.S3_BUCKETNAME }}
  S3_BUCKETREGION: ${{ secrets.S3_BUCKETREGION }}
  S3_PATH: ${{ secrets.S3_PATH }}
  S3_SECRET: ${{ secrets.S3_SECRET }}
  SCM_DO_BUILD_DURING_DEPLOYMENT: ${{ secrets.SCM_DO_BUILD_DURING_DEPLOYMENT }}
  STYTCH_PASSWORD: ${{ secrets.STYTCH_PASSWORD }}
  STYTCH_PID: ${{ secrets.STYTCH_PID }}
  WEBSITE_HTTPLOGGING_RETENTION_DAYS: ${{ secrets.WEBSITE_HTTPLOGGING_RETENTION_DAYS }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: npm install, build, and test
        run: |
          npm install
          npm run start --if-present

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v3
        with:
          name: node-app
          path: .

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v3
        with:
          name: node-app

      - name: 'Deploy to Azure WebApp'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@85270a1854658d167ab239bce43949edb336fa7c
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
