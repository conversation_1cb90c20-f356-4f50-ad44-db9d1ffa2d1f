/**
 * Categorization Controller
 * Handles intelligent ingredient categorization
 */

const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const AppError = require('../utils/appError');
const { 
    intelligentCategorization, 
    categorizeAndCreateGrocery, 
    batchCategorization,
    ORDY_CATEGORIES 
} = require('../utils/intelligentCategorizer');
const Grocery = require('../models/groceryModel');

/**
 * Kategorisiert eine einzelne Zutat
 * POST /api/v1/categorization/single
 */
const categorizeSingleIngredient = catchAsync(async (req, res, next) => {
    const { ingredientName, useAI = true } = req.body;

    if (!ingredientName) {
        return next(new AppError('Ingredient name is required', 400));
    }

    helper.devConsole(`[categorizeSingleIngredient] Categorizing: "${ingredientName}"`);

    try {
        const result = await categorizeAndCreateGrocery(ingredientName, useAI);

        res.status(200).json({
            success: true,
            data: {
                ingredientName: ingredientName,
                category: result.category,
                confidence: result.confidence,
                method: result.method,
                groceryId: result.groceryId,
                matchedKeyword: result.matchedKeyword
            }
        });

    } catch (error) {
        helper.devConsole(`[categorizeSingleIngredient] Error: ${error.message}`);
        return next(new AppError(`Categorization failed: ${error.message}`, 500));
    }
});

/**
 * Kategorisiert mehrere Zutaten in einem Batch
 * POST /api/v1/categorization/batch
 */
const categorizeBatchIngredients = catchAsync(async (req, res, next) => {
    const { ingredientNames, useAI = true } = req.body;

    if (!ingredientNames || !Array.isArray(ingredientNames)) {
        return next(new AppError('ingredientNames must be an array', 400));
    }

    if (ingredientNames.length === 0) {
        return next(new AppError('At least one ingredient name is required', 400));
    }

    if (ingredientNames.length > 50) {
        return next(new AppError('Maximum 50 ingredients per batch', 400));
    }

    helper.devConsole(`[categorizeBatchIngredients] Categorizing ${ingredientNames.length} ingredients`);

    try {
        const results = await batchCategorization(ingredientNames, useAI);

        const successCount = results.filter(r => !r.error).length;
        const errorCount = results.filter(r => r.error).length;

        helper.devConsole(`[categorizeBatchIngredients] Completed: ${successCount} success, ${errorCount} errors`);

        res.status(200).json({
            success: true,
            data: {
                results: results,
                summary: {
                    total: ingredientNames.length,
                    successful: successCount,
                    errors: errorCount
                }
            }
        });

    } catch (error) {
        helper.devConsole(`[categorizeBatchIngredients] Error: ${error.message}`);
        return next(new AppError(`Batch categorization failed: ${error.message}`, 500));
    }
});

/**
 * Gibt alle verfügbaren Kategorien zurück
 * GET /api/v1/categorization/categories
 */
const getAvailableCategories = catchAsync(async (req, res, next) => {
    helper.devConsole('[getAvailableCategories] Returning available categories');

    const categories = Object.keys(ORDY_CATEGORIES).map(categoryName => ({
        name: categoryName,
        color: ORDY_CATEGORIES[categoryName].color,
        keywordCount: ORDY_CATEGORIES[categoryName].keywords.length
    }));

    res.status(200).json({
        success: true,
        data: {
            categories: categories,
            total: categories.length
        }
    });
});

/**
 * Aktualisiert die Kategorie einer bestehenden Grocery
 * PUT /api/v1/categorization/grocery/:groceryId
 */
const updateGroceryCategory = catchAsync(async (req, res, next) => {
    const { groceryId } = req.params;
    const { category } = req.body;

    if (!category) {
        return next(new AppError('Category is required', 400));
    }

    if (!Object.keys(ORDY_CATEGORIES).includes(category)) {
        return next(new AppError('Invalid category', 400));
    }

    helper.devConsole(`[updateGroceryCategory] Updating grocery ${groceryId} to category: ${category}`);

    try {
        const grocery = await Grocery.findById(groceryId);
        
        if (!grocery) {
            return next(new AppError('Grocery not found', 404));
        }

        const oldCategory = grocery.category;
        grocery.category = category;
        await grocery.save();

        helper.devConsole(`[updateGroceryCategory] Updated "${grocery.name}" from "${oldCategory}" to "${category}"`);

        res.status(200).json({
            success: true,
            data: {
                groceryId: grocery._id,
                name: grocery.name,
                oldCategory: oldCategory,
                newCategory: category
            }
        });

    } catch (error) {
        helper.devConsole(`[updateGroceryCategory] Error: ${error.message}`);
        return next(new AppError(`Failed to update grocery category: ${error.message}`, 500));
    }
});

/**
 * Analysiert und kategorisiert alle Groceries ohne Kategorie
 * POST /api/v1/categorization/migrate-existing
 */
const migrateExistingGroceries = catchAsync(async (req, res, next) => {
    const { useAI = false, limit = 100 } = req.body;

    helper.devConsole(`[migrateExistingGroceries] Starting migration (AI: ${useAI}, limit: ${limit})`);

    try {
        // Finde Groceries ohne Kategorie oder mit 'Sonstiges'
        const uncategorizedGroceries = await Grocery.find({
            $or: [
                { category: { $exists: false } },
                { category: null },
                { category: 'Sonstiges' }
            ]
        }).limit(limit);

        if (uncategorizedGroceries.length === 0) {
            return res.status(200).json({
                success: true,
                message: 'No groceries need categorization',
                data: { processed: 0, updated: 0 }
            });
        }

        helper.devConsole(`[migrateExistingGroceries] Found ${uncategorizedGroceries.length} groceries to categorize`);

        let updatedCount = 0;
        const results = [];

        for (const grocery of uncategorizedGroceries) {
            try {
                const categoryResult = await intelligentCategorization(grocery.name, useAI);
                
                if (categoryResult.category !== 'Sonstiges' || !grocery.category) {
                    grocery.category = categoryResult.category;
                    await grocery.save();
                    updatedCount++;
                    
                    results.push({
                        groceryId: grocery._id,
                        name: grocery.name,
                        category: categoryResult.category,
                        confidence: categoryResult.confidence,
                        method: categoryResult.method
                    });
                    
                    helper.devConsole(`[migrateExistingGroceries] Updated "${grocery.name}" → ${categoryResult.category}`);
                }
            } catch (itemError) {
                helper.devConsole(`[migrateExistingGroceries] Error processing "${grocery.name}": ${itemError.message}`);
                results.push({
                    groceryId: grocery._id,
                    name: grocery.name,
                    error: itemError.message
                });
            }
        }

        helper.devConsole(`[migrateExistingGroceries] Migration completed: ${updatedCount}/${uncategorizedGroceries.length} updated`);

        res.status(200).json({
            success: true,
            message: `Migration completed: ${updatedCount} groceries categorized`,
            data: {
                processed: uncategorizedGroceries.length,
                updated: updatedCount,
                results: results
            }
        });

    } catch (error) {
        helper.devConsole(`[migrateExistingGroceries] Error: ${error.message}`);
        return next(new AppError(`Migration failed: ${error.message}`, 500));
    }
});

/**
 * Gibt Kategorisierungs-Statistiken zurück
 * GET /api/v1/categorization/stats
 */
const getCategorizationStats = catchAsync(async (req, res, next) => {
    helper.devConsole('[getCategorizationStats] Generating categorization statistics');

    try {
        // Aggregiere Groceries nach Kategorien
        const categoryStats = await Grocery.aggregate([
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { count: -1 }
            }
        ]);

        const totalGroceries = await Grocery.countDocuments();
        const uncategorized = await Grocery.countDocuments({
            $or: [
                { category: { $exists: false } },
                { category: null },
                { category: 'Sonstiges' }
            ]
        });

        const stats = {
            totalGroceries: totalGroceries,
            categorized: totalGroceries - uncategorized,
            uncategorized: uncategorized,
            categorizationRate: totalGroceries > 0 ? ((totalGroceries - uncategorized) / totalGroceries * 100).toFixed(2) : 0,
            categoryBreakdown: categoryStats.map(stat => ({
                category: stat._id || 'Uncategorized',
                count: stat.count,
                percentage: totalGroceries > 0 ? (stat.count / totalGroceries * 100).toFixed(2) : 0
            }))
        };

        res.status(200).json({
            success: true,
            data: stats
        });

    } catch (error) {
        helper.devConsole(`[getCategorizationStats] Error: ${error.message}`);
        return next(new AppError(`Failed to generate stats: ${error.message}`, 500));
    }
});

module.exports = {
    categorizeSingleIngredient,
    categorizeBatchIngredients,
    getAvailableCategories,
    updateGroceryCategory,
    migrateExistingGroceries,
    getCategorizationStats
};
