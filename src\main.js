import { createApp, watch } from 'vue'
import { createPinia } from 'pinia';
import App from './App.vue'
import router from './router';
import './index.css'
import axios from 'axios'
import { useUserStore  } from './store/userStore';
import { useGrocerylistStore  } from './store/grocerylistStore';
import { useKitchentableStore } from './store/kitchentableStore';
import { useMenuesStore } from './store/menuStore';
import useNotification from './../modules/notificationInformation';
import { useHelperStore } from '../utils/helper';
import { useTempWeekplanStore } from './store/weekplanStore';
import { initSessionManagement, cleanupSessionManagement } from './services/sessionService';
import { setupPWA } from './pwa';

const pinia = createPinia();
const app = createApp(App);

app.use(pinia);
app.use(router);

app.mount('#app');

// Session-Management initialisieren
initSessionManagement();

// PWA-Funktionalität initialisieren
setupPWA();

// Robuste Bildschirmorientierung auf Hochformat sperren
import('./utils/orientationLock.js').then(({ default: orientationLock }) => {
  // Die OrientationLock-Klasse initialisiert sich automatisch
  console.log('Orientierungssperre initialisiert:', orientationLock.getStatus());
}).catch(error => {
  console.warn('Fehler beim Laden der Orientierungssperre:', error);

  // Fallback zur einfachen Orientierungssperre
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.lock('portrait').catch(err => {
      console.warn('Fallback-Orientierungssperre fehlgeschlagen:', err);
    });
  }
});

// Aufräumen beim Beenden der App
window.addEventListener('beforeunload', () => {
  cleanupSessionManagement();
});

const userStore = useUserStore()
const groceryliststore = useGrocerylistStore()
const kitchenstablestore = useKitchentableStore();
const myRecieptsStore = useMenuesStore()
const helper = useHelperStore()
const tempWeekplanStore = useTempWeekplanStore()

const { setNotification } = useNotification();


axios.interceptors.request.use(config => {
        helper.devConsole("[Axios Interceptor] Running...");
        const sessionToken = localStorage.getItem('session_token');
        helper.devConsole(`[Axios Interceptor] Found sessionToken: ${sessionToken ? 'Yes' : 'No'}`);

        if (sessionToken) {
          config.headers['Authorization'] = `Bearer ${sessionToken}`;
          helper.devConsole("[Axios Interceptor] Authorization header set.");
        } else {
          delete config.headers['Authorization'];
          helper.devConsole("[Axios Interceptor] Authorization header removed (no token)." );
        }
        return config;
    },
    error => {
        helper.devConsole("Axios Request Interceptor Error:", error);
        return Promise.reject(error);
    }
);


// Add a response interceptor
axios.interceptors.response.use(
  (response) => {
    //helper.devConsole("axios.interceptors response positive")
    //helper.devConsole(response.data.data?.status)

    return response
  },
  (error) => {
    helper.devConsole("axios.interceptors response negative")
    helper.devConsole(error)
    if (error.response) {
      ///////////////////////////// ERROR RESPONSE => END SESSION /////////////////////////////////
      // FIXED: Only 401 (Unauthorized) should trigger logout, not 400/404/429
      if (error.response.status === 401) {
        // Redirect to login page only for authentication failures
        helper.devConsole("401 Unauthorized - logging out user")
        localStorage.removeItem('session_token')
        localStorage.removeItem('id')
        userStore.user.id = null;
        userStore.user.img = null;
        userStore.user.sessionToken = null;
        userStore.user.firstName = null;
        userStore.user.lastName = null;
        userStore.user.email = null;

        router.push({ name: 'login'})
      }

      // Handle other client errors without logout
      if (error.response.status === 400) {
        helper.devConsole("400 Bad Request - showing notification")
        setNotification("Ungültige Anfrage. Bitte überprüfen Sie Ihre Eingaben.", 'alert')
      }

      if (error.response.status === 404) {
        helper.devConsole("404 Not Found - showing notification")
        setNotification("Die angeforderte Ressource wurde nicht gefunden.", 'alert')
      }

      if (error.response.status === 429) {
        helper.devConsole("429 Too Many Requests - showing notification")
        setNotification("Zu viele Anfragen. Bitte versuchen Sie es später erneut.", 'alert')
      }

      if(error.response.status === 413){
        helper.devConsole(tempWeekplanStore)
        helper.devConsole(tempWeekplanStore.isLoading)
        tempWeekplanStore.isLoading = false
        setNotification("Das übermittelte Bild ist zu gross", 'alert')
      }

      if(error.response.status === 500){
        helper.devConsole(tempWeekplanStore)
        helper.devConsole(tempWeekplanStore.isLoading)
        tempWeekplanStore.isLoading = false
        setNotification("Es ist ein Problem aufgetreten. Bitte erneut probieren.", 'alert')
      }

      if (error.response.status === 503) {
        helper.devConsole("Limit überschritten")
        setNotification('Du hast ein Limit überschritten', 'alert', 'Abo aktualisieren', '/usersettings', 10)
      }
      ///////////////////////////// ERROR RESPONSE => END SESSION /////////////////////////////////
    }
    return Promise.reject(error)
  },
);


app.config.warnHandler = () => null;


/////////////////// SETUP VALUES /////////////////////////////
watch(() => userStore.user.defaultKitchentable, (newVal, oldVal) => {
  //console.log('defaultKitchentable changed from', oldVal, 'to', newVal);
  if (newVal) {
    //console.log("set value")
    groceryliststore.getGrocerylistOverviewByKitchentableId(newVal);
    kitchenstablestore.getKitchenTableList()
    kitchenstablestore.getKitchenTable()
  }
});
/*
// Setup function to restore authentication state
async function setup() {
  const backend_session_token = localStorage.getItem('backend_session_token')
  const session_token = localStorage.getItem('session_token')
  const session_jwt = localStorage.getItem('session_jwt')
  const user_id = localStorage.getItem('user_id')

  ////////////////////// SETUP ////////////////////////////////
  if(backend_session_token){
    await userStore.getUser(user_id, backend_session_token, session_token)
    //await groceryliststore.getAllGroceryListByUserId()
    //await myRecieptsStore.loadAllMenusByUser(userStore.user.id)
  }
}

setup()
*/
