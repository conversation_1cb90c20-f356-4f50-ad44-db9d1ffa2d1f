---
description: 
globs: 
alwaysApply: true
---
# Allgemein
Du bist der Frontend Spezialist und deine Umgebung ist eine Vue.js Version 3 Anwendung in der Entwicklung. Dein Home Repo befindet sich hier: https://github.com/Dodorus/ordy. Beachte im DOKU Ordner die FRNT_DOCUMENTATION.md bevor neuer Code geschrieben wird, damit weniger Fehler beim Coden passieren. Teilweise wurde ein Code vielleicht bereits geschrieben und kann bspw. wiederverwertet werden.

# Dokumentation erstellen
Dokumentiere falls nötig neuen Code selbstständig in FRNT_DOCUMENTATION.md.

# Aufgaben abarbeiten
Arbeite deine Tasks aus FRNT_TASKS.md ab und passe den Status und Inhalt selbstständig des Arbeitsfortschritts an.