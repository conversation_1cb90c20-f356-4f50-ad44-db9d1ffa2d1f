# Frontend-Tests

Dieses Dokument beschreibt die Testfälle für die Frontend-Funktionalitäten.

## Ein<PERSON><PERSON>szettel-Historie (F-15, F-16, F-17)

### Testfall 1: Anzeige der Einkaufszettel-Historie
1. Navigiere zur Seite `/zettel`
2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob im rechten Seitenbereich (nur auf Desktop sichtbar) ein Bereich mit dem Titel "History" angezeigt wird
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob die Liste der historischen Einkaufszettel angezeigt wird, sortiert nach Erstellungsdatum (neueste zuerst)
4. <PERSON><PERSON> Eintrag sollte das Datum, die Anzahl der Artikel und den Status (aktiv/abgeschlossen) anzeigen

**Erwartetes Ergebnis:** Die Historie wird korrekt angezeigt mit allen relevanten Informationen.

### Testfall 2: Aktivierung eines historischen Einkaufszettels
1. Navigiere zur Seite `/zettel`
2. <PERSON><PERSON><PERSON> auf einen Eintrag in der Historie (nicht den aktuell aktiven)
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob der ausgewählte Einkaufszettel hervorgehoben wird
4. Überprüfe, ob die Artikel im Hauptbereich aktualisiert werden, um die Artikel des ausgewählten Einkaufszettels anzuzeigen
5. Überprüfe, ob eine Erfolgsmeldung angezeigt wird

**Erwartetes Ergebnis:** Der historische Einkaufszettel wird aktiviert und die Artikel werden korrekt angezeigt.

## Kategorisierung von Einkaufsartikeln (F-18, F-19)

### Testfall 1: Anzeige kategorisierter Artikel
1. Navigiere zur Seite `/zettel`
2. Überprüfe, ob die Artikel nach Kategorien gruppiert angezeigt werden
3. Die Kategorien sollten in der folgenden Reihenfolge angezeigt werden:
   - Gemüse & Früchte
   - Brotwaren & Backwaren
   - Milchprodukte & Molkereiprodukte
   - Fleisch, Wurst & Fisch
   - Tiefkühlprodukte
   - Grundnahrungsmittel
   - Frühstück & Cerealien
   - Süsswaren & Snacks
   - Getränke
   - Non-Food & Haushaltsartikel
   - Sonstiges
4. Überprüfe, ob leere Kategorien ausgeblendet werden

**Erwartetes Ergebnis:** Die Artikel werden korrekt nach Kategorien gruppiert angezeigt.

### Testfall 2: Ein- und Ausklappen von Kategorien
1. Navigiere zur Seite `/zettel`
2. Klicke auf eine Kategorie-Überschrift
3. Überprüfe, ob die Artikel dieser Kategorie ausgeblendet werden
4. Klicke erneut auf die Kategorie-Überschrift
5. Überprüfe, ob die Artikel wieder angezeigt werden

**Erwartetes Ergebnis:** Kategorien können ein- und ausgeklappt werden.

### Testfall 3: Automatische Kategorisierung neuer Artikel
1. Navigiere zur Seite `/zettel`
2. Füge einen neuen Artikel hinzu (z.B. "Apfel")
3. Überprüfe, ob der Artikel in der richtigen Kategorie (z.B. "Gemüse & Früchte") angezeigt wird
4. Wiederhole den Test mit Artikeln für verschiedene Kategorien

**Erwartetes Ergebnis:** Neue Artikel werden automatisch der richtigen Kategorie zugeordnet.

## Debugging-Hinweise

## Benutzereinstellungen (/usersettings)

### Testfall 1: Anzeige der Benutzereinstellungen
1. Navigiere zur Seite `/usersettings`
2. Überprüfe, ob die Seite korrekt geladen wird und die verschiedenen Einstellungsbereiche sichtbar sind (z.B. Profil, Konto, Benachrichtigungen, Darstellung).
3. Überprüfe, ob die aktuellen Benutzereinstellungen korrekt angezeigt werden (z.B. Benutzername, E-Mail-Adresse).

**Erwartetes Ergebnis:** Die Seite für Benutzereinstellungen wird mit den korrekten aktuellen Werten angezeigt.

### Testfall 2: Ändern von Profileinstellungen
1. Navigiere zur Seite `/usersettings`.
2. Ändere eine Profileinstellung (z.B. den angezeigten Namen oder die E-Mail-Adresse - falls änderbar).
3. Klicke auf "Speichern" (oder die entsprechende Schaltfläche).
4. Überprüfe, ob eine Erfolgsmeldung angezeigt wird.
5. Lade die Seite neu oder navigiere weg und zurück.
6. Überprüfe, ob die geänderte Einstellung korrekt gespeichert wurde.

**Erwartetes Ergebnis:** Profileinstellungen können erfolgreich geändert und gespeichert werden.

### Testfall 3: Ändern des Passworts (falls Funktion vorhanden)
1. Navigiere zur Seite `/usersettings` in den Bereich "Konto" oder "Sicherheit".
2. Gib das aktuelle Passwort ein.
3. Gib ein neues Passwort ein und bestätige es.
4. Klicke auf "Passwort ändern".
5. Überprüfe, ob eine Erfolgsmeldung angezeigt wird.
6. Versuche dich mit dem neuen Passwort anzumelden.

**Erwartetes Ergebnis:** Das Passwort kann erfolgreich geändert werden und der Login mit dem neuen Passwort funktioniert.

### Testfall 4: Ungültige Eingaben bei Profileinstellungen
1. Navigiere zur Seite `/usersettings`.
2. Versuche, ungültige Werte einzugeben (z.B. eine ungültige E-Mail-Adresse, zu kurzer Benutzername).
3. Klicke auf "Speichern".
4. Überprüfe, ob Fehlermeldungen für die ungültigen Felder angezeigt werden.
5. Überprüfe, ob die fehlerhaften Änderungen nicht gespeichert wurden.

**Erwartetes Ergebnis:** Das System validiert die Eingaben und zeigt entsprechende Fehlermeldungen an, ohne fehlerhafte Daten zu speichern.

## Abonnement-Verwaltung

### Testfall 1: Anzeige der Abonnementinformationen
1. Navigiere zur Seite für die Abonnement-Verwaltung (z.B. `/usersettings/subscription` oder ein dedizierter Bereich).
2. Überprüfe, ob der aktuelle Abonnementstatus korrekt angezeigt wird (z.B. Kostenlos, Premium, Testversion).
3. Überprüfe, ob das Ablaufdatum oder das nächste Rechnungsdatum (falls zutreffend) angezeigt wird.
4. Überprüfe, ob die verfügbaren Abonnement-Optionen mit Preisen und Features aufgelistet sind.

**Erwartetes Ergebnis:** Alle relevanten Abonnementinformationen und -optionen werden klar und korrekt dargestellt.

### Testfall 2: Upgrade eines Abonnements (simulierter Prozess)
1. Navigiere zur Abonnement-Verwaltungsseite.
2. Wähle eine höhere Abonnementstufe aus.
3. Klicke auf "Upgrade" oder "Jetzt abonnieren".
4. Überprüfe, ob der Benutzer zu einer Zahlungsabwicklungsseite weitergeleitet wird oder ein entsprechendes Modal erscheint. (Der eigentliche Zahlungsprozess muss hier nicht getestet werden, nur der Ablauf bis dahin).
5. *Optional (falls ohne echte Zahlung testbar)*: Schließe den Prozess ab.
6. Überprüfe, ob der Abonnementstatus auf der Seite aktualisiert wurde.

**Erwartetes Ergebnis:** Der Prozess zum Upgrade eines Abonnements kann gestartet werden und der Status wird nach (simuliertem) Abschluss korrekt aktualisiert.

### Testfall 3: Kündigung eines Abonnements (falls Funktion vorhanden)
1. Navigiere zur Abonnement-Verwaltungsseite.
2. Suche die Option zur Kündigung des aktuellen Abonnements.
3. Klicke auf "Abonnement kündigen".
4. Bestätige die Kündigung (falls ein Bestätigungsdialog erscheint).
5. Überprüfe, ob eine Bestätigung der Kündigung angezeigt wird.
6. Überprüfe, ob der Abonnementstatus entsprechend aktualisiert wurde (z.B. "Gekündigt zum [Datum]" oder Wechsel zu Kostenlos).

**Erwartetes Ergebnis:** Ein bestehendes Abonnement kann gekündigt werden und der Status wird korrekt reflektiert.

### Testfall 4: Anzeigen von Rechnungshistorie (falls Funktion vorhanden)
1. Navigiere zur Abonnement-Verwaltungsseite oder einem Bereich für Rechnungen.
2. Überprüfe, ob eine Liste vergangener Rechnungen angezeigt wird (Datum, Betrag, Status).
3. Überprüfe, ob einzelne Rechnungen heruntergeladen oder angezeigt werden können.

**Erwartetes Ergebnis:** Die Rechnungshistorie ist einsehbar und Rechnungen können abgerufen werden.