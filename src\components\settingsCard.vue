<template>
    <div class="max-w-4xl mx-auto">
        <!-- Modern Profile Image Management -->
        <ProfileImageManager class="mb-16" />

    <!-- Personal Data Section -->
    <div class="bg-white rounded-3xl p-6 mt-6 shadow-custom shadow-gray-200/50 border border-gray-100 mb-8">
        <div class="flex items-center gap-4 mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-YesevaOne text-lg text-gray-800">Persö<PERSON>liche Daten</h3>
                <p class="text-sm text-gray-500">Ihre grundlegenden Informationen</p>
            </div>
        </div>

        <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Vorname</label>
                    <input
                        v-model="userStore.user.firstName"
                        @change="userStore.updateUserData()"
                        type="text"
                        class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Ihr Vorname"
                    />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Nachname</label>
                    <input
                        v-model="userStore.user.lastName"
                        @change="userStore.updateUserData()"
                        type="text"
                        class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Ihr Nachname"
                    />
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">E-Mail-Adresse</label>
                <input
                    v-model="userStore.user.email"
                    @change="handleEmailChange()"
                    type="email"
                    class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                />
            </div>
        </div>
    </div>

    <!-- Address Section -->
    <div class="bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 mb-8">
        <div class="flex items-center gap-4 mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-YesevaOne text-lg text-gray-800">Adresse</h3>
                <p class="text-sm text-gray-500">Ihre Kontaktadresse</p>
            </div>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Straße und Hausnummer</label>
                <input
                    v-model="userStore.user.address.line1"
                    @change="userStore.updateUserData()"
                    type="text"
                    class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Musterstraße 123"
                />
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Postleitzahl</label>
                    <input
                        v-model="userStore.user.address.postal_code_state"
                        @change="userStore.updateUserData()"
                        type="text"
                        class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="12345"
                    />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Ort</label>
                    <input
                        v-model="userStore.user.address.city"
                        @change="userStore.updateUserData()"
                        type="text"
                        class="w-full p-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="Musterstadt"
                    />
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Settings Section -->
    <div class="bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 mb-8">
        <div class="flex items-center gap-4 mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-YesevaOne text-lg text-gray-800">Sichtbarkeit & Datenschutz</h3>
                <p class="text-sm text-gray-500">Ihre Privatsphäre-Einstellungen</p>
            </div>
        </div>

        <div class="space-y-6">
            <!-- Platform Visibility -->
            <div class="p-4 bg-gray-50 rounded-2xl">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800 mb-2">Sichtbarkeit auf der Plattform</h4>
                        <p class="text-sm text-gray-600">Diese Einstellung macht Sie für andere Benutzer wie Familienmitglieder sichtbar. Wenn aktiviert, können andere Sie am Küchentisch finden.</p>
                    </div>
                    <div class="ml-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                v-model="userStore.user.extAccessable"
                                @change="userStore.changedExtAccess()"
                                class="sr-only peer"
                            />
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- GDPR Settings -->
            <div class="p-4 bg-gray-50 rounded-2xl">
                <h4 class="font-medium text-gray-800 mb-3">Cookies, Datenschutz und AGBs</h4>
                <p class="text-sm text-gray-600 mb-4">Alle Datenschutzeinstellungen akzeptiert</p>

                <div class="flex gap-4">
                    <label class="flex items-center cursor-pointer">
                        <input
                            type="radio"
                            value="2"
                            v-model="userStore.user.gdpr"
                            @change="userStore.updateUserData()"
                            class="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 focus:ring-purple-500"
                        />
                        <span class="ml-2 text-sm text-gray-700">Ja</span>
                    </label>
                    <label class="flex items-center cursor-pointer">
                        <input
                            type="radio"
                            value="1"
                            v-model="userStore.user.gdpr"
                            @change="userStore.updateUserData()"
                            class="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 focus:ring-purple-500"
                        />
                        <span class="ml-2 text-sm text-gray-700">Nein</span>
                    </label>
                </div>

                <div class="mt-4">
                    <button
                        @click="agbClicked = !agbClicked"
                        class="text-purple-600 hover:text-purple-700 text-sm font-medium underline"
                    >
                        AGBs öffnen
                    </button>
                    <Agb v-if="agbClicked" class="mt-4"></Agb>
                </div>
            </div>
        </div>
    </div>

<!--
    <div class="w-full">
        <h3 class="mt-10">Kalender und Wochenplan</h3>
        <p class="text-xs">Möchtest du als Standartansicht einen detailierten Kalender oder einfach eine Woche angeben, wenn du deinen Menüplan erstellst?</p>
        <div class="flex flex-row mt-2">
            <div class="flex flex-row">
            <input type="radio" id="0" value="0" v-model="userStore.user.weekplanmode" @change="userStore.updateUserData()"/>
            <label class="ml-1 text-sm font-thin" for="0">Tageskalender</label>
            </div>
            <div class="flex flex-row ml-2">
            <input type="radio" id="1" value="1" v-model="userStore.user.weekplanmode" @change="userStore.updateUserData()"/>
            <label class="ml-1 text-sm font-thin" for="1">Wochenkalender</label>
            </div>
        </div>
    </div>

    <div class="w-full mt-10 h-auto">
        <h3>Weitere Angaben zu deiner Person</h3>
        <p class="text-xs">Aus den nachfolgenden Angaben, wird Ordy ein Profil für Ihre Person anfertigen und diese Daten bei der täglichen Nahrungsaufnahme und Rezepterstellung berücksichtigen.</p>

        <div class="flex flex-col mt-2 font-sans text-sm">
        <p class="text-sm mt-2">Geschlecht</p>
        <p class="text-xs mt-1">Ziel ist eine biologische Einordnung von Personen, kreuzen Sie dies an, was ihrem Befinden am nächsten kommt.</p>
        <div class="flex flex-row mt-1">
            <div class="flex flex-row">
            <input type="radio" id="2" value="2" v-model="userStore.user.sex" @change="userStore.updateUserData()"/>
            <label class="ml-1 text-sm font-thin" for="2">Männlich</label>
            </div>
            <div class="flex flex-row ml-2">
            <input type="radio" id="1" value="1" v-model="userStore.user.sex" @change="userStore.updateUserData()"/>
            <label class="ml-1 text-sm font-thin" for="1">Weiblich</label>
            </div>
            <div class="flex flex-row ml-2">
            <input type="radio" id="3" value="3" v-model="userStore.user.sex" @change="userStore.updateUserData()"/>
            <label class="ml-1 text-sm font-thin" for="3">Kann ich aktuell nicht bestimmen</label>
            </div>
        </div>
        </div>

        <p class="text-sm mt-4">Biologie</p>
        <div class="flex flex-row mt-4">
        <p class="font-xs w-12">Gewicht</p>
        <input class="ml-5 text-sm p-2 min-w-24 w-auto rounded-lg text-center"
            type="input"
            @change="userStore.updateUserData()"
            v-model="userStore.user.weight"
        />
        <p class="ml-2">kg</p>
        </div>
        <div class="flex flex-row mt-2">
        <p class="font-xs w-12">Grösse</p>
        <input class="ml-5 text-sm p-2 min-w-24 w-auto rounded-lg text-center"
            type="input"
            @change="userStore.updateUserData()"
            v-model="userStore.user.height"
        />
        <p class="ml-2">cm</p>
        </div>
        <div class="flex flex-row mt-2">
        <p class="font-xs w-12">Alter</p>
        <input class="ml-5 text-sm p-2 min-w-24 w-auto rounded-lg text-center"
            type="input"
            @change="userStore.updateUserData()"
            v-model="userStore.user.age"
        />
        <p class="ml-2">Jahre</p>
        </div>
        <div class="flex flex-col mt-2 text-sm font-OpenSans font-thin">
        <p class="text-sm">Aktivitätslevel</p>
        <div class="flex flex-row mt-2">
            <input type="radio" id="1" value="1.2" v-model="userStore.user.activness" @change="userStore.updateUserData()"/>
            <label class="ml-2" for="1">Sedentär (wenig bis kein Sport)</label>
        </div>
        <div class="flex flex-row mt-2">
            <input type="radio" id="2" value="1.375" v-model="userStore.user.activness" @change="userStore.updateUserData()"/>
            <label class="ml-2 font-sans" for="2">Leicht Aktiv (leichte Bewegung/Sport 1-3 Tage/Woche)</label>
        </div>
        <div class="flex flex-row mt-2">
            <input type="radio" id="3" value="1.55" v-model="userStore.user.activness" @change="userStore.updateUserData()"/>
            <label class="ml-2" for="3">Mässig Aktiv (mäßige Bewegung/Sport 3-5 Tage/Woche)</label>
        </div>
        <div class="flex flex-row mt-2">
            <input type="radio" id="4" value="1.725" v-model="userStore.user.activness" @change="userStore.updateUserData()"/>
            <label class="ml-2" for="4">Sehr Aktiv (harte Bewegung/Sport 6-7 Tage/Woche)</label>
        </div>
        <div class="flex flex-row mt-2">
            <input type="radio" id="5" value="1.9" v-model="userStore.user.activness" @change="userStore.updateUserData()"/>
            <label class="ml-2" for="5">Extrem Aktiv (sehr harte Bewegung/Sport & körperlicher Job)</label>
        </div>
        </div>
    </div>


    <div class="w-full mt-14 h-auto">
        <h3>Dein Bedarf pro Tag ist:</h3>
        <div class="flex flex-col mt-2">
        <p class="font-xs">Protein {{ userStore.user.protDaily }} Gramm</p>
        <p class="font-xs">Kohlenhydrate {{ userStore.user.kcalDaily }} Gramm</p>
        <p class="font-xs">Fett {{ userStore.user.fatDaily }} Gramm</p>
        </div>
    </div>
-->
    <!-- Danger Zone - Account Deletion -->
    <div class="bg-red-50 border-2 border-red-200 rounded-3xl p-6 shadow-custom shadow-red-200/50 mb-8">
        <div class="flex items-center gap-4 mb-6">
            <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div>
                <h3 class="font-YesevaOne text-lg text-red-800">Gefahrenbereich</h3>
                <p class="text-sm text-red-600">Unwiderrufliche Aktionen</p>
            </div>
        </div>

        <div class="bg-white rounded-2xl p-6 border border-red-200">
            <h4 class="font-semibold text-red-800 mb-3">Account endgültig löschen</h4>
            <p class="text-sm text-red-700 mb-6">
                Wenn Sie Ihren Account löschen, werden alle Ihre Daten unwiderruflich entfernt.
                Dies umfasst alle Rezepte, Einkaufslisten, Küchentische und persönlichen Einstellungen.
                Dieser Vorgang kann nicht rückgängig gemacht werden.
            </p>

            <button
                @click="handleAccountDeletion"
                :disabled="isDeleting"
                class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-2xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
                <svg v-if="isDeleting" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span v-if="isDeleting">Lösche Account...</span>
                <span v-else>Account endgültig löschen</span>
            </button>
        </div>
    </div>
    </div>
</template>
<script setup>
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
// import axiosInstance from '../axiosConfig.js'; // Temporarily commented out due to resolve error
import useNotification from '../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../store/menuStore'
import { useUserStore  } from '../store/userStore';
import { useAboStore  } from '../store/aboStore';
import Agb from '../components/agb.vue';
import ProfileImageManager from '../components/ProfileImageManager.vue';
import { useConfirmationStore } from '../../utils/helper';

const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const store = useMenuStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore()
const aboStore = useAboStore()
const confirmationStore = useConfirmationStore();

const agbClicked = ref(false)
const isDeleting = ref(false);

const validateEmailInput = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

const handleEmailChange = async () => {
    const emailValid = validateEmailInput(userStore.user.email);

    if (emailValid) {
        await userStore.updateUserData();
    } else {
        userStore.user.email = '' // Reset or keep old?
        userStore.user.extAccessable = false // Why is this here?
        setNotification('Bitte geben Sie eine gültige E-Mail-Adresse ein.', 'alert');
    }
};

const handleAccountDeletion = async () => {
    try {
        const isConfirmed = await confirmationStore.showConfirmation(
            'Account endgültig löschen?',
            'Sind Sie sicher, dass Sie Ihren Account unwiderruflich löschen möchten? Alle Ihre Daten (Rezepte, Einkaufszettel, Küchentische etc.) gehen dabei verloren. Dieser Vorgang kann nicht rückgängig gemacht werden.'
        );

        if (isConfirmed) {
            isDeleting.value = true;
            try {
                await userStore.deleteAccount();
                setNotification('Account erfolgreich gelöscht.', 'success');
                await userStore.logout();
                router.push({ name: 'Login' });
            } catch (error) {
                console.error('Fehler beim Löschen des Accounts:', error);
                const errorMessage = error?.message || 'Ein unbekannter Fehler ist aufgetreten.';
                setNotification(`Fehler beim Löschen des Accounts: ${errorMessage}`, 'alert');
            } finally {
                isDeleting.value = false;
            }
        } else {
            console.log('Account deletion cancelled by user.');
        }
    } catch (storeError) {
        console.error('Error showing confirmation dialog:', storeError);
        setNotification('Fehler beim Anzeigen des Bestätigungsdialogs.', 'alert');
        isDeleting.value = false;
    }
};

</script>