<template>
  <div
      v-if="notification"
      class="w-full mx-auto h-auto fixed z-[70] top-4">
    <div
      class="w-3/4 md:w-1/2 lg:w-1/3 flex flex-col mx-auto h-auto rounded-xl mt-2 break-normal p-2"
      :class="{
        'bg-ordypurple-100': notificationState === 'alert',
        'bg-green-500': notificationState === 'success',
        'bg-yellow-400': notificationState === 'info'
      }"
    >
      <div class="inline-block align-middle z-50 w-full my-auto h-auto text-center text-white font-OpenSans font-bold text-xs">
        <div>
          <div>
          {{ notificationText }}
          </div>
          <div class="w-full mt-1" v-if="notificationLink != undefined && notificationLinkText != undefined">
            <router-link :to="notificationLink" class="text-blue-400 z-50 underline ml-2">
              {{ notificationLinkText }}
            </router-link>
          </div>
        </div>
        <span 
          class="animate-ping absolute inline-flex mt-1 h-3 w-3 z-50 rounded-full bg-black ml-32 align-right opacity-75"
          :class="{
            'bg-ordypurple-100': notificationState === 'alert',
            'bg-green-500': notificationState === 'success',
            'bg-yellow-400': notificationState === 'info'
          }"
        ></span>
      </div>
    </div>
 </div>
</template>
<script>
//v-if="notification"
import useNotification from '../../modules/notificationInformation';

export default {
    name: 'basicNotification',
    components: {

    },
    setup() {
        
        const { notification, notificationState, notificationText, notificationLinkText, notificationLink } = useNotification();

        return {
            notification,
            notificationState,
            notificationText,
            notificationLinkText,
            notificationLink,
        }
    }
}
</script>