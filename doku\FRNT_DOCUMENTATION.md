# FRNT_DOCUMENTATION.md

## Übersicht

Dieses Dokument enthält die technische Dokumentation für das `ordy-at-vite5`-Frontend. Es beschreibt den aktuellen Zustand und die Funktionsweise implementierter Features und Komponenten.

## Architektur & Ordnerstruktur

*   **Komponenten:** `src/components/` (Wiederverwendbare UI-Elemente)
*   **Body-Komponenten:** `src/body/` (Spezifischere Komponenten für Seiteninhalte?)
*   **Views/Pages:** `src/views/` (Seiten-Komponenten, die über den Router aufgerufen werden)
*   **State Management (Pinia):** `src/store/` (Globale Zustandsverwaltung)
*   **Routing (Vue Router):** `src/router/` (Definition der Anwendungsrouten)
*   **Assets:** `src/assets/` (Statische Dateien wie Bilder, SVGs)
*   **Haupt-App:** `src/App.vue` (Wurzelkomponente)
*   **Entry Point:** `src/main.js` (Initialisierung der Vue-App)
*   **Globale Styles:** `src/index.css`

## Wichtige Komponenten

### `src/components/auth/LoginForm.vue` (Beispiel)

*   **Zweck:** Zeigt das Login-Formular an und interagiert mit dem `authStore`.
*   **Props:** [...]
*   **Events:** `@login-success`, `@login-error`
*   **Abhängigkeiten:** `authStore`

*   *[Weitere wichtige Komponenten hier dokumentieren, z.B. Navigation, häufig genutzte UI-Blöcke...]*

## State Management (Pinia Stores)

### `src/store/auth.ts` (`authStore`) (Beispiel, falls vorhanden)

*   **State:** `user`, `accessToken`, `isAuthenticated`
*   **Actions:** `login()`, `logout()`, `fetchUser()`, `handleTokenRefresh()`
*   **Getters:** `isAdmin`
*   **Interaktion:** Kommuniziert mit dem Stytch SDK und Backend-API für Auth-Operationen.

### `src/store/menuStore.js` (`useMenuesStore`)

Die Frontend-Logik zum Abrufen und Verwalten der Rezepte für die `/kochbuch`-Ansicht befindet sich im Pinia Store `useMenuesStore` in der Datei `src/store/menuStore.js`. Im Rahmen einer Fehlerbehebung wurden folgende Anpassungen an der Funktion `loadAllMenusByUser` vorgenommen:

1.  **Korrekte API-Parameter-Erstellung:**
    *   **Problem:** Die vorherige Implementierung konstruierte die Query-Parameter für den API-Aufruf nicht korrekt. Es wurden teilweise Filter wie `searchstring` oder `cookingTime[lte]` hartcodiert oder nicht dynamisch basierend auf dem UI-Zustand hinzugefügt. Die Standardsortierung (`sort=-createdAt`) wurde nicht zuverlässig angewendet.
    *   **Fix:** Die Erstellung des `queryParams`-Objekts wurde überarbeitet. Parameter wie `scope` und `page` werden immer gesetzt. Der `sort`-Parameter wird nun korrekt aus dem `filterCreatedAt`-Ref gelesen (Standard: `sort=-createdAt`). Die Filter `searchstring`, `cookingTime` (aus `filterCookingTime`-Ref extrahiert) und `filterOnShoppingList` werden nur noch hinzugefügt, wenn die entsprechenden Refs (`searchMyReciepts`, `filterCookingTime`, `isShoppingListFilterActive`) im Store gesetzt sind. Dadurch wird sichergestellt, dass der initiale API-Aufruf korrekt ohne unerwünschte Filter erfolgt und Filter nur bei Nutzerinteraktion angewendet werden.

2.  **Korrekte Verarbeitung der API-Antwort:**
    *   **Problem:** Die Funktion erwartete fälschlicherweise, dass `response.data.data` direkt das Array der Menüs enthält. Laut API-Dokumentation befindet sich das Array jedoch unter `response.data.data.menus`. Dies führte dazu, dass die empfangenen Rezepte nicht korrekt verarbeitet und im `myReciepts`-Array des Stores gespeichert wurden.
    *   **Fix:** Die Prüfung und Zuweisung der Daten wurde korrigiert. Der Code greift nun auf `response.data.data.menus` zu, um das Rezepte-Array zu erhalten. Die Logik zur Paginierungsprüfung (`hasMoreData`) wurde ebenfalls angepasst, um die Länge von `response.data.data.menus` zu berücksichtigen.

3.  **Behebung von `ReferenceError: hasLoadedOnce`:**
    *   **Problem:** Die Variable `hasLoadedOnce` wurde in `loadAllMenusByUser` verwendet, war aber nicht im Gültigkeitsbereich des Stores definiert.
    *   **Fix:** `hasLoadedOnce` wurde als `ref` im `useMenuesStore` deklariert und initialisiert (`ref(false)`), um den Zustand des initialen Ladevorgangs korrekt zu verfolgen.

Diese Änderungen stellen sicher, dass die `/kochbuch`-Ansicht die Daten korrekt vom Backend abruft, verarbeitet und darstellt, entsprechend der definierten API-Spezifikation.

*   *[Weitere Pinia Stores hier dokumentieren...]*

## Routing (`src/router/index.js`)

*   [Beschreibung der Hauptrouten und eventueller Guards]
*   [Verweis auf Lazy Loading, falls verwendet]

## Authentifizierung & Session Management

Der zentrale Punkt für die Verwaltung des Benutzerzustands und der Authentifizierung ist der `userStore` (`src/store/userStore.js`).

**Prüfen des Login-Status:**

Der primäre Indikator dafür, ob ein Benutzer gegenüber dem **Backend API** als eingeloggt gilt, ist das Vorhandensein des `session_token` (mit Unterstrich) im **`localStorage`**.

```javascript
// Innerhalb einer Komponente oder eines anderen Stores:
import { computed } from 'vue'; // Optional für computed property

const hasSessionToken = computed(() => !!localStorage.getItem('session_token'));

if (localStorage.getItem('session_token')) {
  // Benutzer hat einen Session-Token im localStorage, API-Aufrufe sollten funktionieren
} else {
  // Benutzer hat keinen Session-Token im localStorage, ist nicht eingeloggt
}

// Oder direkt in einer Funktion:
function someAction() {
    const token = localStorage.getItem('session_token');
    if (!token) {
        console.error('Nicht eingeloggt!');
        return;
    }
    // ... mache etwas mit dem Token ...
}
```

*   Der `session_token` wird im `localStorage` durch die Action `setAuthState` (nach erfolgreichem Login/Token-Austausch) gespeichert.
*   Er wird durch die Actions `logout` und `logoutSession` aus dem `localStorage` entfernt.
*   Die Property `user.value.sessionToken` (CamelCase) im `userStore` wird **nicht mehr verwendet**.

**Hinweis zu anderen Tokens:**

*   Es existieren weiterhin `accessToken` und `refreshToken` im `userStore`. Diese scheinen Teil eines anderen Auth-Flows zu sein (evtl. Stytch SDK oder ein Refresh-Mechanismus) und werden aktuell *nicht* für die Authentifizierung von direkten Backend-API-Calls verwendet, die den `session_token` im `Authorization: Bearer`-Header erwarten.
*   Die Konsistenz und das Zusammenspiel dieser verschiedenen Tokens sollten bei Bedarf genauer untersucht und dokumentiert werden.

### Authentifizierungs-Workflow (Stytch) - *Placeholder*

*   [Beschreibung des Login-, Registrierungs- und Session-Management-Flows mit dem Stytch Client SDK - *TODO: Details aus Implementierung ergänzen*]
*   [Umgang mit Tokens (Speicherung, Refresh) - *TODO: Details aus Implementierung ergänzen, Zusammenspiel accessToken/refreshToken/session_token (localStorage) klären*]
*   [Zusammenspiel mit `userStore` und Backend - *TODO: Details aus Implementierung ergänzen*]

## API-Kommunikation & Benachrichtigungen

### Backend API Aufrufe

API-Aufrufe an das Ordy-Backend werden standardmäßig mit der `axios`-Bibliothek durchgeführt. Authentifizierte Anfragen erfordern das Senden des `session_token` aus dem `localStorage`.

**Beispiel (aus `userStore.js`):**

```javascript
import axios from 'axios';

// ...

const deleteAccount = async () => {
  const token = localStorage.getItem('session_token');
  if (!token) { 
    throw new Error('Nicht eingeloggt (localStorage).');
  }
  try {
    await axios.delete('/auth/me', { 
      headers: { Authorization: `Bearer ${token}` } 
    }); 
    // ... weitere Aktionen nach Erfolg ...
  } catch (error) {
    // Fehlerbehandlung (siehe unten)
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.message || `Serverfehler (${status})`;
      throw new Error(message); 
    } else if (error.request) {
      throw new Error('Keine Antwort vom Server erhalten.');
    } else {
      throw new Error('Fehler bei der Vorbereitung des API-Aufrufs.');
    }
  }
};
```

**Wichtige Punkte:**

*   **Basis-URL:** Die Basis-URL für API-Aufrufe wird durch `import.meta.env.VITE_API_BASE_URL` definiert.
*   **Authentifizierung:** Für geschützte Endpunkte muss der `session_token` aus `localStorage.getItem('session_token')` gelesen und im `Authorization`-Header als `Bearer <token>` gesendet werden.
*   **Fehlerbehandlung:** API-Aufrufe sollten in `try...catch`-Blöcken gekapselt sein. `axios`-Fehlerobjekte enthalten oft eine `response`-Eigenschaft (`error.response`), die den HTTP-Status (`error.response.status`) und Server-Antwortdaten (`error.response.data`) enthält.

### Anzeigen von Benachrichtigungen

Um dem Benutzer Feedback anzuzeigen, wird das `useNotification`-Composable verwendet.

**Verwendung:**

1.  **Importieren:** `import useNotification from '../../modules/notificationInformation';`
2.  **Initialisieren:** `const { setNotification } = useNotification();`
3.  **Anzeigen:** `setNotification(message, type);`

**Parameter für `setNotification`:**

*   `message` (String): Der anzuzeigende Text.
*   `type` (String): Bestimmt das Aussehen. Gültige Werte sind:
    *   `'success'`: Grüner Hintergrund (für Erfolgsmeldungen).
    *   `'alert'`: Gelber/Oranger Hintergrund (für Warnungen oder Fehler).
    *   `'info'`: Blauer Hintergrund (für informative Meldungen).
    *   *(Andere Werte oder kein Wert)*: Standardhintergrund (neutral).

**Hinweis:** Der Typ `'error'` wird **nicht** unterstützt. Verwende stattdessen `'alert'` für Fehlermeldungen.

**Beispiel (aus `settingsCard.vue`):**

```vue
<script setup>
import useNotification from '../../modules/notificationInformation';
// ...

const { setNotification } = useNotification();

// ...

try {
  // ... API call ...
  setNotification('Aktion erfolgreich!', 'success');
} catch (error) {
  const errorMessage = error?.message || 'Ein Fehler ist aufgetreten.'; 
  setNotification(`Fehler: ${errorMessage}`, 'alert'); // Verwende 'alert' für Fehler
}
</script>
```

## Build & Deployment

*   **Lokaler Dev-Server:** `npm run dev`
*   **Produktions-Build:** `npm run build`
*   **Deployment:** [Informationen zum Deployment, z.B. Cloudflare Pages, Netlify, Vercel...] 

### StartScreen: Oranger rotierender Schatten hinter Menükarte

*   **Feature:** Hinter der Menükarte (menu-card-copy) auf der Startseite wird ein weicher, oranger Schatten als Glow angezeigt, der langsam um die Karte rotiert.
*   **Technik:**
    *   Ein zusätzliches absolut positioniertes Div (`menu-card-orange-shadow`) wird direkt hinter der Karte eingefügt.
    *   Der Schatten wird per `radial-gradient` und `blur` erzeugt und mit einer CSS-Animation (`rotate-orange-shadow`) kontinuierlich rotiert.
    *   Z-Index: Der Schatten liegt zwischen Lottie-Animation (z-0) und Karte (z-1).
    *   Die Animation ist rein visuell und hat keine Interaktion.
*   **Ort:** `src/body/startScreen.vue`
*   **Letzte Änderung:** 2024-07-27 