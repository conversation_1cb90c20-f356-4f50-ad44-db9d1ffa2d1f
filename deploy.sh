#!/bin/bash

# Ausführung im Fehlerfall sofort beenden
set -e

# Log-Ausgaben zur Nachverfolgung
echo "Attempting to change to application directory /home/<USER>/wwwroot..."
# Sicherstel<PERSON>, dass das Verzeichnis existiert, bevor gewechselt wird
if [ -d "/home/<USER>/wwwroot" ]; then
  cd /home/<USER>/wwwroot
  echo "Successfully changed to directory: $(pwd)"
else
  echo "ERROR: Directory /home/<USER>/wwwroot does not exist. Current directory: $(pwd)"
  # Zeige den Inhalt des aktuellen Verzeichnisses an, um zu debuggen
  ls -la
  exit 1 # Beende das Skript, da npm-Befehle fehlschlagen würden
fi

echo "Starting deployment script steps in $(pwd)..."

echo "Cleaning npm cache..."
npm cache clean --force

echo "Pruning production dependencies..."
npm prune --production

echo "Installing dependencies (omitting dev)..."
npm install --omit=dev

echo "Starting application via npm run start:azure..."
# Dieser Befehl startet die App. Für das Deployment ist das oft nicht nötig,
# da der App Service die App separat startet basierend auf dem Startbefehl im Portal.
# Wenn npm run start:azure hier blockiert, könnte das Deployment hängen.
# Wir kommentieren es vorerst aus, da der App Service Startbefehl dies übernehmen sollte.
# npm run start:azure 
echo "npm install finished. Application will be started by App Service based on portal configuration."

echo "Deployment script finished."