// Pinterest OAuth Helper Script
require('dotenv').config({ path: './config.env' });
const axios = require('axios');

const APP_ID = process.env.PINTEREST_APP_ID;
const SECRET_KEY = process.env.PINTEREST_SECRET_KEY;
// Determine redirect URI based on environment
const NODE_ENV = process.env.NODE_ENV || 'development';
let REDIRECT_URI;

if (NODE_ENV === 'development') {
  REDIRECT_URI = 'http://localhost:8080/auth/pinterest/callback';
} else if (NODE_ENV === 'preview') {
  REDIRECT_URI = 'https://ordy-tst-d8cfc0bzchbqd9ha.switzerlandnorth-01.azurewebsites.net/auth/pinterest/callback';
} else {
  // Production - you may need to set this
  REDIRECT_URI = 'https://www.ordyapp.com/auth/pinterest/callback';
}

if (!APP_ID || !SECRET_KEY) {
  console.error('Error: PINTEREST_APP_ID and PINTEREST_SECRET_KEY must be set in config.env');
  process.exit(1);
}

console.log('Pinterest OAuth Helper');
console.log('=====================');
console.log('App ID:', APP_ID);
console.log('Redirect URI:', REDIRECT_URI);
console.log('');

// Step 1: Generate authorization URL (following Pinterest documentation format)
const authUrl = `https://www.pinterest.com/oauth/?response_type=code&client_id=${APP_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&scope=boards:read,pins:write&state=pinterest_oauth_${Date.now()}`;

console.log('Step 1: Authorization URL');
console.log('========================');
console.log('Please visit this URL in your browser to authorize the application:');
console.log('');
console.log(authUrl);
console.log('');
console.log('After authorization, you will be redirected to your redirect URI with a "code" parameter.');
console.log('Copy the code from the URL and use it in Step 2.');
console.log('');

// Function to exchange code for access token
async function exchangeCodeForToken(authCode) {
  try {
    console.log('Step 2: Exchanging authorization code for access token...');

    const response = await axios.post('https://api.pinterest.com/v5/oauth/token', {
      grant_type: 'authorization_code',
      client_id: APP_ID,
      client_secret: SECRET_KEY,
      code: authCode,
      redirect_uri: REDIRECT_URI
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Success! Access token received:');
    console.log('Access Token:', response.data.access_token);
    console.log('Token Type:', response.data.token_type);
    console.log('Scope:', response.data.scope);
    console.log('');
    console.log('Add this to your config.env file:');
    console.log(`PINTEREST_ACCESS_TOKEN=${response.data.access_token}`);

    return response.data;
  } catch (error) {
    console.error('Error exchanging code for token:');
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    } else {
      console.error('Error message:', error.message);
    }
    return null;
  }
}

// Function to inspect token scopes
async function inspectToken(accessToken) {
  try {
    console.log('Inspecting token scopes...');

    const response = await axios.get(`https://api.pinterest.com/v1/oauth/inspect?access_token=${accessToken}&client_id=${APP_ID}`);

    console.log('Token inspection result:');
    console.log('Scopes:', response.data.scopes);
    console.log('App ID:', response.data.app_id);

    return response.data;
  } catch (error) {
    console.error('Error inspecting token:');
    if (error.response) {
      console.error('Response data:', error.response.data);
    } else {
      console.error('Error message:', error.message);
    }
    return null;
  }
}

// Check if we have command line arguments
const args = process.argv.slice(2);

if (args.length > 0) {
  const command = args[0];

  if (command === 'exchange' && args[1]) {
    // Exchange authorization code for access token
    exchangeCodeForToken(args[1]);
  } else if (command === 'inspect' && args[1]) {
    // Inspect existing token
    inspectToken(args[1]);
  } else {
    console.log('Usage:');
    console.log('  node pinterest-oauth-helper.js exchange <authorization_code>');
    console.log('  node pinterest-oauth-helper.js inspect <access_token>');
  }
} else {
  console.log('Next steps:');
  console.log('1. Visit the authorization URL above');
  console.log('2. After authorization, run: node pinterest-oauth-helper.js exchange <code>');
  console.log('3. Use the returned access token in your config.env file');
}
