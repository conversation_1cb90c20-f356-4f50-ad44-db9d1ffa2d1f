<template>
  <!-- REMOVED Loading Overlay -->
  <!-- <div v-if="cookeasyStore.loadingState"
       class="fixed inset-0 bg-gray-100 bg-opacity-90 flex items-center justify-center z-50 p-8">
    ...
  </div> -->

  <!-- Column Builder-->
  <!-- REMOVED blur effect -->
  <div class="flex flex-col md:flex-row min-h-screen px-6">

    <!-- Middle Container - Angepasste Breite für Desktop -->
    <div class="w-full md:w-3/4 lg:w-3/4 xl:w-3/4 md:pr-12">
      <!-- Head-->
      <div class="w-full flex flex-row mt-12">
        <h1 class="w-full first-letter:md:w-11/12 h-auto pb-3 text-xl leading-[3rem]">Reste verwerten</h1>
        <div class="w-1/12">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="w-full md:w-3/5">Einfach Reste in deinem Haushalt in das nächste Menü einbinden. Gib deine aktuell verfügbaren Nahrungsmittel mit denen du etwas kochen möchtest hier ein:</p>
      <!-- Head-->

      <!-- Your Cookeasy -->
      <div class="w-full">
        <input
          v-model='cookeasyStore.lastRequests'
          type="text"
          class="w-full mt-8 bg-white text-black p-8 rounded-2xl border-none min-h-20 h-auto border-0 focus:border-0 focus:ring-0 focus:outline-none"
          placeholder="erbsen, yoghurt, eier, lauch, gemüsemix"
        />
        <!-- Corrected Button Alignment: Added h-12 to both buttons and used items-center -->
        <div class="flex flex-row w-full gap-4 items-center">
          <click-shadow-button
            @click.prevent="cookeasyStore.startOrCreateConversation()"
            class="flex-grow h-12"
            :element="{'buttondescription': 'Zutaten verwerten', 'active': cookeasyStore.loadingState, 'iconneeded': false}"
            :index="1"
          />
          <button
            @click.prevent="cookeasyStore.cleanConversation()"
            class="w-auto mt-7 px-4 h-12 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-xl text-white shadow-custom default-button-shadow flex items-center justify-center">
            <img src="../assets/icons/reload.png" class="invert h-7 w-7"/>
          </button>
        </div>

        <!-- ADDED Skeleton Loader (conditionally replaces conversation) -->
        <div v-if="cookeasyStore.loadingState" class="w-full pt-12 space-y-4">
            <div class="h-auto w-full rounded-2xl bg-gradient-to-r from-ordypurple-200 via-ordypink-100 to-ordypurple-200 background-animate p-4 space-y-3 opacity-75">
                 <div class="flex justify-end">
                   <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 background-animate"></div>
                 </div>
                 <div class="h-8 w-full rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 background-animate"></div>
                 <div class="h-8 w-3/4 rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 background-animate"></div>
                 <div class="h-8 w-full rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 background-animate"></div>
                 <div class="h-8 w-1/2 rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 background-animate"></div>
            </div>
        </div>

        <!-- Conversation Area (only shown when not loading) -->
        <div v-if="!cookeasyStore.loadingState">
          <p class="w-full bg-gradient-to-br from-gray-50 to-gray-25 p-5 rounded-xl h-auto min-h-6 mt-16">
            Guten Tag, mit welchen Nahrungsmittel darf ich heute ein Rezept erstellen?
          </p>
          <div class="mt-4 mb-4"  v-for="(item,index) in cookeasyStore.cookeasyConversation" :key="index">
            <div
              v-if="item.role != 'system'"
              class="w-full h-auto min-h-6"
              :style="[ item.role == 'assistant' ? 'text-right' : 'text-left']"
            >
            <!-- Reciept Challanges -->
            <div v-if="item.datatypearray" v-for="(item2,index2) in item.content" :key="index2">
              <div
                class="w-full mt-3 bg-gradient-to-br from-gray-50 to-gray-25 p-5 rounded-xl h-auto min-h-6"
                :style="[ item.role == 'assistant' ? 'text-right' : 'text-left']"
              >
                <div class="w-full flex flex-row">
                  <span class="w-10/12 font-YesevaOne">{{ item2.name }}</span>
                  <span class="w-2/12 text-right text-xs pt-1">{{ item2.zubereitungszeit }}</span>
                </div>
                <p class="mt-2">{{ item2.zubereitungsanleitung }}</p>
                <div class="w-full h-auto mt-4">
                  <click-button @click.prevent="cookeasyStore.createReciept(index,index2)" :element="{'buttondescription': 'Daraus ein Menü erstellen', 'active': item2.loading, 'buttonicon': '_blank.svg', 'iconneeded': true }" :index="1"  />
                  <button class="md:w-full md:mt-4 w-full h-12 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 text-white p-2 mt-2 font-YesevaOne text-xs rounded-2xl">Dont like</button>
                </div>
              </div>
            </div>
            <!-- Normal Answers -->
            <div v-if="item.datatypearray == false">
              <div
                class="w-full bg-gradient-to-br from-gray-50 to-gray-25 p-5 rounded-xl h-auto min-h-6"
                :style="[ item.role == 'assistant' ? 'text-right' : 'text-left']"
              >

                <p>{{ item.content }}</p>
              </div>
            </div>
          </div>
          </div>
        </div> <!-- End Conversation Area -->

      </div>
    </div>
    <!-- Your Cookeasy -->

    <!-- Right Container -->
    <div class="hidden md:block md:w-1/4 md:p-10 w-full mt-12 md:mt-0">
      <!-- Leerer Container für konsistentes Layout -->
    </div>
    <!-- Right Container -->

  </div>

</template>
<script setup>

///////////////////// IMPORT /////////////////////////////////
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../components/menuCard.vue'
import kitchentableOverview from '../components/kitchentableOverview.vue'
import useNotification from '../../modules/notificationInformation';
import { useUserStore } from '../store/userStore'
import { useCookeasyStore } from '../store/cookeasyStore'
import clickButton from '../components/clickBorderButton.vue'
import clickShadowButton from '../components/clickShadowButton.vue'

import MenuCard from '../components/menuCard.vue';

  ///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const cookeasyStore = useCookeasyStore();

  ///////////////////// LOCAL FUNCTIONS /////////////////////////////


</script>
<style scoped>
/* Add animation for the background gradient */
.background-animate {
  background-size: 400% 400%;
  animation: backgroundAnimate 3s ease infinite;
}

@keyframes backgroundAnimate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>