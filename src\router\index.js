import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/HomeView.vue';
import Wochenplan from '../views/WochenplanView.vue';
import MeineRezepte from '../views/MeineRezepteView.vue';
import KuechentischView from '../views/KuechentischView.vue';
import CookEasy from '../views/Cookeasy.vue';
import Wochenplan2LPlanung from '../views/SecondLevelView/SearchMenuView.vue';
import Wochenplan2LUpload from '../views/SecondLevelView/UploadMenuView.vue';
import UserSettings from '../views/SecondLevelView/UserSettings.vue';
import MeineRezepteEditCreate from '../views/SecondLevelView/MenuDetailsEdit.vue';
import EinkaufszettelView from '../views/EinkaufszettelView.vue';
import MenuDetails2LMenu from '../views/SecondLevelView/MenuDetails.vue';
import WochenplanMenuDetails2LWochenplan from '../views/SecondLevelView/WochenplanMenuDetails.vue';
import LoginView from '../views/LoginView.vue';
import Logout from '../views/LogoutView.vue';
import Logs from '../views/LogsView.vue';
import AlleRezepteView from '../views/AlleRezepteView.vue';
import ZettelView from '../views/ZettelView.vue';
import MarketingVideo from '../views/MarketingVideo.vue';
import StartseiteRedesign from '../views/StartseiteRedesign.vue';
import MarketingDashboardView from '../views/MarketingDashboardView.vue';
import MarketingUploadView from '../views/MarketingUploadView.vue';
import AdminLayout from '../layouts/AdminLayout.vue';
import AdminDashboard from '../views/admin/AdminDashboard.vue';
// New Marketing Pages
import NewHomepage from '../views/NewHomepage.vue';
import RecipeExplorer from '../views/RecipeExplorer.vue';
import AboutPage from '../views/AboutPage.vue';
import BlogPage from '../views/BlogPage.vue';
//
const routes = [
  // New Marketing Homepage (Main Route)
  {
    path: '/',
    name: 'homepage',
    component: NewHomepage,
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  // Recipe Explorer (Public)
  {
    path: '/rezepte',
    name: 'recipe-explorer',
    component: RecipeExplorer,
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  // About Page (Public)
  {
    path: '/about',
    name: 'about',
    component: AboutPage,
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  // Blog Page (Public)
  {
    path: '/blog',
    name: 'blog',
    component: BlogPage,
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  // Old Homepage (moved to /old-home for backward compatibility)
  {
    path: '/old-home',
    name: 'old-home',
    meta: {
      layout: "startScreen"
    },
    component: Home,
    props: true,
  },
  {
    path: '/wochenplan',
    name: 'wochenplan',
    component: Wochenplan,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    }
  },
  {
    path: '/logs',
    name: 'logs',
    component: Logs,
    props: true,
    meta: {
      layout: null,
      requiresAuth: false,
    }
  },
  {
    path: '/wochenplan/planung',
    name: 'planung',
    component: Wochenplan2LPlanung,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/wochenplan/upload',
    name: 'uploadMenu',
    component: Wochenplan2LUpload,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/wochenplan/menu/:id',
    name: 'wochenplanmenudetails',
    component: WochenplanMenuDetails2LWochenplan,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    }
  },
  //alleRezepteView
  {
    path: '/allerezepte',
    name: 'alleRezepte',
    component: AlleRezepteView,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/kochbuch',
    name: 'kochbuch',
    component: MeineRezepte,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/kochbuch/menu/:id',
    name: 'kochbuch-menudetails',
    component: MenuDetails2LMenu,
    props: true,
    meta: {
      layout: null,
      // requiresAuth not used, its guided over the api call
    }
  },
  {
    path: '/kochbuch/menu/edit/:id',
    name: 'rezept_edit',
    component: MeineRezepteEditCreate,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/einkaufszettel',
    name: 'einkaufszettel_base',
    component: EinkaufszettelView,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/einkaufszettel/:id',
    name: 'einkaufszettel',
    component: EinkaufszettelView,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/zettel',
    name: 'zettel',
    component: ZettelView,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/cookeasy',
    name: 'cookeasy',
    component: CookEasy,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/kuechentisch',
    name: 'kuechentisch',
    component: KuechentischView,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    props: true,
    meta: {
      layout: null
    },
  },
  {
    path: '/usersettings',
    name: 'usersettings',
    component: UserSettings,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/logout',
    name: 'logout',
    component: Logout,
    props: true,
    meta: {
      layout: null,
      requiresAuth: true,
    },
  },
  {
    path: '/marketing/video1',
    name: 'marketingVideo1',
    component: MarketingVideo,
    props: true,
    meta: {
      /* layout: 'marketing', */
      layout: 'blank',
      requiresAuth: false
    },
  },
  {
    path: '/marketing',
    name: 'marketingDashboard',
    component: MarketingDashboardView,
    props: true,
    meta: {
      layout: 'default',
      requiresAuth: true
    },
  },
  {
    path: '/marketing/upload',
    name: 'marketingUpload',
    component: MarketingUploadView,
    props: true,
    meta: {
      layout: 'default',
      requiresAuth: true
    },
  },
  // Admin Routes
  {
    path: '/admin',
    component: AdminLayout,
    meta: {
      layout: 'admin',
      requiresAuth: true,
      requiresAdmin: true
    },
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: AdminDashboard,
        meta: {
          requiresPermission: 'backend_access'
        }
      },
    ]
  },
  // Legal Routes (Public, no authentication required)
  {
    path: '/legal/agb',
    name: 'legal-agb',
    component: () => import('../views/legal/LegalAGB.vue'),
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  {
    path: '/legal/impressum',
    name: 'legal-impressum',
    component: () => import('../views/legal/LegalImpressum.vue'),
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  {
    path: '/legal/terms-of-service',
    name: 'legal-terms',
    component: () => import('../views/legal/LegalTermsOfService.vue'),
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
  {
    path: '/legal/privacy-policy',
    name: 'legal-privacy',
    component: () => import('../views/legal/LegalPrivacyPolicy.vue'),
    meta: {
      layout: 'blank',
      requiresAuth: false
    }
  },
];

// routes finished
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // Scrollt immer nach oben, wenn die Route wechselt aussert eine route war schon besucht
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 }
    }
  }
});

// Importing remaining packages
import { useUserStore } from '../store/userStore'
import useNotification from '../../modules/notificationInformation';
import { useHelperStore } from '../../utils/helper';
import { saveCurrentRoute, getLastRoute, isRouteRestorable } from '../services/sessionService';
// import { useAdminPermissions } from '../composables/useAdminPermissions'; // TEMPORÄR DEAKTIVIERT

// store positions of scrolling
const scrollPositions = {};

router.afterEach((to) => {
  // Speichere die aktuelle Route für die Wiederherstellung
  saveCurrentRoute(to);

  // Scrolle zur gespeicherten Position
  if (scrollPositions[to.name] !== undefined) {
    setTimeout(() => {
      window.scrollTo(0, scrollPositions[to.name]); // Hier scrollen wir zur gespeicherten Position
    }, 0);
  }
});

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  const { setNotification } = useNotification();
  const helper = useHelperStore();

  // Add console log at the beginning to track execution
  const internalUserIdInitial = localStorage.getItem('id');
  const sessionTokenInitial = localStorage.getItem('session_token');
  helper.devConsole(`[Router Guard START] To: ${to.path}, From: ${from?.path}, LS id: '${internalUserIdInitial}', LS session: ${!!sessionTokenInitial}, userStore.id: ${userStore.user.id}`);

  // Store scroll position of the departing route
  if (from?.name) {
      scrollPositions[from.name] = window.scrollY;
  }

  // --- PWA Last-Page-Memory System ---
  // Check if user is navigating to /kochbuch OR / (home) and has a valid session
  if ((to.name === 'kochbuch' || to.name === 'home') && sessionTokenInitial && internalUserIdInitial) {
    const lastRoute = getLastRoute();

    // Use the new helper function to check if route is restorable
    if (lastRoute && isRouteRestorable(lastRoute) && from?.name !== lastRoute.name) {
      helper.devConsole(`[PWA Memory] Redirecting from ${to.name} to last route: ${lastRoute.path}`);

      // Navigate to the last route with all its parameters
      next({
        name: lastRoute.name,
        params: lastRoute.params || {},
        query: lastRoute.query || {}
      });
      return;
    } else if (lastRoute) {
      helper.devConsole(`[PWA Memory] Last route found but not restorable: ${lastRoute.name}`);
    }
  }
  // --- End PWA Last-Page-Memory System ---

  // --- Updated Login Callback Handling ---
  // Check only for session_token (Stytch) and our internal id
  if(to.path === '/' && to.query.session_token && to.query.id) {
    helper.devConsole("Login Callback detected. Processing query data...");
    try {
        localStorage.setItem('session_token', to.query.session_token);
        localStorage.setItem('id', to.query.id);
        localStorage.setItem('last_token_refresh', Date.now().toString());

        // Immer zum Kochbuch weiterleiten nach dem Login
        helper.devConsole("Tokens stored, redirecting to /kochbuch...");
        next({ path: '/kochbuch', replace: true });
        return;
    } catch (error) {
        console.error("Error storing tokens from login callback:", error);
        setNotification('Login fehlgeschlagen, bitte erneut versuchen.', 'alert');
        localStorage.removeItem('session_token');
        localStorage.removeItem('id');
        next({ path: '/', query: {}, replace: true });
        return;
    }
  }
  // --- End Updated Login Callback Handling ---

  // --- RE-ADDED Old User Loading Logic --- // Renamed to: User Loading Logic
  const internalUserId = localStorage.getItem('id');
  // Removed: const backendToken = localStorage.getItem('backend_session_token');
  const sessionToken = localStorage.getItem('session_token'); // Use session_token instead

  // Load user only if ID is stored, session_token exists, AND user is not already loaded
  if(internalUserId && sessionToken && userStore.user.id == null){ // Check sessionToken
    // Add console log INSIDE the condition
    helper.devConsole(`[Router Guard] Condition TRUE: Trying to load user ${internalUserId}`);
    helper.devConsole("User data missing but ID and session token found (e.g., after reload or login redirect), loading user data...");
    try {
      await userStore.getUser(internalUserId); // Load user using stored internal ID
      helper.devConsole("User data successfully loaded.");
    } catch (error) {
       console.error("Error loading user data:", error);
       helper.devConsole("Failed to load user, clearing data and redirecting to login.");
       // Removed: localStorage.removeItem('backend_session_token');
       localStorage.removeItem('session_token');
       localStorage.removeItem('id');
       // Removed: localStorage.removeItem('user_id'); // Already removed in previous step, but good to double-check
       await userStore.clearUser();
       next({ name: 'login', query: { session_expired: 'true' } });
       return;
    }
  }
  // -------------------------------------

  // --- Authentication Check --- // Renamed from REVERTED
  // check if route requires auth and if session_token exists
  if (to.matched.some(record => record.meta.requiresAuth) && !sessionToken){ // Check sessionToken
    helper.devConsole("Auth required but no sessionToken found, redirecting to login."); // Updated log message
    next({ name: 'login' });
    return; // IMPORTANT: Stop further execution
  }
  // -----------------------------------

  // --- Admin Permission Check (Vereinfacht) ---
  if (to.matched.some(record => record.meta.requiresAdmin)) {
    const currentUser = userStore.user;
    const isUserAdmin = currentUser?.adminPermissions?.isAdmin === true;

    if (!isUserAdmin) {
      helper.devConsole("Admin access required but user is not admin, redirecting to kochbuch.");
      setNotification('Zugriff verweigert. Admin-Berechtigungen erforderlich.', 'alert');
      next({ name: 'kochbuch' });
      return;
    }
  }
  // -----------------------------------

  // If none of the above conditions caused a redirect, proceed with navigation
  helper.devConsole(`[Router Guard] Proceeding with navigation to "${to.path}".`);
  next();

})


export default router;
