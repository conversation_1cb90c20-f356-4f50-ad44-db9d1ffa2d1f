# Zutaten- und Rezepte-System Anforderungen

## Überblick
Das Zutaten- und Rezepte-System ist das Herzstück der Ordy-App für Kochenthusiasten. Es ermöglicht die nahtlose Verwaltung von Rezepten mit intelligenter Zutaten-Verknüpfung und automatischer Einkaufszettel-Integration.

## Kernfunktionalitäten

### 1. Rezept-Anzeige und -Bearbeitung
- **Seite:** `/kochbuch/menu/:menuid` (Anzeige)
- **Seite:** `/kochbuch/menu/edit/:menuid` (Bearbeitung)
- **Anforderung:** Zutaten sind direkt im Zubereitungstext verknüpft
- **Anforderung:** Verknüpfungen bleiben stabil bei Zutaten-Änderungen

### 2. Stabile Zutaten-ID-System
- **Implementiert:** Jedes Rezept hat eigene Zutaten-ID-History (`maxUsedStableId`)
- **Regel:** IDs werden NIE wiederverwendet (auch nach Löschung)
- **Format:** 1-basierte IDs pro Rezept (erste Zutat = ID 1)
- **Verknüpfung:** `${ID:x}` Platzhalter im Zubereitungstext
- **Skalierung:** Mengen skalieren automatisch mit Personenanzahl

### 3. AI-Rezeptgenerierung (/wochenplan/upload)
**4 Upload-Möglichkeiten:**
1. **URL-Upload:** Rezept aus Webseite extrahieren
2. **Text-Upload:** Rezept aus Freitext erstellen
3. **Bild-Upload:** Rezept aus Foto/Screenshot generieren
4. **Generator:** Konfigurierbare Rezepterstellung mit Präferenzen

**Anforderungen:**
- Alle generierten Rezepte müssen vollständige Daten haben (Titel, Beschreibung, Nährwerte)
- StableIDs müssen korrekt zugewiesen werden
- Platzhalter im Text müssen funktionieren

### 4. Einkaufszettel-Integration (/zettel)
- **Funktion:** Rezepte können zum Einkaufszettel hinzugefügt werden
- **Kategorisierung:** Automatische Zuordnung zu Einkaufskategorien
- **Reihenfolge:** Feste Kategorie-Reihenfolge für optimalen Einkauf
- **Skalierung:** Mengen entsprechend Personenanzahl

## Technische Architektur

### Backend-Modelle
```javascript
// MenuChild (Rezeptversion)
{
  ingredients: [{
    amount: Number,
    unit: ObjectId(Unit),
    name: ObjectId(Grocery),
    stableId: Number  // KRITISCH: Permanente ID
  }],
  maxUsedStableId: Number,  // Verhindert ID-Wiederverwendung
  preperation: Array,       // Zubereitungsschritte mit ${ID:x}
  // ...
}

// Menu (Rezept-Container)
{
  name: String,
  description: String,
  menuchilds: [{
    numberOfPersons: Number,
    menuChildId: ObjectId(MenuChild)
  }]
}
```

### Frontend-Komponenten
- **MenuDetails.vue:** Rezept-Anzeige mit Zutaten-Buttons
- **MenuDetailsEdit.vue:** Rezept-Bearbeitung
- **UploadMenuView.vue:** 4 Upload-Methoden
- **RecipeGenerator.vue:** Konfigurierbarer Generator

### Datenfluss
1. **Rezept erstellen/bearbeiten** → StableID-Zuweisung → Backend-Speicherung
2. **AI-Generierung** → StableID-Korrektur → Platzhalter-Validierung
3. **Einkaufszettel** → Zutaten-Extraktion → Kategorisierung → WebSocket-Sync

## Kategorien-System (Einkaufszettel)
```javascript
const categoryOrder = [
  'Gemüse & Früchte',           // Grün
  'Brotwaren & Backwaren',      // Gelb
  'Milchprodukte & Molkereiprodukte', // Blau
  'Fleisch, Wurst & Fisch',     // Rot
  'Tiefkühlprodukte',           // Cyan
  'Grundnahrungsmittel',        // Amber
  'Frühstück & Cerealien',      // Orange
  'Süsswaren & Snacks',         // Pink
  'Getränke',                   // Indigo
  'Non-Food & Haushaltsartikel', // Grau
  'Sonstiges'
];
```

## Kritische Implementierungsdetails

### StableID-Management
- **Zuweisung:** Nur bei neuen Zutaten, nie bei bestehenden
- **Erhaltung:** IDs bleiben bei Updates erhalten
- **Löschung:** Gelöschte IDs werden nie wiederverwendet
- **Skalierung:** Nur Mengen ändern sich, nicht IDs

### Platzhalter-System
- **Format:** `${ID:1}`, `${ID:2}`, etc.
- **Veraltete Formate:** `${MENGE:x}`, `${EINHEIT:x}` werden entfernt
- **Validierung:** Platzhalter ohne passende Zutat werden ausgeblendet
- **Korrektur:** AI-generierte hohe IDs werden auf verfügbare IDs gemappt

### Fehlerbehandlung
- **Offline-Sync:** Einkaufszettel-Änderungen in Warteschlange
- **Validierung:** Zutaten-Einheiten werden automatisch ergänzt
- **Fallbacks:** Fehlende Kategorien → "Sonstiges"

## Qualitätssicherung
- **Tests:** Playwright-Tests für alle Kernfunktionen
- **Validierung:** StableID-Konsistenz prüfen
- **Performance:** Effiziente Platzhalter-Verarbeitung
- **UX:** Nahtlose Übergänge zwischen Rezept und Einkaufszettel
