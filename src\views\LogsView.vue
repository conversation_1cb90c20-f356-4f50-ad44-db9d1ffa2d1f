<template>
  <!-- Column Builder-->
  <div class="flex flex-row min-h-screen">
  
    <!-- Middle Container -->
    <div class="w-full md:w-3/4 md:pr-12">
      <!-- Head-->
      <div class="w-full flex flex-row items-center mt-24">
        <h1 class="font-OpenSans text-xl w-full md:w-8/12 font-bold h-auto pb-3">History</h1>
        <!-- Login-->
        <div class="flex w-4/12 justify-end">
          <button 
            @click="router.go(-1)"
            class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-1 px-3 rounded text-sm"
          >
            Zurück
          </button>
        </div>
        <!-- Login -->
      </div>
      
      <!-- Head-->

      <!-- UserProfile -->
      <div class="w-full mt-6">
        Hier werden alle neue Features und Funktionen aller Releases aufgelistet
      </div>
      <!-- UserProfile -->

      <!-- next update -->
      <div class="mt-10">
        <h3>8.04.2025 | Version 2.4.4</h3>
        <p>Sprachassistent</p>
        <h5 class="mt-2">Funktionen & Verbesserungen</h5>
        <li>Sprachassistent in Rezepten und Menüs verfügbar mit neuen Funktionen zu Einkauflisten</li>
        <li>Verbesserte Lager & Generation Funktionen</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>15.04.2025 | Version 2.4.3</h3>
        <p>Stabilitäts- und Offline-Verbesserungen für den Einkaufszettel.</p>
        <h5 class="mt-2">Funktionen & Verbesserungen</h5>
        <li>**Offline-Modus Einkaufszettel:** Einkaufszettel-Aktionen (Items abhaken, Rezepte hinzufügen) funktionieren nun auch ohne Internetverbindung. Änderungen werden lokal gespeichert und synchronisiert, sobald die Verbindung wiederhergestellt ist.</li>
        <li>**Stabilere Verbindung:** Die WebSocket-Verbindung ist nun robuster gegenüber kurzen Unterbrechungen (z.B. auf Mobilgeräten). Die "Offline-Modus"-Meldung erscheint erst bei längeren Ausfällen.</li>
        <li>**Automatisches Laden:** Der aktive Einkaufszettel wird jetzt automatisch geladen, sobald der zugehörige Küchentisch bekannt ist (kein vorheriger Besuch der Zettel-Seite mehr nötig).</li>
        <li>**Benachrichtigungen:** Diverse Anpassungen an Benachrichtigungen für eine klarere Nutzerführung (z.B. bei fehlendem Zettel).</li>
        <h5 class="mt-2">Technische Fixes</h5>
        <li>Behebung eines Anzeige-Fehlers in Rezeptkarten (`menuCard.vue`).</li>
        <li>Korrektur der Datenverarbeitung beim Laden eigener Rezepte (`menuStore.js`).</li>
        <li>Anpassung von Konsolenausgaben für bessere Fehlersuche.</li>
        <li>Interne Code-Korrekturen bei Benachrichtigungsaufrufen.</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>20.12.2024 | Version 2.4.2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
        <li>Vorschau Sprachassistent in Rezepten und Menüs verfügbar</li>
        <li>Bug beheben Login / Registrieren</li>
        <li>Bug beheben Payment</li>
        <li>Website redesigned</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>02.12.2024 | Version 2.4.1</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
        <li>Ändern von Menüanzeigebildern</li>
        <li>Übersichtlicher Switch beim Kochen aus Vorrat (neu "Lager")</li>
        <li>Teilbare Links für Rezepte können einfach per Klick versendet werden</li>
        <h5 class="mt-2">Patch</h5>
        <li>Paywall funktionen & Benutzeränderungen</li>
        <li>Verbesserungen der Usability in der App auf dem Smartphone</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>19.11.2024 | Version 2.3.1</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
        <li>Einbauen einer Nutzungsübersicht über die AI Generierungen von Menüs, Cookeasys und Menuuploads.</li>
        <li>Einbauen eine Paywall gegen unendliches generieren von Menüs und Cookeasys.</li>
        <li>Neu können von beliebigen Rezepten die Nährwerte generiert werden.</li>
        <h5 class="mt-2">Patch</h5>
        <li>Profileinstellungen und Küchentisch sind neu über den Account gegliedert. Küchentisch ist nicht mehr obligatorisch, da es in Zukunft ein Paid-Feature werden soll.</li>
        <li>Verbessern der Menüsuche. Menüs können jetzt über Button aus sehr einfach und übersichtlich gefiltert werden.</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>10.8.2024 | Version 2.3.0</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Aus einzelnen Lebensmittel lassen sich jetzt Menüs automatisch per klick selber generieren. Das ist praktisch für die Resteverwertung im Haushalt.</li>
          <li>Löschen der Einkaufslistenfunktion da kaum genutzt.</li>
          <li>Löschen der Wochenplanung da kaum genutzt.</li>
        <h5 class="mt-2">Patch</h5>
        <li>Einbauen und verbessern der Menüsuche.</li>
        <li>Umbau Navigation. Die Navigation befindet sich neu am unteren Ende des Bildschrimes.</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>Upcoming | Version 2.2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Artikel werden in der Einkaufsliste automatisch zusammengezählt.</li>
        <h5 class="mt-2">Patch</h5>
          <li>Lebensmittelartikel werden neu als Items in einer eigenen Datenbank geführt. Dies ermöglicht unter anderem das Zusammenführen von Artikeln in der Einkaufsliste.</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>2.5.2024 | Update Version 2.2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Umstellen der Wochenplan Ansicht von der Tagesplanung auf die Wochenplanung.</li>
          <li>AGB & Cookies über die Einstellungen steuerbar.</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>10.4.2024 | Aufschalten Version 2.2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Neues Rezept kann über einen Link aus einer persönlichen Datenbank erstellt/importiert werden.</li>
          <li>Neues Rezept kann über einen beliebigen Text aus einer persönlichen Ablage oder Notizbuch hinzugefügt werden.</li>
        <h5 class="mt-2">Patch</h5>
          <li>Behebung von Fehler beim einloggen nach dem Registrieren</li>
          <li>Weiterleitung blockiert nicht mehr, wenn ein neues Rezept wird</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>25.1.2024 | Aufschalten Version 2.1</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Login neu mit Microsoft möglich</li>
          <li>Cookeasy Feauture weiterentwickelt. Food AI bekommt mehrere Zutaten mitgegeben und erstellt daraus ein Menü.</li>

      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>31.12.2023 | Aufschalten Version 2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Verbesserungen beim Erstellen von Menüs</li>
          <li>Beim bearbeiten von Menüs werden Zutaten automatisch erhöht oder reduziert wenn die Personenanzahl verändert wird</li>
          <li>Küchentisch Feautre neu. Sprich die Möglichkeit mit anderen Familienmitglieder oder Freund:innen zusammen Menüs zu planen</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>28.11.2023 | Aufschalten Version 1.4</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Wochenplan zu Einkaufsliste hinzufügen</li>
          <li>Bilder zu Menüs hinzufügen und ändern können</li>
          <li>Food AI erstellt zum Menü ein passendes Bild</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>3.11.2023 | Aufschalten Version 1.3</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Wochenplan Feautre veröffentlicht</li>
          <li>Einkaufszettel Feauture veröffentlicht</li>
      </div>
      <!-- next update -->
      

      <!-- next update -->
      <div class="mt-10">
        <h3>15.10.2023 | Aufschalten Version 1.2</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Food AI Version 1 | Erstellen neuer Rezepte anhand einer AI</li>
          <li>Rezepte selbständig ändern und bearbeiten oder löschen können</li>
      </div>
      <!-- next update -->

      <!-- next update -->
      <div class="mt-10">
        <h3>1.9.2023 | Aufschalten Version 1</h3>
        <p></p>
        <h5 class="mt-2">Funktionen</h5>
          <li>Google Login</li>
          <li>Rezepte selbständig erstellen</li>
      </div>
      <!-- next update -->

    </div>


    <!-- Right Container -->
    <div class="w-1/4 p-10 mt-12">
      
    </div>
    <!-- Right Container -->

  </div>
  
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/userStore'

  const router = useRouter();

  const { setNotification } = useNotification();
  const userStore = useUserStore()

  
</script>
<style scoped>

</style>