const express = require('express');
const adminController = require('../controllers/adminController');
const adminMiddleware = require('../middleware/adminMiddleware');
const oauthController = require('../controllers/oauthController');
const marketingContentController = require('../controllers/marketingContentController');

const router = express.Router();

// Admin authentication verification
router.get('/verify', adminController.verifyAdmin);

// Admin dashboard (requires backend access)
router.get('/dashboard',
    adminMiddleware.requireBackendAccess,
    adminController.getDashboard
);

// Pinterest management routes (requires Pinterest access)
router.post('/pinterest/auth-url',
    adminMiddleware.requirePinterestAccess,
    oauthController.pinterestAuthUrl
);

router.get('/pinterest/token-status',
    adminMiddleware.requirePinterestAccess,
    oauthController.pinterestTokenStatus
);

router.post('/pinterest/token',
    adminMiddleware.requirePinterestAccess,
    oauthController.pinterestToken
);

// Facebook management routes (requires marketing access)
router.post('/facebook/auth-url',
    adminMiddleware.requireMarketingAccess,
    require('../controllers/facebookOAuthController').startOAuth
);

router.get('/facebook/token-status',
    adminMiddleware.requireMarketingAccess,
    require('../controllers/facebookOAuthController').getTokenStatus
);

// Marketing management routes (requires marketing access)
router.post('/marketing/auto-publish-pinterest',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.autoPublishToPinterest
);

router.post('/marketing/auto-publish-tiktok',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.autoPublishToTikTok
);

router.post('/marketing/auto-publish-instagram',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.autoPublishToInstagram
);

router.post('/marketing/auto-publish-facebook',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.autoPublishToFacebook
);

router.post('/marketing/auto-publish-instagram-story',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.autoPublishToInstagramStory
);

router.get('/marketing/content/all',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.getAllMarketingContent
);

// Marketing content management routes
router.post('/marketing/create-test-content',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.createTestMarketingContent
);

router.get('/marketing/content-status',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.getMarketingContentStatus
);

router.post('/marketing/fix-missing-videos',
    adminMiddleware.requireMarketingAccess,
    marketingContentController.fixMissingVideos
);

// User management routes (requires admin)
router.post('/grant-permissions',
    adminMiddleware.requireAdmin,
    adminController.grantAdminPermissions
);

router.post('/revoke-permissions',
    adminMiddleware.requireAdmin,
    adminController.revokeAdminPermissions
);

module.exports = router;
