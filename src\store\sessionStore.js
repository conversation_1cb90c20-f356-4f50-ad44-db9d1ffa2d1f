import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useUserStore } from './userStore'
import { useMenuStore } from './menuStore'

export const useSessionStore = defineStore('session', () => {

    const { setNotification } = useNotification();
    const router = useRouter();
    const userStore = useUserStore();
    const menustore = useMenuStore();

    /* INITIALIZING VALUES */

    const acceptedCookie = ref(false);
    const botyInputText = ref('')

    const messageBody = ref([
          {
            role: "system",
            content: "You are a helpful assistant for every topic around and in kitchen, reciepts and food.",
            function: '',
            body: '',
        },
          {
            role: "assistant", 
            content: 'Wie kann ich dir helfen? Ich kann dir zum Beispiel ein zufälliges Rezept von Ordy zur Verfügung stellen - du musst mir nur eine Zutat oder Name des Menüs sagen.',
            function: '',
            body: '',
        }
        ])

    const messageBodyChatGPT = ref([
        {
            role: "system",
            content: "You are a helpful assistant for every topic around and in kitchen, reciepts and food.",
        },
        {
            role: "assistant", 
            content: 'Wie kann ich dir helfen? Ich kann dir zum Beispiel ein zufälliges Rezept von Ordy zur Verfügung stellen - du musst mir nur eine Zutat oder Name des Menüs sagen.',
        }
        ])

    /* ///////////////////////  FUNCTIONS  //////////////////////////// */


    const createQuestion = async () => {

        messageBodyChatGPT.value.push({
            role: "user",
            content: botyInputText.value,
        })

        messageBody.value.push({
            role: "user",
            content: botyInputText.value,
            function: '',
            body: '',
        })

        botyInputText.value = ''

        console.log("bevore send to api")

        const createdMenu = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/basic', {
            data: messageBodyChatGPT.value
        });

        // wenn "funktion" von GPT aktiviert wurde
        if(createdMenu.data.data.tool_calls){
            console.log("funktion aktiviert")

            for (let i = 0; i < createdMenu.data.data.tool_calls.length; i++){
                //console.log(createdMenu.data.data.tool_calls[i].function.name)

                //////////// load a reciept by ingridient
                if(createdMenu.data.data.tool_calls[i].function.name == 'get_ordy_menus'){
                    console.log("inside get_ordy_menus")
                    //console.log(createdMenu.data.data.tool_calls[i].function.arguments)
                    const ingrident = JSON.parse(createdMenu.data.data.tool_calls[i].function.arguments)
                    const resFoundMenu = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/byIngridient/' + ingrident.ingridient);
                    //console.log(resFoundMenu)

                    // check if something was found, if not change text
                    if(resFoundMenu.data.data.menue.imagelink == ''){

                        messageBody.value.push({
                            role: "assistant",
                            content: 'Es wurde leider kein Rezept mit dieser Zutat gefunden.'
                        })

                    } else {

                        messageBody.value.push({
                            role: "assistant",
                            content: '',
                            function: "get_ordy_menus",
                            body: resFoundMenu.data.data.menue.imagelink,
                        })

                    }

                    messageBodyChatGPT.value.push({
                        role: "assistant",
                        content: ''
                    })
                }
                //////////

              }

        } else {
            //console.log("normaler text")
            messageBody.value.push({
                role: "assistant",
                content: createdMenu.data.data.content,
                function: "",
                body: "",
            })
        }

    }

    return {
        // EXPORTET VALUES
        acceptedCookie,
        messageBody,
        botyInputText,

        // EXPORTED FUNCTIONS
        createQuestion
        
    };

});