const mongoose = require('mongoose');
const MarketingContent = require('../models/marketingContentModel');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const helper = require('../utils/helper');
const publishController = require('./publishController');

// @desc    Get the latest marketing content entry
// @route   GET /api/v1/marketing-content
// @access  Public (or Protected, depending on requirements)
exports.getLatestMarketingContent = catchAsync(async (req, res, next) => {
    const functionName = 'marketingContentController.getLatestMarketingContent';
    helper.devConsole(functionName, 'Fetching latest marketing content...');

    const latestContent = await MarketingContent.findOne()
        .sort({ generationDate: -1 }) // Sort by date descending
        // .limit(1) // findOne implicitly limits to 1, but keeping it explicit is fine too.
        .lean(); // Use .lean() for faster read-only operations if not modifying the doc

    if (!latestContent) {
        helper.devConsole(functionName, 'No marketing content found.');
        // Decide if this is an error or just an empty result
        // Sending 200 with empty data might be better than 404 for frontend
        return res.status(200).json({
            status: 'success',
            data: null, // Indicate no content found
            message: 'No marketing content available yet.'
        });
        // Alternatively, send 404: return next(new AppError('No marketing content found', 404));
    }

    helper.devConsole(functionName, `Found latest content with ID: ${latestContent._id}`);

    res.status(200).json({
        status: 'success',
        data: latestContent
    });
});

// @desc    Get all marketing content
// @route   GET /api/v1/marketing-content/all
// @access  Protected
exports.getAllMarketingContent = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.getAllMarketingContent';
  helper.devConsole(`[${functionName}] Getting all marketing content`);

  const marketingContent = await MarketingContent.find()
    .sort({ generationDate: -1 }) // Neueste zuerst
    .limit(10); // Begrenze auf die letzten 10 Einträge

  res.status(200).json({
    status: 'success',
    results: marketingContent.length,
    data: marketingContent
  });
});

// @desc    Get marketing content by ID
// @route   GET /api/v1/marketing-content/:id
// @access  Protected
exports.getMarketingContentById = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.getMarketingContentById';
  const { id } = req.params;

  helper.devConsole(`[${functionName}] Getting marketing content with ID: ${id}`);

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new AppError('Invalid marketing content ID', 400));
  }

  const marketingContent = await MarketingContent.findById(id);

  if (!marketingContent) {
    return next(new AppError('Marketing content not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: marketingContent
  });
});

// @desc    Publish marketing content to social media
// @route   POST /api/v1/marketing-content/publish
// @access  Protected
exports.publishToSocialMedia = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.publishToSocialMedia';
  const { contentId, platform } = req.body;

  helper.devConsole(`[${functionName}] Publishing content ID ${contentId} to ${platform}`);

  // Validierung
  if (!contentId || !platform) {
    return next(new AppError('Content ID and platform are required', 400));
  }

  if (!['instagram', 'facebook', 'tiktok', 'pinterest'].includes(platform)) {
    return next(new AppError('Invalid platform. Must be one of: instagram, facebook, tiktok, pinterest', 400));
  }

  if (!mongoose.Types.ObjectId.isValid(contentId)) {
    return next(new AppError('Invalid content ID', 400));
  }

  // Content laden
  const content = await MarketingContent.findById(contentId);

  if (!content) {
    return next(new AppError('Marketing content not found', 404));
  }

  if (!content.videoS3Url) {
    return next(new AppError('No video available for this content', 400));
  }

  // Prüfen, ob bereits veröffentlicht
  const platformField = `published_${platform === 'tiktok' ? 'tiktok' : platform === 'facebook' ? 'fb' : platform === 'instagram' ? 'insta' : 'pinterest'}`;

  if (content[platformField] > 0) {
    return next(new AppError(`Content already published to ${platform}`, 400));
  }

  // Veröffentlichen auf der entsprechenden Plattform
  let result;
  const title = content.recipeName;
  const description = content.socialMediaDescription || content.textOne;

  switch (platform) {
    case 'instagram':
      result = await publishController.instagramService.publishVideo(content.videoS3Url, title, description);
      break;
    case 'facebook':
      result = await publishController.facebookService.publishVideo(content.videoS3Url, title, description);
      break;
    case 'tiktok':
      result = await publishController.tiktokService.publishVideo(content.videoS3Url, title, description);
      break;
    case 'pinterest':
      result = await publishController.pinterestService.publishVideo(content.videoS3Url, title, description);
      break;
  }

  if (!result.success) {
    return next(new AppError(`Failed to publish to ${platform}: ${result.error}`, 500));
  }

  // Content aktualisieren
  const updateField = {};
  updateField[platformField] = 1;

  await MarketingContent.findByIdAndUpdate(contentId, updateField);

  res.status(200).json({
    status: 'success',
    message: `Successfully published to ${platform}`,
    platformPostId: result.platformPostId,
    success: true
  });
});

// @desc    Auto-publish latest content to Pinterest
// @route   POST /api/v1/marketing-content/auto-publish-pinterest
// @access  Protected
exports.autoPublishToPinterest = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.autoPublishToPinterest';
  helper.devConsole(`[${functionName}] Starting automatic Pinterest publishing...`);

  try {
    // Get latest marketing content that hasn't been published to Pinterest
    const latestContent = await MarketingContent.findOne({
      published_pinterest: { $ne: 1 }, // Not published to Pinterest
      videoS3Url: { $exists: true, $ne: null } // Has video
    }).sort({ generationDate: -1 });

    if (!latestContent) {
      return res.status(200).json({
        status: 'success',
        message: 'No new content available for Pinterest publishing',
        published: false
      });
    }

    helper.devConsole(`[${functionName}] Found content to publish: ${latestContent._id}`);

    // Check if Pinterest token is available
    const PinterestToken = require('../models/pinterestTokenModel');
    const tokenRecord = await PinterestToken.getCurrentToken();

    if (!tokenRecord || !tokenRecord.accessToken) {
      return res.status(400).json({
        status: 'fail',
        message: 'Pinterest token not configured. Please complete OAuth setup first.',
        published: false,
        setupUrl: 'http://localhost:8080/pinterest-auth.html'
      });
    }

    // Publish to Pinterest
    const title = latestContent.recipeName;
    const description = latestContent.socialMediaDescription || latestContent.textOne;

    const result = await publishController.pinterestService.publishVideo(
      latestContent.videoS3Url,
      title,
      description
    );

    if (!result.success) {
      helper.devConsole(`[${functionName}] Pinterest publishing failed:`, result.error);
      return res.status(500).json({
        status: 'fail',
        message: `Pinterest publishing failed: ${result.error}`,
        published: false
      });
    }

    // Mark as published
    await MarketingContent.findByIdAndUpdate(latestContent._id, {
      published_pinterest: 1
    });

    helper.devConsole(`[${functionName}] Successfully published to Pinterest:`, result.platformPostId);

    res.status(200).json({
      status: 'success',
      message: 'Successfully auto-published to Pinterest',
      published: true,
      contentId: latestContent._id,
      recipeName: latestContent.recipeName,
      platformPostId: result.platformPostId
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Auto-publish failed: ${error.message}`, 500));
  }
});

// @desc    Auto-publish latest marketing content to TikTok
// @route   POST /api/v1/marketing/auto-publish-tiktok
// @access  Protected (Admin only)
exports.autoPublishToTikTok = catchAsync(async (req, res, next) => {
  const functionName = 'autoPublishToTikTok';
  helper.devConsole(`[${functionName}] Starting auto-publish to TikTok...`);

  try {
    // Find the latest marketing content with video
    const latestContent = await MarketingContent.findOne({
      $and: [
        { videoS3Url: { $exists: true } },
        { videoS3Url: { $ne: null } },
        { videoS3Url: { $ne: '' } }
      ]
    }).sort({ generationDate: -1 });

    if (!latestContent) {
      return res.status(404).json({
        status: 'fail',
        message: 'No marketing content with video found',
        published: false
      });
    }

    helper.devConsole(`[${functionName}] Found latest content:`, {
      id: latestContent._id,
      recipeName: latestContent.recipeName,
      videoUrl: latestContent.videoS3Url,
      videoUrlType: typeof latestContent.videoS3Url,
      videoUrlLength: latestContent.videoS3Url ? latestContent.videoS3Url.length : 0,
      hasVideoUrl: !!latestContent.videoS3Url,
      trimmedVideoUrl: latestContent.videoS3Url ? latestContent.videoS3Url.trim() : 'null'
    });

    // Validate video URL
    if (!latestContent.videoS3Url || latestContent.videoS3Url.trim() === '') {
      helper.devConsole(`[${functionName}] Video URL validation failed:`, {
        videoS3Url: latestContent.videoS3Url,
        type: typeof latestContent.videoS3Url,
        length: latestContent.videoS3Url ? latestContent.videoS3Url.length : 0,
        trimmed: latestContent.videoS3Url ? latestContent.videoS3Url.trim() : 'null',
        contentId: latestContent._id,
        recipeName: latestContent.recipeName
      });

      return res.status(400).json({
        status: 'fail',
        message: 'No video URL found in marketing content. Cannot publish to TikTok without video.',
        published: false,
        debug: {
          contentId: latestContent._id,
          recipeName: latestContent.recipeName,
          videoUrl: latestContent.videoS3Url,
          videoUrlType: typeof latestContent.videoS3Url
        }
      });
    }

    // Check TikTok mode (Sandbox or Production)
    const isSandboxMode = process.env.IS_SANDBOX_MODE_TIKTOK === 'true';
    const hasProductionCredentials = !!(process.env.TIKTOK_ACCESS_TOKEN && process.env.TIKTOK_OPEN_ID);

    // EXPLICIT DEBUG: Show exactly what environment variables are loaded
    console.log('🔍 [TikTok Debug] Environment Variables:');
    console.log('IS_SANDBOX_MODE_TIKTOK:', process.env.IS_SANDBOX_MODE_TIKTOK);
    console.log('IS_SANDBOX_MODE_TIKTOK type:', typeof process.env.IS_SANDBOX_MODE_TIKTOK);
    console.log('IS_SANDBOX_MODE_TIKTOK === "true":', process.env.IS_SANDBOX_MODE_TIKTOK === 'true');
    console.log('TIKTOK_ACCESS_TOKEN exists:', !!process.env.TIKTOK_ACCESS_TOKEN);
    console.log('TIKTOK_OPEN_ID exists:', !!process.env.TIKTOK_OPEN_ID);

    helper.devConsole(`[${functionName}] TikTok Mode Check:`, {
      isSandboxMode,
      hasProductionCredentials,
      sandboxFlag: process.env.IS_SANDBOX_MODE_TIKTOK,
      sandboxFlagType: typeof process.env.IS_SANDBOX_MODE_TIKTOK,
      sandboxFlagComparison: process.env.IS_SANDBOX_MODE_TIKTOK === 'true',
      hasAccessToken: !!process.env.TIKTOK_ACCESS_TOKEN,
      hasOpenId: !!process.env.TIKTOK_OPEN_ID
    });

    if (!isSandboxMode && !hasProductionCredentials) {
      return res.status(400).json({
        status: 'fail',
        message: 'TikTok not configured. Please set IS_SANDBOX_MODE_TIKTOK=true for sandbox mode, or provide TIKTOK_ACCESS_TOKEN and TIKTOK_OPEN_ID for production mode.',
        published: false,
        debug: {
          isSandboxMode,
          hasProductionCredentials,
          sandboxFlag: process.env.IS_SANDBOX_MODE_TIKTOK,
          requiredForSandbox: 'IS_SANDBOX_MODE_TIKTOK=true',
          requiredForProduction: ['TIKTOK_ACCESS_TOKEN', 'TIKTOK_OPEN_ID']
        }
      });
    }

    // Publish to TikTok (Sandbox or Production)
    const title = latestContent.recipeName;
    const description = latestContent.socialMediaDescription || latestContent.textOne;

    let result;

    if (isSandboxMode) {
      // Sandbox mode - simulate publishing (PRIORITY over production)
      helper.devConsole(`[${functionName}] Publishing to TikTok SANDBOX mode (simulated)`);

      result = {
        success: true,
        platformPostId: `sandbox_${Date.now()}`,
        status: 'SANDBOX_PUBLISHED',
        message: 'Successfully published to TikTok Sandbox (simulated)',
        sandbox: true,
        credentials: {
          clientKey: process.env.TIKTOK_SANDBOX_CLIENT_KEY?.substring(0, 8) + '...',
          appId: process.env.TIKTOK_SANDBOX_APP_ID
        }
      };
    } else {
      // Production mode - actual publishing
      helper.devConsole(`[${functionName}] Publishing to TikTok PRODUCTION mode`);

      result = await publishController.tiktokService.publishVideo(
        latestContent.videoS3Url,
        title,
        description
      );
    }

    if (!result.success) {
      helper.devConsole(`[${functionName}] TikTok publishing failed:`, result.error);
      return res.status(500).json({
        status: 'fail',
        message: `TikTok publishing failed: ${result.error}`,
        published: false
      });
    }

    // Mark as published
    await MarketingContent.findByIdAndUpdate(latestContent._id, {
      published_tiktok: 1
    });

    helper.devConsole(`[${functionName}] Successfully published to TikTok:`, result.platformPostId);

    res.status(200).json({
      status: 'success',
      message: isSandboxMode ? 'Successfully published to TikTok Sandbox (simulated)' : 'Successfully auto-published to TikTok',
      published: true,
      contentId: latestContent._id,
      recipeName: latestContent.recipeName,
      platformPostId: result.platformPostId,
      uploadStatus: result.status,
      mode: isSandboxMode ? 'sandbox' : 'production',
      sandbox: isSandboxMode || false
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Auto-publish failed: ${error.message}`, 500));
  }
});

// @desc    Auto-publish latest marketing content to Instagram
// @route   POST /api/v1/marketing/auto-publish-instagram
// @access  Protected (Admin only)
exports.autoPublishToInstagram = catchAsync(async (req, res, next) => {
  const functionName = 'autoPublishToInstagram';
  helper.devConsole(`[${functionName}] Starting auto-publish to Instagram...`);

  try {
    // Find the latest marketing content with video
    const latestContent = await MarketingContent.findOne({
      $and: [
        { videoS3Url: { $exists: true } },
        { videoS3Url: { $ne: null } },
        { videoS3Url: { $ne: '' } }
      ]
    }).sort({ generationDate: -1 });

    if (!latestContent) {
      return res.status(404).json({
        status: 'fail',
        message: 'No marketing content with video found',
        published: false
      });
    }

    helper.devConsole(`[${functionName}] Found latest content:`, {
      id: latestContent._id,
      recipeName: latestContent.recipeName,
      videoUrl: latestContent.videoS3Url
    });

    // Validate video URL
    if (!latestContent.videoS3Url || latestContent.videoS3Url.trim() === '') {
      return res.status(400).json({
        status: 'fail',
        message: 'No video URL found in marketing content. Cannot publish to Instagram without video.',
        published: false
      });
    }

    // Check if Instagram credentials are configured
    if (!process.env.INSTAGRAM_TOKEN || !process.env.INSTAGRAM_USER_ID) {
      return res.status(400).json({
        status: 'fail',
        message: 'Instagram credentials not configured. Please set INSTAGRAM_TOKEN and INSTAGRAM_USER_ID.',
        published: false
      });
    }

    // Publish to Instagram
    const title = latestContent.recipeName;
    const description = latestContent.socialMediaDescription || latestContent.textOne;

    const result = await publishController.instagramService.publishVideo(
      latestContent.videoS3Url,
      title,
      description
    );

    if (!result.success) {
      helper.devConsole(`[${functionName}] Instagram publishing failed:`, result.error);
      return res.status(500).json({
        status: 'fail',
        message: `Instagram publishing failed: ${result.error}`,
        published: false
      });
    }

    // Mark as published
    await MarketingContent.findByIdAndUpdate(latestContent._id, {
      published_insta: 1
    });

    helper.devConsole(`[${functionName}] Successfully published to Instagram:`, result.platformPostId);

    res.status(200).json({
      status: 'success',
      message: 'Successfully auto-published to Instagram',
      published: true,
      contentId: latestContent._id,
      recipeName: latestContent.recipeName,
      platformPostId: result.platformPostId
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Auto-publish failed: ${error.message}`, 500));
  }
});

// @desc    Create test marketing content with video for testing
// @route   POST /api/v1/marketing/create-test-content
// @access  Protected (Admin only)
exports.createTestMarketingContent = catchAsync(async (req, res, next) => {
  const functionName = 'createTestMarketingContent';
  helper.devConsole(`[${functionName}] Creating test marketing content with video...`);

  try {
    // Check if test content already exists
    const existingTestContent = await MarketingContent.findOne({
      recipeName: 'Test Rezept für Marketing'
    });

    if (existingTestContent) {
      // Update existing test content with video URL if missing
      if (!existingTestContent.videoS3Url || existingTestContent.videoS3Url.trim() === '') {
        const testVideoUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';
        existingTestContent.videoS3Url = testVideoUrl;
        await existingTestContent.save();

        helper.devConsole(`[${functionName}] Updated existing test content with video URL`);
      }

      return res.status(200).json({
        status: 'success',
        message: 'Test marketing content already exists and updated',
        data: {
          contentId: existingTestContent._id,
          recipeName: existingTestContent.recipeName,
          hasVideo: !!existingTestContent.videoS3Url,
          videoUrl: existingTestContent.videoS3Url
        }
      });
    }

    // Use multiple reliable test video URLs
    const availableTestVideos = [
      // Reliable public test videos
      'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
      // Fallback to a simple test video
      'https://file-examples.com/storage/fe86c86b8b66f8c1b8e7b3e/2017/10/file_example_MP4_480_1_5MG.mp4'
    ];

    // Use a reliable test video URL
    const testVideoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';

    // Create test marketing content with a sample video URL
    const testContent = await MarketingContent.create({
      generationDate: new Date(),
      marketingHook: 'Teste unser neues Marketing System!',
      textOne: 'Entdecke dieses fantastische Rezept',
      textTwo: 'Perfekt für jeden Anlass',
      socialMediaTextMitLink: 'Schau dir dieses tolle Rezept an: {recipeLinkPlaceholder} #ordy #kochen',
      socialMediaTextOhneLink: 'Schau dir dieses tolle Rezept an! #ordy #kochen',
      // Use a test video URL
      videoS3Url: testVideoUrl,
      // Recipe details (using a dummy recipe ID)
      recipeId: new mongoose.Types.ObjectId(),
      recipeName: 'Test Rezept für Marketing',
      recipeImageUrl: 'https://ordy-images.s3.eu-central-1.amazonaws.com/sample-recipe.jpg',
      recipeNumberOfPersons: 4,
      recipeCookingTime: 30,
      // Initialize publishing counters
      published_tiktok: 0,
      published_fb: 0,
      published_insta: 0,
      published_pinterest: 0
    });

    helper.devConsole(`[${functionName}] Test content created successfully:`, {
      id: testContent._id,
      recipeName: testContent.recipeName,
      videoUrl: testContent.videoS3Url
    });

    res.status(201).json({
      status: 'success',
      message: 'Test marketing content created successfully',
      data: {
        contentId: testContent._id,
        recipeName: testContent.recipeName,
        videoUrl: testContent.videoS3Url,
        socialMediaText: testContent.socialMediaTextOhneLink
      }
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Failed to create test content: ${error.message}`, 500));
  }
});

// @desc    Check marketing content status and video availability
// @route   GET /api/v1/marketing/content-status
// @access  Protected (Admin only)
exports.getMarketingContentStatus = catchAsync(async (req, res, next) => {
  const functionName = 'getMarketingContentStatus';
  helper.devConsole(`[${functionName}] Checking marketing content status...`);

  try {
    // Get total count
    const totalCount = await MarketingContent.countDocuments();

    // Get count with videos
    const withVideoCount = await MarketingContent.countDocuments({
      $and: [
        { videoS3Url: { $exists: true } },
        { videoS3Url: { $ne: null } },
        { videoS3Url: { $ne: '' } }
      ]
    });

    // Get latest content with video
    const latestWithVideo = await MarketingContent.findOne({
      $and: [
        { videoS3Url: { $exists: true } },
        { videoS3Url: { $ne: null } },
        { videoS3Url: { $ne: '' } }
      ]
    }).sort({ generationDate: -1 });

    // Get latest content without video
    const latestWithoutVideo = await MarketingContent.findOne({
      $or: [
        { videoS3Url: null },
        { videoS3Url: '' }
      ]
    }).sort({ generationDate: -1 });

    res.status(200).json({
      status: 'success',
      data: {
        totalContent: totalCount,
        contentWithVideo: withVideoCount,
        contentWithoutVideo: totalCount - withVideoCount,
        latestWithVideo: latestWithVideo ? {
          id: latestWithVideo._id,
          recipeName: latestWithVideo.recipeName,
          generationDate: latestWithVideo.generationDate,
          videoUrl: latestWithVideo.videoS3Url
        } : null,
        latestWithoutVideo: latestWithoutVideo ? {
          id: latestWithoutVideo._id,
          recipeName: latestWithoutVideo.recipeName,
          generationDate: latestWithoutVideo.generationDate
        } : null
      }
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Failed to get content status: ${error.message}`, 500));
  }
});

// @desc    Update all marketing content without videos to have test video URLs
// @route   POST /api/v1/marketing/fix-missing-videos
// @access  Protected (Admin only)
exports.fixMissingVideos = catchAsync(async (req, res, next) => {
  const functionName = 'fixMissingVideos';
  helper.devConsole(`[${functionName}] Fixing marketing content without videos...`);

  try {
    // Find all content without videos
    const contentWithoutVideos = await MarketingContent.find({
      $or: [
        { videoS3Url: null },
        { videoS3Url: '' },
        { videoS3Url: { $exists: false } }
      ]
    });

    if (contentWithoutVideos.length === 0) {
      return res.status(200).json({
        status: 'success',
        message: 'All marketing content already has videos',
        data: {
          updatedCount: 0,
          totalContent: await MarketingContent.countDocuments()
        }
      });
    }

    // Test video URL that should work
    const testVideoUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4';

    // Update all content without videos
    const updateResult = await MarketingContent.updateMany(
      {
        $or: [
          { videoS3Url: null },
          { videoS3Url: '' },
          { videoS3Url: { $exists: false } }
        ]
      },
      {
        $set: { videoS3Url: testVideoUrl }
      }
    );

    helper.devConsole(`[${functionName}] Updated ${updateResult.modifiedCount} content entries with video URLs`);

    res.status(200).json({
      status: 'success',
      message: `Successfully updated ${updateResult.modifiedCount} marketing content entries with video URLs`,
      data: {
        updatedCount: updateResult.modifiedCount,
        totalContent: await MarketingContent.countDocuments(),
        videoUrl: testVideoUrl
      }
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Failed to fix missing videos: ${error.message}`, 500));
  }
});

// @desc    Auto-publish latest marketing content to Facebook
// @route   POST /api/v1/admin/marketing/auto-publish-facebook
// @access  Protected (Admin with marketing access)
exports.autoPublishToFacebook = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.autoPublishToFacebook';
  helper.devConsole(`[${functionName}] Auto-publishing latest content to Facebook...`);

  try {
    // Find latest marketing content with video
    const latestContent = await MarketingContent.findOne({
      videoS3Url: { $ne: null, $ne: '' }
    }).sort({ generationDate: -1 });

    if (!latestContent) {
      return res.status(404).json({
        status: 'fail',
        message: 'No marketing content with video found for Facebook publishing.',
        published: false
      });
    }

    // Validate video URL
    if (!latestContent.videoS3Url || latestContent.videoS3Url.trim() === '') {
      return res.status(400).json({
        status: 'fail',
        message: 'No video URL found in marketing content. Cannot publish to Facebook without video.',
        published: false
      });
    }

    // Check if Facebook credentials are configured
    if (!process.env.FACEBOOK_APP_ID || !process.env.FACEBOOK_APP_SECRET) {
      return res.status(400).json({
        status: 'fail',
        message: 'Facebook credentials not configured. Please set FACEBOOK_APP_ID and FACEBOOK_APP_SECRET.',
        published: false
      });
    }

    // Publish to Facebook
    const title = latestContent.recipeName;
    const description = latestContent.socialMediaDescription || latestContent.textOne;

    const result = await publishController.facebookService.publishVideo(
      latestContent.videoS3Url,
      title,
      description
    );

    if (!result.success) {
      helper.devConsole(`[${functionName}] Facebook publishing failed:`, result.error);
      return res.status(500).json({
        status: 'fail',
        message: `Facebook publishing failed: ${result.error}`,
        published: false
      });
    }

    // Mark as published
    await MarketingContent.findByIdAndUpdate(latestContent._id, {
      published_fb: (latestContent.published_fb || 0) + 1
    });

    helper.devConsole(`[${functionName}] Facebook publishing successful!`);

    res.status(200).json({
      status: 'success',
      message: 'Facebook publishing successful!',
      published: true,
      data: {
        contentId: latestContent._id,
        recipeName: latestContent.recipeName,
        facebookPostId: result.platformPostId,
        videoUrl: latestContent.videoS3Url
      }
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Failed to publish to Facebook: ${error.message}`, 500));
  }
});

// @desc    Auto-publish latest marketing content to Instagram Stories
// @route   POST /api/v1/admin/marketing/auto-publish-instagram-story
// @access  Protected (Admin with marketing access)
exports.autoPublishToInstagramStory = catchAsync(async (req, res, next) => {
  const functionName = 'marketingContentController.autoPublishToInstagramStory';
  helper.devConsole(`[${functionName}] Auto-publishing latest content to Instagram Stories...`);

  try {
    // Find latest marketing content with video
    const latestContent = await MarketingContent.findOne({
      videoS3Url: { $ne: null, $ne: '' }
    }).sort({ generationDate: -1 });

    if (!latestContent) {
      return res.status(404).json({
        status: 'fail',
        message: 'No marketing content with video found for Instagram Stories publishing.',
        published: false
      });
    }

    // Validate video URL
    if (!latestContent.videoS3Url || latestContent.videoS3Url.trim() === '') {
      return res.status(400).json({
        status: 'fail',
        message: 'No video URL found in marketing content. Cannot publish to Instagram Stories without video.',
        published: false
      });
    }

    // Check if Instagram credentials are configured
    if (!process.env.INSTAGRAM_TOKEN || !process.env.INSTAGRAM_USER_ID) {
      return res.status(400).json({
        status: 'fail',
        message: 'Instagram credentials not configured. Please set INSTAGRAM_TOKEN and INSTAGRAM_USER_ID.',
        published: false
      });
    }

    // Publish to Instagram Stories
    const title = latestContent.recipeName;
    const description = latestContent.socialMediaDescription || latestContent.textOne;

    const result = await publishController.instagramService.publishStory(
      latestContent.videoS3Url,
      title,
      description
    );

    if (!result.success) {
      helper.devConsole(`[${functionName}] Instagram Stories publishing failed:`, result.error);
      return res.status(500).json({
        status: 'fail',
        message: `Instagram Stories publishing failed: ${result.error}`,
        published: false
      });
    }

    // Mark as published (you might want to add a separate counter for stories)
    await MarketingContent.findByIdAndUpdate(latestContent._id, {
      published_insta_story: (latestContent.published_insta_story || 0) + 1
    });

    helper.devConsole(`[${functionName}] Instagram Stories publishing successful!`);

    res.status(200).json({
      status: 'success',
      message: 'Instagram Stories publishing successful!',
      published: true,
      data: {
        contentId: latestContent._id,
        recipeName: latestContent.recipeName,
        instagramStoryId: result.platformPostId,
        videoUrl: latestContent.videoS3Url
      }
    });

  } catch (error) {
    helper.devConsole(`[${functionName}] Error:`, error.message);
    return next(new AppError(`Failed to publish to Instagram Stories: ${error.message}`, 500));
  }
});