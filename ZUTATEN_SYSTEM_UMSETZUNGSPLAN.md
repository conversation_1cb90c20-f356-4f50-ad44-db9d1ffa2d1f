# Zutaten-System Umsetzungsplan

## Phase 1: Backend StableID-System (AKTIV)

### Task 1.1: StableID-Middleware implementieren ✅ IN PROGRESS
**Priorität:** KRITISCH
**Geschätzte Zeit:** 2-3 Stunden

**Subtasks:**
- [x] StableID-Utility-Funktionen erstellen
- [ ] MenuChild-Controller erweitern
- [ ] Middleware für automatische StableID-Zuweisung
- [ ] Backend-Validierung implementieren

**Dateien:**
- `utils/stableIdManager.js` (neu)
- `controllers/menuchildController.js`
- `models/menuchildModel.js`

### Task 1.2: AI-Rezeptgenerierung verbessern
**Priorität:** HOCH
**Geschätzte Zeit:** 3 Tage

**Subtasks:**
- [ ] GPT-Controller: Vollständige Rezeptdaten generieren (Titel, Beschreibung, Nährwerte)
- [ ] StableID-Zuweisung nach AI-Generierung
- [ ] Platzhalter-Korrektur für hohe IDs
- [ ] Validierung der generierten Daten

**Dateien:**
- `controllers/gptController.js`
- `utils/recipeUtils.js` (erweitern)
- `routes/gptRoutes.js`

### Task 1.3: Einkaufszettel-Integration Backend
**Priorität:** HOCH
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Recipe-to-ShoppingList Controller erweitern
- [ ] Automatische Kategorisierung verbessern
- [ ] Mengen-Skalierung basierend auf Personenanzahl
- [ ] WebSocket-Events für Rezept-Hinzufügung

**Dateien:**
- `controllers/shoppingListItemController.js`
- `controllers/menuController.js`
- `websocket/shoppingListHandler.js`

## Sprint 2: Frontend-Optimierung und UX (Woche 2)

### Task 2.1: Rezept-Anzeige optimieren (/kochbuch/menu/:menuid)
**Priorität:** HOCH
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Zutaten-Buttons mit Kategorie-Farben
- [ ] Platzhalter-Highlighting im Text
- [ ] Responsive Design für Desktop/Mobile
- [ ] Personenanzahl-Skalierung UI

**Dateien:**
- `src/views/SecondLevelView/MenuDetails.vue`
- `src/components/IngredientButton.vue` (neu)
- `src/utils/recipeUtils.js`

### Task 2.2: Rezept-Bearbeitung verbessern (/kochbuch/menu/edit/:menuid)
**Priorität:** HOCH
**Geschätzte Zeit:** 3 Tage

**Subtasks:**
- [ ] Drag-and-Drop für Zutaten in Text
- [ ] Live-Platzhalter-Vorschau
- [ ] Auto-Save Funktionalität
- [ ] Zutaten-Löschung mit Bestätigung bei Text-Referenzen

**Dateien:**
- `src/views/SecondLevelView/MenuDetailsEdit.vue`
- `src/components/DragDropIngredient.vue` (neu)
- `src/store/menuStore.js`

### Task 2.3: Upload-Funktionen stabilisieren (/wochenplan/upload)
**Priorität:** MITTEL
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Error-Handling für alle 4 Upload-Methoden
- [ ] Loading-States verbessern
- [ ] Erfolgs-Feedback optimieren
- [ ] StableID-Korrektur nach Upload

**Dateien:**
- `src/views/SecondLevelView/UploadMenuView.vue`
- `src/store/weekplanStore.js`
- `src/components/RecipeGenerator.vue`

## Sprint 3: Einkaufszettel-Integration Frontend (Woche 3)

### Task 3.1: Rezept-zu-Einkaufszettel Workflow
**Priorität:** HOCH
**Geschätzte Zeit:** 3 Tage

**Subtasks:**
- [ ] Ein-Klick Rezept-Hinzufügung
- [ ] Mengen-Anpassung Dialog
- [ ] Kategorie-Anzeige mit Farben
- [ ] Offline-Sync für Rezept-Hinzufügung

**Dateien:**
- `src/components/menuCard.vue`
- `src/store/activeShoppingListStore.js`
- `src/components/RecipeToShoppingDialog.vue` (neu)

### Task 3.2: Einkaufszettel-Anzeige optimieren
**Priorität:** MITTEL
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Kategorie-basierte Gruppierung
- [ ] Rezept-Herkunft anzeigen
- [ ] Batch-Operationen für Rezept-Zutaten
- [ ] Farbkodierung nach Kategorien

**Dateien:**
- `src/views/ZettelView.vue`
- `src/components/CategorySection.vue` (neu)
- `src/components/ShoppingListItem.vue`

## Sprint 4: Testing und Qualitätssicherung (Woche 4)

### Task 4.1: Playwright End-to-End Tests
**Priorität:** KRITISCH
**Geschätzte Zeit:** 3 Tage

**Subtasks:**
- [ ] Rezept-Erstellung und -Bearbeitung Tests
- [ ] StableID-Persistenz Tests
- [ ] AI-Generierung Tests
- [ ] Einkaufszettel-Integration Tests

**Dateien:**
- `tests/e2e/recipe-management.spec.js` (neu)
- `tests/e2e/stable-ids.spec.js` (neu)
- `tests/e2e/shopping-integration.spec.js` (neu)

### Task 4.2: Performance-Optimierung
**Priorität:** MITTEL
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Platzhalter-Verarbeitung optimieren
- [ ] Lazy Loading für große Rezepte
- [ ] Caching für Kategorie-Zuordnungen
- [ ] Bundle-Size Optimierung

**Dateien:**
- `src/utils/recipeUtils.js`
- `src/store/menuStore.js`
- `webpack.config.js`

## Sprint 5: Erweiterte Features (Woche 5)

### Task 5.1: AI-Text-Editing für Rezepte
**Priorität:** NIEDRIG
**Geschätzte Zeit:** 3 Tage

**Subtasks:**
- [ ] AI-basierte Platzhalter-Einfügung
- [ ] Intelligente Text-Korrektur
- [ ] Automatische Zutaten-Erkennung im Text
- [ ] Kontext-bewusste Vorschläge

### Task 5.2: Erweiterte Einkaufszettel-Features
**Priorität:** NIEDRIG
**Geschätzte Zeit:** 2 Tage

**Subtasks:**
- [ ] Rezept-Mengen-Anpassung im Einkaufszettel
- [ ] Intelligente Duplikat-Erkennung
- [ ] Einkaufsrouten-Optimierung
- [ ] Preis-Schätzungen

## Technische Risiken und Mitigation

### Risiko 1: StableID-Inkonsistenz
**Mitigation:** Umfassende Unit-Tests und Validierung bei jedem Update

### Risiko 2: Performance bei großen Rezepten
**Mitigation:** Lazy Loading und effiziente Platzhalter-Algorithmen

### Risiko 3: AI-Generierung unvollständig
**Mitigation:** Validierung und Fallback-Mechanismen

## Definition of Done
- [ ] Alle Tests bestehen (Unit + E2E)
- [ ] StableID-System funktioniert zuverlässig
- [ ] AI-Generierung produziert vollständige Rezepte
- [ ] Einkaufszettel-Integration nahtlos
- [ ] Performance-Benchmarks erfüllt
- [ ] Code-Review abgeschlossen
- [ ] Dokumentation aktualisiert
