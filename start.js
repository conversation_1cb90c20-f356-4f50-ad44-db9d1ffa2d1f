'use strict';

/**
 * Start-Skript für Ordy API
 * 
 * Dieses Skript wird zum Starten der Anwendung verwendet und
 * führt zuerst ein Cleanup-Skript aus, dann das Startup-Protokoll,
 * bevor es den eigentlichen Server startet. Dies ist für 
 * Azure Deployments hilfreich, um Probleme besser diagnostizieren zu können.
 */

console.log(`Starting Ordy API startup process...`);

// Zuerst Speicherplatz bereinigen
try {
  console.log('Running cleanup script...');
  const cleanup = require('./cleanup');
  console.log('Cleanup completed.');
} catch (error) {
  console.error('Error running cleanup:', error.message);
}

// Überprüfen, ob wir in einer Azure-Umgebung laufen
const isAzure = 
  process.env.WEBSITE_SITE_NAME !== undefined || 
  process.env.WEBSITE_INSTANCE_ID !== undefined;

console.log(`Starting Ordy API in ${process.env.NODE_ENV || 'unknown'} environment...`);
console.log(`Running in Azure: ${isAzure ? 'Yes' : 'No'}`);

// Playwright-Browser in Azure-Umgebung installieren
if (isAzure) {
  const { execSync } = require('child_process');
  try {
    // Setze das Browser-Cache-Verzeichnis auf ein beschreibbares Verzeichnis
    process.env.PLAYWRIGHT_BROWSERS_PATH = '/home/<USER>/.cache/ms-playwright';
    console.log('Installing Playwright browsers in Azure environment...');
    execSync('npx playwright install --with-deps', { stdio: 'inherit' });
    console.log('Playwright browsers installed.');
    // Optional: Verzeichnisinhalt ausgeben
    execSync('ls -la /home/<USER>/.cache/ms-playwright/ || echo "No playwright cache found"', { stdio: 'inherit' });
  } catch (err) {
    console.error('Error installing Playwright browsers:', err.message);
  }
}

// Das Startup-Protokoll ausführen
let logger;
try {
  logger = require('./azure-startup');
  if (isAzure) {
    logger.log('Starting Azure deployment checks...');
  } else {
    logger.log('Starting in local environment...');
  }
} catch (error) {
  console.error('Error loading startup logger:', error.message);
}

// Server starten
try {
  require('./server');
  if (logger) {
    logger.log('Server successfully started.');
  }
} catch (error) {
  console.error('ERROR STARTING SERVER:', error);
  if (logger) {
    logger.log(`FATAL ERROR: ${error.message}\n${error.stack}`);
  }
  process.exit(1);
} 