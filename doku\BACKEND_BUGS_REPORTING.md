# BACKEND_BUGS_REPORTING.md

## Übersicht

## Bug: `extAccessable` wird nicht korrekt auf `externalAccess` in der DB gemappt (08.05.2025)

**Status:**
fixed

**Problembeschreibung:**
Wenn ein <PERSON>utzer seine Einstellungen unter `/usersettings` ändert und dabei den Wert für die externe Zugänglichkeit (vom Frontend als `extAccessable` im Payload gesendet) modifiziert, wird diese Änderung nicht korrekt in das Datenbankfeld `externalAccess` des Benutzerobjekts übernommen. Andere Felder wie `gdpr` werden korrekt aktualisiert.

**Ursache (Vermutung basierend auf Code-Analyse `authController.updateUserData`):
Das Backend nimmt den `req.body.payload` direkt und verwendet ihn in `User.findByIdAndUpdate({ $set: payloadToUpdate }, ...)`. <PERSON><PERSON> das <PERSON>end `extAccessable: true/false` sendet, versucht das Backend, ein Feld namens `extAccessable` zu aktualisieren, anstatt das existierende Datenbankfeld `externalAccess`.

**Betroffene Funktion:**
`exports.updateUserData` in `controllers/authController.js`

**Reproduktion:**
1. Gehe zu `/usersettings` im Frontend.
2. Ändere die Einstellung für "Externe Zugänglichkeit" (oder eine ähnliche Bezeichnung, die `extAccessable` steuert).
3. Speichere die Änderungen.
4. Überprüfe das `externalAccess`-Feld des entsprechenden Benutzers in der Datenbank. Es wird nicht den neuen Wert widerspiegeln. Die Logs zeigen, dass der Payload `extAccessable` enthält.

**Erwartetes Verhalten:**
Eine Änderung von `extAccessable` im Frontend-Payload sollte das Feld `externalAccess` in der MongoDB-Benutzerdokumentation aktualisieren.

**Lösungsvorschlag (Backend):
In der Funktion `authController.updateUserData` muss vor dem Aufruf von `User.findByIdAndUpdate` der `payloadToUpdate` modifiziert werden. Wenn `payloadToUpdate.extAccessable` existiert, sollte dessen Wert `payloadToUpdate.externalAccess` zugewiesen und `payloadToUpdate.extAccessable` entfernt werden, um sicherzustellen, dass das korrekte Datenbankfeld aktualisiert wird.

**Priorität:** Mittel (Funktionalität zur Steuerung der externen Sichtbarkeit ist beeinträchtigt)

---
