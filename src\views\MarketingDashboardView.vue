<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Marketing-Dashboard</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- <PERSON><PERSON>lte: Marketing-Trigger -->
      <div class="md:col-span-1">
        <MarketingTrigger />
      </div>
      
      <!-- <PERSON><PERSON>e Spalte: Letzte Marketing-Inhalte -->
      <div class="md:col-span-2">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <h2 class="text-xl font-semibold mb-4">Letzte Marketing-Inhalte</h2>
          
          <div v-if="isLoading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-ordypurple-100"></div>
          </div>
          
          <div v-else-if="marketingContents.length === 0" class="text-center py-8">
            <p class="text-gray-500">Keine Marketing-Inhalte gefunden.</p>
          </div>
          
          <div v-else class="space-y-4">
            <div v-for="content in marketingContents" :key="content._id" class="border border-gray-200 rounded-md p-4">
              <div class="flex flex-col md:flex-row">
                <!-- Video-Vorschau -->
                <div class="w-full md:w-1/3 mb-4 md:mb-0 md:mr-4">
                  <div v-if="content.videoS3Url" class="relative aspect-[9/16] bg-gray-100 rounded-md overflow-hidden">
                    <video 
                      :src="content.videoS3Url" 
                      class="absolute inset-0 w-full h-full object-cover"
                      controls
                    ></video>
                  </div>
                  <div v-else class="aspect-[9/16] bg-gray-100 rounded-md flex items-center justify-center">
                    <p class="text-gray-500 text-sm">Kein Video</p>
                  </div>
                </div>
                
                <!-- Content-Informationen -->
                <div class="flex-1">
                  <h3 class="font-medium text-gray-800 mb-2">{{ content.recipeName }}</h3>
                  <p class="text-sm text-gray-600 mb-2">Erstellt: {{ formatDate(content.generationDate) }}</p>
                  
                  <div class="mb-4">
                    <p class="text-sm text-gray-700 line-clamp-2">{{ content.socialMediaTextOhneLink || content.textOne }}</p>
                  </div>
                  
                  <!-- Veröffentlichungsstatus -->
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span 
                      class="px-2 py-1 text-xs rounded-full"
                      :class="content.published_insta ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                    >
                      Instagram: {{ content.published_insta ? 'Veröffentlicht' : 'Ausstehend' }}
                    </span>
                    <span 
                      class="px-2 py-1 text-xs rounded-full"
                      :class="content.published_fb ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                    >
                      Facebook: {{ content.published_fb ? 'Veröffentlicht' : 'Ausstehend' }}
                    </span>
                    <span 
                      class="px-2 py-1 text-xs rounded-full"
                      :class="content.published_tiktok ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                    >
                      TikTok: {{ content.published_tiktok ? 'Veröffentlicht' : 'Ausstehend' }}
                    </span>
                    <span 
                      class="px-2 py-1 text-xs rounded-full"
                      :class="content.published_pinterest ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                    >
                      Pinterest: {{ content.published_pinterest ? 'Veröffentlicht' : 'Ausstehend' }}
                    </span>
                  </div>
                  
                  <!-- Aktionen -->
                  <div class="flex justify-end">
                    <button 
                      @click="goToUploadPage(content._id)" 
                      class="bg-ordypurple-100 text-white py-1 px-3 rounded-md hover:bg-ordypurple-200 transition-colors text-sm"
                    >
                      Verwalten
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import useNotification from '../../modules/notificationInformation';
import { useHelperStore } from '../../utils/helper';
import MarketingTrigger from '../components/MarketingTrigger.vue';

const router = useRouter();
const { setNotification } = useNotification();
const helper = useHelperStore();

const isLoading = ref(true);
const marketingContents = ref([]);

// Beim Laden der Komponente die Marketing-Inhalte laden
onMounted(async () => {
  await loadMarketingContents();
});

// Funktion zum Laden der Marketing-Inhalte
const loadMarketingContents = async () => {
  isLoading.value = true;
  
  try {
    const response = await axios.get(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-content`
    );
    
    if (response.data && response.data.data) {
      marketingContents.value = response.data.data;
    } else {
      setNotification('Fehler beim Laden der Marketing-Inhalte.', 'alert');
    }
  } catch (error) {
    helper.devConsole('Fehler beim Laden der Marketing-Inhalte:', error);
    setNotification('Fehler beim Laden der Marketing-Inhalte.', 'alert');
  } finally {
    isLoading.value = false;
  }
};

// Funktion zum Formatieren des Datums
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('de-CH', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Funktion zum Navigieren zur Upload-Seite
const goToUploadPage = (contentId) => {
  router.push(`/marketing/upload?contentId=${contentId}`);
};
</script>
