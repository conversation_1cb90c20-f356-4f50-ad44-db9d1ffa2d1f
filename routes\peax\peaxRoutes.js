const express = require('express');
const authController = require('../../controllers/authController');
const peaxController = require('../../controllers/peax/peaxController');
const gptController = require('../../controllers/gptController');

const router = express.Router();

//router.post('/msauth', peaxController.msauth);

//getconfig
router.post('/getconfig', authController.msauth, peaxController.getconfig);
//deleteconfig
router.post('/deleteconfig', authController.msauth, peaxController.deleteconfig);
//create or change config data
router.post('/createorchangeconfig', authController.msauth, peaxController.createOrChangeConfig);
//get data from ms
router.post('/getmsdata', authController.msauth, peaxController.getmsdata);

//get arriving documents
router.post('/arrivingdocument', authController.basicAuth, peaxController.arrivingDocument);



//router.post('/msreg', peaxController.msreg)getmsdata


module.exports = router;