// TEMPORÄR DEAKTIVIERT - VERURSACHT PERFORMANCE-PROBLEME
export function useAdminPermissions() {
  // Einfache, statische Implementierung ohne Reactive Dependencies
  return {
    hasPermission: () => false,
    isAdmin: { value: false },
    canAccessBackend: { value: false },
    canManagePinterest: { value: false },
    canManageMarketing: { value: false },
    canViewAnalytics: { value: false },
    canManageUsers: { value: false },
    availablePermissions: { value: [] },
    hasAnyAdminPermission: { value: false }
  };
}
