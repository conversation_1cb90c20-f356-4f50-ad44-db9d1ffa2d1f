<template>
    <!-- Innerer Container -->
    <div class="w-full mx-auto">
      <div class="mx-auto">
        <img src="../assets/ordy_logo.svg" alt="Logo" class="mx-auto">
      </div>
      <ul class="bg-white w-7/12 mx-auto mt-10 rounded-xl flex flex-col">
        <li class="w-full px-5 py-3 flex flex-row" @click="routerHomePush">
            <img src="../assets/icons/home.png" alt="home" style="width: 21px;height: 21px;" />
            <span class="block ml-4 w-3/5">home</span>
        </li>
        <li class="w-full px-5 py-3 flex flex-row" @click="routerWochenplanPush">
            <img src="../assets/icons/wochenplan.png" alt="wochenplan" style="width: 21px;height: 21px;" />
            <span class="ml-4 w-20">wochenplan</span>
        </li>
        <li class="w-full px-5 py-3 flex flex-row" @click="routerEinkaufslistePush">
            <img src="../assets/icons/einkaufsliste.png" alt="einkaufsliste" style="width: 21px;height: 21px;" />
            <span class="ml-4 w-20">einkaufsliste</span>
        </li>
        <li class="w-full px-5 py-3 flex flex-row" @click="routerLoginPush">
            <img src="../assets/icons/login.png" alt="login" style="width: 21px;height: 21px;" />
            <span class="ml-4 w-20">login</span>
        </li>
      </ul>
    </div>
</template>
<script setup>
    import { useRouter } from 'vue-router';

    const router = useRouter()

    const routerHomePush = () => {
      router.push({ name: 'home'})
    }
    const routerWochenplanPush = () => {
      router.push({ name: 'wochenplan'})
    }
    const routerEinkaufslistePush = () => {
      router.push({ name: 'einkaufsliste'})
    }
    const routerLoginPush = () => {
      router.push({ name: 'login'})
    }

</script>
<style>

</style>