<template>
  <!-- Column Builder-->
  <div class="flex flex-col md:flex-row min-h-screen px-6">
  
    <!-- Middle Container -->
    <div class="w-full md:w-3/4 md:pr-12">

      <!-- Head-->
      <GoBack />
      <!-- Head-->
      
      <div class="w-full">
        <h1 class="mt-20">Hallo {{ userStore.user.firstName }}</h1>
        <p>Hier kannst du verschiedene Einstellungen für ordy vornehmen und dein Abo verwalten</p>
      </div>

      <!-- component switcher -->
      <div class="relative flex items-center w-full h-12 bg-ordypurple-200 mt-12 rounded-2xl bg-[#A37DFF] pb-4 shadow-custom shadow-[#E0ADFF] overflow-hidden">
        <div class="absolute inset-0 h-full rounded-2xl bg-white transition-all duration-300 ease-in-out" 
            :style="{ width: aboStore.switchSettingsComponentButton ? '50%' : '50%', left: !aboStore.switchSettingsComponentButton ? '0%' : '50%' }"></div>
        <button @click="aboStore.switchSettingsComponentButton = !aboStore.switchSettingsComponentButton" 
                class="z-10 w-1/2 h-12 text-xs pt-4 font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Einstellungen
        </button>
        <button @click="aboStore.switchSettingsComponentButton = !aboStore.switchSettingsComponentButton" 
                class="z-10 w-1/2 h-12 text-xs pt-4 font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Abonnement
        </button>
      </div>
      <!-- component switcher -->

      <settingsCard v-if="!aboStore.switchSettingsComponentButton"></settingsCard>
      <aboCard v-if="aboStore.switchSettingsComponentButton"></aboCard>

      
    </div>
    
  </div>
  
</template>
<script setup>
import { reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../../components/menuCard.vue'
import dayCard from '../../components/dayCard.vue'
import settingsCard from '../../components/settingsCard.vue'
import aboCard from '../../components/aboCard.vue'
import useNotification from '../../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../../store/menuStore'
import { useUserStore } from '../../store/userStore';
import { useAboStore } from '../../store/aboStore';
import GoBack from '../../body/goBack.vue';


const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const store = useMenuStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore()
const aboStore = useAboStore()

</script>