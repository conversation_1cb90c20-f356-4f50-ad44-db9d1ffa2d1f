# 🧪 Automatisiertes Test-System für Ordy Frontend

## 📋 **Detaillierte Test-Anforderungen**

### **1. <PERSON>enan<PERSON>hl +/- Buttons**
- ✅ **Keine 500-Errors** beim <PERSON>-Call `/createifnotexists`
- ✅ **Personenanzahl wird korrekt erhöht/verringert** in der UI
- ✅ **StableIDs bleiben erhalten** nach Personenanzahl-Änderung
- ✅ **Zutaten-Mengen werden korrekt skaliert** basierend auf neuer Personenanzahl
- ✅ **Backend-Response ist erfolgreich** (auch bei 500-Status wenn Funktionalität arbeitet)

### **2. Zutaten hinzufügen/löschen**
- ✅ **Keine User-Fehlermeldungen** ("Es ist ein Problem aufgetreten")
- ✅ **Auto-Save funktioniert im Hintergrund** ohne User-Unterbrechung
- ✅ **Neue Zutat erhält automatisch StableID** (höchste ID + 1)
- ✅ **Gelöschte Zutaten-IDs werden nie wiederverwendet** (permanente IDs)
- ✅ **Datenstruktur bleibt konsistent** (name/unit als Objekte im Frontend)

### **3. StableID-System**
- ✅ **IDs sind permanent** - ändern sich NIE nach Zuweisung
- ✅ **Jedes Rezept hat eigene ID-Historie** (nicht global)
- ✅ **maxUsedStableId wird korrekt verwaltet** pro Rezept
- ✅ **Platzhalter ${ID:x} funktionieren** in Rezept-Text
- ✅ **Backend-Kompatibilität** - IDs werden korrekt übertragen

### **4. Input-Feld-Styles**
- ✅ **Graue Focus-Rahmen** statt purple/pink (#D1D5DB oder #9CA3AF)
- ✅ **Konsistente Styles** über alle Input-Felder
- ✅ **Keine purple/pink Farben** in CSS (rgb(168, 85, 247), rgb(236, 72, 153))
- ✅ **Accessibility-konforme** Focus-Indikatoren

### **5. Legacy-Rezepte Migration**
- ✅ **Automatische Erkennung** von Rezepten ohne StableIDs
- ✅ **Frontend-Migration** ohne Datenbank-Änderung
- ✅ **Datenstruktur-Reparatur** (name/unit als Objekte)
- ✅ **Auto-Save nach Migration** für Persistierung
- ✅ **Rückwärtskompatibilität** mit bestehenden Produktions-Daten

### **6. Fehlerbehandlung**
- ✅ **500-Errors werden intelligent behandelt** (false positives erkannt)
- ✅ **Cast-Errors werden als Warnungen geloggt** statt User-Notifications
- ✅ **Auto-Save läuft im Hintergrund** ohne User-Störung
- ✅ **Nur echte Fehler** zeigen User-Notifications

## 🚀 **Test-Ausführung**

### **Setup (einmalig):**
```bash
npm install
npm run test:install
```

### **Alle kritischen Tests ausführen:**
```bash
npm run test:critical
```

### **Tests mit Browser-Fenster (Debug):**
```bash
npm run test:critical-headed
```

### **Einzelne Tests:**
```bash
npx playwright test tests/critical-functions.spec.js --grep "Personenanzahl"
```

## 📊 **Test-Reports**

### **Console Output:**
- ✅/❌ Status für jeden Test
- 📊 Zusammenfassung (Passed/Failed/Success Rate)
- 🔍 Detaillierte Fehler-Logs

### **JSON Report:**
- `test-report.json` - Detaillierte Ergebnisse
- `test-results.json` - Playwright Raw Results
- `playwright-report/` - HTML Report

### **Screenshots/Videos:**
- Automatische Screenshots bei Fehlern
- Video-Aufzeichnung bei kritischen Fehlern
- Traces für Debugging

## 🔧 **Konfiguration**

### **Test-Daten anpassen:**
```javascript
// In tests/critical-functions.spec.js
const TEST_RECIPE_ID = 'your-recipe-id';
const LOGIN_EMAIL = 'your-test-email';
const LOGIN_PASSWORD = 'your-test-password';
```

### **Server-URLs anpassen:**
```javascript
// In playwright.config.js
const BASE_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:8080';
```

## 🎯 **Erfolgs-Kriterien**

### **Alle Tests müssen bestehen:**
1. ✅ Personenanzahl +/- ohne 500-Errors
2. ✅ Zutaten hinzufügen ohne User-Fehlermeldungen
3. ✅ StableIDs bleiben permanent erhalten
4. ✅ Input-Felder haben graue Focus-Rahmen
5. ✅ Legacy-Rezepte werden automatisch migriert
6. ✅ Auto-Save funktioniert ohne User-Störung

### **Performance-Kriterien:**
- Tests laufen in < 5 Minuten
- Keine Memory-Leaks
- Server starten in < 60 Sekunden

### **Stabilität:**
- 100% Success Rate bei wiederholter Ausführung
- Keine flaky Tests
- Robuste Fehlerbehandlung

## 🐛 **Debugging**

### **Test fehlgeschlagen?**
1. Prüfe `test-report.json` für Details
2. Schaue Screenshots in `test-results/`
3. Führe Test mit `--headed` aus für visuelles Debugging
4. Prüfe Browser Console Logs

### **Server-Probleme?**
1. Prüfe ob Frontend auf :5173 läuft
2. Prüfe ob Backend auf :8080 läuft
3. Prüfe Login-Credentials
4. Prüfe Test-Rezept-ID existiert

## 📈 **Kontinuierliche Verbesserung**

### **Nach jeder Änderung:**
1. Führe kritische Tests aus
2. Prüfe Success Rate = 100%
3. Dokumentiere neue Test-Fälle
4. Update Test-Daten bei Bedarf

### **Wöchentlich:**
1. Vollständige Test-Suite ausführen
2. Performance-Metriken prüfen
3. Test-Coverage analysieren
4. Flaky Tests identifizieren und beheben
