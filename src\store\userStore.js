import { defineStore } from 'pinia';
import { ref, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useHelperStore } from '../../utils/helper';
import router from '../router';

// Setup axios interceptor to automatically handle usage data updates
axios.interceptors.response.use(
    (response) => {
        // Check if response contains updated usage data
        if (response.data && response.data.data && response.data.data.updatedUsageData) {
            const updatedUsageData = response.data.data.updatedUsageData;

            // We'll handle this in the store itself to avoid circular dependencies
            console.log('[Axios Interceptor] Found updated usage data:', updatedUsageData);

            // Emit a custom event that the store can listen to
            window.dispatchEvent(new CustomEvent('usageDataUpdated', {
                detail: updatedUsageData
            }));
        }
        return response;
    },
    (error) => {
        return Promise.reject(error);
    }
);

export const useUserStore = defineStore('user', () => {

    const { setNotification } = useNotification();
    const router = useRouter();
    const helper = useHelperStore()

    /* INITIALIZING VALUES */


    const openSettings = ref(false)

    const user = ref({
        id: null,
        extId: null,
        firstName: null,
        lastName: null,
        email: null,
        img: null,
        extAccessable: false,
        defaultKitchentable: false,
        weekplanmode: 0,
        weight: 0,
        height: 0,
        age: 0,
        sex: 0,
        activness: 0,
        summDaily: 0,
        protDaily: 0,
        kcalDaily: 0,
        fatDaily: 0,
        gdpr: 0,
        install_apple_app: false,
        pwa_install_prompts: {
            count: 0,
            max_count: 4,
            disabled: false,
            last_shown: null
        },
        address: {
            city: '',
            country: '',
            line1: '',
            postal_code_state: '',
            state: ''
        },
        bookedAbo: {
            extStripeSessionId: '',
            extStripeCustomerId: '',
            extStripePriceId: '',
            extStripeSubscriptionId: '',
            type: ''
        },
        bookedAboUsage: {
            nrMenucreationCalls: 0,
            nrCookeasyCalls: 0,
            nrMenuuploadCalls: 0,
            nrRealtimeApiCalls: 0
        },
        adminPermissions: {
            isAdmin: false,
            canAccessBackend: false,
            canManagePinterest: false,
            canManageMarketing: false,
            canViewAnalytics: false,
            permissions: []
        }
    });


    const noSessionUserInformation = ref(true)

    // --- AUTH STATE REFS (Keep these separate from user object) ---
    const accessToken = ref(localStorage.getItem('accessToken') || null);
    const refreshToken = ref(localStorage.getItem('refreshToken') || null);
    const authStatus = ref('idle');
    // Correctly check localStorage key 'session_token'
    const isLoggedIn = computed(() => !!localStorage.getItem('session_token'));
    // ------------------------------------------------------------

    /* INITIALIZING */
    /* FUNCTIONS */


    /* ACTIONS */


    const getUser = async (userId) => {
        helper.devConsole(`FUNC getUser called with ID: ${userId}`);
        if (!userId) {
             helper.devConsole("getUser Error: No user ID provided.");
             return;
        }
        try{

            // --- REVERTED: Use POST /auth/userbyid as before ---
            // This seems to be the endpoint the backend expects, even if unusual for fetching
            const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/userbyid`, {
                user_id: userId // Send the ID in the request body
            });

            helper.devConsole("getUser API Response (POST /api/v1/auth/userbyid):", response);

            // --- KEEPING robust checking before accessing data ---
            if(response.data && response.data.success && response.data.data && response.data.data.user) {
                const userData = response.data.data.user;
                helper.devConsole("getUser: Raw userData received from API:", JSON.parse(JSON.stringify(userData)));

                // Create a temporary object with updated values
                const updatedUser = {
                    ...user.value, // Keep existing values initially
                    firstName: userData.firstName === "null" ? null : (userData.firstName || null), // Handle string "null"
                    lastName: userData.lastName === "null" ? null : (userData.lastName || null), // Handle string "null"
                    email: userData.email === "null" ? null : (userData.email || null), // Handle string "null"
                    img: userData.img || null,
                    extId: userData.extId || null,
                    id: userData._id || userId,
                    extAccessable: userData.externalAccess === true,
                    defaultKitchentable: userData.defaultKitchentable || null,
                    weight: userData.weight || null,
                    height: userData.height || null,
                    age: userData.age || null,
                    protDaily: userData.protDaily ? parseInt(userData.protDaily) : null,
                    kcalDaily: userData.kcalDaily ? parseInt(userData.kcalDaily) : null,
                    fatDaily: userData.fatDaily ? parseInt(userData.fatDaily) : null,
                    sex: userData.sex || null,
                    activness: userData.activness || null,
                    summDaily: userData.summDaily || null,
                    weekplanmode: userData.weekplanmode || null,
                    gdpr: userData.gdpr,
                    install_apple_app: userData.install_apple_app === true,
                    address: userData.address || user.value.address, // Keep existing address if new one is null
                    bookedAbo: userData.bookedAbo || user.value.bookedAbo, // Keep existing abo if new one is null
                    bookedAboUsage: userData.bookedAboUsage || user.value.bookedAboUsage, // Keep existing usage if new one is null
                    adminPermissions: userData.adminPermissions || user.value.adminPermissions, // KRITISCH: Admin-Permissions verarbeiten
                    pwa_install_prompts: userData.pwa_install_prompts || user.value.pwa_install_prompts // KRITISCH: PWA Install Prompts beibehalten
                };

                // Explicitly log values being checked by watcher
                helper.devConsole(`getUser: Prepared updated values - firstName: ${updatedUser.firstName}, email: ${updatedUser.email}`);

                // KRITISCH: Debug adminPermissions
                helper.devConsole(`🔑 getUser: adminPermissions from backend:`, userData.adminPermissions);
                helper.devConsole(`🔑 getUser: adminPermissions in updatedUser:`, updatedUser.adminPermissions);

                // Assign the whole object to trigger reactivity on the 'user' ref itself
                user.value = updatedUser;
                helper.devConsole("getUser: Assigned updatedUser object to user.value");
                helper.devConsole(`🔑 getUser: Final user.value.adminPermissions:`, user.value.adminPermissions);

            } else {
                helper.devConsole("getUser Error: API call succeeded but response structure invalid or success=false.", response.data);
                helper.devConsole("getUser: Throwing error due to invalid data structure.");
                throw new Error("Ungültige Benutzerdaten vom Server empfangen.");
            }
        } catch(err){
             helper.devConsole("getUser API Error caught:", err);
             throw err;
        }
    }

    const updateUserData = async () => {
        const token = localStorage.getItem('session_token');
        if (!token) {
             console.warn('updateUserData: No session_token in localStorage, cannot update.');
             return;
        }
        try {
            // Construct the required payload structure
            const payload = {
                firstName: user.value.firstName,
                lastName: user.value.lastName,
                email: user.value.email,
                address: user.value.address, // Send the whole address object
                externalAccess: user.value.extAccessable,
                // Add other fields managed by settingsCard that should be updatable
                // Example: weekplanmode, weight, height, age, sex, activness, gdpr if they are editable
                gdpr: user.value.gdpr,
                pwa_install_prompts: user.value.pwa_install_prompts, // KRITISCH: PWA Install Prompts senden
                // Include fields based on what settingsCard actually modifies
                // weight: user.value.weight,
                // height: user.value.height,
                // age: user.value.age,
                // sex: user.value.sex,
                // activness: user.value.activness,
                // weekplanmode: user.value.weekplanmode,
            };

            // Use POST and the correct endpoint /api/v1/auth/userdata/update
            await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/userdata/update`,
                { payload }, // Send data wrapped in { payload: ... }
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        // 'Content-Type': 'application/json' // Axios sets this automatically for objects
                    }
                }
            );
            console.log('User data update via POST /api/v1/auth/userdata/update successful.');
        } catch (error) {
            console.error('Error updating user data via POST /api/v1/auth/userdata/update:', error);
            // Handle error appropriately, maybe show notification
            setNotification(error.response?.data?.message || error.message || 'Fehler beim Speichern der Benutzerdaten.', 'alert');
        }
    }

    const changedExtAccess = async () => {
        console.warn('changedExtAccess action triggered, calling updateUserData.');
        await updateUserData();
    }

    const setAuthState = async (backend_token, session_token, user_id) => {
        helper.devConsole(`setAuthState called with session_token: ${session_token}`);
        if(user_id) {
            user.value.id = user_id; // Still set user ID if provided
        }
        // We don't store backend_token in the user object anymore
        // if(backend_token) { user.value.backendSessionToken = backend_token; }

        if(session_token) {
            localStorage.setItem('session_token', session_token); // CORRECT KEY
            helper.devConsole(`Stored session_token in localStorage: ${localStorage.getItem('session_token')}`);
        } else {
            // user.value.sessionToken = null; // REMOVED
            localStorage.removeItem('session_token'); // CORRECT KEY
        }
    }

    const login = async (credentials) => {
        helper.devConsole("login() called - Simulating API call!", credentials);
        authStatus.value = 'pending';
        try {
            await new Promise(resolve => setTimeout(resolve, 500));
            const simulatedSuccess = true;

            if (simulatedSuccess) {
                // Development-only dummy tokens - remove in production
                const newAccessToken = import.meta.env.VITE_ENV === 'development' ? 'dummy-access-token-from-login' : null;
                const newRefreshToken = import.meta.env.VITE_ENV === 'development' ? 'dummy-refresh-token-from-login' : null;
                const simulatedSessionToken = import.meta.env.VITE_ENV === 'development' ? 'dummy-session_token-from-login-ls-correct' : null;
                const simulatedUserId = 'simulated-user-id-ls-correct';

                accessToken.value = newAccessToken;
                refreshToken.value = newRefreshToken;
                localStorage.setItem('refreshToken', newRefreshToken);

                // Call setAuthState to store correct session_token key in localStorage
                await setAuthState(null, simulatedSessionToken, simulatedUserId);
                if (!user.value.id) user.value.id = simulatedUserId; // Set user ID directly if needed

                authStatus.value = 'success';
                helper.devConsole("Simulated Login Success. session_token stored in LS. Fetching profile...");
                await fetchUserProfile();
            } else {
                throw new Error("Simulated login failed by server.");
            }
        } catch (error) {
            helper.devConsole("Login error (simulated or real):", error);
            setNotification(error.message || "Login fehlgeschlagen.", "alert");
            authStatus.value = 'error';
            accessToken.value = null;
            refreshToken.value = null;
            // user.value.sessionToken = null; // REMOVED
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('accessToken');
            localStorage.removeItem('session_token'); // CORRECT KEY
        }
    };

    const setUser = async (payload) => {
        helper.devConsole(payload)
        if(payload.id) user.value.id = payload.id
        // if(payload.sessionToken) user.value.sessionToken = payload.sessionToken // REMOVED
        if(payload.firstName) user.value.firstName = payload.firstName
        if(payload.lastName) user.value.lastName = payload.lastName
        if(payload.email) user.value.email = payload.email
        if(payload.img) user.value.img = payload.img
        // if(payload.backendSessionToken) user.value.backendSessionToken = payload.backendSessionToken // REMOVED
        if(payload.extId) user.value.extId = payload.extId
        if(payload.extAccessable) user.value.extAccessable = payload.extAccessable
        if(payload.defaultKitchentable) user.value.defaultKitchentable = payload.defaultKitchentable
        if(payload.height) user.value.height = payload.height
        if(payload.weight) user.value.weight = payload.weight
        if(payload.age) user.value.age = payload.age
        if(payload.protDaily) user.value.protDaily = payload.protDaily
        if(payload.kcalDaily) user.value.kcalDaily = payload.kcalDaily
        if(payload.fatDaily) user.value.fatDaily = payload.fatDaily
        if(payload.sex) user.value.sex = payload.sex
        if(payload.activness) user.value.activness = payload.activness
        if(payload.summDaily) user.value.summDaily = payload.summDaily
        if(payload.weekplanmode) user.value.weekplanmode = payload.weekplanmode
        if(payload.gdpr) user.value.gdpr = payload.gdpr
        if(payload.install_apple_app) user.value.install_apple_app = payload.install_apple_app
        if(payload.mobileapp) user.value.mobileapp = payload.mobileapp
        if(payload.mobileapp_status) user.value.mobileapp_status = payload.mobileapp_status
        if(payload.address) user.value.address = payload.address
        if(payload.bookedAbo) user.value.bookedAbo = payload.bookedAbo
        if(payload.bookedAboUsage) user.value.bookedAboUsage = payload.bookedAboUsage

        // KRITISCH: Admin-Permissions verarbeiten
        if(payload.adminPermissions) {
            user.value.adminPermissions = payload.adminPermissions;
            console.log('🔑 Admin permissions set in userStore:', payload.adminPermissions);
        }
    }

    const clearUser = async () => {
        user.value.id = null,
        user.value.firstName = null,
        user.value.lastName = null,
        user.value.email = null,
        user.value.img = null,
        // user.value.backendSessionToken = null, // REMOVED
        user.value.extId = null
        user.value.defaultKitchentable = null
        user.value.height = null
        user.value.weight = null
        user.value.age = null
        user.value.protDaily = null
        user.value.kcalDaily = null
        user.value.fatDaily = null
        user.value.sex = null
        user.value.activness = null
        user.value.summDaily = null
        user.value.gdpr = null
        user.value.install_apple_app = null
        user.value.weekplanmode = null
        user.value.mobileapp = null
        user.value.mobileapp_status = null
        user.value.address = null
        user.value.bookedAbo = null
        user.value.bookedAboUsage = null
    }

    const logoutSession = async () => {
        helper.devConsole("inside logout Session func")
        try{
            const response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/logout');
            helper.devConsole(response)

            if(response.data.success){
                localStorage.removeItem('backend_session_token') // Keep removing potentially unused LS keys
                localStorage.removeItem('session_token'); // CORRECT KEY
                localStorage.removeItem('session_jwt')
                localStorage.removeItem('user_id')
                localStorage.removeItem('id')
                // store clear
                user.value.id = null;
                user.value.img = null;
                // user.value.sessionToken = null; // REMOVED
                user.value.firstName = null;
                user.value.lastName = null;
                user.value.email = null;
                router.push('/login')
            }

        } catch(err) {
            helper.devConsole(err)
        }
      }

      const closeTopNavigation = () => {
        helper.devConsole("inside closeHeadNavigation")
        openSettings.value = false
      }

    const fetchUserProfile = async () => {
        // Implementation of fetchUserProfile
    };

    // --- NEW ACTION to update specific profile details ---
    const updateUserProfileDetails = async (payload) => {
        helper.devConsole("Attempting to update profile details:", payload);
        const { firstName, lastName, email } = payload;

        if (!user.value.id) {
            setNotification("Benutzer nicht identifiziert. Update nicht möglich.", "alert");
            throw new Error("User ID not available for profile update.");
        }

        // Optimistic UI update (optional, but good for responsiveness)
        if (firstName !== undefined) user.value.firstName = firstName;
        if (lastName !== undefined) user.value.lastName = lastName;
        if (email !== undefined) user.value.email = email;

        try {
            const apiPayload = {
                firstName: firstName,
                lastName: lastName,
                email: email
            };

            const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/userdata/update`, {
                id: user.value.id,
                payload: apiPayload
            });

            if (response.data.success) {
                helper.devConsole("Profile details updated successfully on backend.");
                setNotification('Profil erfolgreich aktualisiert.', 'success');
                // Optionally re-fetch the full user profile to ensure consistency
                // await fetchUserProfile();
            } else {
                 helper.devConsole("Backend reported failure during profile update:", response.data.message);
                 setNotification(response.data.message || 'Profil konnte nicht aktualisiert werden (Backend).', 'alert');
                 // Revert optimistic update? Maybe not necessary if error is shown.
                 throw new Error(response.data.message || "Backend error during profile update.");
            }
        } catch (error) {
            helper.devConsole("Error updating profile details:", error);
            setNotification(error.response?.data?.message || error.message || 'Fehler beim Aktualisieren des Profils.', 'alert');
            // Revert optimistic update? Depends on desired behavior
            // Consider re-fetching profile on error to get the actual server state
            // await fetchUserProfile();
            throw error; // Re-throw error so the component knows it failed
        }
    };
    // --- END NEW ACTION ---

    // Updated logout action
    const logout = async (callBackend = true) => {
        helper.devConsole("Logging out (Setup Store)...", callBackend);
        const token = localStorage.getItem('session_token'); // CORRECT KEY

        accessToken.value = null;
        refreshToken.value = null;
        // user.value.sessionToken = null; // REMOVED
        // user.value.backendSessionToken = null; // REMOVED
        localStorage.removeItem('session_token'); // CORRECT KEY
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        user.value = { id: null, extId: null, firstName: null, lastName: null, email: null, img: null, extAccessable: false, defaultKitchentable: false, weekplanmode: 0, weight: 0, height: 0, age: 0, sex: 0, activness: 0, summDaily: 0, protDaily: 0, kcalDaily: 0, fatDaily: 0, gdpr: 0, install_apple_app: false, address: {}, bookedAbo: {}, bookedAboUsage: {}, adminPermissions: { isAdmin: false, canAccessBackend: false, canManagePinterest: false, canManageMarketing: false, canViewAnalytics: false, permissions: [] } };
        authStatus.value = 'idle';

        if (callBackend && token) {
            try {
                helper.devConsole("Calling backend logout endpoint...");
                await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/logout`, {
                    headers: { Authorization: `Bearer ${token}` } // Use correct key
                });
                helper.devConsole("Backend logout successful.");
            } catch (error) {
                helper.devConsole("Error calling backend logout (proceeding with local):", error);
            }
        }

        router.push('/login');
    };

    // Updated deleteAccount action
    const deleteAccount = async () => {
      console.log('Attempting to delete account via store...');
      const token = localStorage.getItem('session_token'); // CORRECT KEY
      console.log(`[deleteAccount] Checking localStorage session_token: ${token}`);
      if (!token) {
        throw new Error('Nicht eingeloggt (localStorage). Account kann nicht gelöscht werden.');
      }
      try {
        // Correct path to /api/v1/auth/me based on backend documentation
        await axios.delete(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/me`, {
          headers: { Authorization: `Bearer ${token}` } // Use correct key
        });
        console.log('Account deletion API call successful.');
        await logout(false);
      } catch (error) {
          console.error('API error during account deletion:', error);
          if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.message || `Serverfehler (${status})`;
            throw new Error(message);
          } else if (error.request) {
            throw new Error('Keine Antwort vom Server erhalten.');
          } else {
            throw new Error('Fehler bei der Vorbereitung der Account-Löschung.');
          }
      }
    };

    // Tries to initialize auth state from stored refresh token
    const initAuth = async () => {
        const storedRefreshToken = localStorage.getItem('refreshToken');
        if (storedRefreshToken) {
            helper.devConsole("Found refresh token in localStorage. Attempting refresh...");
            refreshToken.value = storedRefreshToken; // Update state ref

            console.log('Value of authStatus before assignment:', authStatus); // Log before assignment
            authStatus.value = 'pending'; // Line causing the error

            try {
                await refreshTokenFlow();
                 if(!accessToken.value){
                     throw new Error("Refresh token flow failed to get access token");
                 }
            } catch (error) {
                helper.devConsole("Initial refresh token flow failed:", error);
                await logout(false);
            }
        } else {
            helper.devConsole("No refresh token found. User is logged out.");
            authStatus.value = 'idle';
            accessToken.value = null;
            refreshToken.value = null;
        }
    };

    // Placeholder for the actual refresh token logic - Improved Simulation
    const refreshTokenFlow = async () => {
        helper.devConsole("refreshTokenFlow() called - Simulating API call!");
        if (!refreshToken.value) {
             helper.devConsole("refreshTokenFlow: No refresh token available.");
             throw new Error("No refresh token");
        }
        // authStatus is already 'pending' when called from initAuth
        try {
            // --- Simulate API Call ---
             await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network delay
             const simulatedSuccess = true; // Change to false to test failure

             if (simulatedSuccess) {
                 // Development-only dummy tokens - remove in production
                 const newAccessToken = import.meta.env.VITE_ENV === 'development' ? 'dummy-access-token-from-refresh' : null;
                 const newRefreshToken = import.meta.env.VITE_ENV === 'development' ? 'dummy-refresh-token-from-refresh' : null;

                 accessToken.value = newAccessToken;
                 // Update refresh token if backend sends a new one
                 refreshToken.value = newRefreshToken;
                 // localStorage.setItem('accessToken', newAccessToken); // Store if needed
                 localStorage.setItem('refreshToken', newRefreshToken);

                 authStatus.value = 'success';
                 helper.devConsole("Simulated Refresh Success. Fetching profile...");
                 await fetchUserProfile(); // Fetch profile after refresh
             } else {
                 throw new Error("Simulated refresh token invalid/expired.");
             }
            // --- End Simulation ---
        } catch (error) {
             helper.devConsole("refreshTokenFlow Error:", error);
             // Failure means logout
             await logout(false);
             throw error; // Re-throw so initAuth knows it failed
        }
    };

    // Listen for automatic usage data updates from axios interceptor
    const handleUsageDataUpdate = (event) => {
        const updatedUsageData = event.detail;
        if (updatedUsageData && user.value) {
            helper.devConsole('[UserStore] Automatically updating usage data:', updatedUsageData);
            user.value.bookedAboUsage = updatedUsageData.bookedAboUsage;
            user.value.bookedAbo = updatedUsageData.bookedAbo;
        }
    };

    // Setup event listener for usage data updates
    if (typeof window !== 'undefined') {
        window.addEventListener('usageDataUpdated', handleUsageDataUpdate);
    }

    /* RETURN Values and Actions */
    return {
        openSettings,
        user, // user object no longer contains session/backend tokens
        noSessionUserInformation,
        accessToken,
        refreshToken,
        authStatus,
        isLoggedIn, // Now computed based on localStorage

        // Actions
        getUser,
        changedExtAccess,
        updateUserData,
        setAuthState,
        login,
        setUser,
        clearUser,
        logoutSession,
        closeTopNavigation,
        fetchUserProfile,
        updateUserProfileDetails,
        logout,
        deleteAccount,
        initAuth,
        refreshTokenFlow
    };

});