<template>

    <!-- Modern Usage Dashboard -->
    <div class="max-w-4xl mx-auto mt-6 h-auto mb-16">
        <div class="relative">
            <!-- Header with gradient background -->
            <div class="relative bg-gradient-to-r from-ordypurple-100 to-ordypink-200 rounded-3xl p-6 mb-8 shadow-custom shadow-[#E0ADFF]">
                <h1 class="font-YesevaOne text-2xl text-white mb-2"><PERSON><PERSON></h1>
                <p class="text-white/80 text-sm">Übersicht deiner monatlichen Kontingente</p>

                <!-- Floating icon -->
                <div class="absolute top-4 right-4 bg-white/20 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
            </div>

            <!-- Usage Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="(usage, index) in aboStore.uiUsageData" :key="usage.name"
                     class="relative bg-white rounded-3xl p-6 shadow-custom shadow-gray-200/50 border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">

                    <!-- Card Header -->
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="font-YesevaOne text-lg text-gray-800 mb-1">{{ usage.displayName }}</h3>
                            <p class="text-xs text-gray-500">Monatliches Kontingent</p>
                        </div>

                        <!-- Usage Badge -->
                        <div class="bg-gradient-to-r from-ordypurple-100 to-ordypink-200 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            {{ usage.available }} verfügbar
                        </div>
                    </div>

                    <!-- Progress Ring or Bar -->
                    <div class="relative mb-4">
                        <!-- Modern Progress Bar -->
                        <div class="w-full h-3 bg-gray-100 rounded-full overflow-hidden">
                            <div
                                :style="{ width: `${usage.value}%` }"
                                class="h-full bg-gradient-to-r from-ordypurple-100 to-ordypink-200 rounded-full transition-all duration-700 ease-out"
                                :class="usage.value > 80 ? 'animate-pulse' : ''">
                            </div>
                        </div>

                        <!-- Progress Text -->
                        <div class="flex justify-between items-center mt-2">
                            <span class="text-sm font-semibold text-gray-700">{{ usage.used }}/{{ usage.total }}</span>
                            <span class="text-xs text-gray-500">{{ Math.round(usage.value) }}% genutzt</span>
                        </div>
                    </div>

                    <!-- Status Indicators -->
                    <div v-if="usage.available <= 2 && usage.available > 0"
                         class="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-2xl">
                        <div class="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-orange-700 font-medium">
                            Nur noch {{ usage.available }} {{ usage.available === 1 ? 'Nutzung' : 'Nutzungen' }} verfügbar
                        </span>
                    </div>

                    <div v-else-if="usage.available === 0"
                         class="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-2xl">
                        <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                        <span class="text-xs text-red-700 font-medium">
                            Kontingent aufgebraucht - Upgrade für mehr Nutzung
                        </span>
                    </div>

                    <div v-else-if="usage.available > usage.total * 0.5"
                         class="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-2xl">
                        <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span class="text-xs text-green-700 font-medium">
                            Ausreichend Guthaben verfügbar
                        </span>
                    </div>

                    <!-- Decorative Element -->
                    <div class="absolute top-4 right-4 w-8 h-8 bg-gradient-to-r from-ordypurple-100/10 to-ordypink-200/10 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modern Subscription Management Section -->
    <div class="max-w-4xl mx-auto">
        <!-- Section Header -->
        <div class="relative bg-gradient-to-r from-ordypurple-100 to-ordypink-200 rounded-3xl p-6 mb-8 shadow-custom shadow-[#E0ADFF]">
            <h1 class="font-YesevaOne text-2xl text-white mb-2">Abo-Verwaltung</h1>
            <p class="text-white/80 text-sm">Verwalte dein Abonnement und wähle das passende Paket</p>

            <!-- Management Button -->
            <div class="absolute top-4 right-4">
                <button @click.prevent="aboStore.goToCalcWindow()"
                        class="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 hover:scale-105 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Verwalten
                </button>
            </div>
        </div>

        <!-- Modern Carousel with enhanced styling -->
        <div class="relative">
            <carousel v-bind="settings" :model-value="2" :breakpoints="breakpoints" class="modern-carousel">
                <slide v-for="slide in aboStore.items" :key="slide" class="flex flex-row gap-4 px-2">
                    <aboCardItems class="carousel__item transform transition-all duration-300" :element="slide"></aboCardItems>
                </slide>

                <template #addons>
                    <navigation class="modern-navigation" />
                    <pagination class="modern-pagination" />
                </template>
            </carousel>
        </div>
    </div>

</template>
<script setup>
import { reactive, ref, toRefs, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../store/menuStore'
import { useUserStore  } from '../store/userStore';
import { useAboStore  } from '../store/aboStore';
import aboCardItems from '../components/aboCardItems.vue';
import clickShadowButton from '../components/clickShadowButton.vue'
// Special packages
import 'vue3-carousel/dist/carousel.css'
import { Carousel, Slide, Pagination, Navigation } from 'vue3-carousel'

const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const store = useMenuStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore()
const aboStore = useAboStore()

aboStore.setCorrectAbo()
aboStore.setAndCalcAboUsage()

// Refresh usage data when component mounts to get latest data
onMounted(() => {
    aboStore.refreshUsageData();
});

// CATCH REDIRECTS FROM STRIPE
if(route.query.success === "true"){
  setNotification('Das Abo wurde aktiviert', 'success')
  const updatedItems = aboStore.items.map(item => {
    if (item.type === Number(route.query.type)) {
        return { ...item, active: true }; // Setze active auf true für das gefundene Objekt
    } else {
        return { ...item, active: false }; // Setze active auf false für alle anderen
    }
  });
  console.log(updatedItems)
  aboStore.items = updatedItems
}

if(route.query.success === "false"){
  setNotification('Zahlung fehlgeschlagen', 'alert')
}
////////////////////////////

const settings = ref({
    wrapAround: true,
})

const breakpoints = ref({
    700: {
        itemsToShow: 1.5,
        transition:500,
      },
      // 1024 and up
    1224: {
        itemsToShow: 3,
        transition:500,
    },
})

const handleScroll = (event) => {
        const scrollLeft = event.target.scrollLeft;
      const childWidth = event.target.children[0].offsetWidth;
      const index = Math.round(scrollLeft / childWidth);
      currentIndex.value = Math.max(0, Math.min(index, this.items.length - 1));
}

</script>

<style scoped>
/* Modern Carousel Styling */
.modern-carousel .carousel__slide {
  padding: 8px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-carousel .carousel__viewport {
  perspective: 2000px;
  overflow: hidden;
  max-width: 100%;
}

.modern-carousel .carousel__track {
  transform-style: preserve-3d;
}

.modern-carousel .carousel__slide--sliding {
  transition: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-carousel .carousel__slide {
  opacity: 0.7;
  transform: rotateY(-15deg) scale(0.85) translateZ(-100px);
  filter: blur(1px);
}

.modern-carousel .carousel__slide--active ~ .carousel__slide {
  transform: rotateY(15deg) scale(0.85) translateZ(-100px);
}

.modern-carousel .carousel__slide--prev {
  opacity: 0.8;
  transform: rotateY(-8deg) scale(0.92) translateZ(-50px);
  filter: blur(0.5px);
}

.modern-carousel .carousel__slide--next {
  opacity: 0.8;
  transform: rotateY(8deg) scale(0.92) translateZ(-50px);
  filter: blur(0.5px);
}

.modern-carousel .carousel__slide--active {
  opacity: 1;
  transform: rotateY(0) scale(1) translateZ(0);
  filter: blur(0);
  z-index: 10;
}

/* Modern Navigation Styling */
.modern-navigation :deep(.carousel__nav) {
  background: linear-gradient(135deg, #A37DFF, #E0ADFF);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  box-shadow: 0 8px 25px rgba(163, 125, 255, 0.3);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.modern-navigation :deep(.carousel__nav:hover) {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(163, 125, 255, 0.4);
}

.modern-navigation :deep(.carousel__nav--disabled) {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Modern Pagination Styling */
.modern-pagination :deep(.carousel__pagination) {
  padding: 20px 0;
}

.modern-pagination :deep(.carousel__pagination-button) {
  background: rgba(163, 125, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.carousel__pagination-button--active) {
  background: linear-gradient(135deg, #A37DFF, #E0ADFF);
  transform: scale(1.3);
  box-shadow: 0 4px 15px rgba(163, 125, 255, 0.4);
}

.modern-pagination :deep(.carousel__pagination-button:hover) {
  background: rgba(163, 125, 255, 0.4);
  transform: scale(1.1);
}

/* Enhanced Card Hover Effects */
.carousel__item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-carousel {
  animation: fadeInUp 0.6s ease-out;
  max-width: 100%;
  overflow: hidden;
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .modern-carousel .carousel__slide {
    transform: scale(0.95);
    opacity: 0.8;
  }

  .modern-carousel .carousel__slide--active {
    transform: scale(1);
    opacity: 1;
  }

  .modern-navigation :deep(.carousel__nav) {
    width: 40px;
    height: 40px;
  }
}
</style>