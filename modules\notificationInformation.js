import {reactive, toRefs } from "vue";
import { useHelperStore } from "../utils/helper";

const state = reactive({
    notification: false,
    notificationText: '',
    notificationState: '',
    notificationLinkText: '',
    notificationLink: '',
    notificationDuration: 5000
})

//useConfirmation

export default function useNotification(){

    const helper = useHelperStore()

    const setNotification = async (text, status, linktext, linkdestination, visibleforsec) => {
        // helper.devConsole("notificationInformation: " + text) // Temporarily commented out for debugging
        try{
            state.notificationText = text;
            state.notificationState = status;
            state.notification = !state.notification;
            state.notificationLinkText = linktext;
            state.notificationLink = linkdestination;

            if(visibleforsec){
                state.notificationDuration = (visibleforsec*1000);
            }

            setTimeout(() => {
                state.notification = !state.notification;
            }, state.notificationDuration);

        } catch(e){
            helper.devConsole(e)
        }
    }

    return { ...toRefs(state), setNotification };
}