// Test script for Pinterest publishing directly
require('dotenv').config({ path: './config.env' });
const axios = require('axios');
const helper = require('./utils/helper');
const { pinterestService } = require('./controllers/publishController');

// Test video URL - replace with an actual video URL from your S3 bucket
const TEST_VIDEO_URL = process.env.TEST_VIDEO_URL || 'https://ordy-marketing-videos.s3.eu-central-1.amazonaws.com/sample-video.mp4';

// Function to test Pinterest publishing directly
async function testPinterestDirect() {
  console.log('Testing Pinterest publishing directly...');
  console.log('Environment variables:');
  console.log('- PINTEREST_ACCESS_TOKEN exists:', !!process.env.PINTEREST_ACCESS_TOKEN);
  console.log('- PINTEREST_BOARD_ID exists:', !!process.env.PINTEREST_BOARD_ID);
  console.log('- PINTEREST_BOARD_ID value:', process.env.PINTEREST_BOARD_ID);
  
  try {
    // Call the Pinterest service directly
    const result = await pinterestService.publishVideo(
      TEST_VIDEO_URL,
      'Test Recipe Title',
      'This is a test description for the Pinterest pin.'
    );
    
    console.log('Result:', result);
    
    if (result.success) {
      console.log('Successfully published to Pinterest!');
      console.log('Pin ID:', result.platformPostId);
    } else {
      console.log('Failed to publish to Pinterest:', result.error);
    }
  } catch (error) {
    console.error('Error testing Pinterest publishing:', error);
  }
}

// Run the test
testPinterestDirect();
