const express = require('express');
const menuController = require('../controllers/menuController');
const menuchildController = require('../controllers/menuchildController');
const weekplanController = require('../controllers/weekplanController');
const gptController = require('../controllers/gptController');
//const menuRelationController = require('../controllers/menuRelationController');
const authController = require('../controllers/authController');
//const AppError = require('../utils/appError')

const router = express.Router();


router
.route('/one/:weekplanid')
.get(authController.verify, weekplanController.getWeekplan, authController.sendanswer)
.post(authController.verify, weekplanController.createWeekplan, authController.sendanswer)
.delete(authController.verify, weekplanController.deleteWeekplan, authController.sendanswer)

router
.route('/many')
.post(authController.verify, weekplanController.searchWeekplans, authController.sendanswer) // search for Weekplany by userid in date range

router
.route('/add/one/menu/:menuchildid/checkifmenuchildexists')
.post(authController.verify, menuchildController.createAndAddOneMenuchildToMenu, gptController.createCalculationOfNutrions, weekplanController.addOneMenu, authController.sendanswer)


module.exports = router;