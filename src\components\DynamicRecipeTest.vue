<template>
  <div class="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
    <h2 class="text-2xl font-bold mb-6">Test: Dynamische Rezeptanweisungen</h2>
    
    <!-- Personenanzahl Eingabe -->
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">Personenanzahl:</label>
      <input 
        v-model.number="currentPersons" 
        type="number" 
        min="1" 
        max="20" 
        class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <span class="ml-2 text-sm text-gray-600">
        (Original: {{ originalPersons }} Personen)
      </span>
    </div>

    <!-- Test-Zutaten -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3">Zutaten (skaliert):</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div v-for="(ingredient, index) in scaledIngredients" :key="index" class="flex items-center space-x-2">
          <span class="font-medium">{{ ingredient.scaledAmount }}</span>
          <span>{{ ingredient.unit.name }}</span>
          <span>{{ ingredient.name.name }}</span>
        </div>
      </div>
    </div>

    <!-- Test-Anweisungen -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-3">Zubereitungsschritte (mit dynamischen Mengen):</h3>
      <div class="space-y-4">
        <div v-for="(step, index) in testSteps" :key="index" class="border-l-4 border-blue-500 pl-4">
          <h4 class="font-semibold text-gray-800">{{ step.head }}</h4>
          <div class="mt-2">
            <p class="text-sm text-gray-600 mb-1">Original:</p>
            <p class="text-gray-700 bg-gray-50 p-2 rounded">{{ step.content }}</p>
            <p class="text-sm text-gray-600 mb-1 mt-3">Verarbeitet:</p>
            <p class="text-gray-900 bg-green-50 p-2 rounded" v-html="processedContent(step.content)"></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Platzhalter-Referenz -->
    <div class="bg-blue-50 p-4 rounded-lg">
      <h3 class="text-lg font-semibold mb-3">Verfügbare Platzhalter:</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div v-for="(ingredient, index) in testIngredients" :key="index" class="flex items-center space-x-2">
          <code class="bg-blue-100 px-2 py-1 rounded">${{ generateKey(ingredient.name.name) }}</code>
          <span>→ {{ ingredient.name.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { processInstructionText, generateIngredientKey } from '../utils/recipeUtils'

// Test-Daten
const currentPersons = ref(4)
const originalPersons = ref(2)

const testIngredients = ref([
  {
    amount: 250,
    unit: { name: 'g' },
    name: { name: 'Mehl' }
  },
  {
    amount: 3,
    unit: { name: 'EL' },
    name: { name: 'Olivenöl' }
  },
  {
    amount: 2,
    unit: { name: 'Stück' },
    name: { name: 'Zwiebeln' }
  },
  {
    amount: 500,
    unit: { name: 'ml' },
    name: { name: 'Gemüsebrühe' }
  },
  {
    amount: 1,
    unit: { name: 'TL' },
    name: { name: 'Salz' }
  },
  {
    amount: 0.5,
    unit: { name: 'TL' },
    name: { name: 'Pfeffer' }
  }
])

const testSteps = ref([
  {
    head: 'Schritt 1: Vorbereitung',
    content: 'Geben Sie ${MEHL} in eine große Schüssel und formen Sie eine Mulde in der Mitte.'
  },
  {
    head: 'Schritt 2: Anbraten',
    content: 'Erhitzen Sie ${MENGE:OLIVENOEL} EL Olivenöl in einer Pfanne und braten Sie ${ZWIEBELN} glasig an.'
  },
  {
    head: 'Schritt 3: Ablöschen',
    content: 'Gießen Sie ${GEMUESEBRUEHE} dazu und lassen Sie alles 10 Minuten köcheln.'
  },
  {
    head: 'Schritt 4: Würzen',
    content: 'Mit ${SALZ} und ${PFEFFER} abschmecken. Bei Bedarf noch etwas ${MENGE:SALZ} TL Salz hinzufügen.'
  }
])

// Computed: Skalierte Zutaten
const scaledIngredients = computed(() => {
  const scaleFactor = currentPersons.value / originalPersons.value
  return testIngredients.value.map(ingredient => ({
    ...ingredient,
    scaledAmount: Math.round((ingredient.amount * scaleFactor) * 10) / 10
  }))
})

// Funktion: Verarbeitete Anweisungen
const processedContent = (instructionText) => {
  return processInstructionText(
    instructionText,
    testIngredients.value,
    currentPersons.value,
    originalPersons.value
  ).replace(/\n/g, '<br>')
}

// Funktion: Schlüssel generieren
const generateKey = (ingredientName) => {
  return generateIngredientKey(ingredientName)
}
</script>

<style scoped>
code {
  font-family: 'Courier New', monospace;
}
</style>
