<template>
    <div v-if="userstore.user.gdpr == 0 && route.name !== 'login'" class="w-full z-50 fixed md:h-48 h-64 mb-0 bottom-0 bg-black px-10 py-5">
        <div class="w-full md:w-3/4 mx-auto">
            <p class="text-white">Möchten Sie die Datenschutzbestimmungen akzeptieren?</p>
            <p class="text-white text-xs mt-2">
                Wir nutzen Cookies und ähnliche Technologien für verschiedene Zwecke, auch um das Nutzererlebnis ständig zu verbessern und für Analytics. Sie können alles Akzeptieren oder Ablehnen. Lies <a class="underline" target="_blank" href="https://k-innovations.ch/datenschutzerklaerung-der-k-innovations-gmbh/">hier</a> mehr.</p>
            <div class="w-full h-auto gap-5 flex flex-row -mt-3">
                <click-button @click="setGdpr(1)" :element="{'buttondescription': 'Alle ablehenen', 'active': false, 'iconneeded': false}" :index="1"  />
                <click-button @click="setGdpr(2)" :element="{'buttondescription': 'Alle akzeptieren', 'active': false, 'iconneeded': false}" :index="1"  />
            </div>
        </div>
    </div>
        
</template>
<script setup>
    import clickButton from '../components/clickBorderButton.vue'
    import clickShadowButton from '../components/clickShadowButton.vue'
    import { ref, reactive } from 'vue'
    import { useUserStore } from '../store/userStore';
    import { useRoute } from 'vue-router';

    const userstore = useUserStore();
    const route = useRoute(); // Den aktuellen Routen-Namen abrufen

    const setGdpr = (value) => {
        userstore.user.gdpr = value
        if(userstore.user.id){
            userstore.changedExtAccess()
        }
    }


</script>