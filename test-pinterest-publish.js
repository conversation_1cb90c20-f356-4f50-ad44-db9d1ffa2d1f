// Test script for Pinterest publishing
const axios = require('axios');
require('dotenv').config();

// Get the API token from environment variables or use a default test token
const API_TOKEN = process.env.TEST_API_TOKEN || 'your-test-token';

// Function to test the /publish/now endpoint
async function testPinterestPublish() {
  try {
    console.log('Testing Pinterest publishing...');
    
    // Call the /publish/now endpoint
    const response = await axios.post(
      'http://localhost:3000/api/v1/marketing/publish/now',
      {},
      {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`
        }
      }
    );
    
    console.log('Response:', JSON.stringify(response.data, null, 2));
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing Pinterest publishing:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  }
}

// Run the test
testPinterestPublish();
