<template>
  <!-- Column Builder-->
  <div class="flex md:flex-row flex-col min-h-screen px-6">

    <!-- Middle Container -->
    <div class="md:w-3/4 md:pr-12 w-full">
      <!-- Head-->
      <GoBack />
      <div class="w-full flex md:flex-row flex-col mt-10">
        <h1 class="w-full md:w-9/12 h-auto pb-3">{{ weekplanstore.oneWeekplanReciept.menuId.name }}</h1>
        <div class="w-3/12 flex flex-row mb-4">


          <div class="w-6/12 min-w-[40px] max-w-[41px] my-auto">
            <img
              @click="weekplanstore.deleteFromWeekplan(weekplanstore.oneWeekplanReciept._id)"
              src="../../assets/icons/delete.png"
              class="w-1/2"
            />
          </div>
          <span class="text-xs my-auto">Aus dem Wochenplan entfernen</span>
          <div class="w-3/12 my-auto float-right invisible md:visible">
          <svg width="55" class="text float-right justify-end" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
        </div>
      </div>
      <p class="md:w-1/2 w-full">{{ weekplanstore.oneWeekplanReciept.menuId.description }}</p>
      <!-- Head-->

      <!-- HeaderImage -->
      <div
        class="rounded-2xl antialiased bg-cover h-52 w-full mt-8 mb-8 bg-center"
        :style="{ 'background-image': 'url(' + weekplanstore.oneWeekplanReciept.menuId.imagelink + ')' }"

      >
      </div>
      <!-- HeaderImage -->

      <!-- ContentBlock -->
      <div class="flex md:flex-row flex-col">
        <!-- Left Inner Col -->
        <div class="w-full md:w-3/12">
          <h2>Personen</h2>
          <p class="mt-1">{{ weekplanstore.oneWeekplanReciept.menuId.menuchilds.numberOfPersons }} Personen</p>
          <br />
          <h2>Zeit</h2>
          <p class="mt-1">{{ weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.cookingTime }} min</p>
          <br />
          <h2>Zutaten</h2>
          <p class="mt-1">Alle Zutaten in der Übersicht</p>
          <div v-for="(ingredient, index) in weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.ingredients" :key="index">
            <span class="w-full flex flex-row">
              <p class="w-auto mt-1">{{ ingredient.amount }} {{ ingredient.unit.name }} {{ ingredient.name.name }}</p>
            </span>
          </div>
        </div>
        <!-- Left Inner Col -->
        <!-- Left Inner Col -->
        <div class="w-full md:w-9/12 mt-12 md:mt-0 pb-32">
          <div class="w-full flex flex-row">
            <h2 class="w-11/12">Zubereitung</h2>
            <div class="w-1/12">
              <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
                <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
                <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
              </svg>
            </div>
          </div>
          <div class="mt-6 w-full h-auto">
            <div v-for="(step, index) in weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.preperation" :key="index">
              <h3 class="w-full font-bold mt-3">{{ step.head }}</h3>
              <p class="w-full h-auto mt-1" v-html="processedInstructionContent(step.content)"></p>
            </div>
          </div>
        </div>
        <!-- Left Inner Col -->
      </div>
      <!-- ContentBlock -->
    </div>


    <!-- Right Container -->
    <div class="md:w-1/4 w-full md:p-10 md:mt-12 mt-2 pb-12">
       <h2>Nährwerte</h2>
       <div v-for="(nutritions, index) in weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.nutritions" :key="index">
        <div class="flex flex-row">
          <p>{{ nutritions.name }}:&nbsp;</p>
          <p>{{ nutritions.amount }}&nbsp;</p>
          <p>{{ nutritions.unit }}</p>
        </div>
       </div>
    </div>

  </div>

</template>
<script setup>
import { reactive, ref, computed, watchEffect } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../../components/menuCard.vue'
import dayCard from '../../components/dayCard.vue'
import useNotification from '../../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../../store/menuStore'
import { useUserStore } from '../../store/userStore'
import { useWeekplanStore } from '../../store/weekplanStore'
import GoBack from '../../body/goBack.vue';
import { processInstructionText } from '../../utils/recipeUtils'

const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const menustore = useMenuStore();
const weekplanstore = useWeekplanStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore();

// Computed function for processing instruction content with dynamic quantities
const processedInstructionContent = (instructionText) => {
  if (!instructionText || !weekplanstore.oneWeekplanReciept?.menuId?.menuchilds?.menuChildId) {
    return instructionText;
  }

  const ingredients = weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.ingredients || [];
  const currentPersons = weekplanstore.oneWeekplanReciept.menuId.menuchilds.numberOfPersons || 1;
  const originalPersons = weekplanstore.oneWeekplanReciept.menuId.menuchilds.menuChildId.seatCount || currentPersons;

  // Process the instruction text with dynamic quantities
  const processedText = processInstructionText(
    instructionText,
    ingredients,
    currentPersons,
    originalPersons
  );

  // Convert line breaks to HTML for proper display
  return processedText.replace(/\n/g, '<br>');
};

weekplanstore.getOneMenue(route.params.id)

</script>