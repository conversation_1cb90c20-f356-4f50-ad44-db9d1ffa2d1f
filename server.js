const dotenv = require('dotenv');
dotenv.config({ path: './config.env' }); // Load config first!

// import mongoose from 'mongoose'; // Removed unused import
// import ngrok from 'ngrok'; // Removed unused import (only used in commented code)
const process = require('process');
// import AppError from './utils/appError.js'; // Removed unused import
// import globalErrorHandler from './controllers/errorController.js'; // Removed unused import
const { connection1 } = require('./db');
const app = require('./app');

process.on('uncaughtException', err => {
  console.log('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  console.log(err.stack);
  process.exit(1);
});

const port = process.env.PORT || 8080;
// const nodenv = process.env.NODE_ENV; // Commented out as only used in ngrok block
// const addr = 8080; // Commented out as only used in ngrok block

//ngrok nicht aktiv
const server = app.listen(port, () => {
  console.log(`App running on port ${port}...`);
});


//ngrok aktiv
/* 
const nodenv = process.env.NODE_ENV; // Define inside comment block if needed
const addr = 8080; // Define inside comment block if needed
const ngrok = require('ngrok');
const server = app.listen(port, async () => {
  console.log(`Server is running on http://localhost:${port}`);

  if (nodenv !== 'production') { 
    const url = await ngrok.connect({
      proto: "http",
      addr: addr,
      //authtoken: process.env.NGROK_AUTH_TOKEN,
      region: 'eu', // oder 'us', 'ap', 'au', 'sa', 'jp', 'in'
    });
    console.log(`ngrok tunnel opened at: ${url} and port ${addr}`);
  }
});
*/

process.on('unhandledRejection', err => {
  console.log('UNHANDLED REJECTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  console.log(err.stack);
  server.close(() => {
    process.exit(1);
  });
});

process.on('SIGTERM', () => {
  console.log('👋 SIGTERM RECEIVED. Shutting down gracefully');
  server.close(() => {
    console.log('💥 Process terminated!');
  });
});

module.exports = { connection1 }; // Reverted to module.exports