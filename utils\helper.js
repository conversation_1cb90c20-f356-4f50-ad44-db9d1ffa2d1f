const express = require('express');

const devConsole = (value) => {
    let envMode = process.env.NODE_ENV;
    if (envMode === "development" || envMode === "preview") {
        // Erzeugen eines neuen Fehlers und Stack Trace analysieren.
        const error = new Error();
        const stack = error.stack.split('\n')[2];
        const match = stack.match(/\(([^)]+)\)/);
        const location = match ? match[1] : 'unknown location';

        console.log(`[${location}]`, value);
    }
    return '';
};

// Export the function
module.exports = { devConsole };

// Create a global fallback to prevent crashes
if (typeof global !== 'undefined') {
    global.helper = global.helper || {};
    global.helper.devConsole = devConsole;
}

// Also create a global helper object for direct access
global.helper = global.helper || {};
global.helper.devConsole = devConsole;