import { defineStore } from 'pinia';
import { reactive, toRefs, ref } from 'vue';

export const useHelperStore = defineStore('helper', () => {
    // helper functions
    const devConsole = async(...args) => {
        if(import.meta.env.VITE_ENV === "development" || import.meta.env.VITE_ENV === "preview"){
            // Erzeugen eines neuen Fehlers und vollständigen Stack Trace analysieren
            const error = new Error();
            const stackArray = error.stack.split('\n');
            let location = 'unknown location';

            // Durchsuchen des Stack Traces, bis die erste Zeile gefunden wird, die nicht aus 'helper.js' oder 'node_modules' stammt
            for (let i = 2; i < stackArray.length; i++) {
                const stack = stackArray[i].trim();
                if (!stack.includes('node_modules') && !stack.includes('helper.js')) {
                    const match = stack.match(/\(([^)]+)\)/);
                    // Fallback if no parentheses match (e.g., anonymous functions)
                    location = match ? match[1] : stack.replace(/^at\s+/, '').trim(); 
                    // Remove potential line/column numbers after the path
                    location = location.replace(/:\d+:\d+$/, ''); 
                    break;
                }
            }
            // Use console.log to allow multiple arguments and object inspection
            console.log(`[${location}]`, ...args); 
        }
    }

    return {
        // EXPORTET VALUES
        devConsole
    }
})

////////////////////////////////////////////////////////

// Promise-based Confirmation Store
export const useConfirmationStore = defineStore('confirmations', () => {
    const helper = useHelperStore();

    const confirmations = ref({
        showState: false,
        confirmationText: '',
        confirmationState: '', // e.g., 'alert', 'success' (for styling)
        resolvePromise: null, // Function to resolve the promise
    });

    // Returns a promise that resolves with true (confirm) or false (cancel)
    const showConfirmation = (text, stateType = 'alert') => {
        helper.devConsole("showConfirmation called in useConfirmationStore");
        return new Promise((resolve) => { // Only need resolve for true/false
            confirmations.value.confirmationText = text;
            confirmations.value.confirmationState = stateType;
            confirmations.value.resolvePromise = resolve; // Store the resolve function
            confirmations.value.showState = true; // Make the dialog visible
            helper.devConsole("Confirmation state set, promise created.");
        });
    };

    const confirm = () => {
        helper.devConsole("confirm action in useConfirmationStore - resolving promise with true");
        if (typeof confirmations.value.resolvePromise === 'function') {
            confirmations.value.resolvePromise(true); // Resolve the promise with true
        }
        reset();
    };

    const cancel = () => {
        helper.devConsole("cancel action in useConfirmationStore - resolving promise with false");
        if (typeof confirmations.value.resolvePromise === 'function') {
            confirmations.value.resolvePromise(false); // Resolve the promise with false
        }
        reset();
    };

    const reset = () => {
        helper.devConsole("reset action in useConfirmationStore");
        confirmations.value.showState = false;
        confirmations.value.confirmationText = '';
        confirmations.value.confirmationState = '';
        confirmations.value.resolvePromise = null; // Clear the resolve function
    };

    return {
        confirmations,
        showConfirmation, // Expose the new function
        confirm,        // Still needed for the component buttons
        cancel          // Still needed for the component buttons
    };
});

///////////////////////////////////////////////////////////////////////////
