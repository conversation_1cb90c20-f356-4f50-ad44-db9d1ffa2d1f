<template>
  <div
    class="shopping-list-item-card bg-white rounded-xl shadow-md p-3 transition-all duration-200 ease-in-out flex flex-col justify-center items-center text-center"
    @click="handleClick"
    :class="[
      getCategoryColorClass(item.category),
      isReadOnly
        ? 'cursor-default opacity-75'
        : 'cursor-pointer hover:shadow-lg hover:scale-[1.03]'
    ]"
  >
    <div v-if="item.is_purchased" class="absolute top-0 right-0 w-full h-full pointer-events-none">
      <div class="bg-green-100 bg-opacity-30 w-full h-full absolute rounded-xl"></div>
      <span class="text-green-600 text-sm absolute top-1 right-1 bg-white bg-opacity-70 rounded-full w-5 h-5 flex items-center justify-center">✓</span>
    </div>
    <p class="font-medium text-sm text-gray-800 leading-tight mb-1 line-clamp-2 relative z-10" :title="item.name">{{ item.name }}</p>
    <p class="text-xs text-gray-500 relative z-10">{{ item.quantity }} {{ item.unit }}</p>
  </div>
</template>

<script setup>
// defineProps and defineEmits are compiler macros and don't need to be imported.
// import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isReadOnly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['toggle-purchase']);

const handleClick = () => {
  // Nur emittieren wenn nicht read-only
  if (!props.isReadOnly) {
    // Emit the event to the parent (ZettelView)
    // The parent decides the new purchase status based on the current one
    emit('toggle-purchase');
  }
};

// Funktion zur Bestimmung der Kategorie-Farbe
const getCategoryColorClass = (category) => {
  const categoryColors = {
    'Gemüse & Früchte': 'category-vegetables',
    'Brotwaren & Backwaren': 'category-bakery',
    'Milchprodukte & Molkereiprodukte': 'category-dairy',
    'Fleisch, Wurst & Fisch': 'category-meat',
    'Tiefkühlprodukte': 'category-frozen',
    'Grundnahrungsmittel': 'category-staples',
    'Frühstück & Cerealien': 'category-breakfast',
    'Süsswaren & Snacks': 'category-sweets',
    'Getränke': 'category-drinks',
    'Non-Food & Haushaltsartikel': 'category-nonfood',
    'Sonstiges': 'category-other'
  };

  return categoryColors[category] || 'category-other';
};
</script>

<style scoped>
.shopping-list-item-card {
  min-height: 90px; /* Slightly increased min-height */
  /* aspect-ratio: 1 / 1; Removed for more flexibility */
  word-wrap: break-word;
  position: relative;
  border-top: 3px solid transparent;
}
/* Limit item name to 2 lines with ellipsis */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Kategorie-Farben */
.category-vegetables {
  border-top-color: #4ade80; /* Grün */
}
.category-bakery {
  border-top-color: #f59e0b; /* Orange-Braun */
}
.category-dairy {
  border-top-color: #60a5fa; /* Hellblau */
}
.category-meat {
  border-top-color: #ef4444; /* Rot */
}
.category-frozen {
  border-top-color: #3b82f6; /* Blau */
}
.category-staples {
  border-top-color: #a78bfa; /* Lila */
}
.category-breakfast {
  border-top-color: #fbbf24; /* Gelb */
}
.category-sweets {
  border-top-color: #ec4899; /* Pink */
}
.category-drinks {
  border-top-color: #06b6d4; /* Türkis */
}
.category-nonfood {
  border-top-color: #6b7280; /* Grau */
}
.category-other {
  border-top-color: #9ca3af; /* Hellgrau */
}
</style>