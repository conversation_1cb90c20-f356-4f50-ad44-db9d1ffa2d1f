import { defineStore } from 'pinia';
import { ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import { useUserStore } from './userStore';
import { useWeekplanStore } from './weekplanStore';
import { useGrocerylistStore } from './grocerylistStore';
import { useHelperStore, useConfirmationStore } from '../../utils/helper';
import axios from 'axios';

export const useKitchentableStore = defineStore('kitchentable', () => {

    const { setNotification } = useNotification();
    const userstore = useUserStore()
    const weekplanstore = useWeekplanStore();
    const groceryliststore = useGrocerylistStore();
    const helper = useHelperStore();
    const confirmationStore = useConfirmationStore();

    /* INITIALIZING VALUES */

    // Kitchentables

    const kitchentable = ref({
        id: null,
        tableAddress_street: null, 
        tableAddress_plztown: null, 
        tableAddress_country: null, 
        members: null,
        createdAt: null
    });

    const kitchentableForm = ref({
        tableAddress_street: null, 
        tableAddress_plztown: null, 
        tableAddress_country: null, 
        userId: null
    });

    const availableKitchenTableList = ref([]);

    // Search persons

    const availablePersons = ref();

    const kitchentablePerson = ref("")

    // Controlles Kitchentable Overview view true/false
    const openKitchentableSelecter = ref(false)

    /* INITIALIZING */
    /* FUNCTIONS */
    

    /* ACTIONS */

    const deletePerson = async(index) => {
        try {
            const tableId = kitchentable.value.id;
            const memberToDelete = kitchentable.value.members[index]; // Get the full member object
            const memberIdToDelete = memberToDelete?.userId?._id;
            const memberName = `${memberToDelete?.userId?.firstName || ''} ${memberToDelete?.userId?.lastName || ''}`.trim() || 'Diesen Benutzer';

            if (!tableId || !memberIdToDelete) {
                console.error("Missing tableId or memberIdToDelete for deletion.", { tableId, memberIdToDelete });
                setNotification('Fehler: Küchentisch- oder Benutzer-ID nicht gefunden.', 'alert');
                return;
            }

            // --- Confirmation Dialog --- 
            const confirmed = await confirmationStore.showConfirmation(
                'Benutzer entfernen?',
                `Möchten Sie ${memberIdToDelete === userstore.user.id ? 'sich selbst' : `"${memberName}"`} wirklich von diesem Küchentisch entfernen?`
            );

            if (!confirmed) {
                helper.devConsole("User cancelled member removal.");
                return; // Stop if user cancels
            }
            // --- End Confirmation --- 

            const deleteUrl = `${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${tableId}/members/${memberIdToDelete}`;
            helper.devConsole("Attempting to delete member with URL:", deleteUrl);

            const response = await axios.delete(deleteUrl);

            if(response.data.success) {
                const selfRemoved = memberIdToDelete === userstore.user.id;
                if (selfRemoved) {
                    setNotification('Du hast den Küchentisch verlassen.', 'success');
                    await getKitchenTableList();
                    if (availableKitchenTableList.value.length > 0) {
                        helper.devConsole("Setting first available table as active after leaving.");
                        await setKitchenTable(0);
                    } else {
                        helper.devConsole("No kitchentables left after leaving. Clearing state.");
                        kitchentable.value = { id: null, tableAddress_street: null, tableAddress_plztown: null, tableAddress_country: null, members: null, createdAt: null };
                        userstore.user.defaultKitchentable = null;
                        try {
                            await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/userdata/update', { id: userstore.user.id, payload: { defaultKitchentable: null } });
                            helper.devConsole("User default kitchentable set to null in DB.");
                        } catch (updateErr) {
                            helper.devConsole("Error setting user default kitchentable to null:", updateErr);
                        }
                    }
                } else {
                    // Update local member list optimistically BEFORE potential reload
                    kitchentable.value.members.splice(index, 1);
                    setNotification('Benutzer erfolgreich entfernt.', 'success');
                    // Reload list to be sure (might be redundant but safe)
                    await getKitchenTableList(); 
                }
            } else {
                setNotification(response.data.message || 'Fehler beim Entfernen des Benutzers (Backend).', 'alert');
            }
        } catch(err) {
            helper.devConsole(err);
            const errorMessage = err.response?.data?.message || err.message || 'Ein unerwarteter Fehler ist aufgetreten.';
            setNotification(`Fehler: ${errorMessage}`, 'alert'); // Changed to 'alert' for errors
        }
    }

    const setPerson = async(index) => {
        //console.log(index)
        
        let payload = {
                'role': "member",
                'user': availablePersons.value[index]._id
        }

        try{
            const tableId = kitchentable.value.id;
            if (!tableId) {
                 console.error("Missing tableId for adding member.");
                setNotification('Fehler: Küchentisch-ID nicht gefunden.', 'error');
                return;
            }

            // Construct the new PATCH URL: /api/v1/kitchentable/:id/members
            const patchUrl = `${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${tableId}/members`;

            helper.devConsole("Attempting to patch member with URL:", patchUrl, "Payload:", payload);
            
            // Send PATCH request to the new URL with the existing payload
            const response = await axios.patch(patchUrl, payload);
            
            // Keep existing response handling logic
            console.log(response.data.data.data) // TODO: Check if this response structure is still valid
            if(response.data.data.data.length == 0){
                //response.data.text
                setNotification(response.data.data.text, 'alert')
            } else {
                //console.log("else")
                //response.data.text
                //const response2 = await axios.get(import.meta.env.VITE_API_BASE_URL + '/kitchentable/all/related/' + userstore.user.id);
                //console.log(response2)
                kitchentable.value.members = response.data.data.data.members
                //console.log(response.data.data.data)
                setNotification(response.data.data.text, 'success')
            }
            
            // set ui clean
            availablePersons.value = null;
            kitchentablePerson.value = ""

        } catch(err){
            helper.devConsole(err)
        }

    }

    const searchForPersons = async() => {

        //check if any person is set
        if(kitchentablePerson.value != ""){
            try{

                helper.devConsole("GET USER kitchentable.bysearchstring")
                
                // get and set user after hard reload
                const response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/userdata/bysearchstring/' + kitchentablePerson.value);
                //console.log(response)
                if(response.data.success){
                    availablePersons.value = response.data.data
                }

                if(kitchentablePerson.value == ''){
                    availablePersons.value = null;
                }
                

            } catch(err){
                helper.devConsole(err)
            }
        }
    }

    const getKitchenTableList = async () => {
        try{
            const userId = userstore.user.id;
            if (!userId) {
                helper.devConsole("User ID not available, cannot fetch kitchentable list.");
                availableKitchenTableList.value = [];
                return; 
            }

            const response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/kitchentable/all/related/' + userId);
            
            // Reverted path check and added more robust checks
            if(response.data.success && response.data.data && Array.isArray(response.data.data.kitchentables)){
                helper.devConsole("Kitchen table list fetched successfully.");
                helper.devConsole('>>> Received Kitchentable List:', response.data.data.kitchentables); // Log received list
                availableKitchenTableList.value = response.data.data.kitchentables; // Assign the array from data.kitchentables
            } else {
                 helper.devConsole("Failed to fetch kitchentable list or invalid data structure (expected response.data.data.kitchentables).");
                 console.warn("API Response: ", response.data); // Log the actual response for debugging
                 availableKitchenTableList.value = []; 
            }
        } catch(err){
            helper.devConsole("Error fetching kitchentable list:", err);
            availableKitchenTableList.value = []; 
        }
    }

    const setKitchenTable = async (index) =>{
        if (index < 0 || index >= availableKitchenTableList.value.length) {
            console.error("Invalid index provided to setKitchenTable:", index);
            setNotification('Fehler: Ungültige Tischauswahl.', 'error');
            return;
        }

        const selectedTable = availableKitchenTableList.value[index];

        // --- Update local state immediately --- 
        kitchentable.value.id = selectedTable._id;
        kitchentable.value.tableAddress_street = selectedTable.tableAddress_street;
        kitchentable.value.tableAddress_plztown = selectedTable.tableAddress_plztown;
        kitchentable.value.tableAddress_country = selectedTable.tableAddress_country;
        kitchentable.value.members = selectedTable.members;
        kitchentable.value.createdAt = selectedTable.createdAt; // Use createdAt if available
        // --- End Immediate Update --- 

        // Clear weekplan and grocerylist
        weekplanstore.weekplanmenu = []
        weekplanstore.date = [];
        weekplanstore.formattedDate = []
        
        groceryliststore.groceryList = [];
        groceryliststore.groceryObject = {};
        groceryliststore.groceryManualInput = ""

        // Update user default in vue store
        userstore.user.defaultKitchentable = selectedTable._id
        
        // update user default value in backend DB
        const payload = {
            defaultKitchentable: selectedTable._id
        }

        try {
            const response2 = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/userdata/update', {
                id: userstore.user.id,
                payload
            });

            if(response2.data.success){
                helper.devConsole("User default kitchentable updated successfully in DB.");
                openKitchentableSelecter.value = false; // Close dropdown on success
            } else {
                 helper.devConsole("Failed to update user default kitchentable in DB.", response2.data);
                 setNotification('Fehler beim Speichern der Tischauswahl.', 'error');
            }
        } catch (err) {
             helper.devConsole("Error updating user default kitchentable:", err);
             setNotification('Fehler beim Speichern der Tischauswahl.', 'error');
        }
    }

    
    const getKitchenTable = async () => {
        try{

            //console.log("userStore.getKitchenTable")

            // /api/v1/kitchentable/:id
            
            // get and set user after hard reload
            const response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/kitchentable/' + userstore.user.defaultKitchentable);
            //console.log(response)
            if(response.data.success){
                kitchentable.value.id = response.data.data._id
                kitchentable.value.tableAddress_street = response.data.data.tableAddress_street
                kitchentable.value.tableAddress_plztown = response.data.data.tableAddress_plztown
                kitchentable.value.tableAddress_country = response.data.data.tableAddress_country
                kitchentable.value.members = response.data.data.members
                kitchentable.value.createddate = response.data.data.createdAt
            }

        } catch(err){
            helper.devConsole(err)
        }
    }


    const createKitchenTable = async () => {
        try{

            //console.log("userStore.createKitchenTable")

            // add userId to kitchentableForm
            kitchentableForm.value.userId = userstore.user.id

            // get and set user after hard reload
            const response = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/kitchentable/' + userstore.user.extId, {
                data: kitchentableForm.value
            });

            if(response.data.success){

                // send message
                setNotification('Neuer Küchentisch wurde erstellt', 'success')
                
                // Data Settings
                availableKitchenTableList.value.push(response.data.data)
                
                // UI Settings - Correctly reset the form object properties
                kitchentableForm.value.tableAddress_street = null;
                kitchentableForm.value.tableAddress_plztown = null;
                kitchentableForm.value.tableAddress_country = null;
                kitchentableForm.value.userId = null; // Reset userId as well
                
                openKitchentableSelecter.value = true
            } else {
                console.log("else")
            }

        } catch(err){
            helper.devConsole(err)
            //console.log(err)
        }
    }


    const deleteKitchentable = async (id) => {
        helper.devConsole(`Attempting to delete kitchentable with ID: ${id}`);
        if (!id) {
             setNotification('Fehler: Keine Küchentisch-ID zum Löschen angegeben.', 'alert');
             return;
        }

        try {
            // Find table details for confirmation message and check if it was default
            const tableToDelete = availableKitchenTableList.value.find(table => table._id === id);
            const tableName = tableToDelete ? 
                `${tableToDelete.tableAddress_street || 'Unbekannte Straße'}, ${tableToDelete.tableAddress_plztown || 'Unbekannter Ort'}` : 
                'Diesen Küchentisch'; 
            const wasDefaultTable = userstore.user.defaultKitchentable === id;

            // --- Confirmation Dialog --- 
            const confirmed = await confirmationStore.showConfirmation(
                'Küchentisch löschen?',
                `Möchten Sie ${tableName !== 'Diesen Küchentisch' ? `den Küchentisch "${tableName}"`: 'diesen Küchentisch'} wirklich dauerhaft löschen? Alle zugehörigen Daten (Wochenpläne, Einkaufszettel) gehen verloren.`
            );

            if (!confirmed) {
                 helper.devConsole("User cancelled kitchentable deletion.");
                return; // Stop if user cancels
            }
            // --- End Confirmation ---

            helper.devConsole(`User confirmed deletion. Proceeding with API call for ID: ${id}`);
            const response = await axios.delete(`${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${id}`);

            if (response.data.success) {
                setNotification('Küchentisch erfolgreich gelöscht', 'success');
                
                // Update available list by filtering out the deleted table
                availableKitchenTableList.value = availableKitchenTableList.value.filter(table => table._id !== id);
                helper.devConsole("Available kitchentable list updated:", availableKitchenTableList.value);

                // --- Handle Default Kitchentable Logic --- 
                if (wasDefaultTable) {
                    helper.devConsole("Deleted table was the default kitchentable.");
                    if (availableKitchenTableList.value.length > 0) {
                        // Set the first remaining table as the new default
                        helper.devConsole("Setting first available table as new default.");
                        await setKitchenTable(0); 
                    } else {
                        // No tables left, clear the default locally and on backend
                        helper.devConsole("No kitchentables left. Clearing default kitchentable.");
                        kitchentable.value = { id: null, tableAddress_street: null, tableAddress_plztown: null, tableAddress_country: null, members: null, createdAt: null }; // Clear local current table state
                        userstore.user.defaultKitchentable = null; // Clear default in local user store
                        try {
                            await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/userdata/update', { 
                                id: userstore.user.id, 
                                payload: { defaultKitchentable: null } 
                            });
                            helper.devConsole("User default kitchentable set to null in DB.");
                        } catch (updateErr) {
                            helper.devConsole("Error setting user default kitchentable to null in DB:", updateErr);
                            setNotification('Standard-Küchentisch konnte auf Server nicht zurückgesetzt werden.', 'alert');
                        }
                    }
                } else if (kitchentable.value.id === id) {
                     // If the deleted table wasn't the default, but was the currently viewed one, clear the view
                     helper.devConsole("Deleted table was the currently viewed table (but not default). Clearing view.");
                     kitchentable.value = { id: null, tableAddress_street: null, tableAddress_plztown: null, tableAddress_country: null, members: null, createdAt: null };
                }
                 // --- End Default Kitchentable Logic --- 

            } else {
                setNotification(response.data.message || 'Küchentisch konnte nicht gelöscht werden (Backend).', 'alert');
            }
        } catch (err) {
            helper.devConsole("Error deleting kitchentable:", err);
            const errorMessage = err.response?.data?.message || err.message || 'Ein unerwarteter Fehler ist aufgetreten.';
            setNotification(`Fehler: ${errorMessage}`, 'alert'); 
        }
    }

    return {
        kitchentable,
        availableKitchenTableList,
        kitchentableForm,
        openKitchentableSelecter,
        kitchentablePerson,
        availablePersons,
        deletePerson,
        setPerson,
        searchForPersons,
        getKitchenTableList,
        setKitchenTable,
        getKitchenTable,
        createKitchenTable,
        deleteKitchentable
    }

});