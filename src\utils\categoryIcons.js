/**
 * Utility für Kategorie-Icons
 * Ordnet jeder Einkaufslisten-Kategorie ein passendes Icon zu
 */

/**
 * Gibt das passende Icon für eine Kategorie zurück
 * @param {string} category - Die Kategorie
 * @returns {string} SVG-Icon als String
 */
export const getCategoryIcon = (category) => {
  const icons = {
    'Gemüse & Früchte': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`,
    
    'Brotwaren & Backwaren': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15.546c-.523 0-.969-.143-1.25-.781a1.5 1.5 0 00-1.25-.781c-.523 0-.969.143-1.25.781-.281.638-.727.781-1.25.781s-.969-.143-1.25-.781a1.5 1.5 0 00-1.25-.781c-.523 0-.969.143-1.25.781-.281.638-.727.781-1.25.781s-.969-.143-1.25-.781a1.5 1.5 0 00-1.25-.781c-.523 0-.969.143-1.25.781C4.969 15.403 4.523 15.546 4 15.546V18a2 2 0 002 2h12a2 2 0 002-2v-2.454z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15.546V6a2 2 0 012-2h12a2 2 0 012 2v9.546" />
      </svg>`,
    
    'Milchprodukte & Molkereiprodukte': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>`,
    
    'Fleisch, Wurst & Fisch': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a4 4 0 100 8m0-8a4 4 0 110 8m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v4m6-6v-2m0 2a2 2 0 100 4m0-4a2 2 0 100-4m0 4v4" />
      </svg>`,
    
    'Tiefkühlprodukte': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v8m-4-4h8" />
      </svg>`,
    
    'Grundnahrungsmittel': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
      </svg>`,
    
    'Frühstück & Cerealien': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`,
    
    'Süsswaren & Snacks': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>`,
    
    'Getränke': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>`,
    
    'Non-Food & Haushaltsartikel': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>`,
    
    'Sonstiges': `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>`
  };

  return icons[category] || icons['Sonstiges'];
};

/**
 * Gibt die passende Farbe für eine Kategorie zurück
 * @param {string} category - Die Kategorie
 * @returns {string} Tailwind CSS Klasse für die Farbe
 */
export const getCategoryColor = (category) => {
  const colors = {
    'Gemüse & Früchte': 'text-green-600',
    'Brotwaren & Backwaren': 'text-yellow-600',
    'Milchprodukte & Molkereiprodukte': 'text-blue-600',
    'Fleisch, Wurst & Fisch': 'text-red-600',
    'Tiefkühlprodukte': 'text-cyan-600',
    'Grundnahrungsmittel': 'text-amber-600',
    'Frühstück & Cerealien': 'text-orange-600',
    'Süsswaren & Snacks': 'text-pink-600',
    'Getränke': 'text-indigo-600',
    'Non-Food & Haushaltsartikel': 'text-gray-600',
    'Sonstiges': 'text-gray-500'
  };

  return colors[category] || colors['Sonstiges'];
};

/**
 * Gibt die passende Hintergrundfarbe für eine Kategorie zurück
 * @param {string} category - Die Kategorie
 * @returns {string} Tailwind CSS Klasse für die Hintergrundfarbe
 */
export const getCategoryBgColor = (category) => {
  const colors = {
    'Gemüse & Früchte': 'bg-green-100',
    'Brotwaren & Backwaren': 'bg-yellow-100',
    'Milchprodukte & Molkereiprodukte': 'bg-blue-100',
    'Fleisch, Wurst & Fisch': 'bg-red-100',
    'Tiefkühlprodukte': 'bg-cyan-100',
    'Grundnahrungsmittel': 'bg-amber-100',
    'Frühstück & Cerealien': 'bg-orange-100',
    'Süsswaren & Snacks': 'bg-pink-100',
    'Getränke': 'bg-indigo-100',
    'Non-Food & Haushaltsartikel': 'bg-gray-100',
    'Sonstiges': 'bg-gray-50'
  };

  return colors[category] || colors['Sonstiges'];
};

/**
 * Erstellt ein vollständiges Icon-Element für eine Kategorie
 * @param {string} category - Die Kategorie
 * @param {string} additionalClasses - Zusätzliche CSS-Klassen
 * @returns {string} Vollständiges Icon-HTML
 */
export const createCategoryIcon = (category, additionalClasses = '') => {
  const icon = getCategoryIcon(category);
  const color = getCategoryColor(category);
  
  return icon.replace('class="h-5 w-5"', `class="h-5 w-5 ${color} ${additionalClasses}"`);
};

/**
 * Alle verfügbaren Kategorien mit ihren Icons
 * @returns {Array} Array von Kategorie-Objekten
 */
export const getAllCategories = () => {
  return [
    { name: 'Gemüse & Früchte', icon: getCategoryIcon('Gemüse & Früchte'), color: getCategoryColor('Gemüse & Früchte') },
    { name: 'Brotwaren & Backwaren', icon: getCategoryIcon('Brotwaren & Backwaren'), color: getCategoryColor('Brotwaren & Backwaren') },
    { name: 'Milchprodukte & Molkereiprodukte', icon: getCategoryIcon('Milchprodukte & Molkereiprodukte'), color: getCategoryColor('Milchprodukte & Molkereiprodukte') },
    { name: 'Fleisch, Wurst & Fisch', icon: getCategoryIcon('Fleisch, Wurst & Fisch'), color: getCategoryColor('Fleisch, Wurst & Fisch') },
    { name: 'Tiefkühlprodukte', icon: getCategoryIcon('Tiefkühlprodukte'), color: getCategoryColor('Tiefkühlprodukte') },
    { name: 'Grundnahrungsmittel', icon: getCategoryIcon('Grundnahrungsmittel'), color: getCategoryColor('Grundnahrungsmittel') },
    { name: 'Frühstück & Cerealien', icon: getCategoryIcon('Frühstück & Cerealien'), color: getCategoryColor('Frühstück & Cerealien') },
    { name: 'Süsswaren & Snacks', icon: getCategoryIcon('Süsswaren & Snacks'), color: getCategoryColor('Süsswaren & Snacks') },
    { name: 'Getränke', icon: getCategoryIcon('Getränke'), color: getCategoryColor('Getränke') },
    { name: 'Non-Food & Haushaltsartikel', icon: getCategoryIcon('Non-Food & Haushaltsartikel'), color: getCategoryColor('Non-Food & Haushaltsartikel') },
    { name: 'Sonstiges', icon: getCategoryIcon('Sonstiges'), color: getCategoryColor('Sonstiges') }
  ];
};
