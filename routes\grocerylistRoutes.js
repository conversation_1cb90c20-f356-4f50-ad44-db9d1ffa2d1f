const express = require('express');
const grocerylistController = require('../controllers/grocerylistController');
const authController = require('../controllers/authController');
const unitController = require('../controllers/unitController');
const AppError = require('../utils/appError')

const router = express.Router();

router
.route('/one/:id')
.post(authController.verify, grocerylistController.createGrocerylist, authController.sendanswer)
.get(authController.verify, grocerylistController.getOneGrocerylist, authController.sendanswer)
.patch(authController.verify, grocerylistController.updateGrocerylist, authController.sendanswer)
.delete(authController.verify, grocerylistController.deleteGrocerylist, authController.sendanswer)

// add and delete from grocerylist
router
.route('/one/:id/grocerylistupdate')
.post(authController.verify, grocerylistController.updateGrocerylistItems, authController.sendanswer)

router
.route('/all/:kitchentableId')
.get(authController.verify, grocerylistController.getAllGrocerylistsByKitchentableId, authController.sendanswer)

router
.route('/all/grocerys/bykitchentableid')
.post(authController.verify, grocerylistController.getAllGrocerysByKitchentableId, authController.sendanswer)

// Search for individual grocery/ingredient by name
router
.route('/search/grocery/:name')
.get(authController.verify, grocerylistController.searchGroceryByName, authController.sendanswer)

/*
router
.route('/many/weekplan/:weekplanid')
.get(authController.verify, grocerylistController.loadGroceryListItemByUserId, authController.sendanswer)

router
.route('/search/many')
.post(authController.verify, grocerylistController.searchGroceryListItems, authController.sendanswer) // search for Grocerylist by userid in date range

router
.route('/many')
.post(authController.verify, grocerylistController.saveManyGroceryListItems, authController.sendanswer)
*/
module.exports = router;