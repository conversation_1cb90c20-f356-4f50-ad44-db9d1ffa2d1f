<template>
  <div v-if="showAgreement" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-hidden">
    <div class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
      <div class="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
        <h2 class="text-lg font-semibold">Allgemeine Geschäftsbedingungen (AGB)</h2>
      </div>
      
      <div class="p-4 overflow-y-auto flex-1">
        <agb></agb>
      </div>
      
      <div class="p-4 border-t bg-white sticky bottom-0">
        <div class="flex flex-col sm:flex-row justify-end gap-4">
          <button 
            @click="decline" 
            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100"
          >
            A<PERSON>hnen
          </button>
          <button 
            @click="accept" 
            class="px-4 py-2 bg-ordypurple-100 text-white rounded-lg hover:bg-ordypurple-200"
          >
            Akzeptieren
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../store/userStore';
import agb from './agb.vue';
import useNotification from '../../modules/notificationInformation';

const router = useRouter();
const userStore = useUserStore();
const { setNotification } = useNotification();

// Computed property to determine if we should show the agreement
const showAgreement = computed(() => {
  // Show if user is logged in but hasn't accepted the AGB (gdpr value is not 2)
  return userStore.user.id && userStore.user.gdpr !== 2;
});

// Function to handle acceptance
const accept = async () => {
  try {
    // Update user's gdpr status to 2 (accepted)
    userStore.user.gdpr = 2;
    
    // Save the updated user data to the server
    await userStore.updateUserData();
    
    // Show success notification
    setNotification('AGB erfolgreich akzeptiert.', 'success');
  } catch (error) {
    console.error('Error accepting AGB:', error);
    setNotification('Fehler beim Akzeptieren der AGB. Bitte versuchen Sie es erneut.', 'alert');
  }
};

// Function to handle decline
const decline = () => {
  // Log the user out if they decline
  userStore.logout();
  
  // Show notification
  setNotification('Sie müssen die AGB akzeptieren, um die Anwendung nutzen zu können.', 'alert');
  
  // Redirect to login page
  router.push('/login');
};
</script>

<style scoped>
/* No additional styles needed as we're using Tailwind classes */
</style>
