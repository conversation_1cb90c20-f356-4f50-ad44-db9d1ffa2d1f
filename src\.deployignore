# Development and testing files
node_modules/
.git/
.github/
.vscode/
.cursor/
.idea/
*.log
*.test.js
*.spec.js
tests/
test/
coverage/
.nyc_output/

# Environment files
.env*
*.env

# Build artifacts (keep dist for production)
.cache/
.temp/
tmp/
temp/
dev-dist/

# Documentation
*.md
docs/
doku/

# Media files
*.mp4
*.mp3
*.wav
*.avi
*.mov
*.webm

# Archive files
*.zip
*.tar.gz
*.tgz
*.rar

# OS files
.DS_Store
Thumbs.db
desktop.ini

# IDE files
*.swp
*.swo
*~

# Playwright
playwright-report/
test-results/

# Development configuration files
vite.config.mjs
tailwind.config.js
playwright.config.js

# Source maps in production (optional)
*.map
