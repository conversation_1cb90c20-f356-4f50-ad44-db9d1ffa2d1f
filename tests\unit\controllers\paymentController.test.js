// tests/unit/controllers/paymentController.test.js
const request = require('supertest');
const app = require('../../../app');
const User = require('../../../models/userModel');
const { setupTestDb, cleanupTestDb } = require('../../setup/testDb');

describe('PaymentController', () => {
  beforeAll(async () => {
    await setupTestDb();
  });

  afterAll(async () => {
    await cleanupTestDb();
  });

  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/v1/creator/checkout', () => {
    it('should return 500 instead of 401 for Stripe errors', async () => {
      // Create test user
      const user = await User.create({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        sessionToken: 'valid-token'
      });

      // Mock Stripe error
      jest.spyOn(require('stripe'), 'checkout').mockRejectedValue(
        new Error('Stripe API Error')
      );

      const response = await request(app)
        .post('/api/v1/creator/checkout')
        .set('Authorization', `Bearer ${user.sessionToken}`)
        .send({ aboId: 2 });

      expect(response.status).toBe(500);
      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Abo-Änderung');
    });

    it('should successfully process subscription upgrade', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        sessionToken: 'valid-token',
        bookedAbo: { type: 1 }
      });

      // Mock successful Stripe response
      jest.spyOn(require('stripe').checkout.sessions, 'create').mockResolvedValue({
        id: 'cs_test_123',
        url: 'https://checkout.stripe.com/pay/cs_test_123'
      });

      const response = await request(app)
        .post('/api/v1/creator/checkout')
        .set('Authorization', `Bearer ${user.sessionToken}`)
        .send({ aboId: 2 });

      expect(response.status).toBe(200);
      expect(response.body.data.url).toContain('checkout.stripe.com');
    });
  });

  describe('POST /api/v1/creator/assistant/session/start', () => {
    it('should check realtime API license before allowing session', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        sessionToken: 'valid-token',
        bookedAbo: {
          nrRealtimeApiCalls: 10
        },
        bookedAboUsage: {
          nrRealtimeApiCalls: 5,
          nrRealtimeApiCalls_active: true
        }
      });

      const response = await request(app)
        .post('/api/v1/creator/assistant/session/start')
        .set('Authorization', `Bearer ${user.sessionToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should return 402 when realtime API limit exceeded', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        sessionToken: 'valid-token',
        bookedAbo: {
          nrRealtimeApiCalls: 10
        },
        bookedAboUsage: {
          nrRealtimeApiCalls: 10,
          nrRealtimeApiCalls_active: true
        }
      });

      const response = await request(app)
        .post('/api/v1/creator/assistant/session/start')
        .set('Authorization', `Bearer ${user.sessionToken}`);

      expect(response.status).toBe(402);
      expect(response.body.message).toContain('Lizenz aufgebraucht');
    });
  });

  describe('Stripe Webhook Processing', () => {
    it('should update user subscription with realtime API fields', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        stripeCustomerId: 'cus_test_123'
      });

      const webhookPayload = {
        type: 'checkout.session.completed',
        data: {
          object: {
            customer: 'cus_test_123',
            metadata: {
              aboId: '2'
            }
          }
        }
      };

      // Mock Stripe product metadata
      jest.spyOn(require('stripe').products, 'retrieve').mockResolvedValue({
        metadata: {
          nrMenucreationCalls: '23',
          nrCookeasyCalls: '23',
          nrMenuuploadCalls: '10',
          nrRealtimeApiCalls: '25'
        }
      });

      const response = await request(app)
        .post('/api/v1/creator/stripewebhook')
        .send(webhookPayload);

      expect(response.status).toBe(200);

      const updatedUser = await User.findById(user._id);
      expect(updatedUser.bookedAbo.nrRealtimeApiCalls).toBe(25);
      expect(updatedUser.bookedAboUsage.nrRealtimeApiCalls).toBe(0);
    });
  });
});
