/**
 * Einfacher Legacy-Test ohne Umgebungsvariablen
 * Erstellt ein Test-Rezept über die API
 */

const axios = require('axios');

// Test-Daten für Legacy-Rezept
const legacyRecipeData = {
  menu: {
    name: "Legacy Test Rezept",
    description: "Test für Legacy-Migration",
    imagelink: " ",
    freeAccess: false
  },
  menuchild: {
    seatCount: 2,
    cookingTime: 30,
    isStandard: true,
    // ❌ LEGACY: Ingredients ohne StableIDs
    ingredients: [
      {
        amount: "250",  // ❌ String statt Number
        unit: { name: "g" },
        name: { name: "<PERSON><PERSON>" }
        // ❌ Fehlende stableId
      },
      {
        amount: "2",    // ❌ String statt Number  
        unit: { name: "-" },
        name: { name: "<PERSON><PERSON>" }
        // ❌ Fehlende stableId
      }
    ],
    preperation: [
      {
        head: "Teig zubereiten",
        content: "Mehl und Eier vermengen"
      }
    ],
    nutritions: [
      {
        name: "<PERSON><PERSON>",
        amount: "15",  // ❌ String statt Number
        unit: "g"
      }
    ]
  }
};

async function createLegacyTestRecipe() {
  try {
    console.log('🧪 Creating legacy test recipe via API...');
    
    // E<PERSON>elle das Rezept über die API
    const response = await axios.post('http://localhost:8080/api/v1/menu/one', legacyRecipeData, {
      headers: {
        'Content-Type': 'application/json',
        // Hier müssten Sie Ihren Auth-Token einfügen
        'Authorization': 'Bearer YOUR_TOKEN_HERE'
      }
    });
    
    console.log('✅ Legacy test recipe created!');
    console.log('📋 Recipe ID:', response.data.menu._id);
    console.log('🔗 Test URL:', `http://localhost:5174/kochbuch/menu/${response.data.menu._id}`);
    
    return response.data;
    
  } catch (error) {
    console.error('❌ Failed to create test recipe:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Führe Test aus
createLegacyTestRecipe();
