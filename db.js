const mongoose = require('mongoose');
// const dotenv = require('dotenv').config(); // Removed, config is loaded in server.js

let DB = {};
DB.dev = process.env.DATABASE_DEV.replace(
  '<PASSWORD>',
  process.env.DATABASE_DEV_PASSWORD
);
DB.prd = process.env.DATABASE_PRD.replace(
  '<PASSWORD>',
  process.env.DATABASE_PRD_PASSWORD
);

// Löschen der veralteten 'Peax' Konfiguration
// PEAX CONNECT
// DB.devPeax = process.env.PEAX_DB_DEV.replace(
//  '<PASSWORD>',
//  process.env.PEAX_DB_DEV_PASSWORD
// );

// CONNECTIONSTRING
// DB.dev = process.env.DB_DEV.replace('<PASSWORD>', process.env.DB_PASSWORD);
// DB.prod = process.env.DB_PROD.replace('<PASSWORD>', process.env.DB_PASSWORD); // REMOVED - Redundant assignment, DB.prd defined above

// let connectString1;
// let connectString2; // Entfernt, da devPeax entfernt wurde

let connectString;

switch (process.env.NODE_ENV) {
  case 'development':
    connectString = DB.dev;
    break;
  // Entfernen der auskommentierten Zeile
  // case 'development-peax':
  //  connectString2 = DB.devPeax; // Erneut auskommentiert
  //  break;
  case 'production':
    connectString = DB.prd;
    break;
  case 'preview': // Add case for preview environment
    connectString = DB.dev; // Use development DB for preview
    break;
  default:
    throw new Error(`Unsupported environment: ${process.env.NODE_ENV}`);
}

// erste DB verbindung
if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview') {
  console.log("set db prd data to string | vor erstem connect to db");
}
const connection1 = mongoose.createConnection(connectString, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 30000 // Increased timeout to 30 seconds
});
if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview') {
  console.log("set db prd data to string | nach erstem connect to db");
}

connection1.on('connected', () => {
  console.log('Connection 1 (Primary DB) successful!');
});

connection1.on('error', (err) => {
  console.error('Connection 1 error:', err.message);
});

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'preview') {
  console.log("set db prd data to string | end of db.js");
}

module.exports = { connection1 }; // Nur noch connection1 exportieren