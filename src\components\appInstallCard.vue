<template>
    <div v-if="isVisible" class="w-11/12 h-auto md:w-1/3 fixed z-50 ml-auto mr-auto left-0 right-0 bottom-36 text-center">
      <button @click="hide" class="absolute right-0 mr-3 md:mr-5 md:mt-2 mt-0 p-4 md:p-3"></button>
      <img class="w-full h-auto z-50" src="../assets/img/appinstall_info.svg" alt="Install Ordy as App">
    </div>
  </template>

  <script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useUserStore  } from '../store/userStore';

  const userStore = useUserStore()

  const isVisible = ref(false);
  const displayCount = ref(0);
  const maxDisplays = 3;
  let displayInterval;

  const isIOSDevice = () => {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent) && !window.MSStream;
  };

  const show = () => {
    isVisible.value = true;
    displayCount.value++;

    // Initialize pwa_install_prompts if not exists
    if (!userStore.user.pwa_install_prompts) {
      userStore.user.pwa_install_prompts = {
        count: 0,
        disabled: false,
        last_shown: null
      };
    }

    // Update user tracking
    userStore.user.pwa_install_prompts.count++;
    userStore.user.pwa_install_prompts.last_shown = new Date();

    console.log('📱 PWA Install Card shown, count:', userStore.user.pwa_install_prompts.count);

    userStore.updateUserData();
  };

  const hide = () => {
    isVisible.value = false;
  };
    /*
    userStore.updateUserData()"
    v-model="userStore.user.lastName"
    */
  onMounted(() => {
    console.log('🔍 PWA Install Card mounted, checking conditions:', {
      disabled: userStore.user.pwa_install_prompts?.disabled,
      count: userStore.user.pwa_install_prompts?.count,
      max_count: userStore.user.pwa_install_prompts?.max_count,
      install_apple_app: userStore.user.install_apple_app,
      isIOS: isIOSDevice()
    });

    // Check if PWA install prompts are disabled or max count reached
    if (userStore.user.pwa_install_prompts?.disabled ||
        userStore.user.pwa_install_prompts?.count >= 3) { // Hardcoded 3 statt max_count
      console.log('❌ PWA Install Card: Disabled or max count reached');
      return;
    }

    if (userStore.user.install_apple_app === false) {
      if (isIOSDevice()) {
        // Check if enough time has passed since last prompt (at least 1 hour)
        const lastShown = userStore.user.pwa_install_prompts?.last_shown;
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

        if (lastShown && new Date(lastShown) > oneHourAgo) {
          console.log('⏰ PWA Install Card: Too soon to show again');
          return; // Too soon to show again
        }

        console.log('✅ PWA Install Card: Starting display interval');
        displayInterval = setInterval(() => {
          if (displayCount.value < maxDisplays &&
              userStore.user.pwa_install_prompts?.count < 3) { // Hardcoded 3
            show();
          } else {
            // Disable further prompts
            console.log('🚫 PWA Install Card: Disabling further prompts');
            if (!userStore.user.pwa_install_prompts) {
              userStore.user.pwa_install_prompts = { count: 0, disabled: false, last_shown: null };
            }
            userStore.user.pwa_install_prompts.disabled = true;
            userStore.updateUserData();
            clearInterval(displayInterval);
          }
        }, 4 * 60 * 1000);
      }
    }
  });

  onUnmounted(() => {
    clearInterval(displayInterval);
  });
  </script>

  <style scoped>
  /* Füge deine CSS-Stile hier hinzu */
  </style>