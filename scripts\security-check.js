#!/usr/bin/env node

/**
 * Frontend Security Check Script
 * 
 * This script performs comprehensive security checks for the Vue.js frontend:
 * 1. Scans for hardcoded secrets and API keys
 * 2. Checks for console.log statements in production builds
 * 3. Validates environment configuration
 * 4. Checks for test files in production bundle
 * 5. Validates frontend security practices
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Starting frontend security check...');

let hasIssues = false;

// Function to scan file for potential security issues
function scanFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return [];
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // Check for hardcoded secrets
    const secretPatterns = [
      /password\s*[:=]\s*['"][^'"]+['"]/i,
      /secret\s*[:=]\s*['"][^'"]+['"]/i,
      /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
      /token\s*[:=]\s*['"][^'"]+['"]/i,
      /localhost:\d+/i // Hardcoded localhost URLs
    ];
    
    secretPatterns.forEach(pattern => {
      if (pattern.test(line) && !line.includes('import.meta.env') && !line.includes('process.env')) {
        issues.push({
          type: 'HARDCODED_SECRET',
          line: lineNum,
          content: line.trim(),
          severity: 'HIGH'
        });
      }
    });
    
    // Check for console.log in production files
    if (process.env.VITE_ENV === 'production') {
      if (/console\.(log|warn|info)/.test(line) && !line.includes('//') && !line.includes('/*')) {
        issues.push({
          type: 'CONSOLE_LOG',
          line: lineNum,
          content: line.trim(),
          severity: 'MEDIUM'
        });
      }
    }
    
    // Check for localStorage usage without error handling
    if (/localStorage\.(getItem|setItem)/.test(line) && !line.includes('try') && !line.includes('catch')) {
      issues.push({
        type: 'UNSAFE_LOCALSTORAGE',
        line: lineNum,
        content: line.trim(),
        severity: 'LOW'
      });
    }
    
    // Check for eval() usage
    if (/eval\s*\(/.test(line)) {
      issues.push({
        type: 'EVAL_USAGE',
        line: lineNum,
        content: line.trim(),
        severity: 'HIGH'
      });
    }
    
    // Check for innerHTML usage (potential XSS)
    if (/innerHTML\s*=/.test(line) && !line.includes('DOMPurify')) {
      issues.push({
        type: 'POTENTIAL_XSS',
        line: lineNum,
        content: line.trim(),
        severity: 'MEDIUM'
      });
    }
  });
  
  return issues;
}

// Function to recursively scan directory
function scanDirectory(dirPath, extensions = ['.js', '.vue', '.ts']) {
  if (!fs.existsSync(dirPath)) {
    return [];
  }
  
  let allIssues = [];
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', 'dist', '.git', '.vscode', 'coverage'].includes(item)) {
        allIssues = allIssues.concat(scanDirectory(fullPath, extensions));
      }
    } else if (stat.isFile()) {
      const ext = path.extname(fullPath);
      if (extensions.includes(ext)) {
        const issues = scanFile(fullPath);
        if (issues.length > 0) {
          allIssues.push({
            file: fullPath,
            issues: issues
          });
        }
      }
    }
  }
  
  return allIssues;
}

// Check for test files in production directories
function checkForTestFiles() {
  console.log('🔍 Checking for test files in production bundle...');
  
  const testPatterns = [
    '*.test.js',
    '*.spec.js',
    '*-test.js',
    '*-debug.js'
  ];
  
  const productionDirs = ['./dist'];
  let foundTestFiles = [];
  
  productionDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      testPatterns.forEach(pattern => {
        try {
          // Use a cross-platform approach
          const files = execSync(`find ${dir} -name "${pattern}" 2>/dev/null || echo ""`, { encoding: 'utf8' });
          if (files.trim()) {
            foundTestFiles = foundTestFiles.concat(files.trim().split('\n').filter(f => f));
          }
        } catch (error) {
          // Ignore errors, try alternative approach
          try {
            const files = execSync(`dir /s /b ${dir}\\${pattern} 2>nul || echo ""`, { encoding: 'utf8' });
            if (files.trim()) {
              foundTestFiles = foundTestFiles.concat(files.trim().split('\n').filter(f => f));
            }
          } catch (e) {
            // Ignore
          }
        }
      });
    }
  });
  
  if (foundTestFiles.length > 0) {
    console.error('❌ Test files found in production bundle:');
    foundTestFiles.forEach(file => {
      console.error(`   - ${file}`);
    });
    hasIssues = true;
  } else {
    console.log('✅ No test files found in production bundle');
  }
}

// Check environment variables
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');
  
  const requiredVars = [
    'VITE_ENV',
    'VITE_API_BASE_URL',
    'VITE_API_BASE_WS_URL'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing critical environment variables:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    hasIssues = true;
  } else {
    console.log('✅ All critical environment variables are set');
  }
  
  // Check for development values in production
  if (process.env.VITE_ENV === 'production') {
    const devIndicators = [
      'localhost',
      'development',
      'test',
      'debug'
    ];
    
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('VITE_')) {
        const value = process.env[key];
        if (value && devIndicators.some(indicator => value.toLowerCase().includes(indicator))) {
          console.warn(`⚠️  Environment variable ${key} contains development indicator: ${value}`);
        }
      }
    });
  }
}

// Main security scan
console.log('🔍 Scanning frontend source code for security issues...');

const scanResults = scanDirectory('./src');
const utilsScanResults = scanDirectory('./utils');

const allResults = [...scanResults, ...utilsScanResults];

if (allResults.length > 0) {
  console.error('❌ Security issues found:');
  console.error('');
  
  allResults.forEach(result => {
    console.error(`📄 File: ${result.file}`);
    result.issues.forEach(issue => {
      const severity = issue.severity === 'HIGH' ? '🔴' : issue.severity === 'MEDIUM' ? '🟡' : '🟢';
      console.error(`   ${severity} Line ${issue.line}: ${issue.type}`);
      console.error(`      ${issue.content}`);
    });
    console.error('');
  });
  
  hasIssues = true;
} else {
  console.log('✅ No security issues found in source code');
}

// Run additional checks
checkForTestFiles();
checkEnvironmentVariables();

// Final result
console.log('');
if (hasIssues) {
  console.error('❌ Frontend security check failed! Please fix the issues above before deploying.');
  process.exit(1);
} else {
  console.log('🎉 Frontend security check passed! Application is ready for production deployment.');
  process.exit(0);
}
