#!/usr/bin/env node

/**
 * Security Check Script
 * 
 * This script performs comprehensive security checks before production deployment:
 * 1. Scans for hardcoded secrets and API keys
 * 2. Checks for console.log statements in production builds
 * 3. Validates environment configuration
 * 4. Checks for test files in production bundle
 * 5. Validates CORS and security headers
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Starting security check...');

let hasIssues = false;

// Function to scan file for potential security issues
function scanFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return [];
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // Check for hardcoded secrets
    const secretPatterns = [
      /password\s*[:=]\s*['"][^'"]+['"]/i,
      /secret\s*[:=]\s*['"][^'"]+['"]/i,
      /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
      /token\s*[:=]\s*['"][^'"]+['"]/i,
      /mongodb:\/\/[^'"]+/i,
      /postgres:\/\/[^'"]+/i,
      /mysql:\/\/[^'"]+/i
    ];
    
    secretPatterns.forEach(pattern => {
      if (pattern.test(line) && !line.includes('process.env') && !line.includes('<PASSWORD>')) {
        issues.push({
          type: 'HARDCODED_SECRET',
          line: lineNum,
          content: line.trim(),
          severity: 'HIGH'
        });
      }
    });
    
    // Check for console.log in production files
    if (process.env.NODE_ENV === 'production') {
      if (/console\.(log|warn|info)/.test(line) && !line.includes('//') && !line.includes('/*')) {
        issues.push({
          type: 'CONSOLE_LOG',
          line: lineNum,
          content: line.trim(),
          severity: 'MEDIUM'
        });
      }
    }
    
    // Check for TODO/FIXME comments that might indicate incomplete security
    if (/TODO.*security|FIXME.*security|XXX.*security/i.test(line)) {
      issues.push({
        type: 'SECURITY_TODO',
        line: lineNum,
        content: line.trim(),
        severity: 'LOW'
      });
    }
  });
  
  return issues;
}

// Function to recursively scan directory
function scanDirectory(dirPath, extensions = ['.js', '.vue', '.ts', '.json']) {
  if (!fs.existsSync(dirPath)) {
    return [];
  }
  
  let allIssues = [];
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', 'dist', '.git', '.vscode', 'coverage'].includes(item)) {
        allIssues = allIssues.concat(scanDirectory(fullPath, extensions));
      }
    } else if (stat.isFile()) {
      const ext = path.extname(fullPath);
      if (extensions.includes(ext)) {
        const issues = scanFile(fullPath);
        if (issues.length > 0) {
          allIssues.push({
            file: fullPath,
            issues: issues
          });
        }
      }
    }
  }
  
  return allIssues;
}

// Check for test files in production directories
function checkForTestFiles() {
  console.log('🔍 Checking for test files in production bundle...');
  
  const testPatterns = [
    '**/*.test.js',
    '**/*.spec.js',
    '**/test/**',
    '**/tests/**',
    '**/*-test.js',
    '**/*-debug.js'
  ];
  
  const productionDirs = ['./dist', './build'];
  let foundTestFiles = [];
  
  productionDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      testPatterns.forEach(pattern => {
        try {
          const files = execSync(`find ${dir} -name "${pattern.replace('**/', '')}" 2>/dev/null || true`, { encoding: 'utf8' });
          if (files.trim()) {
            foundTestFiles = foundTestFiles.concat(files.trim().split('\n'));
          }
        } catch (error) {
          // Ignore errors
        }
      });
    }
  });
  
  if (foundTestFiles.length > 0) {
    console.error('❌ Test files found in production bundle:');
    foundTestFiles.forEach(file => {
      console.error(`   - ${file}`);
    });
    hasIssues = true;
  } else {
    console.log('✅ No test files found in production bundle');
  }
}

// Check environment variables
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');
  
  const requiredVars = [
    'NODE_ENV',
    'DATABASE_PRD',
    'DATABASE_PRD_PASSWORD',
    'JWT_SECRET',
    'STYTCH_PID_PROD',
    'STYTCH_PASSWORD_PROD'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing critical environment variables:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    hasIssues = true;
  } else {
    console.log('✅ All critical environment variables are set');
  }
  
  // Check for development values in production
  if (process.env.NODE_ENV === 'production') {
    const devIndicators = [
      'localhost',
      'development',
      'test',
      'debug'
    ];
    
    Object.keys(process.env).forEach(key => {
      const value = process.env[key];
      if (value && devIndicators.some(indicator => value.toLowerCase().includes(indicator))) {
        console.warn(`⚠️  Environment variable ${key} contains development indicator: ${value}`);
      }
    });
  }
}

// Main security scan
console.log('🔍 Scanning source code for security issues...');

const scanResults = scanDirectory('./src');
const backendScanResults = scanDirectory('./controllers');
const utilsScanResults = scanDirectory('./utils');

const allResults = [...scanResults, ...backendScanResults, ...utilsScanResults];

if (allResults.length > 0) {
  console.error('❌ Security issues found:');
  console.error('');
  
  allResults.forEach(result => {
    console.error(`📄 File: ${result.file}`);
    result.issues.forEach(issue => {
      const severity = issue.severity === 'HIGH' ? '🔴' : issue.severity === 'MEDIUM' ? '🟡' : '🟢';
      console.error(`   ${severity} Line ${issue.line}: ${issue.type}`);
      console.error(`      ${issue.content}`);
    });
    console.error('');
  });
  
  hasIssues = true;
} else {
  console.log('✅ No security issues found in source code');
}

// Run additional checks
checkForTestFiles();
checkEnvironmentVariables();

// Final result
console.log('');
if (hasIssues) {
  console.error('❌ Security check failed! Please fix the issues above before deploying.');
  process.exit(1);
} else {
  console.log('🎉 Security check passed! Application is ready for production deployment.');
  process.exit(0);
}
