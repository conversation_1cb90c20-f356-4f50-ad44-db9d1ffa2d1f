const express = require('express');
const authController = require('../controllers/authController');
const shoppingListController = require('../controllers/shoppingListController');

const router = express.Router({ mergeParams: true }); // mergeParams needed to access listId from parent router

// Protect all routes after this middleware
router.use(authController.verify);

// Routes related to recipes acting on a specific shopping list
router.route('/recipes')
    .post(shoppingListController.addRecipeToList); // POST /api/shopping-lists/:listId/recipes

router.route('/recipes/:recipeId')
    .delete(shoppingListController.removeRecipeFromList); // DELETE /api/shopping-lists/:listId/recipes/:recipeId

// Route for updating shopping list name
router.route('/name')
    .put(shoppingListController.updateShoppingListName); // PUT /api/shopping-lists/:listId/name

// Route for adding custom items to a specific shopping list
router.route('/items')
    .post(shoppingListController.addCustomItemToList); // POST /api/shopping-lists/:listId/items

// Route to finish (deactivate) a shopping list
router.route('/finish')
    .put(shoppingListController.finishShoppingList); // PUT /api/shopping-lists/:listId/finish

// Route to activate a historical shopping list
router.route('/activate')
    .put(shoppingListController.activateShoppingList); // PUT /api/shopping-lists/:listId/activate

// Route to categorize all items in a shopping list
router.route('/categorize-items')
    .post(shoppingListController.categorizeListItems); // POST /api/shopping-lists/:listId/categorize-items

// Route to sync offline changes
router.route('/sync-offline-changes')
    .post(shoppingListController.syncOfflineChanges); // POST /api/shopping-lists/:listId/sync-offline-changes

// Route to update shopping list name
router.route('/name')
    .put(shoppingListController.updateShoppingListName); // PUT /api/shopping-lists/:listId/name

module.exports = router;