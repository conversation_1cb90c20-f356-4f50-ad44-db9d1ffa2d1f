/**
 * PWA-Funktionalität für Ordy
 *
 * Diese Datei enthält die Logik zur Registrierung und Aktualisierung des Service Workers
 * für die PWA-Funktionalität. Sie stellt sicher, dass Updates korrekt angewendet werden.
 */

import { useHelperStore } from '../utils/helper';
import { registerSW } from 'virtual:pwa-register';

// Aktuelle App-Version aus der package.json
const APP_VERSION = import.meta.env.VITE_APP_VERSION || '0.0.0';

// Service Worker-Registrierung
export function setupPWA() {
  const helper = useHelperStore();

  if ('serviceWorker' in navigator) {
    try {
      // Prüfen, ob eine gespeicherte Version existiert
      const storedVersion = localStorage.getItem('app_version');

      // Wenn die Version sich geändert hat, Cache löschen
      if (storedVersion && storedVersion !== APP_VERSION) {
        helper.devConsole(`App-Version hat sich geändert: ${storedVersion} -> ${APP_VERSION}. Cache wird geleert.`);
        clearCaches();
      }

      // Neue Version speichern
      localStorage.setItem('app_version', APP_VERSION);

      // Service Worker mit der vite-plugin-pwa Funktion registrieren
      const updateSW = registerSW({
        onNeedRefresh() {
          // Ein Update ist verfügbar
          helper.devConsole('Ein Service Worker-Update ist verfügbar.');
          // Sofort aktualisieren ohne Benutzerinteraktion
          setTimeout(() => {
            updateSW(true);
          }, 100);
        },
        onOfflineReady() {
          helper.devConsole('Die App ist jetzt offline-bereit.');
        },
        immediate: true
      });

      // Robuste Bildschirmorientierung auf Hochformat sperren (für PWA)
      import('./utils/orientationLock.js').then(({ orientationLock }) => {
        // Forciere eine erneute Sperre für PWA
        orientationLock.forceLock().then(lockResult => {
          if (lockResult) {
            helper.devConsole('PWA: Orientierungssperre erfolgreich aktiviert');
          } else {
            helper.devConsole('PWA: Orientierungssperre konnte nicht aktiviert werden');
          }

          helper.devConsole('PWA Orientierungsstatus:', orientationLock.getStatus());
        }).catch(error => {
          helper.devConsole(`PWA: Fehler beim Forcieren der Orientierungssperre: ${error.message}`);
        });
      }).catch(error => {
        helper.devConsole(`PWA: Fehler beim Laden der Orientierungssperre: ${error.message}`);
      });

      helper.devConsole(`PWA-Setup abgeschlossen. App-Version: ${APP_VERSION}`);
    } catch (error) {
      helper.devConsole(`PWA-Setup fehlgeschlagen: ${error.message}`);
    }
  } else {
    helper.devConsole('Service Worker werden von diesem Browser nicht unterstützt.');
  }
}

// Alle Caches leeren
async function clearCaches() {
  if ('caches' in window) {
    try {
      // Liste aller Cache-Namen abrufen
      const cacheNames = await caches.keys();

      // Alle Caches löschen
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );

      console.log('Alle Caches wurden erfolgreich geleert.');
    } catch (error) {
      console.error('Fehler beim Leeren der Caches:', error);
    }
  }
}
