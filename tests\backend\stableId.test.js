const stableIdManager = require('../../utils/stableIdManager');

// Simple test framework
function assert(condition, message) {
  if (!condition) {
    throw new Error(`❌ ASSERTION FAILED: ${message}`);
  }
  console.log(`✅ ${message}`);
}

function assertEqual(actual, expected, message) {
  if (actual !== expected) {
    throw new Error(`❌ ASSERTION FAILED: ${message}. Expected: ${expected}, Actual: ${actual}`);
  }
  console.log(`✅ ${message}`);
}

function assertArrayLength(array, expectedLength, message) {
  if (array.length !== expectedLength) {
    throw new Error(`❌ ASSERTION FAILED: ${message}. Expected length: ${expectedLength}, Actual: ${array.length}`);
  }
  console.log(`✅ ${message}`);
}

console.log('🧪 Starting StableID Manager Tests...\n');

// Test 1: assignStableIds - should assign stable IDs to ingredients without IDs
try {
  console.log('📋 Test 1: assignStableIds - new ingredients');
  const ingredients = [
    { name: 'Mehl', amount: 500, unit: 'g' },
    { name: 'Milch', amount: 250, unit: 'ml' },
    { name: '<PERSON><PERSON>', amount: 2, unit: 'Stück' }
  ];

  const result = stableIdManager.assignStableIds(ingredients, 0);

  assertArrayLength(result.updatedIngredients, 3, 'Should have 3 ingredients');
  assertEqual(result.updatedIngredients[0].stableId, 1, 'First ingredient should have stableId 1');
  assertEqual(result.updatedIngredients[1].stableId, 2, 'Second ingredient should have stableId 2');
  assertEqual(result.updatedIngredients[2].stableId, 3, 'Third ingredient should have stableId 3');
  assertEqual(result.newMaxUsedId, 3, 'newMaxUsedId should be 3');
  console.log('✅ Test 1 PASSED\n');
} catch (error) {
  console.error('❌ Test 1 FAILED:', error.message);
}

// Test 2: assignStableIds - should preserve existing stable IDs
try {
  console.log('📋 Test 2: assignStableIds - preserve existing IDs');
  const ingredients = [
    { name: 'Mehl', amount: 500, unit: 'g', stableId: 1 },
    { name: 'Milch', amount: 250, unit: 'ml' }, // Keine stableId
    { name: 'Eier', amount: 2, unit: 'Stück', stableId: 5 }
  ];

  const result = stableIdManager.assignStableIds(ingredients, 5);

  assertEqual(result.updatedIngredients[0].stableId, 1, 'First ingredient should keep stableId 1');
  assertEqual(result.updatedIngredients[1].stableId, 6, 'Second ingredient should get new stableId 6');
  assertEqual(result.updatedIngredients[2].stableId, 5, 'Third ingredient should keep stableId 5');
  assertEqual(result.newMaxUsedId, 6, 'newMaxUsedId should be 6');
  console.log('✅ Test 2 PASSED\n');
} catch (error) {
  console.error('❌ Test 2 FAILED:', error.message);
}

// Test 3: validateStableIds - should validate correct stable IDs
try {
  console.log('📋 Test 3: validateStableIds - valid IDs');
  const menuChild = {
    ingredients: [
      { name: 'Mehl', stableId: 1 },
      { name: 'Milch', stableId: 2 },
      { name: 'Eier', stableId: 3 }
    ],
    maxUsedStableId: 3
  };

  const result = stableIdManager.validateStableIds(menuChild);

  assert(result.isValid === true, 'Validation should pass');
  assertEqual(result.errors.length, 0, 'Should have no errors');
  console.log('✅ Test 3 PASSED\n');
} catch (error) {
  console.error('❌ Test 3 FAILED:', error.message);
}

// Test 4: addIngredientWithStableId
try {
  console.log('📋 Test 4: addIngredientWithStableId');
  const ingredients = [
    { name: 'Mehl', stableId: 1 },
    { name: 'Milch', stableId: 2 }
  ];

  const newIngredient = { name: 'Eier', amount: 2, unit: 'Stück' };

  const result = stableIdManager.addIngredientWithStableId(ingredients, newIngredient, 2);

  assertArrayLength(result.updatedIngredients, 3, 'Should have 3 ingredients after adding');
  assertEqual(result.updatedIngredients[2].stableId, 3, 'New ingredient should have stableId 3');
  assertEqual(result.newMaxUsedId, 3, 'newMaxUsedId should be 3');
  assertEqual(result.assignedStableId, 3, 'assignedStableId should be 3');
  console.log('✅ Test 4 PASSED\n');
} catch (error) {
  console.error('❌ Test 4 FAILED:', error.message);
}

// Test 5: removeIngredientKeepStableIds
try {
  console.log('📋 Test 5: removeIngredientKeepStableIds');
  const ingredients = [
    { name: 'Mehl', stableId: 1 },
    { name: 'Milch', stableId: 2 },
    { name: 'Eier', stableId: 3 }
  ];

  const result = stableIdManager.removeIngredientKeepStableIds(ingredients, 1, 3);

  assertArrayLength(result.updatedIngredients, 2, 'Should have 2 ingredients after removal');
  assertEqual(result.updatedIngredients[0].stableId, 1, 'First ingredient should keep stableId 1');
  assertEqual(result.updatedIngredients[1].stableId, 3, 'Second ingredient should keep stableId 3');
  assertEqual(result.removedStableId, 2, 'removedStableId should be 2');
  console.log('✅ Test 5 PASSED\n');
} catch (error) {
  console.error('❌ Test 5 FAILED:', error.message);
}

console.log('🎉 All StableID Manager tests completed!');
