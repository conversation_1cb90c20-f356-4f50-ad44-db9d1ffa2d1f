const helper = require('./helper');

/**
 * StableID Manager für Zutaten-System
 * 
 * Verwaltet stabile IDs für Zutaten in Rezepten:
 * - IDs sind 1-basiert (erste Zutat = ID 1)
 * - IDs werden NIE wiederverwendet nach Löschung
 * - Jedes Rezept hat eigene ID-History (maxUsedStableId)
 * - Bestehende IDs bleiben PERMANENT erhalten
 */

/**
 * Weist neuen Zutaten stabile IDs zu, ohne bestehende zu ändern
 * @param {Array} ingredients - Array von Zutaten-Objekten
 * @param {Number} currentMaxUsedId - Aktueller maxUsedStableId Wert
 * @returns {Object} { updatedIngredients, newMaxUsedId }
 */
function assignStableIds(ingredients, currentMaxUsedId = 0) {
    helper.devConsole('🔧 StableID Manager: Assigning stable IDs...');
    
    if (!Array.isArray(ingredients)) {
        helper.devConsole('⚠️ StableID Manager: Invalid ingredients array');
        return { updatedIngredients: [], newMaxUsedId: currentMaxUsedId };
    }

    let maxUsedId = currentMaxUsedId;
    let hasChanges = false;

    // Finde die höchste bereits verwendete ID
    ingredients.forEach(ingredient => {
        if (ingredient.stableId && typeof ingredient.stableId === 'number' && ingredient.stableId > maxUsedId) {
            maxUsedId = ingredient.stableId;
        }
    });

    // Weise nur fehlende IDs zu - bestehende bleiben PERMANENT
    const updatedIngredients = ingredients.map((ingredient, index) => {
        if (!ingredient.stableId || typeof ingredient.stableId !== 'number') {
            maxUsedId++;
            hasChanges = true;
            helper.devConsole(`🆔 NEW stable ID ${maxUsedId} assigned to ingredient: "${ingredient.name?.name || ingredient.name || 'Unknown'}" at index ${index}`);
            return {
                ...ingredient,
                stableId: maxUsedId
            };
        } else {
            helper.devConsole(`✅ PERMANENT stable ID ${ingredient.stableId} kept for ingredient: "${ingredient.name?.name || ingredient.name || 'Unknown'}"`);
            return ingredient;
        }
    });

    if (hasChanges) {
        helper.devConsole(`🔢 Updated max stable ID to: ${maxUsedId} (per recipe, never reused)`);
    }

    return {
        updatedIngredients,
        newMaxUsedId: maxUsedId
    };
}

/**
 * Validiert StableID-Konsistenz in einem MenuChild
 * @param {Object} menuChild - MenuChild-Objekt
 * @returns {Object} { isValid, errors, warnings }
 */
function validateStableIds(menuChild) {
    const errors = [];
    const warnings = [];

    if (!menuChild || !menuChild.ingredients) {
        errors.push('MenuChild oder ingredients fehlen');
        return { isValid: false, errors, warnings };
    }

    const ingredients = menuChild.ingredients;
    const maxUsedStableId = menuChild.maxUsedStableId || 0;
    const usedIds = new Set();
    let highestFoundId = 0;

    // Prüfe jede Zutat
    ingredients.forEach((ingredient, index) => {
        const stableId = ingredient.stableId;

        // Prüfe ob stableId existiert
        if (!stableId || typeof stableId !== 'number') {
            warnings.push(`Zutat an Index ${index} hat keine stableId`);
            return;
        }

        // Prüfe auf Duplikate
        if (usedIds.has(stableId)) {
            errors.push(`Doppelte stableId ${stableId} gefunden`);
        }
        usedIds.add(stableId);

        // Prüfe ID-Bereich
        if (stableId < 1) {
            errors.push(`StableId ${stableId} ist kleiner als 1`);
        }

        if (stableId > highestFoundId) {
            highestFoundId = stableId;
        }
    });

    // Prüfe maxUsedStableId Konsistenz
    if (highestFoundId > maxUsedStableId) {
        warnings.push(`maxUsedStableId (${maxUsedStableId}) ist kleiner als höchste gefundene ID (${highestFoundId})`);
    }

    const isValid = errors.length === 0;
    
    if (isValid) {
        helper.devConsole('✅ StableID validation passed');
    } else {
        helper.devConsole('❌ StableID validation failed:', errors);
    }

    return { isValid, errors, warnings };
}

/**
 * Repariert StableID-Inkonsistenzen in einem MenuChild
 * @param {Object} menuChild - MenuChild-Objekt
 * @returns {Object} Repariertes MenuChild-Objekt
 */
function repairStableIds(menuChild) {
    helper.devConsole('🔧 StableID Manager: Repairing stable IDs...');

    if (!menuChild || !menuChild.ingredients) {
        return menuChild;
    }

    const currentMaxUsedId = menuChild.maxUsedStableId || 0;
    const { updatedIngredients, newMaxUsedId } = assignStableIds(menuChild.ingredients, currentMaxUsedId);

    const repairedMenuChild = {
        ...menuChild,
        ingredients: updatedIngredients,
        maxUsedStableId: newMaxUsedId
    };

    helper.devConsole('✅ StableID repair completed');
    return repairedMenuChild;
}

/**
 * Entfernt eine Zutat und behält StableID-Konsistenz bei
 * @param {Array} ingredients - Array von Zutaten
 * @param {Number} indexToRemove - Index der zu entfernenden Zutat
 * @param {Number} maxUsedStableId - Aktueller maxUsedStableId (bleibt unverändert)
 * @returns {Object} { updatedIngredients, removedStableId }
 */
function removeIngredientKeepStableIds(ingredients, indexToRemove, maxUsedStableId) {
    helper.devConsole(`🗑️ StableID Manager: Removing ingredient at index ${indexToRemove}`);

    if (!Array.isArray(ingredients) || indexToRemove < 0 || indexToRemove >= ingredients.length) {
        helper.devConsole('⚠️ Invalid removal parameters');
        return { updatedIngredients: ingredients, removedStableId: null };
    }

    const removedIngredient = ingredients[indexToRemove];
    const removedStableId = removedIngredient.stableId;

    // Entferne die Zutat (Array-Index ändert sich, aber stableIDs bleiben gleich)
    const updatedIngredients = ingredients.filter((_, index) => index !== indexToRemove);

    helper.devConsole(`🔒 Removed ingredient with stableId ${removedStableId} - ID will NEVER be reused`);
    helper.devConsole(`📊 Remaining ingredients: ${updatedIngredients.length}, maxUsedStableId stays: ${maxUsedStableId}`);

    return {
        updatedIngredients,
        removedStableId
    };
}

/**
 * Fügt eine neue Zutat hinzu und weist automatisch eine neue stableId zu
 * @param {Array} ingredients - Bestehende Zutaten
 * @param {Object} newIngredient - Neue Zutat (ohne stableId)
 * @param {Number} maxUsedStableId - Aktueller maxUsedStableId
 * @returns {Object} { updatedIngredients, newMaxUsedId, assignedStableId }
 */
function addIngredientWithStableId(ingredients, newIngredient, maxUsedStableId) {
    helper.devConsole('➕ StableID Manager: Adding new ingredient...');

    const newStableId = maxUsedStableId + 1;
    const ingredientWithStableId = {
        ...newIngredient,
        stableId: newStableId
    };

    const updatedIngredients = [...ingredients, ingredientWithStableId];

    helper.devConsole(`🆔 Assigned new stableId ${newStableId} to ingredient: "${newIngredient.name?.name || newIngredient.name || 'Unknown'}"`);

    return {
        updatedIngredients,
        newMaxUsedId: newStableId,
        assignedStableId: newStableId
    };
}

module.exports = {
    assignStableIds,
    validateStableIds,
    repairStableIds,
    removeIngredientKeepStableIds,
    addIngredientWithStableId
};
