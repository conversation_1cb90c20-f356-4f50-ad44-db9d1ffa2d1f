import { defineStore } from 'pinia';
import { ref, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useWeekplanStore } from '../store/weekplanStore';
import { useUserStore } from '../store/userStore';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/de';
import { useHelperStore } from '../../utils/helper';

export const useGrocerylistStore = defineStore('grocerylist', () => {

    const { setNotification } = useNotification();
    const router = useRouter();
    const weekplanstore = useWeekplanStore();
    const userstore = useUserStore();
    const helper = useHelperStore()
    dayjs.locale('de');
    dayjs.extend(relativeTime);

    /* INITIALIZING VALUES */
    const groceryList = ref([]);

    const groceryObject = ref({
        _id: null,
        kitchentableId: null,
        startDate: null,
        endDate: null,
        locked: null,
        groceryListActive: [],
        groceryListPassive: [],
        createdAt: null
    });


    const groceryManualInput = ref("")
    /* INITIALIZING */

    /* WATCHERS */
    /*
    watch(() => groceryObject.value._id, async (neueID, alteID) => {
        if(groceryObject.value.startDate != undefined){
            await getGrocerysByKitchentableId()
        }
    });
    */
    /* WATCHERS */

    /* FUNCTIONS */
    // DATES CHANGES
    const formattedGroceryList = computed(() => {
        return groceryList.value.map(item => ({
          ...item,
          formattedStartDate: dayjs(item.startDate).format('D. MMMM YY'), //format(new Date(item.startDate), 'dd.MM.yyyy'),
          formattedEndDate: dayjs(item.endDate).format('D. MMMM YY') //format(new Date(item.endDate), 'dd.MM.yyyy')
        }));
      });

      // FORMAT GROCERYS CHANGES
      // formattedPassiveGroceryList
      const formattedActiveGroceryList = computed(() => {
        const grouped = groceryObject.value.groceryListActive.reduce((acc, item) => {
          const { name, unit } = item;
          const key = `${name}-${unit}`;
          const createFormatDate = dayjs().to(dayjs(item.date))
        
          if (!acc[key]) {
            acc[key] = {
              amount: item.amount,
              name: item.name,
              unit: item.unit,
              numberOfPersons: item.numberOfPersons,
              date: new Date(item.date),
              dateformatted: createFormatDate,
              grocerys: [item]
            };
          } else {
            acc[key].amount += item.amount;
            acc[key].numberOfPersons += item.numberOfPersons;
            acc[key].date = new Date(Math.min(acc[key].date, new Date(item.date)));
            acc[key].dateformatted = createFormatDate;
            acc[key].grocerys.push(item);
          }
          
          return acc;
        }, {});
        
        return Object.values(grouped);
      });

      // formattedPassiveGroceryList
      const formattedPassiveGroceryList = computed(() => {
        const grouped = groceryObject.value.groceryListPassive.reduce((acc, item) => {
          const { name, unit } = item;
          const key = `${name}-${unit}`;
          const createFormatDate = dayjs().to(dayjs(item.date))
        
          if (!acc[key]) {
            acc[key] = {
              amount: item.amount,
              name: item.name,
              unit: item.unit,
              numberOfPersons: item.numberOfPersons,
              date: new Date(item.date),
              dateformatted: createFormatDate,
              grocerys: [item]
            };
          } else {
            acc[key].amount += item.amount;
            acc[key].numberOfPersons += item.numberOfPersons;
            acc[key].date = new Date(Math.min(acc[key].date, new Date(item.date)));
            acc[key].dateformatted = createFormatDate;
            acc[key].grocerys.push(item);
          }
          
          return acc;
        }, {});
        
        return Object.values(grouped);
      });

    // SET VALUES TO THE OBJECT
    const setOneGroceryListObject = async (res) => {
        groceryObject.value._id = res._id,
        groceryObject.value.kitchentableId = res.kitchentableId,
        groceryObject.value.startDate = res.startDate,
        groceryObject.value.endDate = res.endDate,
        groceryObject.value.locked = res.locked,
        groceryObject.value.groceryListActive = res.groceryListActive,
        groceryObject.value.groceryListPassive = res.groceryListPassive,
        groceryObject.value.createdAt = res.createdAt
    }

    ////////////////////////////////////////////////////////////////////
    ////////////////////// Grocerylist Overview ////////////////////////
    ////////////////////////////////////////////////////////////////////

    // grocerylist list
    const setGrocerylistOverview = async (res) => {
        //console.log("-- - -  -")
        //console.log(res.data.data)
        groceryList.value = res
    }

    /* GET GROCERYLISTS OVERVIEW BY KITCHENTABLEID */
    const getGrocerylistOverviewByKitchentableId = async (kitchentableid) => {
        helper.devConsole(kitchentableid)
        try{
            const res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/all/' + kitchentableid);
            //console.log(res.data.data)
            await setGrocerylistOverview(res.data.data);

        } catch (error){
            helper.devConsole(error)
            //console.log(error)
        }
        
    }

    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////

    ////////////////////////////////////////////////////////////////////
    ////////////////////// One Grocerylist //// ////////////////////////
    ////////////////////////////////////////////////////////////////////

    // grocery list
    const setGroceryListGrocerys = async (res) => {
        //console.log("-- - - -")
        //console.log(res.data.data)
        groceryObject.value.groceryListActive = [...res.groceryListActive, ...groceryObject.value.groceryListActive]
        groceryObject.value.groceryListPassive = [...res.groceryListPassive, ...groceryObject.value.groceryListPassive]
    }
    
    /* SAVE GROCERYLISTS TO THE STORE*/
    const saveOneGroceryListObject = async () => {
        if(weekplanstore.weekplanmenu.length == 0){
            setNotification('Zuerst einen Zeitraum wählen', 'alert')
            return
        }

        const payload = {}
        payload.kitchentableId =  userstore.user.defaultKitchentable;
        payload.startDate =  weekplanstore.date[0];
        payload.endDate =  weekplanstore.date[1];

        try{
            const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/one/123', {
                grocerylist: payload
            });
            await setOneGroceryListObject(res)
            groceryList.value.push(res.data.data)

            if(res.message == "success"){
                setNotification('Die Einkaufsliste wurde hinzugefügt', 'success')
            }

        } catch (error){
            console.log(error)
        }
        
    }

    
    /* SET GROCERYS AND GROCERYOBJECT BY KITCHENTABLE, START- AND ENDDATE */
    const getGrocerysByKitchentableId = async (index) => {
        //console.log(kitchentableid)
        const payload = {}
        payload.kitchentableId =  userstore.user.defaultKitchentable;
        payload.startDate =  groceryList.value[index].startDate;
        payload.endDate =  groceryList.value[index].endDate;

        await setOneGroceryListObject(groceryList.value[index])

        if(groceryList.value[index].locked == false){
            try{
                const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/all/grocerys/bykitchentableid',{
                    "grocerylist": payload
                });
                //console.log(res.data)
                if(res.data.success){
                    const transformedGrocerys = {
                        groceryListActive: res.data.data,
                        groceryListPassive: []
                    }
                    await setGroceryListGrocerys(transformedGrocerys);
                    router.push({ name: 'einkaufszettel', params: { id: groceryList.value[index]._id } })
                }
            } catch (error){
                console.log(error)
            }
        } else {
            try{
                const res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/one/' + groceryList.value[index]._id);
                
                console.log(res.data)
                if(res.data.success){
                    await setGroceryListGrocerys(res.data.data);
                    router.push({ name: 'einkaufszettel', params: { id: groceryList.value[index]._id } })
                }
            } catch (error){
                console.log(error)
            }
        }
        
    }

    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////

    ////////////////////////////////////////////////////////////////////
    ///////////////////////////// FUNCTIONS ////////////////////////////
    ////////////////////////////////////////////////////////////////////

        
    /* PUSH ONE ITEM TO THE LIST */
    const createOneNewItemToTheList = async () => {

        console.log("drin")
        if(groceryObject.value._id == null){
            setNotification('Zuerst einen Einkaufszettel wählen','alert')
            return
        }

        let response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/ingredients/proof/' + groceryManualInput.value);
        console.log(response.data.data)
        // set to local state
        groceryObject.value.groceryListActive.push(response.data.data)
        // reset
        let payload = {
            "groceryListActive":  response.data.data
        }
        let response2 = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/one/' + groceryObject.value._id + '/activeitem/add', payload);
        console.log(response2.data.data)
        groceryManualInput.value = "";
    }

    /* CHANGE ONE ITEM TO PASSIVE */
    const changeList = async (index, type) => {
        //console.log(index, type)

        // set groceryList and groceryObject to locked = true, no reload of new menus possible
        // very important to update groceryList, functions are pulling locked from there
        if(groceryObject.value.groceryListPassive == 0 && groceryObject.value.locked == false){
            setNotification('Die Einkaufsliste wurde gesperrt. Viel Spass beim Einkaufen', 'alert')
            groceryObject.value.locked = true
            for (let i = 0; i < groceryList.value.length; i++) {
                if (groceryList.value[i]._id === groceryObject.value._id) {
                  // Hier können asynchrone Operationen eingefügt werden
                  groceryList.value[i].locked = true;
                }
              }

        }
        
        if(type == 'activeToPassive'){
            //console.log("activeToPassive")
            //load all ext array items and push them to update
            let currentRecieptObjectName = formattedActiveGroceryList.value[index].name;
            //console.log(groceryObject.value.groceryListActive.length)
            for (let i = groceryObject.value.groceryListActive.length - 1; i >= 0; i--) {
                //console.log("Counter i: " + i)
                if (currentRecieptObjectName == groceryObject.value.groceryListActive[i].name) {
                    groceryObject.value.groceryListPassive.push(groceryObject.value.groceryListActive[i])
                    groceryObject.value.groceryListActive.splice(i,1)

                }
            }
        }

        if(type == 'passiveToActive'){
            console.log("passiveToActive")
            //load all ext array items and push them to update
            let currentRecieptObjectName = formattedPassiveGroceryList.value[index].name;
            for (let i = groceryObject.value.groceryListPassive.length - 1; i >= 0; i--) {
                if (currentRecieptObjectName == groceryObject.value.groceryListPassive[i].name) {
                    groceryObject.value.groceryListActive.push(groceryObject.value.groceryListPassive[i])
                    groceryObject.value.groceryListPassive.splice(i,1)
                }
            }
        }
        

        let payload = {
            "switch":  type,
            "locked": true,
            "groceryListPassive": groceryObject.value.groceryListPassive,
            "groceryListActive": groceryObject.value.groceryListActive
        }

        let response = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/one/' + groceryObject.value._id + '/grocerylistupdate', {
            "grocerylist": payload
        });

        //console.log(response.data.data)
        
        // set new values
        groceryObject.value.groceryListPassive = response.data.data.groceryListPassive
        groceryObject.value.groceryListActive = response.data.data.groceryListActive
        groceryObject.value.locked = response.data.data.locked
    }

    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    


    /* LOAD ALL GROCERY LISTS BY USER TO THE STORE*/
    const getAllGroceryListByUserId = async () => {
        /*
        try{
            const grocerylist = await axios.get(import.meta.env.VITE_API_BASE_URL + '/grocerylist/one/bykitchentableid/' + userstore.user.defaultKitchentable);
            groceryList.value = grocerylist.data.data.data
        } catch (error){
            console.log(error)
        }
        */
    }

    /* SAVE GROCERYLISTS TO THE STORE*/
    const saveGroceryList = async () => {
        if(weekplanstore.weekplanmenu.length == 0){
            setNotification('Zuerst einen Zeitraum wählen, bevor die Zutaten zur Einkaufsliste hinzugefügt werden können', 'alert')
            throw TypeError('Zeitraum mit Menüs angeben');
        }

        const payload = {}
        payload.menus =  weekplanstore.weekplanmenu;
        payload.kitchentableid =  userstore.user.defaultKitchentable;
        payload.dates =  weekplanstore.date;

        try{
            const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/many', payload);
            groceryList.value.push(res.data.data)

            if(res.message == "success"){
                setNotification('Die Zutaten wurden hinzugefügt', 'success')
            }

        } catch (error){
            console.log(error)
        }
        
    }

    /* DELETE GROCERYLIST ITEMS (deleteGroceryObject) */

    const deleteGroceryObject = async () => {

        try{
            const res = await axios.delete(import.meta.env.VITE_API_BASE_URL + '/api/v1/grocerylist/one/' + groceryObject.value._id);
            //console.log(res.data)
            if(res.data.success){
                setNotification('Der Einkaufszettel wurde gelöscht', 'success')

                //console.log(groceryList.value)
                // splice object from grocerylist array
                const newArray = groceryList.value.filter(item => item.id !== groceryObject.value._id);
                helper.devConsole(newArray)
                groceryList.value = newArray
                router.go(-1)
            }

        } catch (error){
            console.log(error)
        }
        
    }


    return {
        // EXPORTET VALUES

        /////// old
        groceryList,
        formattedGroceryList,
        formattedActiveGroceryList,
        formattedPassiveGroceryList,
        groceryObject,
        groceryManualInput,
        // EXPORTED FUNCTIONS
        setOneGroceryListObject,
        setGrocerylistOverview,
        setGroceryListGrocerys,
        saveOneGroceryListObject,
        getGrocerylistOverviewByKitchentableId,
        getGrocerysByKitchentableId,
        ////// old
        getAllGroceryListByUserId,
        saveGroceryList,
        changeList,
        createOneNewItemToTheList,
        deleteGroceryObject
        
    };

});