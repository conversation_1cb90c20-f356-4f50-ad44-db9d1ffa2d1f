<template>
  <!-- Column Builder-->
  <div class="flex flex-col md:flex-row min-h-screen px-6">
  
    <!-- Middle Container -->
    <div class="w-full md:w-3/4 md:pr-12">
      <!-- Head-->
      <div class="w-full flex flex-row mt-10">
        <h1 class="w-11/12 h-auto">Die Rezeptebibliothek</h1>
        <div class="w-1/12">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="w-full md:w-1/2">Alle öffentliche Rezepte in einer Bibliothek</p>
      <!-- Head-->

      <!-- Select Filter -->
      <div class="flex flex-col md:flex-nowrap gap-2 w-full mt-10 h-10">

        <!-- Search Button -->
       <button class="flex-initial w-full md:w-3/5 bg-white rounded-lg flex">
          <input @input.prevent="myRecieptsStore.searchForOpenReciepsByName()" v-model="myRecieptsStore.searchAllReciepts" class="outline-none bg-transparent p-3 w-10/12 border-0" placeholder="Nach Rezepten suchen" />
          <div class="w-2/12"><img class="w-6 pt-3 right-0" src="../assets/icons/search.png" /></div>
        </button>

        <button class="flex flex-initial invisible md:w-1/5 gap-2"><img src="../assets/icons/filter.png" /><span>Zuletzt geändert</span></button>

        <button class="flex flex-initial invisible md:w-1/5 gap-2"><img src="../assets/icons/filter.png" /><span>A -> Z</span></button>
      </div>
      <!-- Select Filter -->

      <!-- Menue -->
      <div class="flex flex-wrap gap-5 pb-12">
        <div v-for="(item,index) in myRecieptsStore.allReciepts" :key="index">
            <MenuCard class="bg-sfgyellow-500" :element="item" :index="index" :key="key" :menuPlanType="{ addToMenu: true, editMenu: false, none: false }" :weekplanParentData="{'plannedSeats': null}" />
        </div>
      </div>
      <!-- Menue -->

    </div>


    <!-- Right Container -->
    <div class="w-full md:w-1/4 md:p-10 pb-32">
      <h2 class="w-full">Einstellungen</h2>
      <p class="w-full">Hier kannst du Einstellungen zu deinen Rezepten vornehmen:</p>

      <click-button @click.prevent="createRezept"  :element="{'buttondescription': 'Eigenes Rezept erstellen', 'active': false, 'buttonicon': 'add.png', 'iconneeded': true}" :index="1"  />
      
    </div>
    
  </div>
  
</template>
<script setup>

///////////////////// IMPORT /////////////////////////////////
import { reactive, ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import menuCard from '../components/menuCard.vue'
import clickButton from '../components/clickBorderButton.vue'
import dayCard from '../components/dayCard.vue'
import useNotification from '../../modules/notificationInformation';
import { useMenuStore } from '../store/menuStore'
import { useUserStore } from '../store/userStore'
import { useMenuesStore } from '../store/menuStore'
import MenuCard from '../components/menuCard.vue';

  ///////////////////// SETUP /////////////////////////////////

  const { setNotification } = useNotification();
  const router = useRouter();
  const route = useRoute();
  const userStore = useUserStore();
  const myRecieptsStore = useMenuesStore();

  const clickedButtonOnce = ref(false);

  ////////////////////// SETUP ////////////////////////////////

  // LOAD ALL MENUES
  myRecieptsStore.searchForAllFreeAccessRecieps()

  
  // CREATE MENU
  const createRezept = async () => {
    
    const data = {
      user_id: userStore.user.id,
      data: {
        "name": "Neues Rezept ohne Namen",
        "description": "-",
        "menuchilds": [],
        "users": [],
        "imagelink": "",
        "freeAccess": "",
        "seatCount": 0,
        "cookingTime": 0,
        "ingredients": [{unit:"Pack", amount: "1", name: "Milch"}],
        "preperation":[{head:"schritt1", content: "Inhalt von Schritt 1"}],
        "nutritions": [],
        "versions": []
      }
    }
    console.log('data', data)
    const createdMenu = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/0', {
      menuid: menuId,
      kitchenid: kitchentableid,
      name: menuTitel.value,
      data: data.data
    });
    //console.log(createdMenu)
    await awaitReciepe(createdMenu)
    
    //router.push({ name: `/rezept_edit/${username}` })
    //$router.go('rezept_edit')
  }

  // PUSH TO URL AS SOON THE ITEM WAS CREATED
  const awaitReciepe = (data) => {
    //{ name: 'login', params: createdMenu.data.menue._id }

    // forward to url
    //console.log(data)
    router.push({ name: 'rezept_edit', params: { id: data.data.data.menue._id }  })
  }
  


  
</script>
<style scoped>
.shake {
  animation: shake 3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>