const express = require('express');
const facebookOAuthController = require('../controllers/facebookOAuthController');

const router = express.Router();

// @desc    Handle Facebook OAuth callback
// @route   GET /auth/facebook/callback
// @access  Public (but validates state)
router.get('/callback', async (req, res) => {
  const { code, state, error } = req.query;

  if (error) {
    return res.status(400).send(`
      <html>
        <body>
          <h1>Facebook Authorization Failed</h1>
          <p>Error: ${error}</p>
          <p>Please try again.</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);
  }

  if (!code) {
    return res.status(400).send(`
      <html>
        <body>
          <h1>Invalid Facebook Callback</h1>
          <p>No authorization code received.</p>
        </body>
      </html>
    `);
  }

  // Use the controller to handle the callback
  try {
    await facebookOAuthController.handleCallback(req, res);
  } catch (error) {
    console.error('[Facebook OAuth Callback] Error:', error);
    return res.status(500).send(`
      <html>
        <body>
          <h1>Facebook OAuth Error</h1>
          <p>An error occurred during the OAuth process: ${error.message}</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);
  }
});

module.exports = router;
