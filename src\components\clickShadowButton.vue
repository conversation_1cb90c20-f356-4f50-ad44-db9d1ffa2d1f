<template>
    <button 
        class="w-full md:w-[200px] items-center md:justify-left justify-center mx-auto md:mx-0 h-auto mt-8 
        rounded-2xl default-button-bg pt-4 pb-4
        pr-2 flex flex-col md:flex-row
        
        shadow-custom default-button-shadow
        "
    >
        <!-- text -->
        <div class="w-7/10 pl-3 font-YesevaOne text-sm text-white">
            {{ element.buttondescription }}
        </div>

        <!-- icon -->
        <div v-if="element.iconneeded" class="w-3/10 mt-3 md:mt-0">
            <img v-if="!element.active" class="w-6" :src="getImageUrl(element.buttonicon)" />
            <img v-if="element.active" class="w-6 animate-spin" src="../assets/icons/reload.png" alt="searching" />
        </div>
        <!--
        <div v-if="element.active" class="absolute bg-white w-52 p-4 -mt-52 border solid border-r-8 border-alarmred-100">
            <p class="text-xs">{{ element.infoboxtext }}</p>
        </div>
        -->

    </button>
</template>
<script setup>
    import { defineProps, toRefs, ref, computed } from 'vue';

    const props = defineProps({
        element: Object,
        index: Number,
    })

    const { element } = toRefs(props);
    
    let buttonwidth = ref(250)
    if(!element.width){
        buttonwidth = element.width
    }

    const getImageUrl = (name) => {
        return new URL(`../assets/icons/${name}`, import.meta.url).href
    }


</script>
<style>
/*
    .imglinkcss {
        background-image: v-bind('element.imagelink');
    }
*/
</style>

