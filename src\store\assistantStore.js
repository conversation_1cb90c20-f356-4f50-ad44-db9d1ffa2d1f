import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';
import useNotification from '../../modules/notificationInformation';
import { useHelperStore } from '../../utils/helper';
import { useUserStore } from './userStore'; // Import wird jetzt aktiv genutzt
import { useMenuStore } from './menuStore';
import { processInstructionText } from '../utils/recipeUtils';

export const useAssistantStore = defineStore('assistant', () => {
    const { setNotification } = useNotification();
    const helper = useHelperStore();
    const menustore = useMenuStore();
    const userStore = useUserStore(); // userStore initialisieren

    const activeAssistant = ref(false);
    const activeAssistantOnPause = ref(false);
    const recording = ref(false);
    const instructions = ref('');
    const assistantPartialTranscript = ref('');
    const assistantFinalTranscript = ref('');

    let audioContext = null;
    let audioWorkletNode = null;
    let stream = null;
    let currentSource = null;
    let userSpeaking = false;
    const audioBufferArray = [];
    const AUDIO_BUFFER_MAX_SIZE = 4096; // Größe des Puffers in Bytes oder Anzahl


    // WebSocket-Setup
    let ws = null;
    let isConnected = false;
    const backendReadyForMessages = ref(false);
    const onMessageCallbacks = [];

    const connectToWebSocket = async (url) => {
        return new Promise((resolve, reject) => {
            if (isConnected) {
                console.warn("WebSocket is already connected.");
                return resolve();
            }

            ws = new WebSocket(url);

            ws.onopen = () => {
                isConnected = true; // Wichtig: Zuerst isConnected setzen
                backendReadyForMessages.value = false; // Initialisierung bei neuer Verbindung
                helper.devConsole('WebSocket connection ESTABLISHED and OPEN. Waiting for backend_ready_for_messages signal...'); // Angepasstes Log
                resolve();
            };

            ws.onmessage = (message) => {
                //helper.devConsole('Received message:', message.data); // Debugging Output
                onMessageCallbacks.forEach(callback => callback(message.data));
            };

            ws.onclose = (event) => {
                helper.devConsole(`WebSocket disconnected. Code: ${event.code}, Reason: '${event.reason}', WasClean: ${event.wasClean}`);
                isConnected = false;
                ws = null;
                // Hier wird die Promise nicht rejected, da onclose auch nach erfolgreichem open und normalem Betrieb ausgelöst werden kann.
                // Die Fehlerbehandlung für den Verbindungsaufbau geschieht primär über ws.onerror.
            };

            ws.onerror = (error) => {
                console.error('WebSocket error:', error); // Debugging Output
                isConnected = false;
                ws = null;
                reject(error);
            };
        });
    };

    const disconnectWebSocket = () => {
        if (ws) {
            ws.close();
            ws = null;
        }
        isConnected = false;
    };

    /*const sendWebSocketMessage = (message) => {
        if (isConnected && ws) {
            //helper.devConsole("message sending..")
            ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket is not connected. Message not sent.');
        }
    };*/
    const sendWebSocketMessage = (message) => {
        const messageString = JSON.stringify(message);
        if (isConnected && ws && ws.readyState === WebSocket.OPEN) {
            // helper.devConsole(`Attempting to send WebSocket message: ${messageString}`); // DEAKTIVIERT für gesprächige Nachrichten
            ws.send(messageString);
            // helper.devConsole(`Successfully sent WebSocket message: ${messageString}`); // DEAKTIVIERT für gesprächige Nachrichten
        } else {
            helper.devConsole(`WebSocket NOT CONNECTED or NOT OPEN. Message NOT SENT: ${messageString}. State: isConnected=${isConnected}, ws.readyState=${ws?.readyState}`);
            console.warn('WebSocket is not connected or open. Message not sent.', message);
        }
    };

    const addWebSocketMessageCallback = (callback) => {
        onMessageCallbacks.push(callback);
    };

    // Function for playing audio
    let isAudioPlaying = false;
    const audioQueue = [];

    const stopAudioPlayback = () => {
        if (currentSource) {
            helper.devConsole('[ASSISTANT STORE] stopAudioPlayback: Stopping current audio source.'); // NEUES LOG
            try {
                currentSource.stop();
                helper.devConsole('[ASSISTANT STORE] stopAudioPlayback: currentSource.stop() called successfully.'); // NEUES LOG
            } catch (e) {
                helper.devConsole(`[ASSISTANT STORE] stopAudioPlayback: Error calling currentSource.stop(): ${e.message}`, e); // NEUES LOG
            }
            currentSource = null;
            audioQueue.length = 0;
            helper.devConsole('[ASSISTANT STORE] stopAudioPlayback: Audio playback stopped and queue cleared.'); // NEUES LOG
        } else {
            helper.devConsole('[ASSISTANT STORE] stopAudioPlayback: No currentSource to stop.'); // NEUES LOG
        }
    };

    const onUserSpeechDetected = () => {
        userSpeaking = true;
        stopAudioPlayback();
        sendWebSocketMessage({ type: 'input_audio_buffer.speech_started' });
    };

    const onUserSpeechEnded = () => {
        userSpeaking = false;
    };

    const playAudioFromBase64 = async (base64AudioData) => {
        helper.devConsole('[ASSISTANT STORE] playAudioFromBase64 called.'); // NEUES LOG
        audioQueue.push(base64AudioData);
        if (!isAudioPlaying) {
            helper.devConsole('[ASSISTANT STORE] isAudioPlaying is false. Starting audio queue processing.'); // NEUES LOG
            isAudioPlaying = true;
            await processAudioQueue();
            isAudioPlaying = false;
            helper.devConsole('[ASSISTANT STORE] Finished audio queue processing. isAudioPlaying set to false.'); // NEUES LOG
        } else {
            helper.devConsole('[ASSISTANT STORE] isAudioPlaying is true. Audio added to queue, will be processed once current playback finishes and queue is processed again.'); // VERBESSERTES LOG
        }
    };

    const processAudioQueue = async () => {
        helper.devConsole('[ASSISTANT STORE] processAudioQueue started.'); // NEUES LOG
        if (!audioContext) { // NEU: Sicherheitscheck für audioContext
            helper.devConsole('[ASSISTANT STORE] processAudioQueue: audioContext is not initialized. Cannot play audio.');
            isAudioPlaying = false; // Zurücksetzen, damit es beim nächsten Mal neu versucht werden kann
            audioQueue.length = 0; // Leere die Queue, da wir nicht abspielen können
            return;
        }

        if (audioContext.state === 'suspended') {
            helper.devConsole('[ASSISTANT STORE] AudioContext is suspended. Attempting to resume.'); // NEUES LOG
            try {
                await audioContext.resume();
                helper.devConsole(`[ASSISTANT STORE] AudioContext state after resume: ${audioContext.state}`); // NEUES LOG
            } catch (e) {
                console.error('[ASSISTANT STORE] Error resuming AudioContext:', e); // NEUES LOG
                isAudioPlaying = false; // Fehler beim Resuming, abbrechen
                audioQueue.length = 0;
                return;
            }
        }

        while (audioQueue.length > 0) {
            helper.devConsole(`[ASSISTANT STORE] Processing item from audioQueue. Queue length: ${audioQueue.length}`); // NEUES LOG
            const base64AudioData = audioQueue.shift();
            try {
                helper.devConsole('[ASSISTANT STORE] Decoding Base64 audio data...'); // NEUES LOG
                const binaryString = window.atob(base64AudioData);
                const len = binaryString.length;
                const bytes = new Uint8Array(len);
                for (let i = 0; i < len; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                helper.devConsole('[ASSISTANT STORE] Adding WAV header...'); // NEUES LOG
                const wavBuffer = addWavHeader(bytes.buffer);
                helper.devConsole('[ASSISTANT STORE] Decoding audio data with audioContext...'); // NEUES LOG
                const audioBuffer = await audioContext.decodeAudioData(wavBuffer);
                helper.devConsole('[ASSISTANT STORE] Audio data decoded. Creating buffer source.'); // NEUES LOG
                currentSource = audioContext.createBufferSource();
                currentSource.buffer = audioBuffer;

                const gainNode = audioContext.createGain();
                gainNode.gain.value = 1.5;

                helper.devConsole('[ASSISTANT STORE] Connecting audio nodes and starting playback...'); // NEUES LOG
                currentSource.connect(gainNode);
                gainNode.connect(audioContext.destination);
                currentSource.start();

                await new Promise(resolve => {
                    currentSource.onended = () => {
                        helper.devConsole('[ASSISTANT STORE] Audio playback ended (onended event). currentSource cleared.'); // VERBESSERTES LOG
                        currentSource = null; // Wichtig: Quelle nach Ende freigeben/entfernen
                        resolve();
                    };
                });
            } catch (error) {
                console.error('[ASSISTANT STORE] Error during audio playback in processAudioQueue:', error);
                helper.devConsole('[ASSISTANT STORE] Error details during audio playback in processAudioQueue: Message:', error.message, 'Stack:', error.stack);
                currentSource = null; // Aufräumen bei Fehler
                // Breche nicht unbedingt die ganze Queue ab, vielleicht ist nur ein Chunk fehlerhaft?
                // Fürs Erste belassen wir es dabei, dass die Schleife weiterläuft für den nächsten Chunk.
            }
        }
        helper.devConsole('[ASSISTANT STORE] processAudioQueue finished (queue empty). isAudioPlaying will be set to false by caller if needed.');
    };

    const addWavHeader = (pcm16Array, sampleRate = 24000) => {
        const buffer = new ArrayBuffer(44 + pcm16Array.byteLength);
        const view = new DataView(buffer);

        let offset = 0;

        const writeString = (str) => {
            for (let i = 0; i < str.length; i++) {
                view.setUint8(offset++, str.charCodeAt(i));
            }
        };

        writeString('RIFF');
        view.setUint32(offset, 36 + pcm16Array.byteLength, true); offset += 4;
        writeString('WAVE');
        writeString('fmt ');
        view.setUint32(offset, 16, true); offset += 4;
        view.setUint16(offset, 1, true); offset += 2;
        view.setUint16(offset, 1, true); offset += 2;
        view.setUint32(offset, sampleRate, true); offset += 4;
        view.setUint32(offset, sampleRate * 2, true); offset += 4;
        view.setUint16(offset, 2, true); offset += 2;
        view.setUint16(offset, 16, true); offset += 2;
        writeString('data');
        view.setUint32(offset, pcm16Array.byteLength, true); offset += 4;

        const pcmView = new Uint8Array(buffer, 44);
        pcmView.set(new Uint8Array(pcm16Array));

        return buffer;
    };

    addWebSocketMessageCallback(async (message) => {
        const data = JSON.parse(message);

        helper.devConsole('[ASSISTANT STORE] Raw WebSocket message received from backend:', data);

        if (data.event === 'backend_ready_for_messages') {
            helper.devConsole('[ASSISTANT STORE] Received \'backend_ready_for_messages\'. User ID:', data.data?.userId);
            backendReadyForMessages.value = true;
            proceedWithSessionSetup();
        } else if (data.event === 'media' && data.media?.payload) {
            // Dieser Block könnte veraltet sein, wenn openai_audio_delta verwendet wird.
            // Vorerst belassen, falls das Backend noch 'media' sendet.
            helper.devConsole('[ASSISTANT STORE] Received legacy \'media\' event. User is not speaking:', !userSpeaking);
            if (!userSpeaking) {
                await playAudioFromBase64(data.media.payload);
            } else {
                helper.devConsole('[ASSISTANT STORE] User IS speaking. Legacy audio from backend will NOT be played.');
            }
        } else if (data.event === 'openai_audio_delta' && data.data?.audio) { // NEU für OpenAI Audio Chunks
            helper.devConsole('[ASSISTANT STORE] Received \'openai_audio_delta\'. User is not speaking:', !userSpeaking);
            if (!userSpeaking) {
                await playAudioFromBase64(data.data.audio);
            } else {
                helper.devConsole('[ASSISTANT STORE] User IS speaking. OpenAI audio_delta will NOT be played immediately.');
            }
        } else if (data.event === 'input_audio_buffer.speech_started') {
            helper.devConsole('[ASSISTANT STORE] Received \'input_audio_buffer.speech_started\' event from backend');
            stopAudioPlayback();
        } else if (data.event === 'openai_speech_started') { // NEU
            helper.devConsole('[ASSISTANT STORE] Received \'openai_speech_started\' event from backend. Stopping local playback if any.');
            stopAudioPlayback();
            // Hier könnte UI-Logik ausgelöst werden, z.B. "KI spricht jetzt..."
        } else if (data.event === 'openai_session_created' && data.sessionId) { // Alt: data.sessionId, Fallback falls Backend diese Struktur noch sendet
            helper.devConsole('[ASSISTANT STORE] Received \'openai_session_created\' (legacy structure). Session ID:', data.sessionId, 'Full data:', data);
        } else if (data.event === 'openai_session_created' && data.data?.sessionId) { // Neu: data.data.sessionId
            helper.devConsole('[ASSISTANT STORE] Received \'openai_session_created\'. Session ID:', data.data.sessionId, 'Full data:', data.data);
        } else if (data.event === 'openai_session_updated' && data.data) { // NEU
            helper.devConsole('[ASSISTANT STORE] Received \'openai_session_updated\'. Data:', data.data);
            // Ggf. relevant für Status-Updates im UI
        } else if (data.event === 'text_delta' && typeof data.text === 'string') { // Alt: data.text, Fallback
            helper.devConsole('[ASSISTANT STORE] Received legacy \'text_delta\'. Appending to assistantPartialTranscript.');
            assistantPartialTranscript.value += data.text;
        } else if (data.event === 'openai_text_delta' && data.data?.text) { // Neu: data.data.text
            helper.devConsole('[ASSISTANT STORE] Received \'openai_text_delta\'. Appending to assistantPartialTranscript.');
            assistantPartialTranscript.value += data.data.text;
        } else if (data.event === 'text_final' && typeof data.text === 'string') { // Alt: data.text, Fallback
            helper.devConsole('[ASSISTANT STORE] Received legacy \'text_final\'. Setting assistantFinalTranscript and clearing partial.');
            assistantFinalTranscript.value = data.text;
            assistantPartialTranscript.value = '';
        } else if (data.event === 'openai_text_final' && data.data?.text) { // Neu: data.data.text
            helper.devConsole('[ASSISTANT STORE] Received \'openai_text_final\'. Setting assistantFinalTranscript and clearing partial.');
            assistantFinalTranscript.value = data.data.text;
            assistantPartialTranscript.value = '';
        } else if (data.event === 'openai_error' && data.data?.message) { // Bevorzugte Struktur für Fehler
            console.error('[ASSISTANT STORE] OpenAI Error from backend:', data.data.message, 'Full Error:', data.data);
            setNotification(`OpenAI Fehler: ${data.data.message}`, "alert");
        } else if (data.event === 'openai_error' && data.error) { // Alternative Struktur für OpenAI Fehler
            console.error('[ASSISTANT STORE] OpenAI Error from backend (alt structure):', data.error);
            setNotification(`OpenAI Fehler: ${data.error.message || 'Unbekannter OpenAI Fehler'}`, "alert");
        } else if (data.event === 'openai_connection_closed' && data.data) {
            helper.devConsole('[ASSISTANT STORE] Received \'openai_connection_closed\': Code:', data.data.code, ', Reason:', data.data.reason);
            setNotification(`OpenAI Verbindung geschlossen: Code ${data.data.code}, Grund: ${data.data.reason}`, "info");
        } else if (data.event === 'client_error' && data.data?.message) { // Vom Backend generierter Client-Fehler
            console.error('[ASSISTANT STORE] Client Error from Backend:', data.data.message);
            setNotification(`Backend Client Fehler: ${data.data.message}`, "alert");
        } else if (data.event === 'backend_error' && data.data?.message) { // NEU für explizite Backend-Fehler
            console.error('[ASSISTANT STORE] Explicit Backend Error:', data.data.message);
            setNotification(`Backend Fehler: ${data.data.message}`, "alert");
        } else {
            helper.devConsole('[ASSISTANT STORE] Received unhandled WebSocket event. Event:', data.event, 'Full_Data:', data);
        }
    });

    // Recording and session management
    /*
    async function startAudioCaptureWithWorklet() {
        stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });

        await audioContext.audioWorklet.addModule('../../utils/processor');

        audioWorkletNode = new AudioWorkletNode(audioContext, 'pcm-encode-processor');

        audioWorkletNode.port.onmessage = (event) => {
            const uint8Array = new Uint8Array(event.data);
            sendWebSocketMessage({ type: 'input_audio_buffer.append', audio: btoa(String.fromCharCode.apply(null, uint8Array)) });
        };

        const input = audioContext.createMediaStreamSource(stream);
        input.connect(audioWorkletNode);
    }*/

        async function startAudioCaptureWithInlineProcessor() {
            try {
                // Get user media (microphone access)
                stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                console.log('Audio stream started');

                if (stream.getAudioTracks().length > 0) {
                    const audioTrack = stream.getAudioTracks()[0];
                    const audioTrackSettings = audioTrack.getSettings();
                    helper.devConsole('[ASSISTANT STORE] Actual audio track settings:', audioTrackSettings);
                    console.log('[ASSISTANT STORE] Actual audio track settings:', audioTrackSettings);
                    helper.devConsole(`[ASSISTANT STORE] Audio track enabled: ${audioTrack.enabled}, muted: ${audioTrack.muted}`);
                    console.log(`[ASSISTANT STORE] Audio track enabled: ${audioTrack.enabled}, muted: ${audioTrack.muted}`);
                    audioTrack.onended = () => { console.warn('[ASSISTANT STORE] Audio track ended.'); helper.devConsole('[ASSISTANT STORE] Audio track ended event fired.'); };
                    audioTrack.onmute = () => { console.warn('[ASSISTANT STORE] Audio track muted.'); helper.devConsole('[ASSISTANT STORE] Audio track mute event fired.'); };
                    audioTrack.onunmute = () => { console.log('[ASSISTANT STORE] Audio track unmuted.'); helper.devConsole('[ASSISTANT STORE] Audio track unmute event fired.'); };
                } else {
                    console.warn('[ASSISTANT STORE] No audio tracks found in the stream.');
                    helper.devConsole('[ASSISTANT STORE] No audio tracks found in the stream.');
                }

                const desiredSampleRate = 16000;
                audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: desiredSampleRate });
                helper.devConsole(`[ASSISTANT STORE] AudioContext initialized. Initial state: ${audioContext.state}, Actual sampleRate: ${audioContext.sampleRate}`);
                console.log(`[ASSISTANT STORE] AudioContext initialized. Initial state: ${audioContext.state}, Desired SR: ${desiredSampleRate}, Actual SR: ${audioContext.sampleRate}`);

                if (audioContext.state === 'suspended') {
                    helper.devConsole('[ASSISTANT STORE] AudioContext is suspended. Attempting to resume...');
                    console.log('[ASSISTANT STORE] AudioContext is suspended. Attempting to resume...');
                    try { await audioContext.resume(); } catch (e) { console.error('[ASSISTANT STORE] Error resuming AC:', e); helper.devConsole('[ASSISTANT STORE] Error resuming AC:', e.name, e.message);}
                    helper.devConsole(`[ASSISTANT STORE] AudioContext state after resume attempt: ${audioContext.state}`);
                    console.log(`[ASSISTANT STORE] AudioContext state after resume attempt: ${audioContext.state}`);
                }

                const processorScript = `
                    class PCMEncodeProcessor extends AudioWorkletProcessor {
                        constructor() { super(); this.hasLoggedFloatSamples = false; }
                        process(inputs) {
                            const inputChannels = inputs[0];
                            if (inputChannels.length > 0) {
                                const samples = inputChannels[0];
                                if (samples.length > 5 && !this.hasLoggedFloatSamples) {
                                    const firstFewSamples = []; for (let i = 0; i < 5; i++) { firstFewSamples.push(samples[i]); }
                                    this.port.postMessage({ type: 'debug_float_samples', data: firstFewSamples });
                                    this.hasLoggedFloatSamples = true;
                                }
                                const int16Array = new Int16Array(samples.length);
                                for (let i = 0; i < samples.length; i++) { int16Array[i] = Math.max(-1, Math.min(1, samples[i])) * 0x7FFF; }
                                this.port.postMessage(int16Array.buffer, [int16Array.buffer]);
                            } return true;
                        }
                    }
                    registerProcessor('pcm-encode-processor', PCMEncodeProcessor);
                `;
                const blob = new Blob([processorScript], { type: 'application/javascript' });
                const moduleURL = URL.createObjectURL(blob);
                await audioContext.audioWorklet.addModule(moduleURL);

                audioWorkletNode = new AudioWorkletNode(audioContext, 'pcm-encode-processor');
                audioWorkletNode.port.onmessage = (event) => {
                    if (event.data && event.data.type === 'debug_float_samples') {
                        helper.devConsole('[ASSISTANT STORE] Raw float samples from Worklet (first 5):', event.data.data);
                        console.log('[ASSISTANT STORE] Raw float samples from Worklet (first 5):', event.data.data);
                    } else if (event.data instanceof ArrayBuffer) {
                        const uint8Array = new Uint8Array(event.data);
                        const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));
                        helper.devConsole('[ASSISTANT STORE] Base64 Encoded Audio Chunk:', base64Audio);
                        console.log('[ASSISTANT STORE] Base64 Encoded Audio Chunk:', base64Audio);
                        sendWebSocketMessage({ type: 'input_audio_buffer.append', audio: base64Audio });
                    } else { console.warn('[ASSISTANT STORE] Received unknown message type from AudioWorklet:', event.data); }
                };

                helper.devConsole(`[ASSISTANT STORE] AudioContext state BEFORE connecting source: ${audioContext.state}`);
                console.log(`[ASSISTANT STORE] AudioContext state BEFORE connecting source: ${audioContext.state}`);

                const inputSourceNode = audioContext.createMediaStreamSource(stream);

                // NEU: AnalyserNode einfügen
                const analyserNode = audioContext.createAnalyser();
                analyserNode.fftSize = 2048; // Standardgröße, kann angepasst werden
                const bufferLength = analyserNode.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);

                inputSourceNode.connect(analyserNode);
                // Optional: AnalyserNode mit dem Worklet verbinden, wenn das Signal weiterverarbeitet werden soll
                // Wenn der Analyser nur zum Debuggen dient, kann diese Verbindung zum Worklet auch temporär entfernt werden,
                // um sicherzustellen, dass der Analyser das Signal nicht beeinflusst (sollte er aber nicht wesentlich tun).
                analyserNode.connect(audioWorkletNode); // Signal geht weiter zum Worklet
                // Für reinen Test, ob überhaupt was am Analyser ankommt, könnte man den Worklet temporär abklemmen:
                // inputSourceNode.connect(analyserNode);
                // und audioWorkletNode NICHT verbinden.

                console.log('Audio AnalyserNode and Worklet Node connected');

                // Debug-Funktion, um Analyser-Daten zu loggen
                const checkAudioActivity = () => {
                    if (!recording.value || !analyserNode) return; // Stoppen, wenn Aufnahme beendet

                    analyserNode.getByteTimeDomainData(dataArray);
                    let sum = 0;
                    for (let i = 0; i < bufferLength; i++) {
                        sum += Math.abs(dataArray[i] - 128); // Abweichung vom Mittelwert (Stille)
                    }
                    const averageDeviation = sum / bufferLength;
                    helper.devConsole(`[ASSISTANT STORE] AnalyserNode - Average Deviation from Silence: ${averageDeviation.toFixed(2)}`);
                    console.log(`[ASSISTANT STORE] AnalyserNode - Average Deviation from Silence: ${averageDeviation.toFixed(2)}`);

                    // Prüfen, ob signifikante Aktivität vorhanden ist
                    if (averageDeviation > 1.0) { // Schwellenwert anpassen, >0.5 oder >1.0 könnte Aktivität anzeigen
                        helper.devConsole('[ASSISTANT STORE] AnalyserNode: Potential audio activity detected!');
                        console.log('[ASSISTANT STORE] AnalyserNode: Potential audio activity detected!');
                    }
                    setTimeout(checkAudioActivity, 500); // Alle 500ms prüfen
                };

                if (audioContext.state === 'running') {
                    checkAudioActivity(); // Starte das periodische Logging vom AnalyserNode
                } else {
                     helper.devConsole('[ASSISTANT STORE] AudioContext not running, Analyser check not started.');
                     console.log('[ASSISTANT STORE] AudioContext not running, Analyser check not started.');
                }

            } catch (error) {
                console.error('Error starting audio capture with worklet:', error);
                helper.devConsole('[ASSISTANT STORE] Error starting audio capture:', error.name, error.message);
                setNotification(`Fehler bei Audioaufnahme: ${error.message}`, "alert");
            }
        }

    const proceedWithSessionSetup = async () => { // NEU: Funktion für Schritte nach Backend-Bereitschaft
        if (!backendReadyForMessages.value) {
            helper.devConsole("Cannot proceed with session setup, backend not ready.");
            return;
        }
        helper.devConsole("Backend is ready. Proceeding with session setup...");

        const currentRecipe = menustore.oneReciept;
        const userFirstName = userStore.user?.firstName || 'Benutzer'; // Vorname des Benutzers abrufen

        let baseInstructions = "Du bist ein Kochassistent und führst den Nutzer Schritt für Schritt durch das Rezept, indem du immer nur die nächste einzelne Handlung in klarer und kurzer Form angibst. Warte nach jedem Schritt auf eine Bestätigung oder Rückmeldung, bevor du fortfährst. Passe die Geschwindigkeit und Komplexität deiner Anweisungen an das Feedback des Nutzers an. Gib zu jedem Schritt praktische, prägnante Tipps oder Hinweise, die das Kochen erleichtern, beispielsweise zeitsparende Techniken oder Hinweise zur Fehlervermeidung. Ergänze deine Anweisungen, wo sinnvoll, um sensorische Beschreibungen wie Farbe, Geruch oder Konsistenz, damit der Nutzer weiß, worauf er achten soll. Biete bei Bedarf Alternativen für Zutaten oder Arbeitsschritte an, ohne den Nutzer zu überfordern. Vermeide es, mehrere Aufgaben gleichzeitig zu nennen, und halte deine Informationen stets übersichtlich. Kommuniziere sachlich und konzentriere dich ausschließlich auf die Kochanleitung, ohne Meta-Kommentare oder emotionale Floskeln. Reagiere flexibel auf Fragen oder Probleme während des Kochprozesses und passe deine Anleitung entsprechend an. Wiederhole keine bereits gegebenen Informationen unnötig.";

        if (userFirstName) {
            // Anweisung für die KI, den Vornamen zu verwenden, an den Anfang stellen
            baseInstructions = `Der Benutzer heißt ${userFirstName}. Sprich ihn bitte mit seinem Vornamen an, wenn es passend ist. ${baseInstructions}`;
            helper.devConsole(`User's first name '${userFirstName}' will be included in instructions.`);
        } else {
            helper.devConsole("User's first name not available or not found in userStore.");
        }

        let sessionUpdateMessage = {
            type: 'session.update',
            payload: {}, // Behalten für rezeptspezifische Daten, falls das Backend sie dort erwartet
            session: { // OpenAI-spezifische Konfiguration
                instructions: baseInstructions, // Basis-Instruktionen (ggf. mit Namen)
                modalities: ["audio", "text"],
                voice: "alloy", // Beispielhafte Stimme, konfigurierbar machen falls nötig
                turn_detection: {
                    type: "semantic_vad",
                    eagerness: "medium",
                    create_response: true
                }
                // temperature: 0.7, // Beispiel, falls von OpenAI hier erwartet und konfigurierbar sein soll
                // language: "de-DE" // Beispiel, falls von OpenAI hier erwartet und konfigurierbar sein soll
            }
        };

        if (currentRecipe && currentRecipe._id) {
            // Verwende verarbeitete Rezeptdaten für den Assistenten
            const processedRecipe = getProcessedRecipeData();

            let recipeInstructions = `Kontext für das aktuelle Gespräch ist das Rezept '${currentRecipe.name}'. Beschreibung: ${currentRecipe.description}.`;

            if (processedRecipe) {
                // Füge verarbeitete Zubereitungsschritte hinzu
                const preparationText = processedRecipe.preperation?.map((step, index) =>
                    `Schritt ${index + 1}: ${step.head} - ${step.content}`
                ).join(' ') || '';

                // Füge skalierte Zutaten hinzu
                const ingredientsText = processedRecipe.scaledIngredients?.map(ingredient =>
                    ingredient.displayText
                ).join(', ') || '';

                recipeInstructions += ` Zutaten für ${processedRecipe.currentPersons} Personen: ${ingredientsText}. Zubereitungsschritte: ${preparationText}`;

                helper.devConsole(`[ASSISTANT] Using processed recipe data with scaled quantities for ${processedRecipe.currentPersons} persons`);
            }

            // Rezept-Instruktionen werden NACH der Namensanweisung und der Standard-KI-Rolle eingefügt
            sessionUpdateMessage.session.instructions = `${recipeInstructions} ${sessionUpdateMessage.session.instructions}`;

            sessionUpdateMessage.payload.recipe_data = {
                id: currentRecipe._id,
                name: currentRecipe.name,
                description: currentRecipe.description,
                processed_data: processedRecipe
            };
            helper.devConsole(`Preparing to send session.update. Instructions further updated with recipe: ${currentRecipe.name}.`);
        } else {
            helper.devConsole("No recipe context. Sending session.update with current instructions (default or with user name).");
        }

        helper.devConsole("Final instructions for OpenAI:", sessionUpdateMessage.session.instructions);
        helper.devConsole("Sending session.update message:", JSON.stringify(sessionUpdateMessage, null, 2));
        sendWebSocketMessage(sessionUpdateMessage);

        helper.devConsole("Starting audio capture after backend is ready...");
        await startAudioCaptureWithInlineProcessor();
    };

    const createSession = async() => {
        if (!recording.value) {
            try {
                // ADDED: Check realtime API license before starting session
                helper.devConsole("Checking realtime API license...");
                const licenseResponse = await axios.post(
                    `${import.meta.env.VITE_API_BASE_URL}/api/v1/creator/assistant/session/start`
                );

                if (!licenseResponse.data.success) {
                    throw new Error('Lizenzprüfung fehlgeschlagen');
                }

                helper.devConsole("License check passed, starting assistant session...");
                setNotification("Aufnahme gestartet", "info");
                recording.value = true;
                activeAssistant.value = true;

                helper.devConsole("Connecting to WebSocket...");
                await initializeConnection();
                // hasLoggedFirstBase64Audio.value = false; // ENTFERNT

                // ALT: Die folgende Logik wird nun in proceedWithSessionSetup() verschoben und
                // erst nach Empfang von 'backend_ready_for_messages' ausgeführt.
                // const currentRecipe = menustore.oneReciept;
                // if (currentRecipe && currentRecipe._id) { ... }
                // await startAudioCaptureWithInlineProcessor();

            } catch (err) {
                console.error("Error during session creation:", err);
                helper.devConsole(err);

                // IMPROVED: Better error handling for license and connection errors
                if (err.response && err.response.status === 402) {
                    setNotification("Deine Sprach-Assistent Kapazität für diesen Monat ist aufgebraucht. Upgrade dein Abo für mehr Nutzung.", "alert");
                } else if (err.response && err.response.status === 401) {
                    setNotification("Authentifizierung fehlgeschlagen. Bitte logge dich erneut ein.", "alert");
                } else if (err.message === 'Lizenzprüfung fehlgeschlagen') {
                    setNotification("Lizenzprüfung fehlgeschlagen. Bitte versuche es erneut.", "alert");
                } else {
                    setNotification("Fehler beim Starten der Assistant-Session. Bitte versuche es erneut.", "alert");
                }

                recording.value = false;
                activeAssistant.value = false;
            }
        }
    };

    const endSession = () => {
        if (recording.value) {
            setNotification("Aufnahme beendet", "info");
            recording.value = false;
            activeAssistant.value = false;

            stopAudioPlayback();
            isAudioPlaying = false;
            audioQueue.length = 0;

            userSpeaking = false;
            backendReadyForMessages.value = false; // NEU: Zurücksetzen bei Sitzungsende
            // hasLoggedFirstBase64Audio.value = false; // ENTFERNT

            // Signalisiere das Ende des Audio-Streams, falls noch verbunden
            // if (isConnected && ws && ws.readyState === WebSocket.OPEN) { // AUSKOMMENTIERT gemäß Backend-Anweisung
            //     sendWebSocketMessage({ type: 'input_audio_buffer.flush' });
            //     helper.devConsole('Sent input_audio_buffer.flush message.');
            // }

            disconnectWebSocket();

            if (audioWorkletNode) {
                audioWorkletNode.port.onmessage = null;
                audioWorkletNode.disconnect();
                audioWorkletNode = null;
            }

            if (audioContext) {
                audioContext.close().catch(error => console.error("Error closing audio context:", error));
                audioContext = null;
            }

            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
        }
    };

    // Funktion zum Verarbeiten der Rezeptdaten für den Assistenten
    const getProcessedRecipeData = () => {
        if (!menustore.oneReciept?.menuchilds?.menuChildId) {
            return null;
        }

        const recipe = menustore.oneReciept.menuchilds.menuChildId;
        const ingredients = recipe.ingredients || [];
        const currentPersons = menustore.oneReciept.menuchilds.numberOfPersons || 1;
        const originalPersons = recipe.seatCount || currentPersons;

        // Verarbeite alle Zubereitungsschritte
        const processedPreparation = recipe.preperation?.map(step => ({
            ...step,
            content: processInstructionText(
                step.content,
                ingredients,
                currentPersons,
                originalPersons
            )
        })) || [];

        return {
            ...recipe,
            preperation: processedPreparation,
            scaledIngredients: ingredients.map(ingredient => {
                const scaleFactor = currentPersons / originalPersons;
                const scaledAmount = Math.round((ingredient.amount * scaleFactor) * 10) / 10;
                return {
                    ...ingredient,
                    scaledAmount: scaledAmount,
                    displayText: `${scaledAmount} ${ingredient.unit.name} ${ingredient.name.name}`
                };
            }),
            currentPersons: currentPersons,
            originalPersons: originalPersons
        };
    };

    const toggleAssistant = () => {
        activeAssistant.value = !activeAssistant.value;
        if (activeAssistant.value) {
            // Verwende verarbeitete Rezeptdaten statt rohe Daten
            const processedRecipe = getProcessedRecipeData();
            instructions.value = processedRecipe || menustore.oneReciept;
            createSession();
        } else {
            instructions.value = ''
            endSession();
        }
    };

    const assistantStatus = computed(() => {
        return activeAssistant.value ? 'Aktiv' : 'Inaktiv';
    });

    const initializeConnection = async () => {
        const session_token = localStorage.getItem('session_token');
        if (!session_token) {
            console.error("Assistant WebSocket: Session token not found. Cannot connect.");
            setNotification("Assistant: Authentifizierungstoken nicht gefunden.", "alert");
            return Promise.reject(new Error("Session token not found")); // Promise ablehnen
        }

        const wsBaseUrl = import.meta.env.VITE_API_BASE_WS_URL || 'ws://localhost:8080';

        if (!wsBaseUrl) {
            console.error("Assistant WebSocket: VITE_API_BASE_WS_URL is not defined and no fallback worked. Cannot construct WebSocket URL.");
            setNotification("Assistant: API-Konfiguration nicht gefunden.", "alert");
            return Promise.reject(new Error("WebSocket base URL not configured")); // Promise ablehnen
        }

        const encodedToken = encodeURIComponent(session_token);
        // Die URL soll NUR den Token enthalten, recipeId wird über context_setup nach onopen gesendet.
        const wsUrl = `${wsBaseUrl}/ws/assist-ant/startconversation?token=${encodedToken}`;

        helper.devConsole(`Assistant WebSocket: Connecting to (URL without recipeId): ${wsUrl}`);
        // connectToWebSocket gibt bereits eine Promise zurück, die bei Fehler rejected.
        // Wir müssen hier nicht erneut in try/catch wrappen, der Fehler propagiert zu createSession.
        return connectToWebSocket(wsUrl);
    };

    return {
        createSession,
        endSession,
        toggleAssistant,
        activeAssistant,
        activeAssistantOnPause,
        recording,
        assistantStatus,
        instructions,
        assistantPartialTranscript,
        assistantFinalTranscript
    };
});