#!/usr/bin/env node

/**
 * ⚠️  SECURITY WARNING: DEVELOPMENT ONLY SCRIPT ⚠️
 *
 * This script grants admin permissions to users.
 *
 * 🚨 NEVER DEPLOY THIS SCRIPT TO PRODUCTION! 🚨
 *
 * Usage: node scripts/grant-admin-direct.js <extId> [permissions]
 *
 * Example:
 * node scripts/grant-admin-direct.js user-test-11111111-1111-1111-1111-111111111111 backend_access,pinterest_management
 */

// Load environment variables FIRST
// Try both .env and config.env files
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: 'config.env' });

// Security check - prevent running in production
if (process.env.NODE_ENV === 'production') {
    console.error('🚨 SECURITY ERROR: This script cannot run in production environment!');
    console.error('🚨 This script is for development use only!');
    process.exit(1);
}

const mongoose = require('mongoose');

// Connect to MongoDB
const connectDB = async () => {
    try {
        // Get database connection string and password
        let DB = process.env.DATABASE_DEV ||
                 process.env.DATABASE_URI ||
                 process.env.MONGODB_URI ||
                 'mongodb://localhost:27017/ordy';

        // Replace <PASSWORD> placeholder with actual password (like in db.js)
        if (DB.includes('<PASSWORD>') && process.env.DATABASE_DEV_PASSWORD) {
            DB = DB.replace('<PASSWORD>', process.env.DATABASE_DEV_PASSWORD);
            console.log('🔗 Connecting to MongoDB...');
            console.log(`📍 Database: ${DB.replace(/\/\/.*:.*@/, '//***:***@')}`); // Hide credentials in log
        } else if (DB.includes('<PASSWORD>')) {
            console.error('❌ DATABASE_DEV_PASSWORD environment variable is not set!');
            console.error('💡 Please set DATABASE_DEV_PASSWORD in your config.env file');
            process.exit(1);
        } else {
            console.log('🔗 Connecting to MongoDB...');
            console.log(`📍 Database: ${DB.replace(/\/\/.*:.*@/, '//***:***@')}`); // Hide credentials in log
        }

        await mongoose.connect(DB);
        console.log('✅ Connected to MongoDB successfully');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        console.error('💡 Make sure your DATABASE_DEV and DATABASE_DEV_PASSWORD environment variables are set correctly');
        process.exit(1);
    }
};

// Grant admin permissions using direct MongoDB operations
const grantAdminPermissions = async (extId, permissions = []) => {
    try {
        // Find user by extId (Stytch User ID)
        const user = await mongoose.connection.db.collection('users').findOne({ extId: extId });

        if (!user) {
            console.error(`❌ User with extId '${extId}' not found`);
            console.log(`💡 Make sure the extId is correct. You can list all users to find the right extId.`);
            return;
        }

        // Default permissions if none specified
        if (permissions.length === 0) {
            permissions = ['backend_access', 'pinterest_management', 'marketing_management', 'analytics_view'];
        }

        // Create admin permissions object
        const adminPermissions = {
            isAdmin: true,
            canAccessBackend: permissions.includes('backend_access'),
            canManagePinterest: permissions.includes('pinterest_management'),
            canManageMarketing: permissions.includes('marketing_management'),
            canViewAnalytics: permissions.includes('analytics_view'),
            permissions: permissions
        };

        // Update user
        await mongoose.connection.db.collection('users').updateOne(
            { extId: extId },
            { $set: { adminPermissions: adminPermissions } }
        );

        console.log('✅ Admin permissions granted successfully!');
        console.log(`🆔 ExtId: ${extId}`);
        console.log(`📧 Email: ${user.email}`);
        console.log(`🔑 Permissions: ${permissions.join(', ')}`);
        console.log(`👤 User ID: ${user._id}`);
        console.log(`📝 Full Name: ${user.firstName} ${user.lastName}`);

    } catch (error) {
        console.error('❌ Error granting admin permissions:', error.message);
    }
};

// List all admin users
const listAdminUsers = async () => {
    try {
        const adminUsers = await mongoose.connection.db.collection('users')
            .find({ 'adminPermissions.isAdmin': true })
            .project({ firstName: 1, lastName: 1, email: 1, extId: 1, adminPermissions: 1 })
            .toArray();

        if (adminUsers.length === 0) {
            console.log('📋 No admin users found');
            return;
        }

        console.log('📋 Current Admin Users:');
        console.log('='.repeat(70));

        adminUsers.forEach((user, index) => {
            console.log(`${index + 1}. ${user.firstName} ${user.lastName}`);
            console.log(`   🆔 ExtId: ${user.extId}`);
            console.log(`   📧 Email: ${user.email}`);
            console.log(`   🔑 Permissions: ${user.adminPermissions.permissions.join(', ')}`);
            console.log('');
        });

    } catch (error) {
        console.error('❌ Error listing admin users:', error.message);
    }
};

// List all users (to find extId)
const listAllUsers = async () => {
    try {
        const users = await mongoose.connection.db.collection('users')
            .find({})
            .project({ firstName: 1, lastName: 1, email: 1, extId: 1, defaultKitchentable: 1 })
            .toArray();

        if (users.length === 0) {
            console.log('📋 No users found');
            return;
        }

        console.log('📋 All Users:');
        console.log('='.repeat(70));

        users.forEach((user, index) => {
            console.log(`${index + 1}. ${user.firstName} ${user.lastName}`);
            console.log(`   🆔 ExtId: ${user.extId}`);
            console.log(`   📧 Email: ${user.email}`);
            console.log(`   🏠 Default Kitchentable: ${user.defaultKitchentable || 'None'}`);
            console.log('');
        });

    } catch (error) {
        console.error('❌ Error listing users:', error.message);
    }
};

// Debug: Check user's kitchentable memberships
const checkUserKitchentables = async (userId) => {
    try {
        console.log(`🔍 Checking kitchentables for user: ${userId}`);

        // Find all kitchentables where user is a member
        const kitchentables = await mongoose.connection.db.collection('kitchentables')
            .find({ 'members.userId': new mongoose.Types.ObjectId(userId) })
            .toArray();

        console.log(`📋 User is member of ${kitchentables.length} kitchentable(s):`);
        console.log('='.repeat(70));

        kitchentables.forEach((kt, index) => {
            console.log(`${index + 1}. Kitchentable ID: ${kt._id}`);
            console.log(`   📍 Address: ${kt.tableAddress_street}, ${kt.tableAddress_plztown}`);
            console.log(`   👥 Total Members: ${kt.members.length}`);

            // Check user's role in this kitchentable
            const userMembership = kt.members.find(m => m.userId.toString() === userId);
            if (userMembership) {
                console.log(`   🔑 User Role ID: ${userMembership.roleId}`);
            }
            console.log('');
        });

        return kitchentables;

    } catch (error) {
        console.error('❌ Error checking user kitchentables:', error.message);
        return [];
    }
};

// Main function
const main = async () => {
    await connectDB();

    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('📋 Listing all admin users...\n');
        await listAdminUsers();
    } else if (args[0] === '--list-all' || args[0] === '-a') {
        console.log('📋 Listing all users...\n');
        await listAllUsers();
    } else if (args[0] === '--check-kt' || args[0] === '-kt') {
        const userId = args[1];
        if (!userId) {
            console.error('❌ Please provide a user ID to check kitchentables');
            console.log('Usage: node scripts/grant-admin-direct.js --check-kt <userId>');
            return;
        }
        console.log('🔍 Checking user kitchentables...\n');
        await checkUserKitchentables(userId);
    } else {
        const extId = args[0];
        const permissionsArg = args[1];
        const permissions = permissionsArg ? permissionsArg.split(',').map(p => p.trim()) : [];

        console.log(`🔧 Granting admin permissions to extId: ${extId}`);
        console.log(`🔑 Permissions: ${permissions.length > 0 ? permissions.join(', ') : 'All default permissions'}\n`);

        await grantAdminPermissions(extId, permissions);
    }

    await mongoose.disconnect();
    console.log('\n✅ Script completed');
};

// Handle errors
process.on('unhandledRejection', (err) => {
    console.error('❌ Unhandled Promise Rejection:', err.message);
    process.exit(1);
});

// Run the script
if (require.main === module) {
    main();
}

module.exports = { grantAdminPermissions, listAdminUsers, listAllUsers };
