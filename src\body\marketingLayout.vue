<template>
  <div class="bg-ordy h-auto z-0 min-h-screen flex flex-col">
    <!-- Top Navigation Container -->
    <div class="w-full h-auto relative z-60">
        <top-navigation></top-navigation>
    </div>

    <!-- Main Content Area (Sidebar + View) -->
    <div class="flex flex-grow flex-col md:flex-row mt-6 md:mt-2 pb-12">

        <!-- Left Sidebar hier -->
        <div class="
        w-full
        md:w-1/12 md:min-w-[250px]
        lg:w-1/12 lg:min-w-[220px]
        xl:w-1/12 xl:min-w-[220px]
        2xl:w-3/12 2xl:min-w-[250px]
        3xl:w-3/12 3xl:min-w-[250px]
        px-5 md:relative md:order-1 flow order-2 fixed bottom-0 z-40">
            <left-sidebar></left-sidebar>
        </div>

        <!-- Content Column (Router View) -->
        <div class="w-full md:w-4/5 order-1 md:order-2 h-auto flex-grow">
            <div class="w-full min-h-full pb-24 pl-6 pr-6 md:pl-0 md:pr-0">
                    <router-view></router-view>
            </div>
        </div>

    </div>
    <!-- Footer can be added here if needed -->
  </div>
</template>

<script setup>
// Import navigation components
import topNavigation from './topNavigation.vue'
import leftSidebar from './leftSidebar.vue'
</script>

<style scoped>
/* Add any layout-specific styles here */
.bg-ordy{
    background: linear-gradient(152.67deg, #FFFFFF -4.31%, #EBDFFF 55.19%, #F8E5F3 117.2%, #FFFFFF 117.21%);
}
</style> 