# API Endpoint Documentation

This document provides an overview of the available API endpoints, their functionalities, required parameters, and expected responses.

## Auth & Payment Routes (`routes/authRoutes.js`)

### 1. Process Stripe Checkout Result

*   **Endpoint:** `GET /api/v1/auth/payment/checkout/retrival`
*   **Description:** Handles the redirect from <PERSON><PERSON> after a Checkout Session completes (both success and cancellation). It processes the session information provided by <PERSON><PERSON>.
*   **Query Parameters:**
    *   `session_id` (string, required): The session ID returned by <PERSON><PERSON> in the redirect URL.
*   **Responses:**
    *   `302 Found`: Redirects the user back to the frontend application after processing the session.
    *   `400 Bad Request`: Error occurred while processing the Stripe session information.

### 2. Authenticate OAuth Token

*   **Endpoint:** `GET /api/v1/auth/reg`
*   **Description:** Authenticates an OAuth token (e.g., from Stytch). It takes the token, verifies it with the provider, creates a new user in the local database if they don\'t exist, or logs in an existing user. Finally, it redirects to the frontend, passing session information as query parameters.
*   **Query Parameters:**
    *   `token` (string, required): The OAuth authentication token provided by the identity service (e.g., Stytch after successful OAuth flow).
*   **Responses:**
    *   `302 Found`: Redirects to the frontend application with session parameters (e.g., session token, user details) appended to the URL.
    *   `500 Internal Server Error`: Error during Stytch authentication or user creation/retrieval in the local database.

### 3. Verify Session Token (Middleware)

*   **Endpoint:** `POST /api/v1/auth/verify`
*   **Description:** Acts as middleware to verify a session token (e.g., from Stytch). It expects the token in the `Authorization: Bearer <token>` header. If the token is valid, it authenticates the session with the provider and attaches user data to the `req` object for subsequent handlers. This is typically used to protect routes.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** Not directly consumed by this middleware, but it passes the request body along to the next handler. The body format depends on the subsequent route handler.
*   **Responses:**
    *   *(Delegated)*: If verification succeeds, it calls the next middleware/handler. The final status code depends on that handler.
    *   `401 Unauthorized`: The provided token is invalid, expired, or missing.

### 4. User Logout

*   **Endpoint:** `GET /api/v1/auth/logout`
*   **Description:** Logs the current user out by attempting to revoke their active session with the authentication provider (e.g., Stytch), based on the token provided in the Authorization header. It returns success even if the session was already invalid.
*   **Security:** Requires `Bearer Token` authentication.
*   **Responses:**
    *   `200 OK`: Logout successful (session revoked or was already invalid). Returns `{ "success": true, "message": "Logout success" }`.

### 5. Get User Data by ID (Post-Login/Reload)

*   **Endpoint:** `POST /api/v1/auth/userbyid`
*   **Description:** Retrieves user data after a successful login or page reload. It uses the `verify` middleware to authenticate the token and then fetches the corresponding user details from the local database.
*   **Security:** Requires `Bearer Token` authentication (handled by the preceding `verify` middleware).
*   **Request Body:** Populated by the `verify` middleware with user information derived from the token. Not directly sent by the client for this specific route logic.
*   **Responses:**
    *   `201 Created`: User data successfully retrieved. Returns `{ "status": "success", "success": true, "data": { "user": { ...User Object... } } }`. (Refers to `#components/schemas/User`)
    *   `401 Unauthorized`: If the token verification fails in the middleware.

### 6. Search Users

*   **Endpoint:** `GET /api/v1/auth/userdata/bysearchstring/{searchstring}`
*   **Description:** Searches for users who have `externalAccess=true` based on a partial match of their email address or first name. Requires authentication.
*   **Security:** Requires `Bearer Token` authentication.
*   **Path Parameters:**
    *   `searchstring` (string, required): The search term to match against user emails or first names.
*   **Responses:**
    *   `201 Created`: Users found successfully. Returns `{ "success": true, "data": [ { "email": "...", "extId": "...", "_id": "..." }, ... ] }`.
    *   `401 Unauthorized`: If token verification fails.

### 7. Get Authenticated User Data

*   **Endpoint:** `POST /api/v1/auth/userdata`
*   **Description:** Fetches the complete data object for the currently authenticated user.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** None explicitly required for this route logic (authentication is handled by middleware).
*   **Responses:**
    *   `201 Created`: User data retrieved successfully. Returns `{ "success": true, "data": { ...User Object... } }`. (Refers to `#components/schemas/User`)
    *   `401 Unauthorized`: If token verification fails.

### 8. Update User Data

*   **Endpoint:** `POST /api/v1/auth/userdata/update`
*   **Description:** Updates specific fields for the currently authenticated user.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `id` (string, objectId): The ID of the user to update (Note: Might be derivable from the token, redundancy check needed).
    *   `payload` (object, required): An object containing the key-value pairs of the fields to update. Example: `{ "firstName": "NewFirstName", "defaultKitchentable": "newTableId" }`
*   **Responses:**
    *   `200 OK`: User data updated successfully. Returns `{ "success": true, "data": { ...update info... } }` (e.g., MongoDB update result).
    *   `400 Bad Request`: Error during the update process (e.g., invalid data).
    *   `401 Unauthorized`: If token verification fails.

### 9. Initialize Stripe Checkout Session

*   **Endpoint:** `GET /api/v1/auth/payment/checkout/{priceid}/{userid}`
*   **Description:** Creates and initializes a Stripe Checkout Session for a specific product (identified by `priceid`) and a specific user.
*   **Security:** Requires `Bearer Token` authentication.
*   **Path Parameters:**
    *   `priceid` (string, required): The Price ID of the product in Stripe.
    *   `userid` (string, objectId, required): The ID of the user for whom the payment is being initiated.
*   **Responses:**
    *   `201 Created`: Stripe Checkout Session created successfully. Returns `{ "status": "success", "success": true, "data": { ...Stripe Session Object... } }`.
    *   `401 Unauthorized`: If token verification fails.
    *   `500 Internal Server Error`: Error occurred while creating the Stripe session.

## GPT Creator Routes (`routes/gptRoutes.js`)

### 10. Create Recipe from Image

*   **Endpoint:** `POST /api/v1/creator/functions/reciept/createbyimage`
*   **Description:** Uploads an image of a recipe, analyzes it using AI to extract ingredients and instructions, creates a structured recipe (Menu and Menuchild) in the database, and optionally generates a corresponding AI image for the recipe. Requires a valid license.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (multipart/form-data, required)
    *   `image` (file, required): The recipe image file.
    *   `user_id` (string, objectId, optional): The ID of the user (may be inferred from the verified token).
*   **Responses:**
    *   `201 Created`: Recipe successfully created from the image. Returns API response containing the menu object. (Refers to `#components/schemas/ApiResponseMenu`)
    *   `400 Bad Request`: Invalid request, or error during image analysis or recipe generation.
    *   `401 Unauthorized`: Authentication failed.
    *   `402 Payment Required`: License check failed (e.g., insufficient `menuuploads` quota).

### 11. Create Weekly Menu Plan

*   **Endpoint:** `POST /api/v1/creator/menulist`
*   **Description:** Generates weekly menu plan suggestions using GPT based on user input (prompt). Requires a valid license.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `prompt` (string, required): The user\'s request for the meal plan (e.g., "Create a vegetarian meal plan for 2 people for 5 days.").
    *   *(Potentially other fields)*
*   **Responses:**
    *   `201 Created`: Menu list successfully generated. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.
    *   `402 Payment Required`: License check failed (e.g., insufficient `menucreations` quota).

### 12. Create Recipe from Text

*   **Endpoint:** `POST /api/v1/creator/functions/reciept/createbytext`
*   **Description:** Processes a text input (like an ingredients list or a description), generates a structured recipe using AI, creates the corresponding Menu and Menuchild entries in the database, and optionally generates an AI image. Requires a valid license.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `recieptText` (string, required): The text containing the recipe information. Example: "Pasta with tomato sauce and basil for 4 people..."
    *   `user_id` (string, objectId, optional): The ID of the user (may be inferred from the verified token).
*   **Responses:**
    *   `201 Created`: Recipe successfully created from text. Returns API response containing the menu object. (Refers to `#components/schemas/ApiResponseMenu`)
    *   `400 Bad Request`: Invalid request or error during text analysis/recipe generation.
    *   `401 Unauthorized`: Authentication failed.
    *   `402 Payment Required`: License check failed (e.g., insufficient `menuuploads` quota).

### 13. Create Recipe from URL

*   **Endpoint:** `POST /api/v1/creator/functions/reciept/createbyurl`
*   **Description:** Fetches the content of a webpage URL, extracts recipe information using AI, creates structured Menu and Menuchild entries, and optionally generates an AI image. Requires a valid license.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `url` (string, url, required): The URL of the webpage containing the recipe.
    *   `user_id` (string, objectId, optional): The ID of the user (may be inferred from the verified token).
*   **Responses:**
    *   `201 Created`: Recipe successfully created from the URL. Returns API response containing the menu object. (Refers to `#components/schemas/ApiResponseMenu`)
    *   `400 Bad Request`: Invalid URL or error during content extraction/recipe generation.
    *   `401 Unauthorized`: Authentication failed.
    *   `402 Payment Required`: License check failed (e.g., insufficient `menuuploads` quota).

### 14. Get GPT Model Details

*   **Endpoint:** `GET /api/v1/creator/details`
*   **Description:** Retrieves information about the underlying GPT model being used by the creator functions.
*   **Security:** Requires `Bearer Token` authentication.
*   **Responses:**
    *   `200 OK`: Model details retrieved successfully. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 15. Proof Ingredient with GPT

*   **Endpoint:** `GET /api/v1/creator/functions/ingredients/proof/{ingridient}`
*   **Description:** Sends an ingredient name to GPT for validation, enrichment, or categorization.
*   **Security:** Requires `Bearer Token` authentication.
*   **Path Parameters:**
    *   `ingridient` (string, required): The name of the ingredient to be checked.
*   **Responses:**
    *   `201 Created`: Ingredient successfully processed by GPT. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 16. Generate Recipes from Ingredients

*   **Endpoint:** `POST /api/v1/creator/functions/ingredients/toreciept`
*   **Description:** Takes a list of available ingredients and uses GPT to generate suitable recipe suggestions. Requires a valid license.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `ingredients` (array of strings, required): A list of available ingredients. Example: `["Tomatoes", "Onions", "Garlic", "Pasta"]`
    *   *(Potentially other options)*
*   **Responses:**
    *   `201 Created`: Recipe suggestions successfully generated. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.
    *   `402 Payment Required`: License check failed (e.g., insufficient `cookeasy` quota).

### 17. Calculate Nutritional Information

*   **Endpoint:** `POST /api/v1/creator/functions/ingredients/nutritionalcalc`
*   **Description:** Sends ingredient data or a full recipe structure to GPT for calculating estimated nutritional information.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `recipeData` (object or string, required): The data (ingredients, quantities, etc.) needed for the nutritional calculation.
*   **Responses:**
    *   `201 Created`: Nutritional information calculated successfully. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 18. Create Image from Recipe

*   **Endpoint:** `POST /api/v1/creator/functions/image/createoneimagebyreciept`
*   **Description:** Generates an AI image based on the details of an existing recipe (Menu object) and saves the image URL back to the Menu object in the database.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required)
    *   `menu` (object, required): Object containing at least the `id` and `name` (and potentially `description`) of the menu item to generate an image for.
        *   `id` (string, objectId, required)
        *   `name` (string, required)
        *   `description` (string, optional)
*   **Responses:**
    *   `201 Created`: Image successfully generated and linked to the menu. Returns API response containing the updated menu object. (Refers to `#components/schemas/ApiResponseMenu`)
    *   `400 Bad Request`: Error during image generation.
    *   `401 Unauthorized`: Authentication failed.

### 19. Basic GPT Chat (Unprotected)

*   **Endpoint:** `POST /api/v1/creator/functions/basic`
*   **Description:** A simple, unprotected endpoint to send a message to GPT and receive a response.
*   **Security:** None.
*   **Request Body:** (application/json, required)
    *   `message` (string, required): The message to send to GPT. Example: `"Hello GPT!"`
*   **Responses:**
    *   `200 OK`: Successful response from GPT. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)

### 20. Basic Innovation Function

*   **Endpoint:** `POST /api/v1/creator/functions/kinnovations/basic`
*   **Description:** Placeholder for a basic innovation-related function using GPT. Specific functionality depends on implementation.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required): Structure depends on the specific function\'s needs.
*   **Responses:**
    *   `201 Created`: Innovation function executed successfully. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 21. Create Innovation Meeting

*   **Endpoint:** `POST /api/v1/creator/functions/kinnovations/createmeeting`
*   **Description:** Creates a new meeting related to innovation tasks. Specific details (e.g., calendar integration) depend on implementation.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required): Structure depends on the specific function\'s needs (e.g., meeting topic, participants, time).
*   **Responses:**
    *   `201 Created`: Meeting successfully created. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 22. Get Free Calendar Slots for Innovation

*   **Endpoint:** `GET /api/v1/creator/functions/kinnovations/getfreecalendarslots`
*   **Description:** Retrieves available time slots from a calendar (likely integrated via another service) potentially for scheduling innovation-related activities. May also interact with Airtable.
*   **Security:** Requires `Bearer Token` authentication.
*   **Responses:**
    *   `200 OK`: Free calendar slots retrieved successfully. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

### 23. Respond to Innovation Phone Request

*   **Endpoint:** `POST /api/v1/creator/functions/kinnovations/responsePhoneRequest`
*   **Description:** Handles responding to a phone request related to innovation tasks, possibly interacting with Airtable data.
*   **Security:** Requires `Bearer Token` authentication.
*   **Request Body:** (application/json, required): Structure depends on the specific function\'s needs (e.g., request details, response content).
*   **Responses:**
    *   `201 Created`: Response sent successfully. Returns a generic API response object. (Refers to `#components/schemas/ApiResponseObject`)
    *   `401 Unauthorized`: Authentication failed.

## Referenced Schemas

### User Schema (`components/schemas/User`)

*   **Type:** `object`
*   **Properties:**
    *   `_id` (string, format: objectId): Unique identifier for the user.
    *   `extId` (string): External identifier from the authentication provider (e.g., Stytch ID).
    *   `extAuthService` (string): Name of the external authentication service used (e.g., "stytch").
    *   `firstName` (string): User\'s first name.
    *   `lastName` (string): User\'s last name.
    *   `email` (string, format: email): User\'s email address.
    *   `img` (string, format: url): URL to the user\'s profile image.
    *   `defaultKitchentable` (string, format: objectId): ID of the user\'s default kitchen table (if applicable).
    *   `externalAccess` (boolean): Flag indicating if the user is publicly searchable.
    *   *(... potentially other user-specific fields ...)*

### API Response Menu Schema (`components/schemas/ApiResponseMenu`)

*   **Type:** `object`
*   **Properties:**
    *   `status` (string, example: "success"): Status indicator.
    *   `success` (boolean, example: true): Boolean success flag.
    *   `data` (object): Contains the Menu object data. (Refers to a `Menu` schema, assumed to exist elsewhere or implicitly defined by usage).

### Generic API Response Object Schema (`components/schemas/ApiResponseObject`)

*   **Type:** `object`
*   **Properties:**
    *   `status` (string, example: "success"): Status indicator.
    *   `success` (boolean, example: true): Boolean success flag.
    *   `data` (object): Contains generic object data, varies depending on the endpoint.

## API Endpunkte für das Frontend (Zusammenfassung)

Hier ist eine Übersicht über wichtige Endpunkte, ihre korrekten Pfade und erwarteten Daten, um häufige Fehler zu vermeiden. **Achtet immer auf das korrekte HTTP Verb (GET, POST, etc.) und den vollständigen Pfad inklusive `/api/v1/`!**

---

### Authentifizierung & Benutzer

**1. Benutzerdaten abrufen (nach Login/Auth)**

*   **Beschreibung:** Holt die Daten des aktuell authentifizierten Benutzers.
*   **Methode:** `POST`
*   **URL:** `/api/v1/auth/userbyid`
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Euer_Stytch_Session_Token>`)
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (201 Created):**
    ```json
    // ACHTUNG: Doppelte Verschachtelung durch HTTP-Client (z.B. Axios)!
    // Zugriff im Frontend oft über response.data.data.user
    {
      "status": "success",
      "success": true,
      "data": {
        "user": {
          "_id": "...",
          "stytchId": "...",
          "email": "...",
          "firstName": "...",
          // ... weitere Benutzerfelder
        }
      }
    }
    ```
*   **Wichtige Hinweise:** Unbedingt die **`POST`** Methode verwenden! Auf die doppelte `data`-Verschachtelung in der Antwort achten.

**2. Logout**

*   **Beschreibung:** Loggt den aktuellen Benutzer aus (invalidiert die Stytch Session).
*   **Methode:** `GET`
*   **URL:** `/api/v1/auth/logout`
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Euer_Stytch_Session_Token>`)
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (200 OK):**
    ```json
    {
      "success": true,
      "message": "Logout success"
    }
    ```
*   **Wichtige Hinweise:** Unbedingt die **`GET`** Methode verwenden!

---

### Kitchentable & Einkaufslisten

**1. Einzelnen Kitchentable abrufen**

*   **Beschreibung:** Holt die Details zu einem spezifischen Kitchentable anhand seiner ID.
*   **Methode:** `GET`
*   **URL:** `/api/v1/kitchentable/:id` (Ersetze `:id` durch die Kitchentable ID)
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Token>`)
*   **Parameter (Pfad):** `:id` (Die ID des Kitchentables)
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (200 OK):** (Struktur hängt vom Model ab, typischerweise das Kitchentable Objekt)
    ```json
    {
      "status": "success",
      "data": {
        "data": { // Annahme: Standard-Wrapper des Controllers
           "_id": "...", // Kitchentable ID
           "name": "WG-Küche",
           "members": [ ... ],
           // ... weitere Felder
        }
      }
    }
    ```
*   **Wichtige Hinweise:** Pfad ist **`kitchentable` (Singular)**!

**2. Aktive Einkaufsliste eines Kitchentables abrufen**

*   **Beschreibung:** Holt die aktuell als "aktiv" markierte Einkaufsliste für einen Kitchentable.
*   **Methode:** `GET`
*   **URL:** `/api/v1/kitchentable/:kitchentableId/shopping-list/active` (Ersetze `:kitchentableId` durch die Kitchentable ID)
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Token>`)
*   **Parameter (Pfad):** `:kitchentableId` (Die ID des Kitchentables)
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (200 OK):** (Struktur hängt vom Model ab, typischerweise das ShoppingList Objekt)
    ```json
    {
        "status": "success",
        "data": {
            "shoppingList": { // Annahme: Name des Datenfelds
                "_id": "...", // ShoppingList ID
                "kitchentableId": "...",
                "status": "active",
                "items": [ ... ],
                // ... weitere Felder
            }
        }
    }
    ```
*   **Wichtige Hinweise:** Dieser Endpunkt liefert **nur die aktive** Liste, keine Übersicht aller Listen. Der Pfad beginnt mit `/api/v1/kitchentable/`.

---

### Menüs / Rezepte

**1. Menüs/Rezepte filtern und abrufen**

*   **Beschreibung:** Ruft eine Liste von Menüs ab, gefiltert nach Benutzer-Scope (eigene oder Kitchentable-Rezepte) und optional weiteren Kriterien.
*   **Methode:** `GET`
*   **URL:** `/api/v1/menu/complete/allbyuserid/:userid/kitchentableid/:kitchentableid` (Ersetze `:userid` und `:kitchentableid`)
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Token>`)
*   **Parameter (Pfad):**
    *   `:userid`: ID des Benutzers.
    *   `:kitchentableid`: ID des relevanten Kitchentables (wird für `scope=kitchentable` benötigt).
*   **Parameter (Query):**
    *   `scope` (Optional): `my_reciepts` (Standard) oder `kitchentable`.
    *   `sort` (Optional): Sortierfeld (z.B. `createdAt`, `-name`).
    *   `page` (Optional): Seite für Paginierung (0-basiert).
    *   `searchstring` (Optional): Textsuche im Menünamen.
    *   `cookingTime` (Optional): Filter nach Kochzeit.
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (201 Created - *Anmerkung: 201 für GET ist unüblich, aber so definiert*):**
    ```json
    {
      "status": "success",
      "success": true,
      "data": [ // Array von Menu-Objekten
        {
          "_id": "...",
          "name": "...",
          "author": "...",
          // ... weitere Menu-Felder
        },
        // ... weitere Menüs
      ]
    }
    ```
*   **Wichtige Hinweise:** Der Pfad ist exakt so definiert, inklusive `allbyuserid` und `kitchentableid` im Pfad selbst. Unbedingt das Präfix `/api/v1/` verwenden!

---

### Marketing

**1. Marketing Workflow auslösen**

*   **Beschreibung:** Startet den automatisierten Prozess zur Erstellung von Marketing-Inhalten (Text, Bilder, Video).
*   **Methode:** `POST`
*   **URL:** `/api/v1/marketing-workflow/trigger`
*   **Authentifizierung:** **Erforderlich** (Header: `Authorization: Bearer <Token>`)
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (200 OK):**
    ```json
    {
        "marketingContentId": "...", // ID des erstellten DB-Eintrags
        "videoS3Url": "https://..." // URL zum Video auf S3 (oder null)
    }
    ```
*   **Wichtige Hinweise:** Dies ist ein langlaufender Prozess, Timeout im Frontend erhöhen!

**2. Letzten öffentlichen Marketing-Inhalt abrufen**

*   **Beschreibung:** Holt den zuletzt erstellten Marketing-Inhalt, der öffentlich zugänglich ist.
*   **Methode:** `GET`
*   **URL:** `/api/v1/marketing-content/latest`
*   **Authentifizierung:** **Nicht erforderlich.**
*   **Request Body:** Keiner erforderlich.
*   **Erfolgsantwort (200 OK):** (Struktur hängt vom Model ab)
    ```json
    {
        "status": "success",
        "data": {
            "marketingContent": {
                "_id": "...",
                "hook": "...",
                "imageUrls": [ ... ],
                "videoUrl": "...",
                // ... weitere Felder
            }
        }
    }
    ```

---

## Umfassende (aber teilweise generische) API Endpoint Liste (Automatisch extrahiert)

**ACHTUNG:** Diese Liste wurde automatisch generiert und konzentriert sich auf Pfade und Methoden. Details zu Request/Response-Strukturen sind oft nicht enthalten oder geraten. Nutzen Sie dies als vorläufige Referenz, bis `/api-docs` verfügbar und vollständig ist.

---

### Auth Routes (`/api/v1/auth`) - (aus `authRoutes.js`)

*   `GET /payment/checkout/retrival`: Verarbeitet Stripe Redirect (Query: `session_id`). *(Dokumentiert)*
*   `POST /payment/stripe_webhook`: Stripe Webhook Endpoint. *(Auskommentiert im Code, aber evtl. relevant)*
*   `GET /reg`: Authentifiziert OAuth Token (Query: `token`). *(Dokumentiert)*
*   `POST /verify`: Middleware zur Token-Verifizierung (Header: `Authorization: Bearer <token>`). *(Dokumentiert)*
*   `GET /logout`: Benutzer-Logout (Header: `Authorization: Bearer <token>`). *(Dokumentiert)*
*   `POST /userbyid`: Holt Benutzerdaten nach Auth (Header: `Authorization: Bearer <token>`). *(Dokumentiert)*
*   `GET /userdata/bysearchstring/:searchstring`: Sucht Benutzer (Path: `searchstring`, Header: `Authorization: Bearer <token>`). *(Dokumentiert)*
*   `POST /userdata`: Holt Daten des eingeloggten Benutzers (Header: `Authorization: Bearer <token>`). *(Dokumentiert)*
*   `POST /userdata/update`: Aktualisiert Benutzerdaten (Header: `Authorization: Bearer <token>`, Body: `{ id, payload }`). *(Dokumentiert)*
*   `GET /payment/checkout/:priceid/:userid`: Initialisiert Stripe Checkout (Path: `priceid`, `userid`, Header: `Authorization: Bearer <token>`). *( Fehlte im Summary)*

---

### GPT Creator Routes (`/api/v1/creator`) - (aus `gptRoutes.js`)

*(Hinweis: Sehr viele Endpunkte, Liste möglicherweise unvollständig bei komplexen Logiken)*

*   `POST /functions/reciept/createbyimage`: Rezept aus Bild erstellen (Multipart Form: `image`, `user_id`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /menulist`: Wochenplan erstellen (Body: `prompt`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/reciept/createbytext`: Rezept aus Text erstellen (Body: `recieptText`, `user_id`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/reciept/createbyurl`: Rezept aus URL erstellen (Body: `url`, `user_id`, Header: `Authorization`). *(Dokumentiert)*
*   `GET /details`: GPT Model Details abrufen (Header: `Authorization`). *(Dokumentiert)*
*   `GET /functions/ingredients/proof/:ingridient`: Zutat prüfen (Path: `ingridient`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/ingredients/toreciept`: Rezept aus Zutaten generieren (Body: `ingredients`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/ingredients/nutritionalcalc`: Nährwertberechnung (Body: `recipeData`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/image/createoneimagebyreciept`: Bild aus Rezept generieren (Body: `menu`, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/basic`: Einfacher GPT Chat (Body: `message`, Keine Auth). *(Dokumentiert)*
*   `POST /functions/kinnovations/basic`: Basis Innovationsfunktion (Body: ?, Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/kinnovations/createmeeting`: Innovations-Meeting erstellen (Body: ?, Header: `Authorization`). *(Dokumentiert)*
*   `GET /functions/kinnovations/getfreecalendarslots`: Freie Kalender-Slots (Header: `Authorization`). *(Dokumentiert)*
*   `POST /functions/kinnovations/responsePhoneRequest`: Telefonanfrage beantworten (Body: ?, Header: `Authorization`). *(Dokumentiert)*

---

### Grocery List Routes (`/api/v1/grocerylist`) - (aus `grocerylistRoutes.js`)

*   `GET /all/related/:userid`: Alle Listen eines Benutzers (Path: `userid`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /all/related/:userid/kitchentableid/:kitchentableid`: Alle Listen eines Users/Kitchentables (Path: `userid`, `kitchentableid`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /:id`: Eine spezifische Liste abrufen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one`: Neue Einkaufsliste erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `PATCH /:id`: Einkaufsliste aktualisieren (Path: `id`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `DELETE /:id`: Einkaufsliste löschen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*

---

### Kitchentable Routes (`/api/v1/kitchentable`) - (aus `kitchentableRoutes.js`)

*   `GET /:id`: Einzelnen Kitchentable abrufen (Path: `id`, Header: `Authorization`). *(Dokumentiert im Summary)*
*   `POST /:id`: Kitchentable erstellen (Path: `id` - ungewöhnlich!, Body: ?, Header: `Authorization`). *(Erstellung fehlte)*
*   `DELETE /:id`: Kitchentable löschen (Path: `id`, Header: `Authorization`). *(Löschen fehlte)*
*   `GET /:kitchentableId/shopping-list/active`: Aktive Einkaufsliste holen (Path: `kitchentableId`, Header: `Authorization`). *(Dokumentiert im Summary)*
*   `PATCH /:id/members`: Mitglieder hinzufügen/aktualisieren (Path: `id`, Body: ?, Header: `Authorization`). *(Hinzufügen/Update fehlte)*
*   `DELETE /:id/members/:memberId`: Mitglied entfernen (Path: `id`, `memberId`, Header: `Authorization`). *(Entfernen fehlte)*
*   `GET /all/related/:userid`: Alle Kitchentables eines Benutzers (Path: `userid`, Header: `Authorization`). *(Fehlte komplett)*

---

### Marketing Content Routes (`/api/v1/marketing-content`) - (aus `marketingContentRoutes.js`)

*   `GET /latest`: Letzten öffentlichen Inhalt abrufen (Keine Auth). *(Dokumentiert im Summary)*
*   `GET /`: Alle Marketing-Inhalte abrufen (Header: `Authorization`?). *(Benötigt Prüfung der Implementierung, fehlte)*
*   `GET /:id`: Spezifischen Inhalt abrufen (Path: `id`, Header: `Authorization`?). *(Benötigt Prüfung der Implementierung, fehlte)*

---

### Marketing Workflow Routes (`/api/v1/marketing-workflow`) - (aus `marketingWorkflowRoutes.js`)

*   `POST /trigger`: Workflow starten (Header: `Authorization`). *(Dokumentiert im Summary)*

---

### Menu Routes (`/api/v1/menu`) - (aus `menuRoutes.js`)

*   `POST /one`: Leeres Menü erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /one/:menuid`: Einzelnes Menü abrufen (Path: `menuid`, Header: `Authorization`). *(Pfad /one/ fehlte im Summary)*
*   `PATCH /one/:menuid`: Menü aktualisieren (Path: `menuid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one/:menuid/delete/relateduser`: Benutzer-Beziehung zu Menü löschen (Path: `menuid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /one/:menuid/free`: Menü ohne Auth abrufen (Path: `menuid`). *(Fehlte komplett)*
*   `GET /one/child/:menuchildid`: Menü-Child abrufen (Path: `menuchildid`, Header: `Authorization`). *(Fehlte komplett)*
*   `PATCH /one/child/:menuchildid`: Menü-Child aktualisieren (Path: `menuchildid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one/child/:menuchildid`: Menü-Child erstellen (Path: `menuchildid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one/child/:menuchildid/createifnotexists`: Menü-Child erstellen/hinzufügen (Path: `menuchildid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one/:menuid/child/:menuchildid/ingredient`: Zutat hinzufügen (Path: `menuid`, `menuchildid`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /complete`: Vollständiges Menü erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /completewithimage`: Vollständiges Menü mit Bild erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /complete/allbyuserid/:userid/kitchentableid/:kitchentableid`: Menüs nach User/Kitchentable abrufen (Path: `userid`, `kitchentableid`, Query: `scope`, `sort`, etc., Header: `Authorization`). *(Dokumentiert im Summary)*
*   `GET /complete/all/freeaccess/:searchstring`: Freie Menüs mit Suche (Path: `searchstring`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /complete/all/freeaccess`: Freie Menüs ohne Suche (Header: `Authorization`). *(Fehlte komplett)*
*   `DELETE /related/:id`: Menü-Relation löschen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*
*   `PATCH /related/:id`: Menü-Relation aktualisieren (Path: `id`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /related/:id`: Menü-Relation erstellen (Path: `id`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /related/:id`: Menü-Relation abrufen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /unit/all`: Alle Einheiten abrufen (Header: `Authorization`). *(Fehlte komplett)*
*   `POST /unit/one`: Einheit erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /uploadimage`: (Unklare Funktion, evtl. Testroute?) *(Fehlte komplett)*

---

### OAuth Routes (`/api/v1/oauth`) - (aus `oauthRoutes.js`)

*   `GET /google`: Google OAuth Start. *(Fehlte komplett)*
*   `GET /google/callback`: Google OAuth Callback. *(Fehlte komplett)*

---

### PEAX Routes (`/api/v1/peax`) - (aus `routes/peax/peaxRoutes.js`)

*(Annahme: Benötigt Prüfung der spezifischen Datei `peaxRoutes.js`)*

---

### Shopping List Item Routes (`/api/shopping-list-items`) - (aus `shoppingListItemRoutes.js`)

*   `PATCH /:itemId`: Item aktualisieren (Path: `itemId`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `DELETE /:itemId`: Item löschen (Path: `itemId`, Header: `Authorization`). *(Fehlte komplett)*
*   `PATCH /:itemId/reset`: Item zurücksetzen (Path: `itemId`, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /toggle-checked`: Status mehrerer Items ändern (Body: ?, Header: `Authorization`). *(Fehlte komplett)*

---

### Shopping List Routes (`/api/shopping-lists/:listId`) - (aus `shoppingListRoutes.js`)

*   `POST /recipes`: Rezept zur Liste hinzufügen (Path: `listId`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `DELETE /recipes/:recipeId`: Rezept von Liste entfernen (Path: `listId`, `recipeId`, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /items`: Eigenes Item zur Liste hinzufügen (Path: `listId`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `PUT /finish`: Liste abschließen (Path: `listId`, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /sync-offline-changes`: Offline-Änderungen synchronisieren (Path: `listId`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*

---

### Weekplan Routes (`/api/v1/weekplan`) - (aus `weekplanRoutes.js`)

*   `GET /:id`: Wochenplan abrufen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*
*   `POST /one`: Wochenplan erstellen (Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `PATCH /:id`: Wochenplan aktualisieren (Path: `id`, Body: ?, Header: `Authorization`). *(Fehlte komplett)*
*   `DELETE /:id`: Wochenplan löschen (Path: `id`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /all/related/:userid`: Alle Pläne eines Users (Path: `userid`, Header: `Authorization`). *(Fehlte komplett)*
*   `GET /all/related/:userid/kitchentableid/:kitchentableid`: Alle Pläne User/Kitchentable (Path: `userid`, `kitchentableid`, Header: `Authorization`). *(Fehlte komplett)*

---
