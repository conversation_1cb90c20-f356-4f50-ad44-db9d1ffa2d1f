import { defineStore } from 'pinia';
import { ref, computed, watch, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useUserStore } from './userStore'
import { useMenuStore, useMenuesStore } from './menuStore'
import { useHelperStore } from '../../utils/helper'
import { fixStableIdsAfterAIGeneration } from '@/utils/recipeUtils'
import dayjs from 'dayjs';
import 'dayjs/locale/de';
import Pica from 'pica';

export const useWeekplanStore = defineStore('weekplan', () => {

    /////////////////////////////// INIT /////////////////////////////////////////
    const { setNotification } = useNotification();
    const router = useRouter();
    const userStore = useUserStore();
    const menustore = useMenuStore();
    const helper = useHelperStore();
    dayjs.locale('de');
    /////////////////////////////// INIT /////////////////////////////////////////

    /////////////////////////////// DATES AND DATE FORMATTING STARTING /////////////////////////////////////////
    const startdate = ref(new Date());
    const enddate = ref(new Date(startdate.value.setDate(startdate.value.getDate() + 7)))

    const date = ref();
    const numberOfDays = ref(Number(0));
    const formattedStartDate = ref();
    const formattedEndDate = ref();
    date.value = [new Date(), enddate.value];

    const initDateValues = async (dateValue) => {
        const formatted = dayjs(dateValue).format('D. MMMM YY');
        return formatted;
    }

    const initializeDates = async () => {
        formattedStartDate.value = await initDateValues(startdate.value);
        formattedEndDate.value = await initDateValues(enddate.value);
    }

    // Ruf die initialisierung im Lifecycle Hook auf
    initializeDates();


    /* WATCHER */
    // Watcher für Datum-Array
    watch(() => date.value[0], async (neuerWert) => {
        formattedStartDate.value = await initDateValues(neuerWert);
    }, { immediate: true });

    watch(() => date.value[1], async (neuerWert) => {
        formattedEndDate.value = await initDateValues(neuerWert);
    }, { immediate: true });
    /* WATCHER */

    /////////////////////////////// DATES AND DATE FORMATTING ENDING /////////////////////////////////////////

    const weekplanmenu = ref([]);
    const oneWeekplanReciept = ref({
        _id: null,
        kitchentableId: null,
        date: null,
        daytime: null,
        numberOfPersons: null,
        __v: null,
        menuId: {
            _id: null,
            name: null,
            description: null,
            users: [],
            imagelink: null,
            freeAccess: null,
            createdAt: null,
            __v: null,
            // additional data only in vue
            permission: null,
            //
            menuchilds: {
                _id: null,
                numberOfPersons: null,
                menuChildId: {
                    _id: null,
                    cookingTime: null,
                    createdAt: null,
                    ingredients: [],
                    isStandard: null,
                    nutritions: [],
                    parentId: null,
                    preperation: [],
                    versions: [],
                    _v: null
                }
            }
        }
    })


    /* INITIALIZING */

    /* COMPUTED FUNCTIONS */
    // DATES CHANGES
    const formattedNutrionList = computed(() => {
        let nutrients = {
            "Fett": 0,
            "Kohlenhydrate": 0,
            "Protein": 0,
            "Ballaststoffe": 0
          };

        let personsNeeds = {
            "Fett": userStore.user.fatDaily,
            "Kohlenhydrate": userStore.user.kcalDaily,
            "Protein": userStore.user.protDaily
          };

        // Berechnen der summierten Nährwerte
        weekplanmenu.value.forEach(item => {
            item.menuId.menuchilds.forEach(child => {
              child.menuChildId.nutritions.forEach(nutrion => {
                if (nutrients.hasOwnProperty(nutrion.name)) {
                  nutrients[nutrion.name] += (Number(nutrion.amount) / Number(item.numberOfPersons));
                }
              });
            });
          });

        return {
            ...nutrients
        };
    });

    /* FUNCTIONS */
    function getDayName(dateStr, locale){
        var date = new Date(dateStr);
        return date.toLocaleDateString(locale, {
            weekday: "long",
            month: "long",
            day: "numeric",
            year: "numeric",
          }
        );
    }

    /* SET ONE MENU OBJECT FROM WEEKPLANMENU */
    const setOneMenue = async(res) => {
        helper.devConsole("setOneMenue at menuStore")
        //helper.devConsole(res.data.data)
        /*
        _id: null,
        kitchentableId: null,
        date: null,
        daytime: null,
        numberOfPersons: 102,
        */

        // WEEKPLAN ITEM
        oneWeekplanReciept.value._id = res.data.data._id;
        oneWeekplanReciept.value.kitchentableId = res.data.data.name,
        oneWeekplanReciept.value.date = res.data.data.users,
        oneWeekplanReciept.value.daytime = res.data.data.description,
        oneWeekplanReciept.value.numberOfPersons = res.data.data.imagelink,
        oneWeekplanReciept.value.__v = res.data.data.__v

        /*
        _id: null,
        name: null,
        description: null,
        users: [],
        imagelink: null,
        freeAccess: null,
        createdAt: null,
        __v: null,
        // additional data only in vue
        permission: null,
        */

        // MENU
        oneWeekplanReciept.value.menuId._id = res.data.data.menuId._id;
        oneWeekplanReciept.value.menuId.name = res.data.data.menuId.name,
        oneWeekplanReciept.value.menuId.users = res.data.data.menuId.users,
        oneWeekplanReciept.value.menuId.description = res.data.data.menuId.description,
        oneWeekplanReciept.value.menuId.imagelink = res.data.data.menuId.imagelink,
        oneWeekplanReciept.value.menuId.freeAccess = res.data.data.menuId.freeAccess,
        oneWeekplanReciept.value.menuId.__v = res.data.data.menuId.__v

        // check if user is owner or creator
        const filteredData = oneWeekplanReciept.value.menuId.users.filter(item =>
            item.userId._id === userStore.user.id &&
            (item.roleId.name === "creator" || item.roleId.name === "owner")
        );
        //helper.devConsole(filteredData)
        if(filteredData.length > 0){
            //helper.devConsole("länger 0")
            oneWeekplanReciept.value.menuId.permission = "edit"
        }

        // MENUCHILD
        oneWeekplanReciept.value.menuId.menuchilds._id = res.data.data.menuId.menuchilds[0]._id
        oneWeekplanReciept.value.menuId.menuchilds.numberOfPersons = res.data.data.menuId.menuchilds[0].numberOfPersons
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId._id = res.data.data.menuId.menuchilds[0].menuChildId._id
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.seatCount = res.data.data.menuId.menuchilds[0].menuChildId.seatCount
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.cookingTime = res.data.data.menuId.menuchilds[0].menuChildId.cookingTime
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.createdAt = res.data.data.menuId.menuchilds[0].menuChildId.createdAt
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.ingredients = res.data.data.menuId.menuchilds[0].menuChildId.ingredients
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.isStandard = res.data.data.menuId.menuchilds[0].menuChildId.isStandard
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.nutritions = res.data.data.menuId.menuchilds[0].menuChildId.nutritions
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.preperation = res.data.data.menuId.menuchilds[0].menuChildId.preperation
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.parentId = res.data.data.menuId.menuchilds[0].menuChildId.parentId
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId.versions = res.data.data.menuId.menuchilds[0].menuChildId.versions
        oneWeekplanReciept.value.menuId.menuchilds.menuChildId._v = res.data.data.menuId.menuchilds[0].menuChildId._v
    }

    /* SET Weekplan */
    const setWeekplanmenu = async(newArray) => {
        helper.devConsole("setWeekplanmenu at weekplanStore")
        //weekplanmenu.value = newArray
    }

    /* GET ONE MENU OBJECT FROM WEEKPLANMENU */
    const getOneMenue = async (id) => {
        helper.devConsole("getOneMenuFromWeekplan at weekplanstore")
        try{
            const res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/weekplan/one/' + id);
            //helper.devConsole(res)
            await setOneMenue(res)
            helper.devConsole(res.data.data)
        } catch (error){
            helper.devConsole(error)
        }
    }

    /* deleteFromWeekplan */
    const deleteFromWeekplan = async (id) => {
        //helper.devConsole(id)
        try{
            await axios.delete(import.meta.env.VITE_API_BASE_URL + '/api/v1/weekplan/one/' + id);
            router.go(-1)
        } catch (error){
            helper.devConsole(error)
        }
    }

    /* DATES CHOOSED */
    // Load related Menus to this date range
    const loadWeekplanItems = async () => {
        // reset values
        weekplanmenu.value = []

        // set weekplan
        const weekplanData = {
            "kitchentableid": userStore.user.defaultKitchentable,
            "dates": date.value
        }

        // set settings
        const settingsData = {
            "outputtype": "complete_array"
        }

        const NewDataProp = []
        const menuesOutput = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/weekplan/many', {
            "weekplan": weekplanData,
            "settings": settingsData
        });

        weekplanmenu.value = menuesOutput.data.data

    }

    /* SET ONE NEW WEEKPLAN ITEM TO THE STORE */
    const createWeekplanItem = async (menuid, date, phase, plannedSeats, index, element) => {
        //helper.devConsole(element)
        ///one/
        try{

        //check if defaultKitchentable is set
        if(userStore.user.defaultKitchentable == ""){
            setNotification('Erstelle zuerst einen Küchentisch oder lasse dich zu einem Küchentisch einladen', 'alert')
            throw TypeError('Kein Küchentisch definiert');
        }

        if(!date || !phase || !plannedSeats){
            setNotification('Gib ein Datum, Tageszeit und Personen an, um es dem Wochenplan hinzuzufügen', 'alert')
            throw TypeError('Gib ein Datum, Tageszeit und Personen an');
        }

        let weekplan = {
            "date": date,
            "phase": phase,
            "plannedSeats": plannedSeats,
            "kitchentableid": userStore.user.defaultKitchentable
        }

        const res = await menustore.countPerson(
            Number(plannedSeats),
            element.menuchilds[0],
            weekplan
        )

        //helper.devConsole(res)
        if(res.data.success){
            setNotification('Hinzugefügt', 'success')
        }

        /*

        const payload = {
            'kitchentableid': userStore.user.defaultKitchentable,
            'menuid': menuid,
            'date': date,
            'plannedSeats': plannedSeats,
            'phase': phase
        }

        // check if number of persons for this reciept already exists, if yes okey, if not create
        //const resRecieptGet = await axios.get(import.meta.env.VITE_API_BASE_URL + '/weekplan/one/0', payload);


        // if everything checked is okey
        const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/weekplan/one/0', payload);

        if(res.data.status == "error"){
            setNotification('Etwas hat nicht funktioniert', 'alert')
            throw TypeError('Gib ein Datum und Tageszeit an');
        }

        if(res.data.status == "success"){
            setNotification('Zu deinem Wochenplan hinzugefügt..', 'success')
        }
        */


        } catch (error){
            helper.devConsole(error)
        }
    }

    const deleteWeekplanItem = async (index) => {

        // index => inside weekplanmenu
        let idToRemove = weekplanmenu.value[index]._id

        try{

            // delete from db
            const res = await axios.delete(import.meta.env.VITE_API_BASE_URL + '/api/v1/weekplan/one/' + idToRemove);

            if(res.data.success){
                setNotification('Das Menü wurde gelöscht..', 'success')
                let newArray = weekplanmenu.value.splice(index,1)
                await setWeekplanmenu(newArray)
            } else {
                setNotification('Etwas hat nicht funktioniert', 'alert')
            }

        } catch (error){
            helper.devConsole(error)
        }


    }

    return {
        // EXPORTET VALUES
        weekplanmenu,
        oneWeekplanReciept,
        date,
        formattedStartDate,
        formattedEndDate,
        numberOfDays,
        formattedNutrionList,
        // EXPORTED FUNCTIONS
        deleteFromWeekplan,
        setOneMenue,
        setWeekplanmenu,
        getOneMenue,
        getDayName,
        createWeekplanItem,
        loadWeekplanItems,
        deleteWeekplanItem,
    };

});



export const useTempWeekplanStore = defineStore('tempweekplan', () => {

    /////////////////////////////// INIT /////////////////////////////////////////
    const { setNotification } = useNotification();
    const userStore = useUserStore();
    const helper = useHelperStore()
    /////////////////////////////// INIT /////////////////////////////////////////

    const switchSettingsComponentButton = ref(1)
    const tempweekplanmenu = ref([]);
    const isLoading = ref(false)
    const isLoadingStep2 = ref(false)
    const recieptUrl = ref()
    const recieptText = ref()
    const recieptImage = ref()
    const tempImage = ref(null)
    const tempImageBlob = ref(null)
    const recieptCreationOnGoing = ref(false)
    const imageIsLoading = ref(false)

    // Camera references
    const video = ref(null)
    const canvas = ref(null)
    const videoNotAllowed = ref(false)
    const noCameraFound = ref(false)

    // --- Aus SearchMenuView übernommen ---
    const searchText = ref("Generieren")

    // Preferences für den Generator (Struktur korrigiert für preference-card)
    const datasetPreferences = reactive([
      {
          "boxid": "1", // Behalte boxid für den Key
          "boxdescription": "Wie viele Personen seit ihr?", // Verwende boxdescription
          "boxname": "personen", // Behalte boxname für die Logik
          "boxvalues": [ // Verwende boxvalues
            { id: 1, item: '1 Person', selected: true }, // Setze einen sinnvollen Default
            { id: 2, item: '2 Personen', selected: false },
            { id: 3, item: '3 Personen', selected: false },
            { id: 4, item: '4 Personen', selected: false },
            { id: 5, item: '5 Personen', selected: false },
            { id: 6, item: '6 Personen', selected: false }
          ]
          // type und preferencevalue werden hier nicht mehr benötigt
      },
      {
          "boxid": "2",
          "boxdescription": "Wie viele Proteine möchtest du zu dir nehmen?",
          "boxname": "proteine",
          "boxvalues": [
            { id: 1, item: 'weniger als normal', selected: false },
            { id: 2, item: 'normal', selected: true }, // Default
            { id: 3, item: 'mehr als normal', selected: false },
            { id: 4, item: 'deutlich mehr als normal', selected: false }
          ]
      },
       {
         "boxid": "3",
         "boxdescription": "Hast du spezielle Wünsche zum neuen Menü?",
         "boxname": "praeferenz",
         "boxvalues": [
           { id: 1, item: 'egal', selected: true }, // Default "egal"
           { id: 2, item: 'low carb', selected: false },
           { id: 3, item: 'keto', selected: false },
           { id: 4, item: 'paleo', selected: false },
           { id: 5, item: 'low suger', selected: false }
         ]
       },
       {
         "boxid": "4",
         "boxdescription": "Gibt es einen Lebensstil, welchen du uns mitgeben möchtest?",
         "boxname": "liefestyle", // *** TYPO ANGEPASST an Backend ***
         "boxvalues": [
            { id: 1, item: 'egal', selected: true },
           { id: 2, item: 'vegetarisch', selected: false },
           { id: 3, item: 'vegan', selected: false },
           { id: 4, item: 'mixed (vegetarisch, einmal Fleisch)', selected: false },
           { id: 5, item: 'nur Fleisch', selected: false }
         ]
       },
       {
         "boxid": "5",
         "boxdescription": "Gibt es zuberücksichtigende Allergien?",
         "boxname": "allergie",
         "boxvalues": [
           { id: 1, item: 'keine', selected: true }, // Default "keine"
           { id: 2, item: 'ziebeln', selected: false },
           { id: 3, item: 'knoblauch', selected: false },
           { id: 4, item: 'nüsse', selected: false },
           { id: 5, item: 'huhn', selected: false }
         ]
       },
    ]);

    // Optional: Wochentage (Struktur bleibt gleich, da nicht direkt verwendet)
    const datasetDays = reactive([
      {
            "dayid": "1",
            "imagelink": "linear-gradient(0deg, rgba(0, 0, 0, 0.7 ), rgba(0, 0, 0, 0.7)), url('../src/assets/img/_calendar.jpg')", // Pfad anpassen?
            "day": "Montag",
            "values": [
              { id: 1, item: 'morgen', selected: false },
              { id: 2, item: 'mittag', selected: false },
              { id: 3, item: 'abend', selected: false }
            ]
      },
      // ... (Weitere Tage hier) ...
      {
            "dayid": "7",
            "imagelink": "linear-gradient(0deg, rgba(0, 0, 0, 0.7 ), rgba(0, 0, 0, 0.7)), url('../src/assets/img/_calendar.jpg')", // Pfad anpassen?
            "day": "Sonntag",
            "values": [
              { id: 1, item: 'morgen', selected: false },
              { id: 2, item: 'mittag', selected: false },
              { id: 3, item: 'abend', selected: false }
            ]
      },
    ]);
    // --- Ende Übernahme aus SearchMenuView ---

    // Function to switch components
    const setActiveButton = (value) => {
        helper.devConsole(value)
        switchSettingsComponentButton.value = value
    }

    /* Unified function to update the result list */
    const updateItemToList = (resultData) => {
        if (!resultData) {
            tempweekplanmenu.value = []; // No data -> empty array
            helper.devConsole("updateItemToList: Received no data, setting empty array.");
        } else if (Array.isArray(resultData)) {
            tempweekplanmenu.value = resultData; // Array received -> assign directly
            helper.devConsole("updateItemToList: Received array, assigning directly.", tempweekplanmenu.value);
        } else {
            tempweekplanmenu.value = [resultData]; // Single object received -> wrap in array and assign
            helper.devConsole("updateItemToList: Received single object, assigning as array.", tempweekplanmenu.value);
        }
    };

    // Remove temp reciepts from list
    const clearFoundMenus = async () => {
        // Don't clear if a creation process is ongoing to avoid flickering
        if (!isLoading.value) {
             tempweekplanmenu.value = []
             helper.devConsole("clearFoundMenus: Cleared temp menu list.");
        }
    }

    const createURLreciept = async () => {
        if(!recieptUrl.value || recieptUrl.value.trim() === ''){
            setNotification('Bitte eine URL eingeben', 'alert')
            return
        }

        // await clearFoundMenus(); // Moved clearing logic inside updateItemToList conceptually
        isLoading.value = true;
        updateItemToList(null); // Clear previous results visually immediately
        try {
            const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/reciept/createbyurl', {
                data: recieptUrl.value,
                user_id: userStore.user.id,
                settings: {switch: "forward"}
            });

            // KRITISCH: Korrigiere stableIds nach KI-Generierung
            const correctedRecipe = fixStableIdsAfterAIGeneration(res.data.data);
            updateItemToList(correctedRecipe); // Use unified update function
            recieptUrl.value = '';
            setNotification('Das Rezept wurde erstellt', 'success');

        } catch(err) {
            helper.devConsole("Error in createURLreciept:", err);
            let userMessage = 'Rezept konnte nicht erstellt werden.';
            if (err.response) {
                userMessage = err.response.data?.message || `Serverfehler (${err.response.status}).`;
            } else if (err.request) {
                userMessage = 'Netzwerkfehler. Bitte Verbindung prüfen.';
            } else {
                userMessage = 'Fehler beim Senden der Anfrage.';
            }
            setNotification(userMessage, 'alert');
            updateItemToList(null); // Ensure list is empty on error
        } finally {
             isLoading.value = false;
        }
    }

    const createURLText = async () => {
        if(!recieptText.value || recieptText.value.trim() === ''){
            setNotification('Bitte einen Text eingeben', 'alert')
            return
        }

        // await clearFoundMenus();
        isLoading.value = true;
        updateItemToList(null);
        try {
            const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/reciept/createbytext', {
                data: recieptText.value,
                user_id: userStore.user.id,
                settings: {switch: "forward"}
            });

            // KRITISCH: Korrigiere stableIds nach KI-Generierung
            const correctedRecipe = fixStableIdsAfterAIGeneration(res.data.data);
            updateItemToList(correctedRecipe); // Use unified update function
            recieptText.value = '';
            setNotification('Das Rezept wurde erstellt', 'success');

        } catch(err) {
            helper.devConsole("Error in createURLText:", err);
            let userMessage = 'Rezept konnte nicht erstellt werden.';
            if (err.response) {
                userMessage = err.response.data?.message || `Serverfehler (${err.response.status}).`;
            } else if (err.request) {
                userMessage = 'Netzwerkfehler. Bitte Verbindung prüfen.';
            } else {
                userMessage = 'Fehler beim Senden der Anfrage.';
            }
            setNotification(userMessage, 'alert');
            updateItemToList(null);
        } finally {
             isLoading.value = false;
        }
    }


    const clearImage = async() => {
        // Setze das tempImage zurück
        tempImage.value = null;
        tempImageBlob.value = null;
    }

    const clickSetImage = async () => {
        helper.devConsole('clickSetImage called');

        // Use the canvas reference from the store
        const canvasElement = canvas.value;

        if (!canvasElement) {
            helper.devConsole('Canvas not available in store');
            setNotification('Kamera nicht verfügbar. Bitte erneut versuchen.', 'alert');
            return;
        }

        const ctx = canvasElement.getContext("2d");
        if (!ctx) {
            helper.devConsole('Canvas context not available');
            setNotification('Kamera-Kontext nicht verfügbar. Bitte erneut versuchen.', 'alert');
            return;
        }

        try {
            // Nutze toDataURL für das Base64 Image, falls du es benötigst
            tempImage.value = canvasElement.toDataURL('image/jpeg', 0.9);
            helper.devConsole('Base64 image created:', tempImage.value.substring(0, 50) + '...');

            // Verwende toBlob mit einer Callback-Funktion
            canvasElement.toBlob((blob) => {
                if (blob) {
                    // Weisen Sie das Blob hier einmal tempImageBlob.value zu
                    tempImageBlob.value = blob;

                    // Optional: Logge das Blob oder verarbeite es weiter
                    helper.devConsole('Blob erstellt:', blob);
                    setNotification('Bild aufgenommen! Jetzt "Rezept erstellen" klicken.', 'success');
                } else {
                    helper.devConsole('Fehler beim Erstellen des Blobs');
                    setNotification('Fehler beim Erstellen des Bildes. Bitte erneut versuchen.', 'alert');
                }
            }, 'image/jpeg', 0.9); // MIME-Typ als zweiter Parameter (optional)
        } catch (error) {
            helper.devConsole('Error in clickSetImage:', error);
            setNotification('Fehler beim Aufnehmen des Bildes. Bitte erneut versuchen.', 'alert');
        }
    };


    const createIMAGEreciept = async () => {
        helper.devConsole("createIMAGEreciept in weekplanStore")
        if (!tempImageBlob.value) {
             setNotification('Bitte zuerst ein Bild auswählen oder aufnehmen.', 'alert');
             return;
        }

        // await clearFoundMenus();
        isLoading.value = true;
        updateItemToList(null);
        const formData = new FormData();
        formData.append('image', tempImageBlob.value, 'upload.jpg');

        try {
            const response = await axios.post(
                import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/reciept/createbyimage',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );

            helper.devConsole("createIMAGEreciept success response:", response.data)

            // KRITISCH: Korrigiere stableIds nach KI-Generierung
            const correctedRecipe = fixStableIdsAfterAIGeneration(response.data.data);
            updateItemToList(correctedRecipe); // Use unified update function
            setNotification("Rezept wurde erfasst.", "success");

        } catch (error) {
            helper.devConsole('Fehler beim Hochladen des Bildes:', error);
            let userMessage = 'Rezept konnte nicht aus Bild erstellt werden.';
             if (error.response) {
                userMessage = error.response.data?.message || `Serverfehler (${error.response.status}).`;
            } else if (error.request) {
                userMessage = 'Netzwerkfehler. Bitte Verbindung prüfen.';
            } else {
                userMessage = 'Fehler beim Senden der Anfrage.';
            }
            setNotification(userMessage, 'alert');
            updateItemToList(null);
        } finally {
             isLoading.value = false;
             tempImageBlob.value = null;
             tempImage.value = null;
        }
    }

    // Funktion zur Erstellung der Rezepte über den Generator
    const createWeekplanFromGenerator = async (preferencesObject, customText) => {
        helper.devConsole("createWeekplanFromGenerator called in store with:", { preferencesObject, customText });

        // await clearFoundMenus();
        isLoading.value = true;
        updateItemToList(null);
        recieptCreationOnGoing.value = true; // Keep this? Needed for generator specific UI?
        searchText.value="Rezepte werden erstellt...";

        let payloadData = { /* ... payload creation ... */ };
         // --- Baue das Payload-Objekt für das Backend ---
        payloadData = {
            ...preferencesObject,
            user_id: userStore.user.id,
            days: "1" // *** FEHLENDEN days-Wert hinzugefügt (Standardwert) ***
        };

        if (customText && customText.trim()) {
            // Der Backend-Code verwendet dies aktuell nicht, aber wir senden es trotzdem mit.
            // Ggf. Backend anpassen oder Key hier ändern/entfernen.
            payloadData.custom_wunsch = customText.trim();
        }

        // Entferne Präferenzen mit 'egal' oder 'keine' (bleibt gleich)
        for (const key in payloadData) {
            if (payloadData[key] === 'egal' || payloadData[key] === 'keine') {
                // Ausnahme: 'personen' nie löschen, auch wenn es 'egal' wäre (unwahrscheinlich)
                if (key !== 'personen') {
                    delete payloadData[key];
                }
            }
        }

        helper.devConsole("Final Payload for /creator/menulist:", payloadData);

        let completedRecipes = []; // Define here to be accessible in finally block if needed

        try {
            const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/menulist',
                { data: payloadData }
            );
            helper.devConsole("Generator Response (Step 1):", res.data);

            if(!res.data || !res.data.data || res.data.data.length === 0){
                 setNotification('Keine Rezepte vom Generator erhalten.', 'info');
                 throw new Error('No recipes received from generator');
             }

             searchText.value="Vervollständigung der Rezepte...";
             // completedRecipes is already defined above

             for (let i = 0; i < res.data.data.length; i++){
                 // 🔧 KRITISCH: Generator verwendet jetzt das gleiche Format wie andere Generatoren
                 const validatedRecipe = res.data.data[i];
                 helper.devConsole(`🔄 Processing validated generator recipe ${i + 1}:`, validatedRecipe);

                 // Füge user_id hinzu
                 const recipeToComplete = {
                     ...validatedRecipe,
                     user_id: userStore.user.id
                 };

                 helper.devConsole(`✅ Recipe ready for completion:`, recipeToComplete);

                 try {
                     const res2 = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/completewithimage',
                         recipeToComplete
                     );
                      if (res2.data && res2.data.data && res2.data.data._id) {
                         const res3 = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/' + res2.data.data._id);
                         if (res3.data && res3.data.data) {
                              completedRecipes.push(res3.data.data);
                         }
                     } else {
                          helper.devConsole(`Skipping recipe ${i}, completion failed or no ID returned.`);
                     }
                 } catch (completionError) {
                     helper.devConsole(`Error completing recipe ${i}:`, completionError);
                     // Optionally notify user about partial failure?
                 }
             }

             // KRITISCH: Korrigiere stableIds für alle generierten Rezepte
             const correctedRecipes = completedRecipes.map(recipe => fixStableIdsAfterAIGeneration(recipe));
             updateItemToList(correctedRecipes); // Use unified update function
             searchText.value="Rezeptvorschläge generieren";

             if(correctedRecipes.length === 0){
                  setNotification('Konnte keine Rezepte vervollständigen.', 'alert');
             } else {
                 setNotification(`${correctedRecipes.length} Rezept erfolgreich erstellt.`, 'success');
             }

        } catch(err) {
            helper.devConsole("Error in createWeekplanFromGenerator:", err);
            let userMessage = 'Ein unbekannter Fehler ist aufgetreten.';
            const messageType = 'alert';

            if (err.response) {
              const status = err.response.status;
              const backendMessage = err.response?.data?.message;
              if (status === 401) {
                console.warn('createWeekplanFromGenerator: Received 401, interceptor should handle.');
                // error.value = `Unauthorized (401) - Interceptor should handle. Original: ${err.message}`;
                // Don't set notification here, interceptor should handle redirect/message
                // But ensure list is cleared
                updateItemToList(null);
                return; // Exit early
              } else if (status >= 400 && status < 500) {
                userMessage = backendMessage || `Fehlerhafte Anfrage (Code: ${status}). Bitte überprüfe deine Eingaben.`;
              } else if (status >= 500) {
                userMessage = 'Ups, da ist etwas schiefgelaufen. Dies passiert manchmal - bitte einfach nochmals probieren.';
              }
            } else if (err.request) {
              userMessage = 'Netzwerkfehler beim Erstellen des Wochenplans. Bitte prüfe deine Verbindung und versuche es erneut.';
            } else {
              userMessage = 'Fehler beim Vorbereiten der Anfrage für den Wochenplan-Generator.';
            }

            setNotification(userMessage, messageType);
            searchText.value = "Fehler - Erneut versuchen?";
            updateItemToList(null); // Ensure list is empty on error
            // Update internal error state - include status if available
            // error.value = `Generator Error: ${err.response?.status || 'N/A'} - ${err.message || 'Unknown'}`; // Assuming error ref exists

        } finally {
            isLoading.value = false;
            recieptCreationOnGoing.value = false; // Reset generator specific flag
            // Reset search text only if it wasn't set to an error message
            if (searchText.value !== "Fehler - Erneut versuchen?") {
                 searchText.value = "Rezeptvorschläge generieren";
            }
        }
    };

    return {
        tempweekplanmenu,
        isLoading,
        isLoadingStep2,
        recieptUrl,
        recieptText,
        recieptImage,
        tempImage,
        tempImageBlob,
        recieptCreationOnGoing,
        switchSettingsComponentButton,
        imageIsLoading,
        // Camera references
        video,
        canvas,
        videoNotAllowed,
        noCameraFound,
        // Export der neuen Variablen
        searchText,
        datasetPreferences,
        datasetDays,
        // Funktionen
        setActiveButton,
        updateItemToList,
        clearFoundMenus,
        createURLreciept,
        createURLText,
        createIMAGEreciept,
        clickSetImage,
        clearImage,
        createWeekplanFromGenerator
    }
});