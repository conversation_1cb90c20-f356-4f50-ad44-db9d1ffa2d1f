<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
            <router-link to="/rezepte" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-ordypurple-100 font-medium">Blog</router-link>
            <router-link to="/about" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
            <router-link to="/login" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all transform hover:scale-105 font-medium shadow-lg">
              Kostenlos starten
            </router-link>
          </div>
          <button @click="toggleMobileMenu" class="md:hidden p-2">
            <svg class="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div v-if="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
          <router-link to="/" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
          <router-link to="/rezepte" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
          <router-link to="/blog" @click="mobileMenuOpen = false" class="block text-ordypurple-100 font-medium">Blog</router-link>
          <router-link to="/about" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
          <router-link to="/login" @click="mobileMenuOpen = false" class="w-full bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all font-medium text-center block">
            Kostenlos starten
          </router-link>
        </div>
      </div>
    </nav>

    <!-- Article Content -->
    <div v-if="article" class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Breadcrumb -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li><router-link to="/blog" class="hover:text-ordypurple-100">Blog</router-link></li>
          <li><span class="mx-2">/</span></li>
          <li><span class="text-gray-900">{{ article.category }}</span></li>
        </ol>
      </nav>

      <!-- Article Header -->
      <header class="mb-12">
        <div class="mb-6">
          <span class="inline-block px-3 py-1 text-sm font-medium rounded-full"
                :class="getCategoryColor(article.category)">
            {{ getCategoryName(article.category) }}
          </span>
        </div>
        
        <h1 class="text-4xl md:text-5xl font-YesevaOne text-gray-900 mb-6 leading-tight">
          {{ article.title }}
        </h1>
        
        <div class="flex items-center justify-between text-gray-600 mb-8">
          <div class="flex items-center space-x-4">
            <span>{{ formatDate(article.publishedAt) }}</span>
            <span>•</span>
            <span>{{ article.readTime }} Min Lesezeit</span>
          </div>
          <div class="flex items-center space-x-4">
            <button @click="shareArticle" class="text-gray-500 hover:text-ordypurple-100 transition-colors">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="w-full h-64 md:h-96 rounded-xl shadow-lg mb-8 bg-gradient-to-br overflow-hidden"
             :class="getCategoryGradient(article.category)">
          <div class="flex items-center justify-center h-full text-white">
            <div class="text-center">
              <div class="w-24 h-24 mx-auto mb-6 opacity-80" v-html="getCategoryIcon(article.category)"></div>
              <h3 class="text-3xl font-YesevaOne mb-2">{{ getCategoryName(article.category) }}</h3>
              <p class="text-lg opacity-90">{{ article.title.split(':')[0] }}</p>
            </div>
          </div>
        </div>
      </header>

      <!-- Article Body -->
      <article class="prose prose-lg max-w-none">
        <div class="text-xl text-gray-700 mb-8 font-medium leading-relaxed">
          {{ article.excerpt }}
        </div>
        
        <div v-html="article.content" class="text-gray-800 leading-relaxed"></div>
      </article>

      <!-- Article Footer -->
      <footer class="mt-16 pt-8 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-gray-600">Teilen:</span>
            <button @click="shareOnTwitter" class="text-blue-500 hover:text-blue-600 transition-colors">
              Twitter
            </button>
            <button @click="shareOnLinkedIn" class="text-blue-700 hover:text-blue-800 transition-colors">
              LinkedIn
            </button>
            <button @click="shareOnFacebook" class="text-blue-600 hover:text-blue-700 transition-colors">
              Facebook
            </button>
          </div>
          <router-link to="/blog" class="text-ordypurple-100 hover:text-ordypurple-200 font-medium">
            ← Zurück zum Blog
          </router-link>
        </div>
      </footer>

      <!-- Related Articles -->
      <section class="mt-16">
        <h2 class="text-2xl font-YesevaOne text-gray-900 mb-8">Ähnliche Artikel</h2>
        <div class="grid md:grid-cols-2 gap-8">
          <article v-for="relatedArticle in relatedArticles" :key="relatedArticle.id" 
                   class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                   @click="navigateToArticle(relatedArticle.slug)">
            <img :src="relatedArticle.image" :alt="relatedArticle.title" class="w-full h-48 object-cover rounded-t-xl">
            <div class="p-6">
              <div class="flex items-center justify-between mb-3">
                <span class="text-sm font-medium px-3 py-1 rounded-full"
                      :class="getCategoryColor(relatedArticle.category)">
                  {{ getCategoryName(relatedArticle.category) }}
                </span>
                <span class="text-sm text-gray-500">{{ relatedArticle.readTime }} Min</span>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ relatedArticle.title }}</h3>
              <p class="text-gray-600 text-sm line-clamp-3">{{ relatedArticle.excerpt }}</p>
            </div>
          </article>
        </div>
      </section>
    </div>

    <!-- Loading State -->
    <div v-else class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="animate-pulse">
        <div class="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
        <div class="h-12 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div class="h-64 bg-gray-200 rounded-xl mb-8"></div>
        <div class="space-y-4">
          <div class="h-4 bg-gray-200 rounded w-full"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 rounded w-4/5"></div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center mb-4">
              <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-8 w-auto">
              <span class="ml-2 text-xl font-YesevaOne">Ordy</span>
            </div>
            <p class="text-gray-400 mb-4">
              Die intelligenteste Koch-App für Familien. Kochen war noch nie so einfach.
            </p>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Produkt</h3>
            <ul class="space-y-2 text-gray-400">
              <li><router-link to="/rezepte" class="hover:text-white transition-colors">Rezepte</router-link></li>
              <li><router-link to="/blog" class="hover:text-white transition-colors">Blog</router-link></li>
            </ul>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Unternehmen</h3>
            <ul class="space-y-2 text-gray-400">
              <li><router-link to="/about" class="hover:text-white transition-colors">Über uns</router-link></li>
            </ul>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Support</h3>
            <ul class="space-y-2 text-gray-400">
              <li><router-link to="/legal/agb" class="hover:text-white transition-colors">AGB</router-link></li>
              <li><router-link to="/legal/privacy-policy" class="hover:text-white transition-colors">Datenschutz</router-link></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 K-Innovations GmbH. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// Reactive data
const article = ref(null)
const mobileMenuOpen = ref(false)

// Mock articles data (in production this would come from an API)
const articles = ref([
  {
    id: 1,
    slug: 'lebensmittelverschwendung-wissenschaft',
    title: 'Die Wissenschaft hinter Lebensmittelverschwendung: Warum wir 40% unserer Lebensmittel wegwerfen',
    excerpt: 'Eine tiefgreifende wissenschaftliche Analyse der psychologischen, sozialen und strukturellen Faktoren, die zu massiver Lebensmittelverschwendung führen.',
    content: `
      <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8 rounded-r-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-700">
              <strong>Schockierende Zahlen:</strong> 40% aller produzierten Lebensmittel landen im Müll – das sind 1,3 Milliarden Tonnen pro Jahr weltweit.
            </p>
          </div>
        </div>
      </div>

      <h2>Die erschreckende Realität der Lebensmittelverschwendung</h2>
      <p>Jedes Jahr landen weltweit etwa 1,3 Milliarden Tonnen Lebensmittel im Müll – das entspricht einem Drittel aller produzierten Nahrungsmittel. Diese Zahl ist nicht nur ethisch problematisch, sondern auch ökonomisch und ökologisch verheerend.</p>

      <p>In Deutschland werfen Haushalte durchschnittlich 75 Kilogramm Lebensmittel pro Person und Jahr weg, was einem Wert von etwa 235 Euro entspricht. Hochgerechnet auf alle deutschen Haushalte bedeutet das eine Verschwendung von über 6,1 Millionen Tonnen Lebensmitteln jährlich.</p>

      <div class="bg-gray-50 p-6 rounded-lg my-8">
        <h3 class="text-lg font-semibold mb-4 text-gray-900">Lebensmittelverschwendung nach Kategorien:</h3>
        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 bg-red-500 rounded mr-3"></div>
            <span>Obst & Gemüse: 44%</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
            <span>Brot & Backwaren: 20%</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
            <span>Milchprodukte: 18%</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-yellow-500 rounded mr-3"></div>
            <span>Fleisch & Fisch: 12%</span>
          </div>
        </div>
      </div>

      <h2>Psychologische Faktoren: Warum unser Gehirn uns zum Verschwenden verleitet</h2>
      <p>Verhaltensökonomen haben verschiedene kognitive Verzerrungen identifiziert, die systematisch zu Lebensmittelverschwendung führen. Diese psychologischen Mechanismen sind tief in unserer Evolution verwurzelt und funktionieren in der modernen Überflussgesellschaft kontraproduktiv.</p>

      <div class="space-y-6 my-8">
        <div class="border-l-4 border-purple-400 pl-6">
          <h3 class="font-semibold text-purple-900 mb-2">Optimismus-Bias</h3>
          <p>Wir überschätzen systematisch, wie viel wir kochen werden und unterschätzen gleichzeitig, wie lange Lebensmittel haltbar sind. Studien zeigen, dass Menschen ihre zukünftige Kochaktivität um durchschnittlich 40% überschätzen.</p>
        </div>

        <div class="border-l-4 border-blue-400 pl-6">
          <h3 class="font-semibold text-blue-900 mb-2">Verfügbarkeitsheuristik</h3>
          <p>Wir kaufen basierend auf dem, was wir gerade sehen, nicht auf dem, was wir tatsächlich brauchen. Supermärkte nutzen diese Schwäche gezielt durch strategische Produktplatzierung und Sonderangebote.</p>
        </div>

        <div class="border-l-4 border-red-400 pl-6">
          <h3 class="font-semibold text-red-900 mb-2">Verlustaversion</h3>
          <p>Paradoxerweise führt die Angst vor Mangel zu Überkonsum. Menschen kaufen lieber zu viel, als das Risiko einzugehen, dass etwas fehlen könnte – ein evolutionärer Mechanismus, der in Zeiten des Überflusses problematisch wird.</p>
        </div>
      </div>

      <h2>Strukturelle Probleme: Ein System des Überflusses</h2>
      <p>Unser Lebensmittelsystem ist auf Überfluss ausgelegt. Von der Produktion über den Handel bis zum Verbraucher – jede Stufe der Wertschöpfungskette trägt zur Verschwendung bei:</p>

      <ul class="space-y-3 my-6">
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span><strong>Supermärkte:</strong> Großpackungen und "Buy-one-get-one-free" Angebote verführen zu Impulskäufen</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span><strong>Mindesthaltbarkeitsdatum:</strong> Wird oft als Verfallsdatum missverstanden</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span><strong>Portionsgrößen:</strong> Standardpackungen entsprechen selten dem tatsächlichen Bedarf</span>
        </li>
      </ul>

      <h2>Die Rolle der Technologie: KI als Lösung</h2>
      <p>Moderne KI-Systeme können helfen, diese jahrhundertealten Muster zu durchbrechen. Durch maschinelles Lernen und Datenanalyse entstehen völlig neue Möglichkeiten der Lebensmittelverwaltung:</p>

      <div class="grid md:grid-cols-3 gap-6 my-8">
        <div class="bg-blue-50 p-6 rounded-lg">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Intelligente Planung</h3>
          <p class="text-sm text-gray-600">Realistische Mengenplanung basierend auf historischen Verbrauchsmustern und Familiengröße</p>
        </div>

        <div class="bg-green-50 p-6 rounded-lg">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Resteverwertung</h3>
          <p class="text-sm text-gray-600">Personalisierte Rezeptvorschläge basierend auf vorhandenen Zutaten im Kühlschrank</p>
        </div>

        <div class="bg-purple-50 p-6 rounded-lg">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Bewusstseinsbildung</h3>
          <p class="text-sm text-gray-600">Tracking und Feedback über Verschwendungsmuster zur Verhaltensänderung</p>
        </div>
      </div>

      <h2>Praktische Lösungsansätze für den Alltag</h2>
      <p>Basierend auf aktueller Forschung aus der Verhaltenspsychologie und Ernährungswissenschaft empfehlen Experten folgende evidenzbasierte Strategien:</p>

      <div class="bg-gradient-to-r from-green-50 to-blue-50 p-8 rounded-xl my-8">
        <h3 class="text-xl font-semibold mb-6 text-gray-900">Der 4-Schritte-Plan gegen Lebensmittelverschwendung:</h3>
        <ol class="space-y-4">
          <li class="flex items-start">
            <span class="flex-shrink-0 w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-4">1</span>
            <div>
              <strong>Realistische Wochenplanung:</strong> Planen Sie maximal 5-6 Mahlzeiten pro Woche und lassen Sie Raum für spontane Änderungen oder Restaurantbesuche.
            </div>
          </li>
          <li class="flex items-start">
            <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">2</span>
            <div>
              <strong>Präzise Einkaufslisten:</strong> Erstellen Sie Listen basierend auf konkreten Rezepten mit exakten Mengenangaben – keine Schätzungen.
            </div>
          </li>
          <li class="flex items-start">
            <span class="flex-shrink-0 w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-4">3</span>
            <div>
              <strong>Wöchentliche Kühlschrank-Inventur:</strong> Jeden Sonntag 10 Minuten für eine systematische Bestandsaufnahme einplanen.
            </div>
          </li>
          <li class="flex items-start">
            <span class="flex-shrink-0 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold mr-4">4</span>
            <div>
              <strong>Kreative Resteverwertung:</strong> Etablieren Sie feste "Reste-Tage" und sammeln Sie ein Repertoire an flexiblen Rezepten.
            </div>
          </li>
        </ol>
      </div>

      <h2>Fazit: Kleine Änderungen, große Wirkung</h2>
      <p>Die Reduzierung von Lebensmittelverschwendung ist nicht nur eine Frage des Umweltschutzes, sondern auch der sozialen Gerechtigkeit und wirtschaftlichen Vernunft. Durch das Verständnis der psychologischen und strukturellen Ursachen können wir gezielt Gegenmaßnahmen ergreifen.</p>

      <p>Moderne Technologie, insbesondere KI-gestützte Planungstools, bieten dabei völlig neue Möglichkeiten. Sie können uns helfen, die evolutionären Schwächen unseres Gehirns zu überwinden und nachhaltiger zu leben – ohne Verzicht auf Komfort oder Genuss.</p>
    `,
    category: 'sustainability',
    image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=800&h=400&fit=crop',
    publishedAt: '2024-11-10',
    readTime: 8
  },
  {
    id: 2,
    slug: 'ki-personalisierte-ernaehrung',
    title: 'Wie KI personalisierte Ernährung revolutioniert: Ein Blick in die Zukunft',
    excerpt: 'Maschinelles Lernen und fortschrittliche Algorithmen ermöglichen es heute, Ernährungspläne zu erstellen, die perfekt auf individuelle Bedürfnisse zugeschnitten sind.',
    content: `
      <h2>Die Revolution der personalisierten Ernährung</h2>
      <p>Künstliche Intelligenz verändert fundamental, wie wir über Ernährung denken und planen. Was früher allgemeine Empfehlungen waren, wird heute zu maßgeschneiderten Lösungen für jeden Einzelnen.</p>

      <h2>Wie KI Ernährungspläne erstellt</h2>
      <p>Moderne Algorithmen analysieren hunderte von Faktoren:</p>
      <ul>
        <li>Genetische Prädispositionen und Stoffwechseltyp</li>
        <li>Aktivitätslevel und Trainingsgewohnheiten</li>
        <li>Allergien und Unverträglichkeiten</li>
        <li>Geschmackspräferenzen und kulturelle Hintergründe</li>
        <li>Gesundheitsziele und medizinische Bedingungen</li>
      </ul>

      <h2>Praktische Anwendungen</h2>
      <p>KI-gestützte Ernährungsberatung zeigt bereits heute beeindruckende Ergebnisse in verschiedenen Bereichen der Gesundheitsvorsorge und Leistungsoptimierung.</p>
    `,
    category: 'technology',
    image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=400&fit=crop',
    publishedAt: '2024-11-08',
    readTime: 12
  },
  {
    id: 3,
    slug: 'ki-zukunft-kochen',
    title: 'Wie KI die Zukunft des Kochens revolutioniert',
    excerpt: 'Eine wissenschaftliche Analyse darüber, wie künstliche Intelligenz dabei hilft, Lebensmittelverschwendung zu reduzieren und personalisierte Ernährung zu ermöglichen.',
    content: `
      <h2>Die digitale Küchenrevolution</h2>
      <p>Künstliche Intelligenz transformiert unsere Küchen von Grund auf. Was einst Intuition und Erfahrung erforderte, wird heute durch intelligente Algorithmen unterstützt, die lernen, verstehen und vorhersagen.</p>

      <h2>Intelligente Rezeptgenerierung</h2>
      <p>KI-Systeme können aus vorhandenen Zutaten kreative, ausgewogene Mahlzeiten entwickeln. Sie berücksichtigen dabei Nährwerte, Geschmackskombinationen und sogar saisonale Verfügbarkeit.</p>

      <h2>Reduzierung von Lebensmittelverschwendung</h2>
      <p>Durch präzise Vorhersagen des Verbrauchs und intelligente Menüplanung können Haushalte ihre Lebensmittelverschwendung um bis zu 40% reduzieren.</p>
    `,
    category: 'technology',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=400&fit=crop',
    publishedAt: '2024-11-15',
    readTime: 8
  }
])

// Computed properties
const relatedArticles = computed(() => {
  if (!article.value) return []
  return articles.value
    .filter(a => a.id !== article.value.id && a.category === article.value.category)
    .slice(0, 2)
})

// Methods
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const getCategoryColor = (category) => {
  const colors = {
    sustainability: 'bg-green-100 text-green-800',
    technology: 'bg-blue-100 text-blue-800',
    family: 'bg-purple-100 text-purple-800',
    nutrition: 'bg-orange-100 text-orange-800'
  }
  return colors[category] || 'bg-gray-100 text-gray-800'
}

const getCategoryName = (category) => {
  const names = {
    sustainability: 'Nachhaltigkeit',
    technology: 'Technologie',
    family: 'Familie',
    nutrition: 'Ernährung'
  }
  return names[category] || category
}

const getCategoryGradient = (categoryId) => {
  const gradients = {
    sustainability: 'from-green-500 to-green-600',
    nutrition: 'from-blue-500 to-blue-600',
    family: 'from-purple-500 to-purple-600',
    technology: 'from-gray-500 to-gray-600',
    recipes: 'from-red-500 to-red-600'
  }
  return gradients[categoryId] || 'from-gray-500 to-gray-600'
}

const getCategoryIcon = (categoryId) => {
  const icons = {
    sustainability: `<svg fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
    </svg>`,
    nutrition: `<svg fill="currentColor" viewBox="0 0 20 20">
      <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
    </svg>`,
    family: `<svg fill="currentColor" viewBox="0 0 20 20">
      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
    </svg>`,
    technology: `<svg fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"/>
    </svg>`,
    recipes: `<svg fill="currentColor" viewBox="0 0 20 20">
      <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
    </svg>`
  }
  return icons[categoryId] || icons.technology
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('de-DE', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value.title,
      url: window.location.href
    })
  } else {
    navigator.clipboard.writeText(window.location.href)
    alert('Link kopiert!')
  }
}

const shareOnTwitter = () => {
  const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(article.value.title)}&url=${encodeURIComponent(window.location.href)}`
  window.open(url, '_blank')
}

const shareOnLinkedIn = () => {
  const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`
  window.open(url, '_blank')
}

const shareOnFacebook = () => {
  const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`
  window.open(url, '_blank')
}

const navigateToArticle = (slug) => {
  router.push(`/blog/${slug}`)
}

// Lifecycle
onMounted(() => {
  const slug = route.params.slug
  article.value = articles.value.find(a => a.slug === slug)
  
  if (!article.value) {
    router.push('/blog')
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose h2 {
  font-size: 1.5rem;
  font-family: 'Yeseva One', serif;
  color: #111827;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}
</style>
