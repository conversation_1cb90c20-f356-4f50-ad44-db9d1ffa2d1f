<template>
  <!-- Column Builder-->
  <div class="flex md:flex-row flex-col min-h-screen px-6">
  
    <!-- Middle Container -->
    <div class="w-full md:w-3/4 lg:w-3/4 xl:w-3/4 lg:pr-0 xl:pr-12 text-center md:text-left">
      <!-- Head-->
      <div class="w-full flex md:flex-row flex-col mt-10 md:mt-10 text-center md:text-left">
        <h1 class="md:w-8/12 lg:w-10/12 w-full h-auto -mt-4 md:pl-0 text-left">Wochenplanung</h1>
        <div class="flex md:w-4/12 lg:w-2/12 w-full">
          <div class="md:w-2/3 w-full">
            
          </div>
          <div class="w-0 md:w-1/3 invisible md:visible">
            <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
              <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
              <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
            </svg>
          </div>
        </div>
      </div>
      <p class="w-full md:w-1/2 mt-6 md:mt-0 text-left pl-1 md:pl-0">Gib deine Präferenzen an und lasse dich überraschen...</p>
      <!-- Head-->

      <!-- Select Weekdays -->
      <div class="w-full mt-8 bg-white rounded-xl grid grid-flow-col xl:min-w-[750px] lg:min-w-[550px] md:min-w-[320px] min-w-[320px]">
        <div></div>
        <div class="md:p-8 mx-auto md:px-2 pt-8 pb-8">
          <VueDatePicker
            v-if="userstore.user.weekplanmode == 0"
            class="mx-auto md:w-full"
            calendar-class-name="md:mx-auto"
            v-model="weekplanstore.date" 
            range 
            format-locale
            inline
            auto-apply 
            multi-calendars 
            :enable-time-picker="false"
            position="right"
            @update:model-value="weekplanstore.loadWeekplanItems"
          />
          <VueDatePicker
          v-if="userstore.user.weekplanmode == 1"
            class="mx-auto w-full"
            calendar-class-name="md:mx-auto"
            v-model="weekplanstore.date" 
            week-picker 
            inline
            auto-apply 
            position="right"
            @update:model-value="weekplanstore.loadWeekplanItems"
          />
        </div>
        <div></div>
      </div>
      <!-- Select Weekdays -->

      
      <!-- Menue found -->
      <div class="w-full">
        <div class="w-full mx-auto md:flex flex-nowrap md:flex-wrap md:gap-5 pb-12 mt-8" v-for="(item1,index) in weekplanstore.weekplanmenu" :key="index">
      
                <h3 class="w-full text-left h-6">
                  {{  item1.date }}
                </h3>
                <p class="mt-2 text-base text-left">
                  {{ item1.daytime }}
                </p>
              <MenuCard class="mx-auto bg-ordypurple-100" :weekplanParentData="item1" :menuPlanType="{ addToMenu: false, editMenu: true, none: false }" :element="item1.menuId" :index="index" :index2="index" :key="key" />
        </div>
      </div>
      <!-- Menue found -->
    </div>


    <!-- Right Container -->
    <div class="w-full md:w-1/4 xl:min-w-[300px] lg:min-w-[220px] lg:w-1/4 xl:w-1/4 md:p-8 mt-4">
        <div class="w-full md:flex-col flex flex-row gap-3">
        <click-button 
          @click.prevent="goToPlanung" 
          :element="{'buttondescription': 'Neue Rezepte kreieren', 'active': false, 'buttonicon': 'search.png', 'iconneeded': true }" 
          :index="1"  
        />

        <click-button 
          @click.prevent="goToUploadMenu" 
          :element="{'buttondescription': 'Rezepte aus Quelle erstellen', 'active': false, 'buttonicon': 'add.png', 'iconneeded': true }" 
          :index="1"  
        />

        <click-button 
          @click.prevent="goToAllReciepts" 
          :element="{'buttondescription': 'Öffentliche Rezeptbibliothek', 'active': false, 'buttonicon': 'rezepte.png', 'iconneeded': true }" 
          :index="1"  
        />
      </div>
      <h2 class="mt-10">Nährwerte</h2>
      <p class="mt-2 mb-1">In diesem Zeitraum wirst du im Schnitt pro Mahlzeit folgende Nährwerte zu dir nehmen</p>
      <div>
        <div>
          <p>
            <span>{{ weekplanstore.formattedNutrionList.Fett }}g</span><span> , vs deine tägliche Ration </span><span>{{ userstore.user.fatDaily }}g</span><span> Fett</span>
          </p>
          <p>
            <span>{{ weekplanstore.formattedNutrionList.Kohlenhydrate }}g</span><span> , vs deine tägliche Ration </span><span>{{ userstore.user.kcalDaily }}g</span><span> Kohlenhydrate</span>
          </p>
          <p>
            <span>{{ weekplanstore.formattedNutrionList.Protein }}g</span><span> , vs deine tägliche Ration </span><span>{{ userstore.user.fatDaily }}g</span><span> Proteine</span>
          </p>
      </div>
        
      </div>

      <h2 class="mt-4">Einkaufen</h2>
      <p class="mt-2">Wähle eine beliebige Zeit und füge dann die Rezepte zu deinem Einkaufszettel hinzu</p>
      
      <button @click="groceryliststore.saveOneGroceryListObject()" 
      class="rounded-xl text-xs mt-4 p-2 h-auto w-full md:w-10/12 mx-auto font-OpenSans font-base border-solid border-2"
      :class="weekplanstore.weekplanmenu.length == 0 ? 'bg-white text-black border-black' : 'bg-ordypurple-100 text-white border-white'"
    
      >
      <!-- bg-ordypurple-100 -->
      <span class="font-semibold">Auf den Einkaufszettel</span><br />
      <span class="text-xs">{{ weekplanstore.formattedStartDate }} - {{ weekplanstore.formattedEndDate }} </span>
     
      </button>

      <div>
        <grocerylistOverview />
      </div>
    </div>
      
    
    <!-- Right Container -->

  </div>
  
</template>
<script setup>
import { reactive, ref, watch } from 'vue';
import axios from 'axios';
import MenuCard from '../components/menuCard.vue';
import clickButton from '../components/clickBorderButton.vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import grocerylistOverview from '../components/grocerylistOverview.vue'
import '@vuepic/vue-datepicker/dist/main.css'
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router';
import { useUserStore } from '../store/userStore';
import { useWeekplanStore } from '../store/weekplanStore';
import { useGrocerylistStore } from '../store/grocerylistStore';
import { useHelperStore } from '../../utils/helper';
  

  const { setNotification } = useNotification();

  const router = useRouter();
  const weekplanstore = useWeekplanStore()
  const groceryliststore = useGrocerylistStore();
  const userstore = useUserStore();
  const helper = useHelperStore()

  watch(() => userstore.user.defaultKitchentable, (newVal, oldVal) => {
    helper.devConsole('defaultKitchentable changed from', oldVal, 'to', newVal);
      if (newVal) {
        //console.log("set value")
        groceryliststore.getGrocerylistOverviewByKitchentableId(newVal);
      }
  });

  
  const goToPlanung = () => {
    router.push({ name: 'planung'})
  };

  const goToUploadMenu = () => {
    router.push({ name: 'uploadMenu'})
  };

  const goToAllReciepts = () => {
    router.push({ name: 'alleRezepte'})
  }

  
</script>
<style scoped lang="scss">

@media (min-width: 1088px) {
  .dp__flex_display {
    flex-direction: row;
  }
}

:deep(.dp__theme_light) {
  --dp-primary-color: rgba(163, 125, 255, 1);
  --dp-border-color:  rgba(180, 57, 0, 0);
  --dp-menu-border-color:  rgba(180, 57, 0, 0);
  --dp-border-color-hover:  rgba(180, 57, 0, 0);
  --dp-border-color-focus:  rgba(180, 57, 0, 0);
}

:deep(.dp__theme_dark) {
  --dp-primary-color: rgba(163, 125, 255, 1);
  --dp-border-color:  rgba(180, 57, 0, 0);
  --dp-menu-border-color:  rgba(180, 57, 0, 0);
  --dp-border-color-hover:  rgba(180, 57, 0, 0);
  --dp-border-color-focus:  rgba(180, 57, 0, 0);
}

/*
.dp-custom-menu {
  border:none;
  background-color: red;
  margin-left:auto;
  margin-right: auto;
  --dp-primary-color: #b43973;
}

.dp-main {
  width: auto !important;
  background-color: #54c240;
  margin-left: auto;
  margin-right: auto;
  --dp-primary-color: rgb(180 57 115);
}

.dp-menu{
  --dp-menu-width: 200px !important;
  --dp-primary-color: rgb(180 57 115);
}

.dp__flex_display {
  display: flex;
  flex-direction: column;
}

@media (min-width: 1088px) {
  .dp__flex_display {
    flex-direction: row;
  }
}
*/

/*
.dp__theme_light {
    --dp-background-color: #fff;
    --dp-text-color: #212121;
    --dp-hover-color: #f3f3f3;
    --dp-hover-text-color: #212121;
    --dp-hover-icon-color: #959595;
    --dp-primary-color: rgb(180 57 115) !important;
    --dp-primary-disabled-color: #6bacea;
    --dp-primary-text-color: #f8f5f5;
    --dp-secondary-color: #c0c4cc;
    --dp-border-color: #ddd;
    --dp-menu-border-color: #ddd;
    --dp-border-color-hover: #aaaeb7;
    --dp-disabled-color: #f6f6f6;
    --dp-scroll-bar-background: #f3f3f3;
    --dp-scroll-bar-color: #959595;
    --dp-success-color: #76d275;
    --dp-success-color-disabled: #a3d9b1;
    --dp-icon-color: #959595;
    --dp-danger-color: #ff6f60;
    --dp-marker-color: #ff6f60;
    --dp-tooltip-color: #fafafa;
    --dp-disabled-color-text: #8e8e8e;
    --dp-highlight-color: rgb(25 118 210 / 10%);
    --dp-range-between-dates-background-color: var(--dp-hover-color, #f3f3f3);
    --dp-range-between-dates-text-color: var(--dp-hover-text-color, #212121);
    --dp-range-between-border-color: var(--dp-hover-color, #f3f3f3);
}
*/

</style>