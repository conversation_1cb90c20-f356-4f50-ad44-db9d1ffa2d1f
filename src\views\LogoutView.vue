<template>
  <!-- Column Builder-->
  <div class="flex flex-row min-h-screen">
  
    <!-- Middle Container -->
    <div class="w-3/4 pr-12">
      <!-- Head-->
      <div class="w-full flex flex-row mt-24">
        <h1 class="font-OpenSans text-xl w-8/12 font-bold h-auto pb-3">Logout</h1>
        <!-- Login-->
        <div class="flex w-4/12">
          
        </div>
        <!-- Login -->
      </div>
      
      <!-- Head-->

      <!-- UserProfile -->
      <div class="w-full mt-6">

        <button @click="userStore.logoutSession()">Logout</button>
      </div>
      <!-- UserProfile -->
    </div>


    <!-- Right Container -->
    <div class="w-1/4 p-10 mt-12">
      
    </div>
    <!-- Right Container -->

  </div>
  
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/userStore'

  const router = useRouter();

  const { setNotification } = useNotification();
  const userStore = useUserStore()

  
</script>
<style scoped>

</style>