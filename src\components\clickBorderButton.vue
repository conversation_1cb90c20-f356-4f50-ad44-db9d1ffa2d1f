<template>
    <button 
        class="w-full items-center justify-center mx-auto h-auto mt-8 
        rounded-2xl bg-white border-solid border-black pt-4 pb-4
        pr-2 border-2 flex flex-col md:flex-row
        
        shadow-custom default-button-shadow
        "
    >
        <!-- text -->
        <div class="w-9/12 text-black px-2 font-YesevaOne text-xs">
            {{ element.buttondescription }}
        </div>

        <!-- icon -->
        <div v-if="element.iconneeded" class="w-3/12 mt-3 mx-auto md:mt-0">
            <img v-if="!element.active" class="w-6 mx-auto" :src="getImageUrl(element.buttonicon)" />
            <img v-if="element.active" class="w-6 animate-spin mx-auto" src="../assets/icons/reload.png" alt="searching" />
        </div>
        <!--
        <div v-if="element.active" class="absolute bg-white w-52 p-4 -mt-52 border solid border-r-8 border-alarmred-100">
            <p class="text-xs">{{ element.infoboxtext }}</p>
        </div>
        -->

    </button>
</template>
<script setup>
    import { defineProps, toRefs, ref, computed } from 'vue';

    const props = defineProps({
        element: Object,
        index: Number,
    })

    const { element } = toRefs(props);

    const getImageUrl = (name) => {
        return new URL(`../assets/icons/${name}`, import.meta.url).href
    }


</script>
<style>
/*
    .imglinkcss {
        background-image: v-bind('element.imagelink');
    }
*/
</style>

