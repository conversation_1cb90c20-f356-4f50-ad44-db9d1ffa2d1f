<template>
  <div class="w-full space-y-6">
    <!-- Prä<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> -->
    <div>
        <h3 class="text-md font-semibold mb-3">Mit Konfigurator ein Rezept erstellen</h3>
        <p class="text-xs text-gray-600 mb-4">Wähle deine Vorlieben aus. Alles außer der Personenzahl ist optional.</p>
        <div class="space-y-2">
            <div v-for="element in tempweekplanStore.datasetPreferences" v-bind:key="element.boxid">
                <!-- Annahme: preference-card aktualisiert das 'selected' im übergebenen element.boxvalues -->
                <preference-card :element="element" />  
            </div>
        </div>
    </div>

    <!-- Benutzerdefiniertes Textfeld -->
    <div>
        <label for="customPrompt" class="block text-md font-semibold mb-3">Zusätzliche Wünsche?</label>
        <textarea 
            id="customPrompt"
            v-model="customPromptText"
            rows="3"
            class="w-full md:w-2/3 mt-1 px-3 py-2 rounded-lg h-24 bg-white border border-gray-300 focus:outline-none focus:ring-1 focus:ring-ordypurple-500 focus:border-ordypurple-500"
            placeholder="z.B. schnell gemacht, unter 500 Kalorien, ohne Pilze...">
        </textarea>
    </div>

    <!-- Generator starten Button -->
    <div class="pt-4">
        <click-shadow-button 
            @click.prevent="generateRecipes" 
            :element="{'buttondescription': tempweekplanStore.searchText, 'active': 'false', 'iconneeded': false}" 
            :index="1"  
            :disabled="tempweekplanStore.isLoading"
        />
    </div>

    <!-- Ladeanzeige (Skeleton Loader) -->
    <div v-if="tempweekplanStore.isLoading" class="flex flex-nowrap lg:flex-wrap gap-5 pr-5 pb-12 mt-8 w-full h-auto justify-center">
      <div class="h-128 w-full md:w-114 rounded-2xl bg-gradient-to-r from-ordypurple-200 via-ordypink-100 to-ordypurple-200 background-animate">
        <div class="h-20 float-right mt-6 mr-6 w-20 rounded-2xl bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 background-animate"></div>
        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
        <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useTempWeekplanStore } from '@/store/weekplanStore';
import { useUserStore } from '@/store/userStore';
import useNotification from '@/../modules/notificationInformation'; // Pfad ggf. anpassen
import preferenceCard from '@/components/preferenceCard.vue'; // Pfad ggf. anpassen
import clickShadowButton from '@/components/clickShadowButton.vue'; // Pfad ggf. anpassen
import { useHelperStore } from '@/../utils/helper'; // Pfad ggf. anpassen

const tempweekplanStore = useTempWeekplanStore();
const userStore = useUserStore();
const { setNotification } = useNotification();
const helper = useHelperStore();

const customPromptText = ref('');

const generateRecipes = async () => {
    helper.devConsole("generateRecipes called (RecipeGenerator.vue)");

    // --- Validierung Personenzahl (bleibt gleich) ---
    let selectedPersonenValue = null;
    const anzahlPersonenPref = tempweekplanStore.datasetPreferences.find(p => p.boxid === "1");
    if (anzahlPersonenPref && anzahlPersonenPref.boxvalues) {
        const selectedOption = anzahlPersonenPref.boxvalues.find(option => option.selected === true);
        if (selectedOption && selectedOption.item) {
             const match = selectedOption.item.match(/\d+/);
             if (match) {
                 selectedPersonenValue = parseInt(match[0], 10);
             }
        }
    }

    if (selectedPersonenValue === null || selectedPersonenValue <= 0) {
        setNotification('Bitte gib eine gültige Anzahl Personen an.', 'alert');
        return;
    }
    // --- Ende Validierung ---

    // --- Sammle ausgewählte Präferenzen als Objekt ---
    const selectedPreferences = {};
    tempweekplanStore.datasetPreferences.forEach(pref => {
        if (pref.boxvalues) {
            const selectedOption = pref.boxvalues.find(option => option.selected === true);
            // Verwende pref.boxname als Key für das Backend
            if (selectedOption && selectedOption.item) { 
                 selectedPreferences[pref.boxname] = selectedOption.item; 
            }
        }
    });
    // --- Ende Sammeln ---

    helper.devConsole("Selected Preferences Obj:", selectedPreferences);
    helper.devConsole("Custom Prompt Text:", customPromptText.value);

    // Rufe die Store-Funktion mit dem Objekt und dem Text auf
    await tempweekplanStore.createWeekplanFromGenerator(selectedPreferences, customPromptText.value);
    
};

</script>

<style scoped>
.background-animate {
    background-size: 400%;
    -webkit-animation: AnimationName 3s ease infinite;
    -moz-animation: AnimationName 3s ease infinite;
    animation: AnimationName 3s ease infinite;
  }
  @keyframes AnimationName {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
</style> 