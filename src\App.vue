<template>
  <Analytics />
  <div class="h-screen w-full">
    <start-screen v-if="router.currentRoute.value.meta.layout==='startScreen'"></start-screen>
    <basic-screen v-if="router.currentRoute.value.meta.layout===null"></basic-screen>
    <minimal-layout v-if="router.currentRoute.value.meta.layout==='minimal'"></minimal-layout>
    <marketing-layout v-if="router.currentRoute.value.meta.layout==='marketing'"></marketing-layout>
    <BlankLayout v-if="router.currentRoute.value.meta.layout==='blank'"></BlankLayout>

    <!-- Admin Layout -->
    <router-view v-if="router.currentRoute.value.meta.layout==='admin'"></router-view>
    <basicConfirmation />

    <CompleteProfilePopup
      v-if="showCompleteProfilePopup"
      :initial-user="userStore.user"
      @close="showCompleteProfilePopup = false"
    />

    <!-- AGB Acceptance Modal -->
    <AgbAcceptance />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Analytics } from '@vercel/analytics/vue';
import { useUserStore } from './store/userStore';
import { useHelperStore } from '../utils/helper';
import startScreen from './body/startScreen.vue'
import basicScreen from './body/basicScreen.vue'
import minimalLayout from './body/minimalLayout.vue';
import marketingLayout from './body/marketingLayout.vue';
import BlankLayout from './layouts/BlankLayout.vue';
import basicConfirmation from './body/basicConfirmation.vue';
import CompleteProfilePopup from './components/CompleteProfilePopup.vue';
import AgbAcceptance from './components/AgbAcceptance.vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const helper = useHelperStore();

// --- State for the profile completion popup ---
const showCompleteProfilePopup = ref(false);
// ---------------------------------------------

onMounted(() => {
  helper.devConsole("App.vue mounted. Attempting auth initialization...");
  // --- REMOVED URL Query Parameter Check ---
  // Only attempt to initialize from stored tokens
  userStore.initAuth();
  // ------------------------------------------
});

// --- Watcher to show the popup if needed ---
watch(
    // Watch the user object for id, firstName, email changes
    () => userStore.user,
    (user) => {
        // Use optional chaining for safety
        const userId = user?.id;
        const firstName = user?.firstName;
        const email = user?.email;

        helper.devConsole(`Watcher triggered (user only): userId='${userId}', firstName='${firstName}', email='${email}'`);

        // Check if user is identified (has an ID)
        const isUserIdentified = userId !== null && userId !== undefined;

        // Explicitly check for null, undefined, or empty string for profile fields
        const isFirstNameMissing = firstName === null || firstName === undefined || firstName === '';
        const isEmailMissing = email === null || email === undefined || email === '';
        const profileIncomplete = isFirstNameMissing || isEmailMissing;

        helper.devConsole(`Watcher checks (user only): isUserIdentified=${isUserIdentified}, isFirstNameMissing=${isFirstNameMissing}, isEmailMissing=${isEmailMissing}, profileIncomplete=${profileIncomplete}`);

        // Determine if the popup should be shown: User must be identified AND profile must be incomplete
        const shouldShow = isUserIdentified && profileIncomplete;

        // Set the state
        showCompleteProfilePopup.value = shouldShow;

        helper.devConsole(`Watcher final decision (user only): Set showCompleteProfilePopup to ${shouldShow}`);

        if (shouldShow) {
            helper.devConsole("Popup should be visible because user is identified and profile is incomplete.");
        } else {
            if (!isUserIdentified) {
                 helper.devConsole("Reason not showing: User is not identified (userId is null).");
            } else if (!profileIncomplete) {
                 helper.devConsole("Reason not showing: Profile seems complete (firstName and email are present).");
            } else {
                 helper.devConsole("Reason not showing: Unspecified (isUserIdentified and profileIncomplete must be true).");
            }
        }
    },
    {
        immediate: true, // Check immediately on component mount
        deep: true // Watch nested properties within userStore.user
    }
);
// -------------------------------------------

</script>

<style>
/* App ist nur im Hochformat nutzbar */
</style>