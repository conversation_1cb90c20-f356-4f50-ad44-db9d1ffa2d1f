# Beispiel einer robots.txt Datei

# Alle Suchmaschinen dürfen alle Inhalte durchsuchen
User-agent: *
Disallow:

# Sitemap-URL angeben (optional, aber empfohlen)
Sitemap: https://www.ordy.ch/sitemap.xml

# Sperren des Zugriffs auf administrative Bereiche
User-agent: *
Disallow: /admin/
Disallow: /login/

# Blockiere spezifische Suchmaschinen-Bots (wenn notwendig)
User-agent: BadBot
Disallow: /

# Erlaube alle Inhalte außer spezifische Verzeichnisse oder Dateien
User-agent: *
Disallow: /temporary/
Disallow: /private/

# Erlaube den Zugriff auf spezifische Dateien ohne Einschränkungen
User-agent: *
Allow: /public/

# Hinzufügen von Crawl-Verzögerungen (optional)
User-agent: *
Crawl-delay: 10