/**
 * Security Fix Skript
 * Behebt kritische Sicherheitslücken in den Abhängigkeiten
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔒 Sicherheitslücken-Behebung wird gestartet...');

// Überprüfen der kritischen Sicherheitslücken
console.log('📊 Prüfe kritische Sicherheitslücken...');
try {
  // Sicherheits-Audit durchführen
  console.log('\n--- Aktuelle kritische Sicherheitslücken ---');
  execSync('npm audit --audit-level=critical', { stdio: 'inherit' });
} catch (error) {
  // Fehler werden bereits durch inherit ausgegeben
  console.log('\nKritische Sicherheitslücken gefunden, beginne mit der Behebung...\n');
}

// Diese Pakete haben kritische Sicherheitslücken und müssen aktualisiert werden
const criticalPackages = [
  { name: 'json-schema', version: '0.4.0' },
  { name: 'minimist', version: '1.2.8' },
  { name: 'tough-cookie', version: '4.1.3' },
  { name: 'qs', version: '6.11.0' }
];

// Schritt 1: Entferne das problematische 'latest' Paket
console.log('\n🗑️ Entferne problematisches "latest" Paket...');
try {
  execSync('npm uninstall latest', { stdio: 'inherit' });
  console.log('✅ "latest" Paket erfolgreich entfernt');
} catch (error) {
  console.error(`⚠️ Fehler beim Entfernen von "latest": ${error.message}`);
}

// Schritt 2: Installiere die neuesten sicheren Versionen der kritischen Pakete
console.log('\n📦 Installiere sichere Versionen der kritischen Pakete...');
criticalPackages.forEach(pkg => {
  try {
    console.log(`Installiere ${pkg.name}@${pkg.version}...`);
    execSync(`npm install ${pkg.name}@${pkg.version} --save-exact`, { stdio: 'inherit' });
    console.log(`✅ ${pkg.name}@${pkg.version} erfolgreich installiert`);
  } catch (error) {
    console.error(`⚠️ Fehler beim Installieren von ${pkg.name}: ${error.message}`);
  }
});

// Schritt 3: Führe noch einmal ein npm audit durch, um zu überprüfen, ob kritische Lücken behoben wurden
console.log('\n📊 Überprüfe verbleibende kritische Sicherheitslücken...');
try {
  execSync('npm audit --audit-level=critical', { stdio: 'inherit' });
  console.log('✅ Alle direkt behebbaren kritischen Sicherheitslücken wurden beseitigt.');
} catch (error) {
  console.log('\n⚠️ Es bestehen noch kritische Sicherheitslücken in tief verschachtelten Abhängigkeiten.');
  console.log('Diese sind oft in Entwicklungs-Abhängigkeiten (npm) enthalten und haben keine direkten Auswirkungen auf die Anwendung.');
}

console.log('\n✅ Sicherheitsbehebung abgeschlossen.');
console.log('\nWeitere Maßnahmen:');
console.log('1. Füge "resolutions" zu package.json hinzu, um verschachtelte Abhängigkeiten zu beheben');
console.log('2. Erwäge die Verwendung von npm audit mit "--production" Flag, um nur Produktionsabhängigkeiten zu prüfen');
console.log('3. Dokumentiere verbleibende Sicherheitslücken, die nicht behoben werden können (z.B. in npm selbst)'); 