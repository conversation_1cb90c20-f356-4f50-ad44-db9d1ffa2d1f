const axios = require('axios');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const helper = require('../utils/helper');

// Pinterest OAuth Configuration
const getPinterestConfig = () => {
  const NODE_ENV = process.env.NODE_ENV || 'development';
  let redirectUri;

  if (NODE_ENV === 'development') {
    redirectUri = 'http://localhost:8080/auth/pinterest/callback';
  } else if (NODE_ENV === 'preview') {
    redirectUri = 'https://ordy-tst-d8cfc0bzchbqd9ha.switzerlandnorth-01.azurewebsites.net/auth/pinterest/callback';
  } else {
    // Production
    redirectUri = 'https://www.ordyapp.com/auth/pinterest/callback';
  }

  return {
    clientId: process.env.PINTEREST_APP_ID,
    clientSecret: process.env.PINTEREST_SECRET_KEY,
    redirectUri: redirectUri,
    scopes: 'boards:read,pins:write,pins:read'
  };
};

// @desc    Start Pinterest OAuth flow
// @route   GET /api/v1/pinterest/oauth/start
// @access  Protected (Admin only)
exports.startOAuth = catchAsync(async (req, res, next) => {
  helper.devConsole('[pinterestOAuth.startOAuth] Starting Pinterest OAuth flow');

  const config = getPinterestConfig();
  
  if (!config.clientId || !config.clientSecret) {
    return next(new AppError('Pinterest OAuth credentials not configured', 500));
  }

  // Generate state for CSRF protection
  const state = `pinterest_oauth_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  
  // Store state in session or database for verification (simplified for now)
  // In production, you should store this securely
  
  const authUrl = `https://www.pinterest.com/oauth/?` +
    `response_type=code` +
    `&client_id=${config.clientId}` +
    `&redirect_uri=${encodeURIComponent(config.redirectUri)}` +
    `&scope=${encodeURIComponent(config.scopes)}` +
    `&state=${state}`;

  helper.devConsole('[pinterestOAuth.startOAuth] Generated auth URL:', authUrl);

  res.status(200).json({
    status: 'success',
    message: 'Pinterest OAuth flow initiated',
    data: {
      authUrl: authUrl,
      state: state,
      redirectUri: config.redirectUri,
      scopes: config.scopes
    }
  });
});

// @desc    Handle Pinterest OAuth callback
// @route   GET /api/v1/pinterest/oauth/callback
// @access  Public (but should validate state)
exports.handleCallback = catchAsync(async (req, res, next) => {
  helper.devConsole('[pinterestOAuth.handleCallback] Handling Pinterest OAuth callback');

  const { code, state, error } = req.query;

  if (error) {
    helper.devConsole('[pinterestOAuth.handleCallback] OAuth error:', error);
    return next(new AppError(`Pinterest OAuth error: ${error}`, 400));
  }

  if (!code) {
    return next(new AppError('No authorization code received', 400));
  }

  const config = getPinterestConfig();

  try {
    // Exchange code for access token
    helper.devConsole('[pinterestOAuth.handleCallback] Exchanging code for tokens');
    
    const tokenResponse = await axios.post('https://api.pinterest.com/v5/oauth/token', {
      grant_type: 'authorization_code',
      client_id: config.clientId,
      client_secret: config.clientSecret,
      code: code,
      redirect_uri: config.redirectUri
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const { access_token, refresh_token, token_type, scope } = tokenResponse.data;

    helper.devConsole('[pinterestOAuth.handleCallback] Tokens received successfully');
    helper.devConsole('[pinterestOAuth.handleCallback] Scopes:', scope);

    // In production, you should store these tokens securely in your database
    // For now, we'll return them in the response
    
    res.status(200).json({
      status: 'success',
      message: 'Pinterest OAuth completed successfully',
      data: {
        access_token: access_token,
        refresh_token: refresh_token,
        token_type: token_type,
        scope: scope,
        instructions: [
          'Add these tokens to your environment variables:',
          `PINTEREST_ACCESS_TOKEN=${access_token}`,
          `PINTEREST_REFRESH_TOKEN=${refresh_token}`
        ]
      }
    });

  } catch (error) {
    helper.devConsole('[pinterestOAuth.handleCallback] Error exchanging code:', 
      error.response?.data || error.message);
    return next(new AppError(
      `Failed to exchange code for tokens: ${error.response?.data?.message || error.message}`, 
      500
    ));
  }
});

// @desc    Refresh Pinterest access token
// @route   POST /api/v1/pinterest/oauth/refresh
// @access  Protected (Admin only)
exports.refreshToken = catchAsync(async (req, res, next) => {
  helper.devConsole('[pinterestOAuth.refreshToken] Refreshing Pinterest access token');

  const refreshToken = process.env.PINTEREST_REFRESH_TOKEN || req.body.refresh_token;
  
  if (!refreshToken) {
    return next(new AppError('No refresh token available', 400));
  }

  const config = getPinterestConfig();

  try {
    const tokenResponse = await axios.post('https://api.pinterest.com/v5/oauth/token', {
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: config.clientId,
      client_secret: config.clientSecret
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const { access_token, refresh_token: new_refresh_token, token_type, scope } = tokenResponse.data;

    helper.devConsole('[pinterestOAuth.refreshToken] New tokens received successfully');

    res.status(200).json({
      status: 'success',
      message: 'Pinterest access token refreshed successfully',
      data: {
        access_token: access_token,
        refresh_token: new_refresh_token || refreshToken, // Some APIs don't return new refresh token
        token_type: token_type,
        scope: scope,
        instructions: [
          'Update these tokens in your environment variables:',
          `PINTEREST_ACCESS_TOKEN=${access_token}`,
          new_refresh_token ? `PINTEREST_REFRESH_TOKEN=${new_refresh_token}` : 'Keep existing PINTEREST_REFRESH_TOKEN'
        ]
      }
    });

  } catch (error) {
    helper.devConsole('[pinterestOAuth.refreshToken] Error refreshing token:', 
      error.response?.data || error.message);
    return next(new AppError(
      `Failed to refresh token: ${error.response?.data?.message || error.message}`, 
      500
    ));
  }
});

// @desc    Test Pinterest integration
// @route   POST /api/v1/pinterest/test
// @access  Protected (Admin only)
exports.testIntegration = catchAsync(async (req, res, next) => {
  helper.devConsole('[pinterestOAuth.testIntegration] Testing Pinterest integration');

  const accessToken = process.env.PINTEREST_ACCESS_TOKEN;
  const boardId = process.env.PINTEREST_BOARD_ID;

  if (!accessToken) {
    return next(new AppError('Pinterest access token not configured', 400));
  }

  if (!boardId) {
    return next(new AppError('Pinterest board ID not configured', 400));
  }

  try {
    // Test 1: Get user account info
    const userResponse = await axios.get('https://api.pinterest.com/v5/user_account', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    // Test 2: Get boards
    const boardsResponse = await axios.get('https://api.pinterest.com/v5/boards', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    // Test 3: Check if the configured board exists
    const targetBoard = boardsResponse.data.items?.find(board => board.id === boardId);

    res.status(200).json({
      status: 'success',
      message: 'Pinterest integration test completed',
      data: {
        user: {
          username: userResponse.data.username,
          account_type: userResponse.data.account_type
        },
        boards: {
          total: boardsResponse.data.items?.length || 0,
          configured_board_found: !!targetBoard,
          configured_board: targetBoard || null
        },
        token_status: 'valid',
        ready_for_publishing: !!targetBoard
      }
    });

  } catch (error) {
    helper.devConsole('[pinterestOAuth.testIntegration] Error testing integration:', 
      error.response?.data || error.message);
    
    let errorMessage = 'Pinterest integration test failed';
    if (error.response?.status === 401) {
      errorMessage = 'Pinterest access token is invalid or expired';
    } else if (error.response?.status === 403) {
      errorMessage = 'Pinterest access token lacks required permissions';
    }

    return next(new AppError(
      `${errorMessage}: ${error.response?.data?.message || error.message}`, 
      error.response?.status || 500
    ));
  }
});
