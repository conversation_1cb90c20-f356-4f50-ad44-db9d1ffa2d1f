const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const Units = require('../models/unitModel');
const express = require('express');
const Grocery = require('../models/groceryModel');
const Menu = require('../models/menuModel');
const AppError = require('../utils/appError');
const userModel = require('../models/userModel');
const stripe = require('stripe')(process.env.STRIPE_KEY_SECRETKEY)
const envMode = process.env.NODE_ENV
const webHookSecret = process.env.STRIPE_WEBHOOK_SECRET
const baseUrl = process.env.BASE_URL
const uiBaseUrl = process.env.NODE_ENV === 'development'
  ? 'http://localhost:5173' // Entwicklungs-URL
  : process.env.NODE_ENV === 'preview'
  ? 'https://test.ordy.ch' // Preview-URL
  : 'https://ordy.ch'; // Produktions-URL


/////// SETTINGS /////////////
// Definiere die Preis-IDs für jede Umgebung
const priceIds = {
  development: {
    'priceid_1': 'price_1QL0gU8OMOfBdtQmikjmBJeO',
    'priceid_2': 'price_1QKNrn8OMOfBdtQmF3QgR18i',
    'priceid_3': 'price_1QKirr8OMOfBdtQmFOZbTu7T'
  },
  preview: {
    'priceid_1': 'price_1QMbJkG1QDxmQSlXkxMFG12I',
    'priceid_2': 'price_1QMbDLG1QDxmQSlXmgxCYnQD',
    'priceid_3': 'price_1QMbEuG1QDxmQSlXcnlmkUYv'
  },
  production: {
    'priceid_1': 'price_1QMbMZG1QDxmQSlXMuutrnrI',
    'priceid_2': 'price_1QMbOCG1QDxmQSlXr2J8LZgN',
    'priceid_3': 'price_1QMbOsG1QDxmQSlXwRuf0Lz3'
  }
};
////////////////////////////////////

//@GET ""
exports.checkout = catchAsync(async (req, res, next) => {

  //////////////////// SETTINGS /////////////////////////////
  // Checkout if user wants to change subscription
  ///////////////////////////////////////////////////////////

  helper.devConsole("checkout in paymentController")
  //console.log(req.params)
  //console.log(req.params.userid)
  //console.log("go")

// Bestimme die aktuelle Umgebung
const currentEnvironment = process.env.NODE_ENV;
helper.devConsole(currentEnvironment)

// Preis-IDs für die aktuelle Umgebung abrufen
const currentPriceIds = priceIds[currentEnvironment] || {};

// Logik zum Setzen von type und quantity
let type, quantity;

if (req.params.priceid === currentPriceIds['priceid_1']) {
  type = 0;
  quantity = 0;
} else if (req.params.priceid === currentPriceIds['priceid_2']) {
  type = 1;
  quantity = 1;
} else if (req.params.priceid === currentPriceIds['priceid_3']) {
  type = 2;
  quantity = 1;
}

  try{

      const user = await userModel.findOne({
        '_id': req.params.userid
      })
      //helper.devConsole(user)


      //helper.devConsole("Check bevore action | user and type check")
      //helper.devConsole(user)
      //helper.devConsole(type)

      if(type == 0 && user._id){
        ////////////////////////////////
        ////////// delete / cancel abo //////////
        ////////////////////////////////
        helper.devConsole("Subscription Type | Delete")
        const subscription = await stripe.subscriptions.cancel(
          user.bookedAbo.extStripeSubscriptionId,
          {
            prorate: true,
          }
        );

        /////////////// UPDATE DB USER ///////////////
        let updateUser = await userModel.updateOne(
          { _id: req.params.userid },
          {
            $set: {
              'bookedAbo.extStripeSessionId': '',
              'bookedAbo.extStripePriceId': '',
              'bookedAbo.extStripeCustomerId': '',
              'bookedAbo.extStripeSubscriptionId': '',
              'bookedAbo.type': type,
              'bookedAboUsage.nrCookeasyCalls': 0,
              'bookedAboUsage.nrMenucreationCalls': 0,
              'bookedAboUsage.nrMenuuploadCalls': 0,
              'bookedAboUsage.nrRealtimeApiCalls': 0,
              'bookedAbo.nrCookeasyCalls': 5,
              'bookedAbo.nrMenucreationCalls': 8,
              'bookedAbo.nrMenuuploadCalls': 3,
              'bookedAbo.nrRealtimeApiCalls': 10
            }
          }
        );
        //helper.devConsole(updateUser.bookedAbo)
        /////////////// UPDATE DB USER ///////////////

        //console.log(subscription)
        req.body.answerobject = {}
        req.body.answerobject.reload = true
        req.body.answerobject.message = "Das Abo wurde gecancelt."
        req.body.answerobject.url = uiBaseUrl + '/usersettings?success=true&type=0'

      }

      if (user._id && user.bookedAbo.extStripeCustomerId !== "" && (type === 1 || type === 2)) {
        ////////////////////////////////
        ////////// update abo //////////
        ////////////////////////////////
        //helper.devConsole("Subscription Type | Update")
          const subscription_object = await stripe.subscriptions.retrieve(user.bookedAbo.extStripeSubscriptionId);
          //helper.devConsole(subscription_object.items.data)

          const subscription = await stripe.subscriptions.update(
            subscription_object.id,
            {
              items: [
                {
                  // Provide the exact Price ID (for example, pr_1234) of the product you want to sell
                  id: subscription_object.items.data[0].id,
                  price: req.params.priceid,
                  quantity: quantity,
                },
              ],
            }
          );
          //helper.devConsole(subscription)

          /////////////// UPDATE DB USER ///////////////

          /*let updateUser = await userModel.updateOne(
            { _id: req.params.userid },
            {
              $set: {
                'bookedAbo.extStripePriceId': req.params.priceid,
                'bookedAbo.type': type,
              }
            }
          );
          helper.devConsole(updateUser)*/
          /////////////// UPDATE DB USER ///////////////


          // URL forwarding back to client if needed
          req.body.answerobject = {}
          req.body.answerobject.reload = true
          req.body.answerobject.message = "Das Abo wurde geändert."
          req.body.answerobject.url = uiBaseUrl + '/usersettings?success=true&type=' + type
      }


      if (user._id && user.bookedAbo.extStripeCustomerId === "" && (type === 1 || type === 2)) {
        ////////////////////////////////////
        ////////// create new abo //////////
        ////////////////////////////////////
        //helper.devConsole("Subscription Type | Create")
        helper.devConsole(baseUrl + '/auth/payment/checkout/retrival?session_id={CHECKOUT_SESSION_ID}&user_id=' + req.params.userid + '&type=' + type + '&price_id=' + req.params.priceid)
          const session = await stripe.checkout.sessions.create({
            line_items: [
              {
                // Provide the exact Price ID (for example, pr_1234) of the product you want to sell
                price: req.params.priceid,
                quantity: 1,
              },
            ],
            mode: 'subscription',
            success_url: baseUrl + '/auth/payment/checkout/retrival?session_id={CHECKOUT_SESSION_ID}&user_id=' + req.params.userid +
            '&type=' + type +
            '&price_id=' + req.params.priceid,
            cancel_url: uiBaseUrl + '/usersettings?success=false'
          })
          helper.devConsole(session.url)

        // URL forwarding back to client
        req.body.answerobject = {}
        req.body.answerobject.reload = true
        req.body.answerobject.url = session.url


      }

    next()

  } catch(error){
    helper.devConsole(error)
    // FIXED: Use 500 for Stripe API errors instead of 401 to prevent logout
    // 401 should only be used for authentication failures
    res.status(500).json({
      status: 'error',
      message: 'Fehler beim Verarbeiten der Abo-Änderung. Bitte versuchen Sie es erneut.',
      data: error.message || error
    });
  }



});

//@POST  ""
exports.retrival = catchAsync(async (req, res, next) => {

  //////////////////// SETTINGS /////////////////////////////
  // Checkout if user wants to change subscription
  ///////////////////////////////////////////////////////////

  helper.devConsole("retrival in paymentController")
  //console.log(req.query)
  //console.log(req.body)
  //console.log(req.params)
  //console.log("go")

  try{
    const session = await stripe.checkout.sessions.retrieve(req.query.session_id)
    //const lineItems = await stripe.checkout.sessions.listLineItems(req.query.session_id)
    //helper.devConsole(session)
    if(session && session.status === 'complete'){
      //helper.devConsole(session)

      const subscription = await stripe.subscriptions.retrieve(
        session.subscription
      );

      const product = await stripe.products.retrieve(
        subscription.plan.product
      );

      //helper.devConsole(product.metadata.nrCookeasyCalls)

      ///////// UPDATE USER WITH NEW ABO ////////
      const updateUser = await userModel.updateOne(
        { _id: req.query.user_id },
        {
          $set: {
            'bookedAbo.extStripePriceId': req.query.price_id,
            'bookedAbo.extStripeCustomerId': session.customer,
            'bookedAbo.extStripeSubscriptionId':session.subscription,
            'bookedAbo.extStripeSessionId': req.query.session_id,
            'bookedAbo.type': req.query.type,
            'bookedAboUsage.nrCookeasyCalls': 0,
            'bookedAboUsage.nrMenucreationCalls': 0,
            'bookedAboUsage.nrMenuuploadCalls': 0,
            'bookedAboUsage.nrRealtimeApiCalls': 0,
            'bookedAboUsage.nrCookeasyCalls_active': true,
            'bookedAboUsage.nrMenucreationCalls_active': true,
            'bookedAboUsage.nrMenuuploadCalls_active': true,
            'bookedAboUsage.nrRealtimeApiCalls_active': true,
            'bookedAbo.nrCookeasyCalls': product.metadata.nrCookeasyCalls,
            'bookedAbo.nrMenucreationCalls': product.metadata.nrMenucreationCalls,
            'bookedAbo.nrMenuuploadCalls': product.metadata.nrMenuuploadCalls,
            'bookedAbo.nrRealtimeApiCalls': product.metadata.nrRealtimeApiCalls || 10
          }
        }
      );

      // REDIRECT USER
      if(updateUser){
        res.redirect(uiBaseUrl+'/usersettings?success=true&type=' + req.query.type);
      }

    } else {
      helper.devConsole("Error inside retrival")
      res.redirect(uiBaseUrl+'/usersettings?success=false');
      res.status(301).json({
        status: 'error',
        message: 'Stripe: session war nicht richtig ausgechekt',
      });
    }

    //console.log(lineItems)

  } catch(error){
    helper.devConsole(error)
    // FIXED: Use 500 for Stripe session retrieval errors instead of 401 to prevent logout
    // 401 should only be used for authentication failures
    res.status(500).json({
      status: 'error',
      message: 'Fehler beim Abrufen der Zahlungssitzung. Bitte versuchen Sie es erneut.',
      data: error.message || error
    });
  }

});


//@POST ""
exports.menucreationsLicenceChecker = catchAsync(async (req, res, next) => {
  /// ONLY MENUCREATIONS AND FORWARD
    req.body.usageobject = {}
    req.body.usageobject.type = 'nrMenucreationCalls'
    next()
})

//@POST ""
exports.cookeasyLicenceChecker = catchAsync(async (req, res, next) => {
  /// ONLY MENUCREATIONS AND FORWARD
  req.body.usageobject = {}
  req.body.usageobject.type = 'nrCookeasyCalls'
  next()
})

//@POST ""
exports.menuuploadsLicenceChecker = catchAsync(async (req, res, next) => {
  /// ONLY MENUCREATIONS AND FORWARD
  req.body.usageobject = {}
  req.body.usageobject.type = 'nrMenuuploadCalls'
  next()
})

//@POST ""
exports.realtimeApiLicenceChecker = catchAsync(async (req, res, next) => {
  /// ONLY REALTIME API (ASSISTANT) AND FORWARD
  req.body.usageobject = {}
  req.body.usageobject.type = 'nrRealtimeApiCalls'
  next()
})

//@POST ""
exports.licenceChecker = catchAsync(async (req, res, next) => {

  //////////////////// SETTINGS /////////////////////////////
  // Checking active licence and available requests
  ///////////////////////////////////////////////////////////
  // Sicherstellen, dass req.body.licence existieren
  req.body = req.body || {};
  req.body.licence = req.body.licence || {};

  // Standardwert für directresponse setzen, wenn es nicht definiert ist
  req.body.licence.directresponse = req.body.licence.directresponse || false;

  try{

  helper.devConsole("licenceChecker in paymentController")

  // *** FIX 1: Check if req.user exists ***
  if (!req.user || !req.user._id) {
      helper.devConsole("licenceChecker Error: req.user not found or missing _id. Was authController.verify run before?");
      return next(new AppError('User authentication data missing.', 500)); // Internal error if verify didn't run
  }

  const userId = req.user._id; // Get user ID from authenticated user
  const usageType = req.body.usageobject?.type; // Safely access usage type

  if (!usageType) {
      helper.devConsole("licenceChecker Error: usageobject.type missing in req.body");
      return next(new AppError('Usage type for licence check not specified.', 400));
  }

  helper.devConsole(`licenceChecker: Checking usage type '${usageType}' for User ID '${userId}'`);

  // *** FIX 2: Use correct user ID for finding and updating ***
  // Use findByIdAndUpdate for atomicity
  const user = await userModel.findByIdAndUpdate(
    userId,
    { $inc: { [`bookedAboUsage.${usageType}`]: 1 } },
    { new: true } // Return the updated document
  );

  if (!user) {
      // This should ideally not happen if req.user exists, but handle defensively
      helper.devConsole(`licenceChecker Error: User with ID ${userId} not found during update.`);
      // Rollback increment attempt conceptually (though findByIdAndUpdate didn't find user)
      // Consider if specific error handling needed here
      return next(new AppError('User not found during license check.', 404));
  }

  helper.devConsole(`licenceChecker: User ${userId} found. Current usage for ${usageType}: ${user.bookedAboUsage[usageType]}, Limit: ${user.bookedAbo[usageType]}`);

  // Store user data for potential later use (replacing old req.body.userobject logic)
  // Be cautious about modifying req.body, maybe use req.locals? For now, follow pattern.
  req.body.checkedUser = user; // Store the found/updated user

  /////////////// CHECK LIMIT REACHED ///////////////////
  if (user.bookedAboUsage[usageType] > user.bookedAbo[usageType]) {

    helper.devConsole(`licenceChecker: LIMIT REACHED for User ${userId}, usage type ${usageType}`);
    // Rollback the increment because the limit was exceeded
    await userModel.findByIdAndUpdate(userId, {
        $inc: { [`bookedAboUsage.${usageType}`]: -1 },
        // Optionally mark as inactive if that logic is needed
        // $set: { [`bookedAboUsage.${usageType}_active`]: false }
    });

    // Map technical usage types to user-friendly names
    const usageTypeNames = {
        'nrMenucreationCalls': 'KI-Menüerstellungen',
        'nrCookeasyCalls': 'KI-Kochassistent',
        'nrMenuuploadCalls': 'Rezept-Uploads',
        'nrRealtimeApiCalls': 'Sprach-Assistent'
    };

    const friendlyName = usageTypeNames[usageType] || usageType;

    // Return a specific error indicating the limit was reached
    // Use 402 Payment Required or 403 Forbidden or custom error
    return next(new AppError(`Deine freie Kapazität für ${friendlyName} ist für diesen Monat leider aufgebraucht.`, 402)); // 402 Payment Required is suitable

  } else {
    helper.devConsole(`licenceChecker: Limit OK for User ${userId}, usage type ${usageType}. Proceeding...`);

    // Store updated usage data for potential inclusion in response
    req.body.updatedUsageData = {
        bookedAboUsage: user.bookedAboUsage,
        bookedAbo: user.bookedAbo
    };

    // Otherwise, forward to the next middleware/controller
    next();
  }
  ////////////////////////////////////////////////////

  } catch(error){
    helper.devConsole(`licenceChecker: UNEXPECTED ERROR for User ${req.user?._id}:`, error);
    // *** FIX 3: Pass error to global handler ***
    // Do NOT send 401 here for internal errors
    next(error);
  }

});


//@POST auth/payment/stripe_webhook
exports.stripewebhook = catchAsync( async (req, res, next) => {
  helper.devConsole("stripewebhook in paymentController")
  const sig = req.headers['stripe-signature']

  try{
    /////////////// AUTH //////////
    ///////////////////////////////
    const event = await stripe.webhooks.constructEvent(req.body, sig, webHookSecret);
    helper.devConsole(event.type)

    /////////// SETTINGS /////////
    //////////////////////////////
    const user = await userModel.findOne({
      'bookedAbo.extStripeCustomerId': event.data.object.customer
    })

    if(!user){
      helper.devConsole("stripewebhook | NO ACTIVE USER FOUND")
      return res.status(401).json({ message: "No active user found." });
    } else {
      helper.devConsole("User found")
    }
    //////////////////////////////////





    /////////// invoice.paid //////////////
    ///////////////////////////////////////
    if(event.type == 'invoice.paid'){
      helper.devConsole("stripewebhook | invoice.paid")
      // load subscripton
      const activeSubscription = await stripe.subscriptions.retrieve(
        event.data.object.subscription
      );
      helper.devConsole("activeSubscription")
      helper.devConsole(activeSubscription)

      // load product
      const metadataProduct = await stripe.products.retrieve(
        activeSubscription.plan.product
      );
      helper.devConsole("metadataProduct")
      helper.devConsole(metadataProduct)
      ////////////////////

      ///////// UPDATE USER WITH CORRECT PRICE MODEL ////////
      await userModel.updateOne(
        { _id: user._id },
        {
          $set: {
            //nrMenucreationCalls_active
            'bookedAboUsage.nrCookeasyCalls': 0,
            'bookedAboUsage.nrMenucreationCalls': 0,
            'bookedAboUsage.nrMenuuploadCalls': 0,
            'bookedAboUsage.nrRealtimeApiCalls': 0,
            'bookedAboUsage.nrCookeasyCalls_active': true,
            'bookedAboUsage.nrMenucreationCalls_active': true,
            'bookedAboUsage.nrMenuuploadCalls_active': true,
            'bookedAboUsage.nrRealtimeApiCalls_active': true,
            'bookedAbo.nrCookeasyCalls': metadataProduct.metadata.nrCookeasyCalls,
            'bookedAbo.nrMenucreationCalls': metadataProduct.metadata.nrMenucreationCalls,
            'bookedAbo.nrMenuuploadCalls': metadataProduct.metadata.nrMenuuploadCalls,
            'bookedAbo.nrRealtimeApiCalls': metadataProduct.metadata.nrRealtimeApiCalls || 10,
            'bookedAbo.extStripePriceId': activeSubscription.plan.id,
            'bookedAbo.type': metadataProduct.metadata.type,
          }
        }
      );
      /////////////////////////////////////////////////////////////
    }

    ///////////////////////////////////////////////////////////


    //// customer.subscription.deleted ///////
    ///////////////////////////////////////
    if(event.type == 'customer.subscription.deleted' || event.type == 'invoice.payment_failed'){
      helper.devConsole("stripewebhook | customer.subscription.deleted")
      ///////// UPDATE USER WITH CORRECT PRICE MODEL ////////
      await userModel.updateOne(
        { _id: user._id },
        {
          $set: {
            'bookedAboUsage.nrCookeasyCalls': 0,
            'bookedAboUsage.nrMenucreationCalls': 0,
            'bookedAboUsage.nrMenuuploadCalls': 0,
            'bookedAboUsage.nrRealtimeApiCalls': 0,
            'bookedAboUsage.nrCookeasyCalls_active': true,
            'bookedAboUsage.nrMenucreationCalls_active': true,
            'bookedAboUsage.nrMenuuploadCalls_active': true,
            'bookedAboUsage.nrRealtimeApiCalls_active': true,
            'bookedAbo.nrCookeasyCalls': 5,
            'bookedAbo.nrMenucreationCalls': 8,
            'bookedAbo.nrMenuuploadCalls': 3,
            'bookedAbo.nrRealtimeApiCalls': 10,
            'bookedAbo.type': 0,
            'bookedAbo.extStripeSessionId': '',
            'bookedAbo.extStripeCustomerId': '',
            'bookedAbo.extStripePriceId': '',
            'bookedAbo.extStripeSubscriptionId': ''
          }
        }
      );
      /////////////////////////////////////////////////////////////
    }

    return res.sendStatus(200)

  } catch (err){
    helper.devConsole(err)
    return res.sendStatus(501)
  }
  //const session = await stripe.checkout.sessions.retrieve(req.query.session_id)
})