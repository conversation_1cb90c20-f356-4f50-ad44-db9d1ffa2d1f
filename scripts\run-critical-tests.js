/**
 * Automatisiertes Test-Script für kritische Funktionen
 * 
 * Die<PERSON>:
 * 1. Startet Frontend und Backend Server
 * 2. Führt Playwright-Tests aus
 * 3. Generiert detaillierte Reports
 * 4. Stoppt Server nach Tests
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

class TestRunner {
  constructor() {
    this.frontendProcess = null;
    this.backendProcess = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: [],
      details: []
    };
  }

  async startServers() {
    console.log('🚀 Starting servers...');
    
    try {
      // Frontend Server starten
      console.log('📱 Starting Frontend Server (Vite)...');
      this.frontendProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'pipe',
        shell: true
      });
      
      // Backend Server starten (falls vorhanden)
      console.log('🔧 Starting Backend Server...');
      this.backendProcess = spawn('npm', ['start'], {
        stdio: 'pipe',
        shell: true,
        cwd: '../ordy-api' // Annahme: Backend ist im Nachbar-Ordner
      });
      
      // Warte auf Server-Start
      await this.waitForServers();
      console.log('✅ Servers started successfully');
      
    } catch (error) {
      console.error('❌ Error starting servers:', error);
      throw error;
    }
  }

  async waitForServers() {
    console.log('⏳ Waiting for servers to be ready...');
    
    // Warte auf Frontend (max 60 Sekunden)
    for (let i = 0; i < 60; i++) {
      try {
        const response = await fetch('http://localhost:5173');
        if (response.ok) {
          console.log('✅ Frontend server ready');
          break;
        }
      } catch (error) {
        if (i === 59) throw new Error('Frontend server failed to start');
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Warte auf Backend (max 30 Sekunden)
    for (let i = 0; i < 30; i++) {
      try {
        const response = await fetch('http://localhost:8080/health');
        if (response.ok) {
          console.log('✅ Backend server ready');
          break;
        }
      } catch (error) {
        if (i === 29) {
          console.warn('⚠️ Backend server not responding, continuing with tests...');
          break;
        }
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async runTests() {
    console.log('🧪 Running Playwright tests...');
    
    try {
      const { stdout, stderr } = await execAsync('npx playwright test tests/critical-functions.spec.js --reporter=json');
      
      // Parse Test Results
      const results = JSON.parse(stdout);
      this.parseTestResults(results);
      
      console.log('✅ Tests completed');
      return this.testResults;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      this.testResults.errors.push(error.message);
      return this.testResults;
    }
  }

  parseTestResults(results) {
    results.suites?.forEach(suite => {
      suite.specs?.forEach(spec => {
        spec.tests?.forEach(test => {
          const testName = test.title;
          const status = test.outcome;
          
          if (status === 'expected') {
            this.testResults.passed++;
            this.testResults.details.push({
              name: testName,
              status: 'PASSED',
              duration: test.results[0]?.duration || 0
            });
            console.log(`✅ ${testName}`);
          } else {
            this.testResults.failed++;
            const error = test.results[0]?.error?.message || 'Unknown error';
            this.testResults.details.push({
              name: testName,
              status: 'FAILED',
              error: error,
              duration: test.results[0]?.duration || 0
            });
            console.log(`❌ ${testName}: ${error}`);
          }
        });
      });
    });
  }

  async generateReport() {
    console.log('📊 Generating test report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.passed + this.testResults.failed,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        successRate: `${Math.round((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100)}%`
      },
      details: this.testResults.details,
      errors: this.testResults.errors
    };
    
    // Speichere Report
    const reportPath = path.join(process.cwd(), 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Console Output
    console.log('\n📋 TEST SUMMARY:');
    console.log(`Total Tests: ${report.summary.total}`);
    console.log(`Passed: ${report.summary.passed}`);
    console.log(`Failed: ${report.summary.failed}`);
    console.log(`Success Rate: ${report.summary.successRate}`);
    
    if (report.summary.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.details
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`- ${test.name}: ${test.error}`);
        });
    }
    
    console.log(`\n📄 Full report saved to: ${reportPath}`);
    return report;
  }

  async stopServers() {
    console.log('🛑 Stopping servers...');
    
    if (this.frontendProcess) {
      this.frontendProcess.kill();
      console.log('✅ Frontend server stopped');
    }
    
    if (this.backendProcess) {
      this.backendProcess.kill();
      console.log('✅ Backend server stopped');
    }
  }

  async run() {
    try {
      await this.startServers();
      const results = await this.runTests();
      const report = await this.generateReport();
      
      return report;
      
    } catch (error) {
      console.error('❌ Test runner failed:', error);
      throw error;
    } finally {
      await this.stopServers();
    }
  }
}

// Script ausführen wenn direkt aufgerufen
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new TestRunner();
  
  runner.run()
    .then(report => {
      console.log('\n🎉 Test run completed successfully!');
      process.exit(report.summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('\n💥 Test run failed:', error);
      process.exit(1);
    });
}

export default TestRunner;
