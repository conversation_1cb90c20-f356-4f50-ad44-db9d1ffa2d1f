/**
 * Azure App Test Script
 * 
 * Dieses Skript führt grundlegende Tests durch, um zu überprüfen, 
 * ob die App ordnungsgemäß funktioniert.
 */

'use strict';

const axios = require('axios');
const dotenv = require('dotenv');

// Laden der Umgebungsvariablen
dotenv.config({ path: './config.env' });

// Konfiguration
const PORT = process.env.PORT || 8080;
const BASE_URL = `http://localhost:${PORT}`;

console.log(`🔍 Testing API on ${BASE_URL}`);

// Grundlegende Tests definieren
const tests = [
  {
    name: 'Server Status',
    endpoint: '/',
    method: 'GET',
    expectStatus: [200, 404], // 404 akzeptieren, falls kein Root-Handler definiert ist
    validator: () => true // Keine spezifische Validierung
  },
  {
    name: '<PERSON> Root',
    endpoint: '/api/v1',
    method: 'GET',
    expectStatus: [200, 404], // 404 akzeptieren, falls kein API-Root-Handler definiert ist
    validator: () => true // Keine spezifische Validierung
  },
  {
    name: 'Swagger Documentation',
    endpoint: '/api-docs',
    method: 'GET',
    expectStatus: [200, 301, 302],
    validator: () => true
  }
];

// Tests ausführen
async function runTests() {
  let passedTests = 0;
  let failedTests = 0;
  
  for (const test of tests) {
    try {
      console.log(`\n🧪 Running test: ${test.name}`);
      const response = await axios({
        method: test.method,
        url: `${BASE_URL}${test.endpoint}`,
        validateStatus: (status) => test.expectStatus.includes(status)
      });
      
      console.log(`  Status: ${response.status}`);
      
      // Prüfen, ob die Antwort den Erwartungen entspricht
      if (test.expectStatus.includes(response.status) && test.validator(response.data)) {
        console.log(`  ✅ Test passed`);
        passedTests++;
      } else {
        console.log(`  ❌ Test failed: Unexpected response`);
        failedTests++;
      }
    } catch (error) {
      console.error(`  ❌ Test failed: ${error.message}`);
      if (error.response) {
        console.error(`  Status: ${error.response.status}`);
      }
      failedTests++;
    }
  }
  
  // Ergebnisse ausgeben
  console.log('\n📊 Test Results:');
  console.log(`  Total tests: ${tests.length}`);
  console.log(`  Passed: ${passedTests}`);
  console.log(`  Failed: ${failedTests}`);
  
  if (failedTests === 0) {
    console.log('\n✅ All tests passed! The app is running correctly.');
    return true;
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs for details.');
    return false;
  }
}

// Hauptfunktion
async function main() {
  try {
    // Warten, bis der Server gestartet ist
    console.log('Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Tests ausführen
    const testsPassed = await runTests();
    
    // Ausgabe zusammenfassen
    if (testsPassed) {
      console.log('\n🎉 The app appears to be running correctly and should work on Azure.');
    } else {
      console.log('\n🔧 The app has some issues that might cause problems on Azure.');
    }
  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

// Skript ausführen
main(); 