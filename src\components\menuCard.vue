<template>
    <!-- menu card https://www.figma.com/file/49NcsyNfVIB2EIvJ2OCknX/ordy?node-id=9-586&t=tGQORj3doRlBijWg-4 -->
    <transition name="fade" appear>

        <div
            class="rounded-2xl antialiased bg-cover h-128 w-80 bg-ordypurple-100"
            :style="{ 'background-image': 'url(' + element.imagelink + ')' }"
        >
            <!-- head -->
            <div class="w-full mt-6 p-6 flex flex-row text-right text-white">
                <div class="w-4/5 pr-4 h-44">
                    <h3 class="mt-1 text-base">{{ element.name }}</h3>
                </div>
                <div class="w-1/5">
                    <button class="w-16 h-16 bg-white rounded-lg"><img src="../assets/icons/reload.png" class="mx-auto" /></button>
                </div>
            </div>

            <!-- foot -->
            <div class="w-full mt-48 p-6 flex flex-nowrap gap-4">
                <div class="w-5/12">
                    <div class="h-9 w-full bg-white rounded-lg flex flex-row justify-center pt-2">
                        <span class="h-6 pt-1 text-xs">{{ element.numberOfPersons }}</span>
                        <img class="h-6" src="../assets/icons/people.png" alt="people" />
                        <span class="h-6 break-keep text-xs pt-1">{{ element.cookingTime }} min</span>
                    </div>
                </div>
                <div class="w-5/12" v-if="menuPlanType.editMenu">
                    <router-link :to="`/wochenplan/menu/${weekplanParentData._id}`">
                        <button class="h-9 bg-white rounded-lg w-full text-xs">zum Rezept</button>
                    </router-link>
                </div>
                <div class="w-5/12" v-else>
                    <router-link :to="`/kochbuch/menu/${element._id}`">
                        <button class="h-9 bg-white rounded-lg w-full text-xs">zum Rezept</button>
                    </router-link>
                </div>

                <!-- --------------- menuPlanType.addToMenu ----------------- -->
                <div class="w-2/12 mx-auto h-auto" v-if="menuPlanType.addToMenu">
                    <button
                        @mousedown="handleMouseDown"
                        @mouseup="handleMouseUpOrLeave"
                        @mouseleave="handleMouseUpOrLeave"
                        @touchstart.prevent="handleMouseDown"
                        @touchend.prevent="handleMouseUpOrLeave"
                        class="h-9 w-9 float-right rounded-3xl transition-colors duration-150 ease-in-out focus:outline-none"
                        :class="{ 'bg-white': !isOptimisticallyOnList, 'bg-black': isOptimisticallyOnList }"
                    >
                        <img
                            src="../assets/icons/add.png"
                            class="mx-auto transition-all duration-150 ease-in-out"
                            alt="add"
                            :class="{ 'filter invert': isOptimisticallyOnList }"
                        />
                    </button>
                    <div v-if="addMenuTo" class="absolute w-64 h-42 -mt-20 -ml-56 rounded-xl bg-white flex flex-col shadow-lg z-10">
                        <div class="w-full flex flex-row border-b border-gray-200">
                            <div class="w-10/12 p-2 pl-2 px-0 text-xs py-1 font-bold mt-1">Zum Wochenplan hinzufügen</div>
                            <button @click="addMenuTo = false" class="w-2/12 p-1 pr-6 text-center font-bold text-gray-500 hover:text-black">x</button>
                        </div>
                        <div class="p-2 space-y-2">
                            <div class="w-full">
                                <select id="wochenplan" v-model="wochenplan" autocomplete="off" class="w-full px-1 py-1 text-sm text-gray-600 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-1 focus:ring-ordypurple-500">
                                    <option disabled value="">Zeit bestimmen</option>
                                    <option value="Morgen">Morgen</option>
                                    <option value="Mittag">Mittag</option>
                                    <option value="Abend" selected>Abend</option>
                                </select>
                            </div>
                            <div class="w-full">
                                <input
                                    class="w-full bg-white text-sm text-gray-600 px-2 py-1 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-ordypurple-500"
                                    id="anzahlPersonen"
                                    v-model="countsOfPersonsToSet"
                                    placeholder="Anzahl Personen"
                                    type="number"
                                    min="1"
                                    autocomplete="off"
                                />
                            </div>
                            <div class="w-full">
                                <DatePicker
                                    v-model="setDate"
                                    mode="date"
                                    :color="selectedColor"
                                    :attributes='attributes'
                                    class="w-full"
                                    autocomplete="off"
                                    :popover="{ visibility: 'focus' }"
                                >
                                    <template v-slot="{ inputValue, inputEvents }">
                                        <input
                                            class="w-full bg-white text-sm text-gray-600 px-2 py-1 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-ordypurple-500"
                                            id="datum"
                                            :value="inputValue"
                                            v-on="inputEvents"
                                            placeholder="Tag auswählen"
                                            readonly
                                            autocomplete="off"
                                        />
                                    </template>
                                </DatePicker>
                            </div>
                            <div class="pt-1">
                                <button
                                    @click="weekplanstore.createWeekplanItem(element._id, setDate, wochenplan, countsOfPersonsToSet, index, element); addMenuTo = false"
                                    class="w-full py-1.5 rounded-lg bg-ordyblue-300 text-white text-sm font-semibold hover:bg-ordyblue-400 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-ordyblue-500 transition-colors duration-150 ease-in-out"
                                >
                                    Bestätigen
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- --------------- menuPlanType.addToMenu ----------------- -->
                <!-- --------------- menuPlanType.editMenu ----------------- -->
                <div class="w-2/12 mx-auto h-auto" v-if="menuPlanType.editMenu">
                    <button @click="addToWeekplan = !addToWeekplan" class="h-9 w-9 float-right bg-white rounded-3xl"><img src="../assets/icons/add.png" class="mx-auto" alt="add"></button>
                    <div v-if="addToWeekplan" class="absolute w-64 h-18 -mt-20 -ml-52 rounded-xl bg-white flex flex-col">
                        <div class="w-full flex flex-row">
                            <div class="w-10/12 p-2 font-bold">Änderungen</div>
                            <button @click="addToWeekplan = !addToWeekplan" class="w-2/12 p-2 pr-6 text-center">x</button>
                        </div>
                        <div>
                            <button @click="weekplanstore.deleteWeekplanItem(index), addToWeekplan = !addToWeekplan, showElementAnimation = !showElementAnimation" class="w-10/12 mx-auto ml-4 mt-2 mb-4 rounded-xl bg-gray-100">Löschen</button>
                        </div>
                    </div>
                </div>
                <!-- --------------- menuPlanType.editMenu ----------------- -->
                <!-- --------------- menuPlanType.none ----------------- -->
                <div class="w-2/12 mx-auto h-auto" v-if="menuPlanType.none">
                    <button @click="addToWeekplan = !addToWeekplan" class="h-9 w-9 float-right bg-white rounded-3xl"><img src="../assets/icons/add.png" class="mx-auto" alt="add"></button>
                </div>
                <!-- --------------- menuPlanType.none ----------------- -->
            </div>
            <!-- foot -->

            <!-- foot -->

            <!-- foot -->
        </div>
    </transition>
</template>
<script setup>
    import { ref, reactive, onBeforeUnmount, onMounted, onDeactivated, watch } from 'vue';
    import { DatePicker } from 'v-calendar'
    import 'v-calendar/dist/style.css';
    import { useWeekplanStore } from '../store/weekplanStore';
    import { useUserStore } from '../store/userStore';
    import { useActiveShoppingListStore } from '../store/activeShoppingListStore';
    import useNotification from '../../modules/notificationInformation';
    import { useMenuStore, useMenuesStore } from '../store/menuStore';
    import { useHelperStore } from '../../utils/helper';

    // date picker
    const date = new Date()
    const year = date.getFullYear();
    const month = date.getMonth();
    const setDate = ref('');
    const wochenplan = ref('');
    const countsOfPersonsToSet = ref('')
    const showElementAnimation = ref(false)
    const attributes = ref([
        {
            key: 1,
            dot: true,
            dates: new Date(year, month, 29),
        }
    ])

    const weekplanstore = useWeekplanStore()
    const userStore = useUserStore()
    const activeListStore = useActiveShoppingListStore();
    const { setNotification } = useNotification();
    const menuStore = useMenuesStore();
    const helper = useHelperStore();

    // add to Menu
    const addToWeekplan = ref(false);
    const addMenuTo = ref(false)

    // Refs für das neue Klickverhalten
    const pressTimer = ref(null);     // Speichert die Timeout-ID
    const pressDurationThreshold = 2000; // 2 Sekunden

    const props = defineProps({
        element: Object,
        index: Number,
        index2: Number,
        menuPlanType: Object,
        weekplanParentData: Object,
    })

    const isOptimisticallyOnList = ref(false);

    const handleMouseDown = () => {
        // Timer starten, der nach 2 Sekunden das Popup öffnet
        pressTimer.value = setTimeout(() => {
            addMenuTo.value = true;      // Popup öffnen
            pressTimer.value = null;     // Timer-ID zurücksetzen
        }, pressDurationThreshold);
    };

    const handleMouseUpOrLeave = async () => {
        if (pressTimer.value) {
            // Timer läuft noch -> Klick war < 2 Sekunden
            clearTimeout(pressTimer.value);
            pressTimer.value = null;

            // --- Check if list is ready, FETCH if not (existing code) ---
            if (!activeListStore.listId) {
                try {
                    await activeListStore.fetchActiveList(); // Fetch/create the list
                    if (!activeListStore.listId) { // Check again after fetch
                         setNotification('Fehler beim Erstellen/Laden des Zettels.', 'alert');
                         return;
                    }
                } catch (fetchError) {
                    // Error is already handled and notified within fetchActiveList
                    return; // Stop if fetching failed
                }
            }
            // -------------------------------------------

            // --- Stabilisierte Add/Remove-Funktionalität ---
            const currentStatus = isOptimisticallyOnList.value;
            const newStatus = !currentStatus;

            // Sofortige UI-Aktualisierung für bessere UX
            isOptimisticallyOnList.value = newStatus;

            try {
                if (newStatus) {
                    // ADDING: Rezept zum Einkaufszettel hinzufügen
                    await activeListStore.addRecipe(props.element._id);
                    helper.devConsole(`Recipe ${props.element._id} successfully added to shopping list`);
                } else {
                    // REMOVING: Rezept vom Einkaufszettel entfernen
                    await activeListStore.removeRecipe(props.element._id);
                    helper.devConsole(`Recipe ${props.element._id} successfully removed from shopping list`);
                }

                // Backend-Synchronisation erfolgreich - Status im Store aktualisieren
                menuStore.setMenuZettelStatus(props.element._id, newStatus);
                helper.devConsole(`Menu store updated: Recipe ${props.element._id} status = ${newStatus}`);

            } catch (error) {
                // Backend-Fehler: Optimistische Aktualisierung rückgängig machen
                console.error("Failed to update recipe status:", error);
                isOptimisticallyOnList.value = currentStatus; // Zurück zum ursprünglichen Status

                // Store-Status auch zurücksetzen
                menuStore.setMenuZettelStatus(props.element._id, currentStatus);

                // Benutzer informieren
                const action = newStatus ? 'hinzufügen' : 'entfernen';
                setNotification(`Fehler beim ${action} des Rezepts zum Einkaufszettel`, 'alert');
                helper.devConsole(`Recipe ${props.element._id} operation failed, reverted to status: ${currentStatus}`);
            }
            // ---------------------------------

            addMenuTo.value = false; // Popup sicherheitshalber schließen
        }
        // Wenn der Timer bereits ausgelöst wurde (langer Klick), passiert hier nichts
    };

    onMounted (() => {
        showElementAnimation.value = true;
        // Ensure initial state sync AFTER component is mounted
        isOptimisticallyOnList.value = props.element.isOnActiveShoppingList;
        // console.log(`[MenuCard ${props.element._id}] Mounted: Synced local state to ${isOptimisticallyOnList.value}`);

        // --- Define Watcher here ---
        watch(() => props.element.isOnActiveShoppingList, (newValue) => {
          isOptimisticallyOnList.value = newValue;
          // console.log(`[MenuCard ${props.element._id}] Watcher: prop changed to ${newValue}, updated local state to ${isOptimisticallyOnList.value}`);
        });
    })

    onDeactivated (() => {
        showElementAnimation.value = false;
    })

</script>
<style>

.background-animate {
    background-size: 400%;

    -webkit-animation: AnimationName 3s ease infinite;
    -moz-animation: AnimationName 3s ease infinite;
    animation: AnimationName 3s ease infinite;
  }

  @keyframes AnimationName {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

.fade-leave-to {
  opacity: 0;
}

.fade-leave-from {
  opacity: 1;
}

.fade-leave-active {
  transition: opacity 1s ease
}

.fade-enter-from {
  opacity: 0
}
.fade-enter-to {
  opacity: 1
}
.fade-enter-active {
  transition: opacity 1s ease
}

</style>

