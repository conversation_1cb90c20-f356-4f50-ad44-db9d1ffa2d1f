<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center">
              <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
            </router-link>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
            <router-link to="/rezepte" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
            <router-link to="/about" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="flex min-h-screen">
      <!-- Left Side - Hero Content -->
      <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/10"></div>
        <div class="relative z-10 flex flex-col justify-center px-12 text-white">
          <div class="max-w-md">
            <h1 class="text-4xl font-YesevaOne mb-6">
              Willkommen bei <span class="text-yellow-300">Ordy</span>
            </h1>
            <p class="text-xl mb-8 leading-relaxed">
              Die intelligenteste Koch-App für Familien. Plane deine Mahlzeiten, erstelle Einkaufslisten und entdecke neue Rezepte – alles an einem Ort.
            </p>

            <!-- Features -->
            <div class="space-y-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <span>KI-gestützte Menüplanung</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <span>Automatische Einkaufslisten</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-4">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <span>Personalisierte Rezeptvorschläge</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full"></div>
        <div class="absolute bottom-20 right-32 w-20 h-20 bg-white/10 rounded-full"></div>
        <div class="absolute top-1/2 right-10 w-16 h-16 bg-white/10 rounded-full"></div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="w-full lg:w-1/2 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <!-- Header -->
          <div class="text-center">
            <h2 class="text-3xl font-YesevaOne text-gray-900 mb-2">
              Anmelden oder Registrieren
            </h2>
            <p class="text-gray-600">
              Starte deine kulinarische Reise mit Ordy
            </p>
          </div>

          <!-- Login Form Container -->
          <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            <div id="gtap" class="space-y-4"></div>
          </div>

          <!-- Benefits -->
          <div class="text-center space-y-4">
            <p class="text-sm text-gray-500">
              Kostenlos starten • Keine Kreditkarte erforderlich
            </p>

            <div class="grid grid-cols-3 gap-4 text-center">
              <div class="p-4 bg-white rounded-lg shadow-sm">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-xs font-medium text-gray-900">Nachhaltig</p>
                <p class="text-xs text-gray-500">Weniger Verschwendung</p>
              </div>

              <div class="p-4 bg-white rounded-lg shadow-sm">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <p class="text-xs font-medium text-gray-900">Familienfreundlich</p>
                <p class="text-xs text-gray-500">Für alle Altersgruppen</p>
              </div>

              <div class="p-4 bg-white rounded-lg shadow-sm">
                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-xs font-medium text-gray-900">KI-gestützt</p>
                <p class="text-xs text-gray-500">Intelligente Vorschläge</p>
              </div>
            </div>
          </div>

          <!-- Footer Links -->
          <div class="text-center text-sm text-gray-500 space-x-4">
            <router-link to="/legal/privacy-policy" class="hover:text-ordypurple-100 transition-colors">Datenschutz</router-link>
            <span>•</span>
            <router-link to="/legal/agb" class="hover:text-ordypurple-100 transition-colors">AGB</router-link>
            <span>•</span>
            <router-link to="/about" class="hover:text-ordypurple-100 transition-colors">Über uns</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* Custom styles for modern login page */
</style>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router'

  const router = useRouter();

  const { setNotification } = useNotification();

  import { StytchUIClient, Products } from '@stytch/vanilla-js'

  let stytchToken;
  if(import.meta.env.VITE_ENV === 'development'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_DEV
  }

  if(import.meta.env.VITE_ENV === 'preview'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_DEV
  }

  if(import.meta.env.VITE_ENV === 'production'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_PROD
  }

  //console.log("here we go")
  //console.log(stytchToken)

  const stytch = new StytchUIClient(stytchToken)

  const config = {
    "products": [
      "oauth",
    ],
    "oauthOptions": {
      "providers": [
        { 
          type: 'apple' 
        },
        {
          type: 'google',
        },
        {
          type: 'microsoft',
        }
      ],
    },
  };

  const styles = {
    "container": {
      "backgroundColor": "#FFFFFF",
      "borderColor": "transparent",
      "borderRadius": "16px",
      "width": "100%",
      "padding": "0px"
    },
    "colors": {
      "primary": "#8B5CF6",
      "secondary": "#6B7280",
      "success": "#10B981",
      "error": "#EF4444"
    },
    "buttons": {
      "primary": {
        "backgroundColor": "#8B5CF6",
        "textColor": "#FFFFFF",
        "borderColor": "#8B5CF6",
        "borderRadius": "12px",
        "height": "48px",
        "fontSize": "16px",
        "fontWeight": "600"
      },
      "secondary": {
        "backgroundColor": "#FFFFFF",
        "textColor": "#374151",
        "borderColor": "#D1D5DB",
        "borderRadius": "12px",
        "height": "48px",
        "fontSize": "16px",
        "fontWeight": "500"
      }
    },
    "fontFamily": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    "hideHeaderText": true,
    "logo": {
      "logoImageUrl": ""
    },
    "inputs": {
      "backgroundColor": "#F9FAFB",
      "borderColor": "#E5E7EB",
      "borderRadius": "12px",
      "textColor": "#111827",
      "placeholderColor": "#9CA3AF",
      "height": "48px",
      "fontSize": "16px"
    }
  }


onMounted(() => {
  stytch.mountLogin({
    elementId: '#gtap',
    styles,
    config
  })
})
  /*
  const setupStytch = async() => {

    const Stytch = await loadStytch()

    let stytch = Stytch("public-token-test-a873928f-1dae-4c29-8573-68693bce5a94").mount({
      elementId: '#gtap',
      loginOrSignupView: {
        products: ['oauth'],
        oAuthOptions: {
          providers: [{ type: 'google' }],
        }
      },
      style: {
        width: 'calc(100% - 20px)'
      }
    })
  }

  setupStytch()
  */


  /*

  // login.jsx
import React from 'react';
import { StytchLogin, Products } from '@stytch/react';
const Login = () => {
              
  const config = {
  "products": [
    "oauth",
    "emailMagicLinks",
    "passwords"
  ],
  "oauthOptions": {
    "providers": [
      {
        "type": "google"
      },
      {
        "type": "facebook"
      }
    ],
    "loginRedirectURL": "https://www.authkit.dev",
    "signupRedirectURL": "https://www.authkit.dev"
  },
  "emailMagicLinksOptions": {
    "loginRedirectURL": "https://www.authkit.dev",
    "loginExpirationMinutes": 30,
    "signupRedirectURL": "https://www.authkit.dev",
    "signupExpirationMinutes": 30
  },
  "passwordOptions": {
    "loginRedirectURL": "https://www.authkit.dev",
    "resetPasswordRedirectURL": "https://www.authkit.dev"
  }
};
  const styles = {
  "container": {
    "backgroundColor": "#FFFFFF",
    "borderColor": "#000000",
    "borderRadius": "8px",
    "width": "400px"
  },
  "colors": {
    "primary": "#000000",
    "secondary": "#000000",
    "success": "#0C5A56",
    "error": "#8B1214"
  },
  "buttons": {
    "primary": {
      "backgroundColor": "#000000",
      "textColor": "#FFFFFF",
      "borderColor": "#19303D",
      "borderRadius": "4px"
    },
    "secondary": {
      "backgroundColor": "#FFFFFF",
      "textColor": "#000000",
      "borderColor": "#000000",
      "borderRadius": "4px"
    }
  },
  "fontFamily": "Tahoma",
  "hideHeaderText": false,
  "logo": {
    "logoImageUrl": ""
  }
}
                            
  return <StytchLogin config={config} styles={styles} />;
}
              
export default Login;

  */
  
</script>