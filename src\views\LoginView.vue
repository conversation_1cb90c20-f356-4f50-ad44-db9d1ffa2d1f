<template>
  <!-- Column Builder-->
  <div class="md:flex md:flex-row md:min-h-screen px-6">
  
    <!-- Middle Container -->
    <div class="md:w-3/4 md:pr-12 font-OpenSans md:flex md:gap-4 md:mt-32 md:flex-row flex flex-col">

      <div class="md:w-1/3 md:order-1 order-3">
        <transition name="slideTopDown" appear>
          <img class="mx-auto mt-12" src="./../assets/img/login_img1.png" />
        </transition>
        <transition name="slideDownTopNoBounce" appear>
          <div class="mx-auto md:mx-auto md:w-4/6 w-full">
            <h1 class="text-base md:mt-32 mt-8">Menüplanung einfach gemacht</h1>
            <p class="mt-2">
              <PERSON><PERSON> Woche aufs neue planen was und wie man Kochen sollte. Am Dienstag muss noch vorgekocht werden für den Mittwoch, aber am Mittwochabend isst man auswärts. Alles kein Problem und einfach zu planen...
            </p>
          </div>
        </transition>
      </div>

      <!-- UserProfile -->
      <div class="md:w-1/2 md:position md:order-2 order-1">
        <transition name="slideTopDownNoBounce" appear>
          <div class="container w-full md:w-10/12 md:mt-72 mt-16 h-auto my-auto box-shadow mx-auto rounded-lg bg-white border-2 border-black">
            <h1 class="pt-12 pb-4 pl-5 w-full text-lg">Login oder Registrieren</h1>
            <div id="gtap" class="pt-4 pb-10"></div>
          </div>
        </transition>
      </div>
      <!-- UserProfile -->

      <div class="md:w-1/3 md:order-3 order-2">
        
        <transition name="slideTopDownNoBounce" appear>
            <div class="md:mx-auto md:w-4/6 w-full mx-auto mt-16">
              <h1 class="text-base">AI gestützte Beratung, gratis</h1>
              <p class="md:mt-2">
                Die Plattform unterstützt ein gesundes Essverhalten mit praktischen Tipps und Ergänzungen im Menüplan. Nutze das Gratisangebot direkt über ordy.ch.
              </p>
            </div>
        </transition>
        <transition name="slideDownTop" appear>
          <img class="md:mt-32 mt-4 mx-auto" src="./../assets/img/login_img2.png" />
        </transition>
      </div>

    </div>


    <!-- Right Container -->
    <div class="w-1/4 p-10 mt-12">
      
    </div>
    <!-- Right Container -->

  </div>
  
</template>
<style>
.box-shadow {
  box-shadow: -12px 12px 0px 0px rgba(217,169,234,100);
}


.slideDownTop-enter { 
  opacity: 0;
}
.slideTopDown-enter { 
  opacity: 0;
}

.slideDownTop-enter-active {
  animation: slide-in-down-top 0.9s infinite forwards;
  transition: opacity .5s;
}

.slideDownTopNoBounce-enter-active {
  animation: slide-in-down-top-no-bounce 0.7s infinite forwards;
  transition: opacity .5s;
}

.slideTopDownNoBounce-enter-active {
  animation: slide-in-top-down-no-bounce 0.7s infinite forwards;
  transition: opacity .5s;
}

.slideTopDown-enter-active {
  animation: slide-in-top-down 0.5s infinite forwards;
  transition: opacity .5s;
}

@keyframes slide-in-top-down-no-bounce {
  from {
    transform: translateY(-20px);
  }
  to {
    transform: translateY(0px);
  }
}

@keyframes slide-in-down-top-no-bounce {
  from {
    transform: translateY(20px);
  }
  to {
    transform: translateY(0px);
  }
}

@keyframes slide-in-top-down {
  from {
    transform: translateY(-20px);
  }
  to {
    transform: translateY(0);
  }
  25% { transform: scale(1.09, 1.1); }
  50% { transform: scale(1.1, 1.09); }
  75% { transform: scale(1, 1.05); }
  9% { transform: scale(1, 1.00); }
}

@keyframes slide-in-down-top {
  from {
    transform: translateY(20px)
  }
  to {
    transform: translateY(0);
  }
  25% { transform: scale(0.9, 1.1); }
  50% { transform: scale(1.1, 0.9); }
  75% { transform: scale(0.95, 1.05); }
}


</style>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import useNotification from '../../modules/notificationInformation';
import { useRouter } from 'vue-router'

  const router = useRouter();

  const { setNotification } = useNotification();

  import { StytchUIClient, Products } from '@stytch/vanilla-js'

  let stytchToken;
  if(import.meta.env.VITE_ENV === 'development'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_DEV
  }

  if(import.meta.env.VITE_ENV === 'preview'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_DEV
  }

  if(import.meta.env.VITE_ENV === 'production'){
    stytchToken = import.meta.env.VITE_STYTCH_TOKEN_PROD
  }

  //console.log("here we go")
  //console.log(stytchToken)

  const stytch = new StytchUIClient(stytchToken)

  const config = {
    "products": [
      "oauth",
    ],
    "oauthOptions": {
      "providers": [
        { 
          type: 'apple' 
        },
        {
          type: 'google',
        },
        {
          type: 'microsoft',
        }
      ],
    },
  };

  const styles = {
  "container": {
    "backgroundColor": "#FFFFFF",
    "borderColor": "#FFFFFF",
    "borderRadius": "8px",
    "width": "100%"
  },
  "colors": {
    "primary": "#000000",
    "secondary": "#5C727D",
    "success": "#0C5A56",
    "error": "#8B1214"
  },
  "buttons": {
    "primary": {
      "backgroundColor": "#000000",
      "textColor": "#FFFFFF",
      "borderColor": "#19303D",
      "borderRadius": "4px",
      "height": "100px"
    },
    "secondary": {
      "backgroundColor": "#FFFFFF",
      "textColor": "#000000",
      "borderColor": "#19303D",
      "borderRadius": "4px"
    }
  },
  "fontFamily": "Roboto",
  "hideHeaderText": true,
  "logo": {
    "logoImageUrl": ""
  }
}


onMounted(() => {
  stytch.mountLogin({
    elementId: '#gtap',
    styles,
    config
  })
})
  /*
  const setupStytch = async() => {

    const Stytch = await loadStytch()

    let stytch = Stytch("public-token-test-a873928f-1dae-4c29-8573-68693bce5a94").mount({
      elementId: '#gtap',
      loginOrSignupView: {
        products: ['oauth'],
        oAuthOptions: {
          providers: [{ type: 'google' }],
        }
      },
      style: {
        width: 'calc(100% - 20px)'
      }
    })
  }

  setupStytch()
  */


  /*

  // login.jsx
import React from 'react';
import { StytchLogin, Products } from '@stytch/react';
const Login = () => {
              
  const config = {
  "products": [
    "oauth",
    "emailMagicLinks",
    "passwords"
  ],
  "oauthOptions": {
    "providers": [
      {
        "type": "google"
      },
      {
        "type": "facebook"
      }
    ],
    "loginRedirectURL": "https://www.authkit.dev",
    "signupRedirectURL": "https://www.authkit.dev"
  },
  "emailMagicLinksOptions": {
    "loginRedirectURL": "https://www.authkit.dev",
    "loginExpirationMinutes": 30,
    "signupRedirectURL": "https://www.authkit.dev",
    "signupExpirationMinutes": 30
  },
  "passwordOptions": {
    "loginRedirectURL": "https://www.authkit.dev",
    "resetPasswordRedirectURL": "https://www.authkit.dev"
  }
};
  const styles = {
  "container": {
    "backgroundColor": "#FFFFFF",
    "borderColor": "#000000",
    "borderRadius": "8px",
    "width": "400px"
  },
  "colors": {
    "primary": "#000000",
    "secondary": "#000000",
    "success": "#0C5A56",
    "error": "#8B1214"
  },
  "buttons": {
    "primary": {
      "backgroundColor": "#000000",
      "textColor": "#FFFFFF",
      "borderColor": "#19303D",
      "borderRadius": "4px"
    },
    "secondary": {
      "backgroundColor": "#FFFFFF",
      "textColor": "#000000",
      "borderColor": "#000000",
      "borderRadius": "4px"
    }
  },
  "fontFamily": "Tahoma",
  "hideHeaderText": false,
  "logo": {
    "logoImageUrl": ""
  }
}
                            
  return <StytchLogin config={config} styles={styles} />;
}
              
export default Login;

  */
  
</script>