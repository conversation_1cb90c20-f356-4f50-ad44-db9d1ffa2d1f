/**
 * Einfaches Script zum Ausführen des Legacy-Tests
 */

require('dotenv').config();
const { createLegacyTestData } = require('./testLegacyMigration');

async function runTest() {
    console.log('🧪 Starting Legacy Migration Test...');
    
    try {
        await createLegacyTestData();
        console.log('\n🎉 Test setup complete!');
        console.log('\nNow test the migration by opening the URLs shown above.');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
    
    process.exit(0);
}

runTest();
