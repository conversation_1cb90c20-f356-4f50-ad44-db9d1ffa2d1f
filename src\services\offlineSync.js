/**
 * Offline-Synchronisierungsservice für Ordy
 *
 * Dieser Service ermöglicht das Speichern von Änderungen, wenn die App offline ist,
 * und synchronisiert diese, sobald die Verbindung wiederhergestellt wird.
 */

import { useHelperStore } from '../../utils/helper';
import { useNetworkMonitor } from './networkMonitor';

// Konstanten für lokale Speicherung
const OFFLINE_ACTIONS_KEY = 'ordy_offline_actions';
const LAST_SYNC_TIMESTAMP_KEY = 'ordy_last_sync_timestamp';
const SYNC_PENDING_KEY = 'ordy_sync_pending';
const LOCAL_STATE_SNAPSHOT_KEY = 'ordy_local_state_snapshot';

// Singleton-Instanz
let instance = null;

/**
 * OfflineSyncManager-Klasse für die Verwaltung von Offline-Aktionen
 */
class OfflineSyncManager {
  constructor() {
    if (instance) {
      return instance;
    }

    this.helper = useHelperStore();
    this.networkMonitor = useNetworkMonitor();
    this.syncCallbacks = [];
    this.initialized = false;
    this.syncInProgress = false;

    // Singleton-Instanz speichern
    instance = this;
  }

  /**
   * Initialisiert den OfflineSyncManager
   */
  initialize() {
    if (this.initialized) {
      return;
    }

    this.helper.devConsole('OfflineSyncManager wird initialisiert');

    // Listener für Netzwerkwiederverbindung hinzufügen
    this.removeNetworkListener = this.networkMonitor.addReconnectListener(() => {
      this.helper.devConsole('Netzwerkverbindung wiederhergestellt. Prüfe auf ausstehende Synchronisierungen...');
      this.checkAndSync();
    });

    // Regelmäßige Überprüfung auf ausstehende Synchronisierungen - weniger häufig
    this.syncCheckInterval = setInterval(() => {
      if (this.networkMonitor.getIsOnline() && this.hasPendingSync()) {
        this.helper.devConsole('Regelmäßige Überprüfung: Ausstehende Synchronisierungen gefunden');
        this.checkAndSync();
      }
    }, 60000); // Alle 60 Sekunden prüfen (weniger häufig)

    this.initialized = true;
  }

  /**
   * Speichert eine Aktion für die spätere Synchronisierung
   * @param {Object} action - Die zu speichernde Aktion
   * @param {string} action.type - Der Typ der Aktion (z.B. 'updateItemPurchasedStatus')
   * @param {Object} action.payload - Die Daten der Aktion
   * @param {string} action.listId - Die ID der Einkaufsliste
   * @returns {boolean} - true, wenn die Aktion erfolgreich gespeichert wurde
   */
  saveAction(action) {
    try {
      // Bestehende Aktionen laden
      const existingActions = this.getActions();

      // Neue Aktion mit Zeitstempel hinzufügen
      const actionWithTimestamp = {
        ...action,
        timestamp: Date.now(),
        synced: false
      };

      // Aktion hinzufügen und speichern
      existingActions.push(actionWithTimestamp);
      localStorage.setItem(OFFLINE_ACTIONS_KEY, JSON.stringify(existingActions));

      // Markieren, dass eine Synchronisierung aussteht
      this.setPendingSync(true);

      this.helper.devConsole(`Offline-Aktion gespeichert: ${action.type}`);
      return true;
    } catch (error) {
      console.error('Fehler beim Speichern der Offline-Aktion:', error);
      this.helper.devConsole(`Fehler beim Speichern der Offline-Aktion: ${error.message}`);
      return false;
    }
  }

  /**
   * Lädt alle gespeicherten Offline-Aktionen
   * @returns {Array} - Liste der gespeicherten Aktionen
   */
  getActions() {
    try {
      const actionsJson = localStorage.getItem(OFFLINE_ACTIONS_KEY);
      return actionsJson ? JSON.parse(actionsJson) : [];
    } catch (error) {
      console.error('Fehler beim Laden der Offline-Aktionen:', error);
      return [];
    }
  }

  /**
   * Markiert eine Aktion als synchronisiert
   * @param {number} index - Der Index der Aktion in der Liste
   */
  markActionAsSynced(index) {
    try {
      const actions = this.getActions();
      if (actions[index]) {
        actions[index].synced = true;
        localStorage.setItem(OFFLINE_ACTIONS_KEY, JSON.stringify(actions));
      }
    } catch (error) {
      console.error('Fehler beim Markieren der Aktion als synchronisiert:', error);
    }
  }

  /**
   * Entfernt alle synchronisierten Aktionen
   */
  cleanupSyncedActions() {
    try {
      const actions = this.getActions();
      const pendingActions = actions.filter(action => !action.synced);
      localStorage.setItem(OFFLINE_ACTIONS_KEY, JSON.stringify(pendingActions));

      // Wenn keine ausstehenden Aktionen mehr vorhanden sind, Synchronisierung als abgeschlossen markieren
      if (pendingActions.length === 0) {
        this.setPendingSync(false);
      }
    } catch (error) {
      console.error('Fehler beim Bereinigen der synchronisierten Aktionen:', error);
    }
  }

  /**
   * Speichert den Zeitstempel der letzten Synchronisierung
   */
  updateLastSyncTimestamp() {
    localStorage.setItem(LAST_SYNC_TIMESTAMP_KEY, Date.now().toString());
  }

  /**
   * Gibt den Zeitstempel der letzten Synchronisierung zurück
   * @returns {number} - Zeitstempel der letzten Synchronisierung oder 0
   */
  getLastSyncTimestamp() {
    const timestamp = localStorage.getItem(LAST_SYNC_TIMESTAMP_KEY);
    return timestamp ? parseInt(timestamp, 10) : 0;
  }

  /**
   * Prüft, ob eine Synchronisierung nötig ist
   * @param {number} threshold - Schwellenwert in Millisekunden (Standard: 5 Minuten)
   * @returns {boolean} - true, wenn eine Synchronisierung nötig ist
   */
  isSyncNeeded(threshold = 5 * 60 * 1000) {
    const lastSync = this.getLastSyncTimestamp();
    const now = Date.now();
    return now - lastSync > threshold || this.getActions().some(action => !action.synced);
  }

  /**
   * Setzt den Status der ausstehenden Synchronisierung
   * @param {boolean} isPending - true, wenn eine Synchronisierung aussteht
   */
  setPendingSync(isPending) {
    localStorage.setItem(SYNC_PENDING_KEY, isPending ? 'true' : 'false');
  }

  /**
   * Prüft, ob eine Synchronisierung aussteht
   * @returns {boolean} - true, wenn eine Synchronisierung aussteht
   */
  hasPendingSync() {
    return localStorage.getItem(SYNC_PENDING_KEY) === 'true' || this.getActions().some(action => !action.synced);
  }

  /**
   * Registriert einen Callback für die Synchronisierung
   * @param {Function} callback - Funktion, die aufgerufen wird, wenn eine Synchronisierung durchgeführt werden soll
   * @returns {Function} - Funktion zum Entfernen des Callbacks
   */
  registerSyncCallback(callback) {
    this.syncCallbacks.push(callback);

    return () => {
      this.syncCallbacks = this.syncCallbacks.filter(cb => cb !== callback);
    };
  }

  /**
   * Prüft, ob eine Synchronisierung nötig ist und führt sie durch
   */
  async checkAndSync() {
    if (this.syncInProgress || !this.networkMonitor.getIsOnline()) {
      return;
    }

    this.syncInProgress = true;

    try {
      if (this.hasPendingSync() || this.isSyncNeeded()) {
        this.helper.devConsole('Starte Synchronisierung von Offline-Aktionen...');

        // Alle registrierten Callbacks aufrufen
        for (const callback of this.syncCallbacks) {
          try {
            await callback();
          } catch (error) {
            console.error('Fehler beim Ausführen des Sync-Callbacks:', error);
          }
        }

        this.updateLastSyncTimestamp();
      }
    } catch (error) {
      console.error('Fehler bei der Synchronisierung:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Speichert einen Snapshot des aktuellen lokalen Zustands
   * @param {Array} items - Aktuelle Items
   * @param {string} listId - Listen-ID
   */
  saveLocalStateSnapshot(items, listId) {
    try {
      const snapshot = {
        timestamp: Date.now(),
        listId: listId,
        items: JSON.parse(JSON.stringify(items)),
        offlineActions: this.getActions()
      };

      localStorage.setItem(LOCAL_STATE_SNAPSHOT_KEY, JSON.stringify(snapshot));
      this.helper.devConsole('Lokaler Zustand-Snapshot gespeichert');
    } catch (error) {
      console.error('Fehler beim Speichern des lokalen Zustand-Snapshots:', error);
    }
  }

  /**
   * Lädt den gespeicherten lokalen Zustand-Snapshot
   * @returns {Object|null} - Snapshot oder null, wenn nicht vorhanden
   */
  getLocalStateSnapshot() {
    try {
      const snapshotJson = localStorage.getItem(LOCAL_STATE_SNAPSHOT_KEY);
      return snapshotJson ? JSON.parse(snapshotJson) : null;
    } catch (error) {
      console.error('Fehler beim Laden des lokalen Zustand-Snapshots:', error);
      return null;
    }
  }

  /**
   * Löscht den gespeicherten lokalen Zustand-Snapshot
   */
  clearLocalStateSnapshot() {
    try {
      localStorage.removeItem(LOCAL_STATE_SNAPSHOT_KEY);
      this.helper.devConsole('Lokaler Zustand-Snapshot gelöscht');
    } catch (error) {
      console.error('Fehler beim Löschen des lokalen Zustand-Snapshots:', error);
    }
  }

  /**
   * Bereinigt den OfflineSyncManager
   */
  cleanup() {
    if (this.removeNetworkListener) {
      this.removeNetworkListener();
    }

    if (this.syncCheckInterval) {
      clearInterval(this.syncCheckInterval);
    }

    this.syncCallbacks = [];
    this.initialized = false;
  }
}

// Exportiere Funktionen, die den OfflineSyncManager verwenden

/**
 * Gibt eine Instanz des OfflineSyncManagers zurück
 * @returns {OfflineSyncManager} - OfflineSyncManager-Instanz
 */
export function useOfflineSyncManager() {
  const manager = new OfflineSyncManager();
  if (!manager.initialized) {
    manager.initialize();
  }
  return manager;
}

/**
 * Speichert eine Aktion für die spätere Synchronisierung
 * @param {Object} action - Die zu speichernde Aktion
 * @returns {boolean} - true, wenn die Aktion erfolgreich gespeichert wurde
 */
export function saveOfflineAction(action) {
  return useOfflineSyncManager().saveAction(action);
}

/**
 * Lädt alle gespeicherten Offline-Aktionen
 * @returns {Array} - Liste der gespeicherten Aktionen
 */
export function getOfflineActions() {
  return useOfflineSyncManager().getActions();
}

/**
 * Markiert eine Aktion als synchronisiert
 * @param {number} index - Der Index der Aktion in der Liste
 */
export function markActionAsSynced(index) {
  useOfflineSyncManager().markActionAsSynced(index);
}

/**
 * Entfernt alle synchronisierten Aktionen
 */
export function cleanupSyncedActions() {
  useOfflineSyncManager().cleanupSyncedActions();
}

/**
 * Speichert den Zeitstempel der letzten Synchronisierung
 */
export function updateLastSyncTimestamp() {
  useOfflineSyncManager().updateLastSyncTimestamp();
}

/**
 * Gibt den Zeitstempel der letzten Synchronisierung zurück
 * @returns {number} - Zeitstempel der letzten Synchronisierung oder 0
 */
export function getLastSyncTimestamp() {
  return useOfflineSyncManager().getLastSyncTimestamp();
}

/**
 * Prüft, ob eine Synchronisierung nötig ist
 * @param {number} threshold - Schwellenwert in Millisekunden (Standard: 5 Minuten)
 * @returns {boolean} - true, wenn eine Synchronisierung nötig ist
 */
export function isSyncNeeded(threshold = 5 * 60 * 1000) {
  return useOfflineSyncManager().isSyncNeeded(threshold);
}

/**
 * Speichert einen Snapshot des aktuellen lokalen Zustands
 * @param {Array} items - Aktuelle Items
 * @param {string} listId - Listen-ID
 */
export function saveLocalStateSnapshot(items, listId) {
  useOfflineSyncManager().saveLocalStateSnapshot(items, listId);
}

/**
 * Lädt den gespeicherten lokalen Zustand-Snapshot
 * @returns {Object|null} - Snapshot oder null, wenn nicht vorhanden
 */
export function getLocalStateSnapshot() {
  return useOfflineSyncManager().getLocalStateSnapshot();
}

/**
 * Löscht den gespeicherten lokalen Zustand-Snapshot
 */
export function clearLocalStateSnapshot() {
  useOfflineSyncManager().clearLocalStateSnapshot();
}
