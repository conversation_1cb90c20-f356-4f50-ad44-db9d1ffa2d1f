// <PERSON>ript to get Pinterest board ID
const axios = require('axios');
require('dotenv').config({ path: './config.env' });

// Get the access token from environment variables
const ACCESS_TOKEN = process.env.PINTEREST_ACCESS_TOKEN;

if (!ACCESS_TOKEN) {
  console.error('Error: PINTEREST_ACCESS_TOKEN is not defined in config.env');
  process.exit(1);
}

// Function to get user's boards
async function getUserBoards() {
  try {
    console.log('Fetching Pinterest boards...');
    
    // Get the authenticated user's information first
    const userResponse = await axios.get(
      'https://api.pinterest.com/v5/user_account',
      {
        headers: {
          'Authorization': `Bearer ${ACCESS_TOKEN}`,
          'Content-Type': 'application/json',
        }
      }
    );
    
    const username = userResponse.data.username;
    console.log(`Found Pinterest user: ${username}`);
    
    // Now get the user's boards
    const boardsResponse = await axios.get(
      'https://api.pinterest.com/v5/boards',
      {
        headers: {
          'Authorization': `Bearer ${ACCESS_TOKEN}`,
          'Content-Type': 'application/json',
        }
      }
    );
    
    console.log('\nYour Pinterest Boards:');
    console.log('======================');
    
    if (boardsResponse.data.items && boardsResponse.data.items.length > 0) {
      boardsResponse.data.items.forEach(board => {
        console.log(`Name: ${board.name}`);
        console.log(`URL: ${board.url}`);
        console.log(`ID: ${board.id}`);
        console.log('----------------------');
      });
      
      // Look for the specific board
      const targetBoardName = 'leckere-rezepte-in-60-minuten';
      const targetBoard = boardsResponse.data.items.find(board => 
        board.url.includes(targetBoardName) || 
        board.name.toLowerCase().includes(targetBoardName.replace(/-/g, ' '))
      );
      
      if (targetBoard) {
        console.log('\nFound your target board!');
        console.log(`Board Name: ${targetBoard.name}`);
        console.log(`Board ID: ${targetBoard.id}`);
        console.log('\nAdd this to your config.env file:');
        console.log(`PINTEREST_BOARD_ID=${targetBoard.id}`);
      } else {
        console.log('\nCould not find a board matching "leckere-rezepte-in-60-minuten"');
        console.log('Please choose one of the boards listed above and add its ID to your config.env file.');
      }
    } else {
      console.log('No boards found for this account.');
    }
  } catch (error) {
    console.error('Error fetching Pinterest boards:');
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error message:', error.message);
    }
  }
}

// Run the function
getUserBoards();
