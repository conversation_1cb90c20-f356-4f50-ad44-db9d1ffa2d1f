const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const helper = require('../utils/helper');
const axios = require('axios');

// Facebook OAuth Configuration
const getFacebookConfig = () => {
  const NODE_ENV = process.env.NODE_ENV || 'development';
  let redirectUri;

  if (NODE_ENV === 'development') {
    redirectUri = 'http://localhost:8080/auth/facebook/callback';
  } else if (NODE_ENV === 'preview') {
    redirectUri = 'https://ordy-tst-d8cfc0bzchbqd9ha.switzerlandnorth-01.azurewebsites.net/auth/facebook/callback';
  } else {
    // Production
    redirectUri = 'https://www.ordyapp.com/auth/facebook/callback';
  }

  return {
    clientId: process.env.FACEBOOK_APP_ID,
    clientSecret: process.env.FACEBOOK_APP_SECRET,
    redirectUri: redirectUri,
    scopes: 'pages_manage_posts,pages_read_engagement,instagram_basic,instagram_content_publish,instagram_manage_insights'
  };
};

// @desc    Start Facebook OAuth flow
// @route   GET /api/v1/facebook/oauth/start
// @access  Public (for now, add auth later)
exports.startOAuth = catchAsync(async (req, res, next) => {
  helper.devConsole('[facebookOAuth.startOAuth] Starting Facebook OAuth flow...');

  const config = getFacebookConfig();

  if (!config.clientId || !config.clientSecret) {
    return next(new AppError('Facebook OAuth credentials not configured', 400));
  }

  // Generate state for CSRF protection
  const state = `facebook_oauth_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  
  // Store state in session or database for verification (simplified for now)
  // In production, you should store this securely
  
  const authUrl = `https://www.facebook.com/v22.0/dialog/oauth?` +
    `response_type=code` +
    `&client_id=${config.clientId}` +
    `&redirect_uri=${encodeURIComponent(config.redirectUri)}` +
    `&scope=${encodeURIComponent(config.scopes)}` +
    `&state=${state}`;

  helper.devConsole('[facebookOAuth.startOAuth] Generated auth URL:', authUrl);

  res.status(200).json({
    status: 'success',
    message: 'Facebook OAuth flow initiated',
    data: {
      authUrl: authUrl,
      state: state,
      redirectUri: config.redirectUri,
      scopes: config.scopes
    }
  });
});

// @desc    Handle Facebook OAuth callback
// @route   GET /api/v1/facebook/oauth/callback
// @access  Public (but should validate state)
exports.handleCallback = catchAsync(async (req, res, next) => {
  helper.devConsole('[facebookOAuth.handleCallback] Handling Facebook OAuth callback');

  const { code, state, error } = req.query;

  if (error) {
    helper.devConsole('[facebookOAuth.handleCallback] OAuth error:', error);
    return next(new AppError(`Facebook OAuth error: ${error}`, 400));
  }

  if (!code) {
    return next(new AppError('No authorization code received', 400));
  }

  const config = getFacebookConfig();

  if (!config.clientId || !config.clientSecret) {
    return next(new AppError('Facebook OAuth credentials not configured', 400));
  }

  try {
    // Exchange authorization code for access token
    const tokenUrl = `https://graph.facebook.com/v22.0/oauth/access_token`;
    const tokenParams = {
      client_id: config.clientId,
      client_secret: config.clientSecret,
      redirect_uri: config.redirectUri,
      code: code
    };

    helper.devConsole('[facebookOAuth.handleCallback] Exchanging code for token...');
    const tokenResponse = await axios.get(tokenUrl, { params: tokenParams });

    if (!tokenResponse.data || !tokenResponse.data.access_token) {
      helper.devConsole('[facebookOAuth.handleCallback] Invalid token response:', tokenResponse.data);
      return next(new AppError('Failed to obtain access token from Facebook', 400));
    }

    const accessToken = tokenResponse.data.access_token;
    helper.devConsole('[facebookOAuth.handleCallback] Access token obtained successfully');

    // Get user's pages (for Facebook posting)
    const pagesUrl = `https://graph.facebook.com/v22.0/me/accounts`;
    const pagesResponse = await axios.get(pagesUrl, {
      params: { access_token: accessToken }
    });

    let pageInfo = null;
    if (pagesResponse.data && pagesResponse.data.data && pagesResponse.data.data.length > 0) {
      // Use the first page (you might want to let user choose)
      const firstPage = pagesResponse.data.data[0];
      pageInfo = {
        pageId: firstPage.id,
        pageName: firstPage.name,
        pageAccessToken: firstPage.access_token
      };
      helper.devConsole('[facebookOAuth.handleCallback] Page info obtained:', pageInfo.pageName);
    }

    // Get Instagram Business Account (if connected)
    let instagramBusinessAccountId = null;
    if (pageInfo && pageInfo.pageId) {
      try {
        const instagramUrl = `https://graph.facebook.com/v22.0/${pageInfo.pageId}`;
        const instagramResponse = await axios.get(instagramUrl, {
          params: {
            fields: 'instagram_business_account',
            access_token: pageInfo.pageAccessToken
          }
        });

        if (instagramResponse.data && instagramResponse.data.instagram_business_account) {
          instagramBusinessAccountId = instagramResponse.data.instagram_business_account.id;
          helper.devConsole('[facebookOAuth.handleCallback] Instagram Business Account ID:', instagramBusinessAccountId);
        }
      } catch (instagramError) {
        helper.devConsole('[facebookOAuth.handleCallback] Could not get Instagram Business Account:', instagramError.message);
      }
    }

    // Save tokens to database
    const FacebookToken = require('../models/facebookTokenModel');
    const savedToken = await FacebookToken.saveTokens(
      pageInfo ? pageInfo.pageAccessToken : accessToken, // Use page token if available
      null, // Facebook doesn't provide refresh tokens in this flow
      config.scopes,
      pageInfo ? pageInfo.pageId : null,
      pageInfo ? pageInfo.pageName : null,
      instagramBusinessAccountId
    );

    helper.devConsole('[facebookOAuth.handleCallback] Tokens saved successfully');

    // Return success response
    res.status(200).send(`
      <html>
        <body>
          <h1>Facebook OAuth Successful!</h1>
          <p>Your Facebook account has been connected successfully.</p>
          <p><strong>Page:</strong> ${pageInfo ? pageInfo.pageName : 'No page found'}</p>
          <p><strong>Instagram Business Account:</strong> ${instagramBusinessAccountId ? 'Connected' : 'Not connected'}</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);

  } catch (error) {
    helper.devConsole('[facebookOAuth.handleCallback] Error during token exchange:', error.message);
    return next(new AppError(`Failed to complete Facebook OAuth: ${error.message}`, 500));
  }
});

// @desc    Get Facebook token status
// @route   GET /api/v1/facebook/oauth/status
// @access  Protected
exports.getTokenStatus = catchAsync(async (req, res, next) => {
  helper.devConsole('[facebookOAuth.getTokenStatus] Checking Facebook token status...');

  try {
    const FacebookToken = require('../models/facebookTokenModel');
    const token = await FacebookToken.getCurrentToken();

    if (!token) {
      return res.status(200).json({
        status: 'success',
        data: {
          hasToken: false,
          isActive: false,
          message: 'No Facebook token found'
        }
      });
    }

    // Test token validity by making a simple API call
    let isValid = false;
    let tokenInfo = null;

    try {
      const testResponse = await axios.get('https://graph.facebook.com/v22.0/me', {
        params: { access_token: token.accessToken }
      });
      
      if (testResponse.data && testResponse.data.id) {
        isValid = true;
        tokenInfo = testResponse.data;
      }
    } catch (testError) {
      helper.devConsole('[facebookOAuth.getTokenStatus] Token validation failed:', testError.message);
    }

    res.status(200).json({
      status: 'success',
      data: {
        hasToken: true,
        isActive: token.isActive && isValid,
        isExpired: token.isExpired(),
        isExpiringSoon: token.isExpiringSoon(),
        pageId: token.pageId,
        pageName: token.pageName,
        instagramBusinessAccountId: token.instagramBusinessAccountId,
        lastRefreshed: token.lastRefreshed,
        expiresAt: token.expiresAt,
        tokenInfo: tokenInfo
      }
    });

  } catch (error) {
    helper.devConsole('[facebookOAuth.getTokenStatus] Error:', error.message);
    return next(new AppError(`Failed to check Facebook token status: ${error.message}`, 500));
  }
});
