# FRONTEND_TASKS.md

## Übersicht

Aktuelle Entwicklungsaufgaben für das `ordy-at-vite5`-Frontend.

| ID   | Beschreibung                        | Status   | Zugehörige Anforderung ID | Erstellt am | Erledigt am |
| :--- | :---------------------------------- | :------- | :------------------------ | :---------- | :---------- |
| F-1  | Grundlegendes Vue/Vite/Pinia Setup | done     | -                         | 2023-02-01  | 2023-02-03  |
| F-2  | Stytch SDK Integration & Login-Form | doing    | Auth-1                    | 2023-10-20  |             |
| F-3  | Frontend-Dokumentation vervollständigen | todo     | -                         | 2024-07-27  |             |
|      |                                     |          |                           |             |             |

# FRONTEND_BUGS.md

## Übersicht

Aktuelle Fehler und Probleme im `ordy-at-vite5`-Frontend.

| ID   | Beschreibung                        | Status   | Entdeckt am | Behoben am |
| :--- | :---------------------------------- | :------- | :---------- | :--------- |
| B-1  | Timeout bei Hauptnavigation         | open     | 2024-08-31  |            |
| B-4  | Bestätigungsabfrage bei letztem Einkaufszettel-Item fehlt | done     | 2024-07-29  | 2024-07-30  |
|      |                                     |          |             |            |
|      |                                     |          |             |            |

## Details

### B-1: Timeout bei Hauptnavigation

**Status:** done
**Entdeckt am:** 2024-08-31

**Beschreibung:**
Beim Klick auf die Hauptnavigationselemente in der linken Seitenleiste ("Zettel", "Lager", "Generieren") tritt reproduzierbar ein Playwright `TimeoutError: locator.click: Timeout 5000ms exceeded` auf.

**Beobachtetes Verhalten:**
Obwohl der Timeout-Fehler auftritt, wird die Navigation zur Zielseite (z.B. `/zettel`, `/cookeasy`, `/wochenplan/upload`) in den meisten Fällen trotzdem korrekt durchgeführt. Beim ersten Klick auf "Zettel" wurde zusätzlich ein `Stale aria-ref` Fehler gemeldet.

**Mögliche Ursachen:**
- Timing-Probleme: Die Seite ändert ihren Zustand (z.B. durch asynchrones Laden von Daten oder Komponenten-Updates) genau während der Klick-Aktion von Playwright.
- Event-Handler: Möglicherweise blockieren oder verzögern Event-Handler die Navigation oder das Feedback an Playwright.
- Playwright-Konfiguration: Eventuell sind die Standard-Timeouts für die Testumgebung zu kurz eingestellt.

**Reproduktion:**
1. Einloggen in die Anwendung.
2. Auf eines der Navigations-Icons in der linken Leiste klicken (Zettel, Lager, Generieren).
3. Timeout-Fehler in Playwright beobachten.
4. Überprüfen, ob die URL trotzdem gewechselt hat.

**Erwartetes Verhalten:**
Die Navigation sollte ohne Timeout-Fehler erfolgen. 




### B-2: Rezepte im Kochbuch ändern
**Status:** done

**Entdeckt am:** 2025-5-8

**Beschreibung:**
Wenn ein Menü in `/kochbuch/menu/edit/68134c99ee2694e706380b87` bearbeitet wird und bspw die Personen Anzahl von 2 auf 3 erhöt wird, muss diese Anzahl Personen dann auch in `/kochbuch` in der Rezeptübersicht sichtbar werden. Allerdings steht wenn man nach dem edit das Rezept in die Rezeptübersicht verlässt immer noch 2 anstatt 3 bei den anzahl Personen. Das ist ein etwas schwieriger zu lösender Bug basierend auf der Architektur.

**Reproduktion:**
1. Ein Menü/Rezept öffnen
2. Bearbeiten (`/kochbuch/menu/edit/68134c99ee2694e706380b87`)
3. Anzahl Personen ändern
4. In das Menü/Rezept zurück gehen (`/kochbuch/menu/6818863e0d67ee15dd43e5a5`)
5. Anzahl Personen korrekt geändert
6. Zurück gehen in die Übersicht (`/kochbuch`)
7. Anzahl Personen nicht geändert

**Mögliche Ursachen:**
- isStandard wurde wahrscheinlich nicht korrigiert im menuModel, das korrekte menuchild muss verknüpft sein und die personCount oder numberOfPersons muss aktualisiert werden.
- Entweder wird das Store-Objekt (Menü) korrekt geändert (es ist nur 1 Wert numberOfPersons) oder wenn /kochbuch aufgerufen wird, wird ein reload aller Rezepte ausgelöst. Wenn dies der Fall ist, müssen die aktuell gesetzten Filter in `/kochbuch` beachtet werden.



### B-3: Rezepte teilen
**Status:** done

**Entdeckt am:** 2025-5-8
**Behoben am:** 2024-07-29

**Beschreibung:**
Wenn auf der Seite `/kochbuch/menu/68134c99ee2694e706380b87` bei "Link generieren" im Kapitel "Teilen" bei einem Rezept geklickt wird, muss der Link zum URL zwingend in die Zwischenablage kopiert werden. Zudem muss falls das Rezept noch nicht geteilt ist die Bestätigungsanfrage erscheinen: Dieses Rezept veröffentlichen? Bei Ja (Rezept veröffentlichen auf True setzen) und Link in die Zwischenablage. Bei Nein nichts machen. Existierende confirmation component dafür benutzen.



### B-4: Bestätigungsabfrage bei letztem Einkaufszettel-Item fehlt
**Status:** done
**Entdeckt am:** 2024-07-29
**Behoben am:** 2024-07-30

**Beschreibung:**
Wenn auf der Seite `/zettel` das letzte verbleibende Item auf der Einkaufsliste als erledigt markiert wird, wird keine Bestätigungsabfrage angezeigt, ob der gesamte Einkaufszettel abgeschlossen werden soll. Das Backend sendet ein Event `last_item_confirmation_needed`, welches nicht korrekt verarbeitet wird.

**Reproduktion:**
1. Einkaufszettel mit mehreren Items öffnen
2. Alle bis auf ein Item als erledigt markieren
3. Das letzte Item als erledigt markieren
4. Es erscheint keine Bestätigungsabfrage

**Erwartetes Verhalten:**
Bei Markierung des letzten Items sollte ein Dialog erscheinen mit der Frage, ob der Einkaufszettel abgeschlossen werden soll. Bei "JA" sollte das Item als erledigt markiert und die Liste abgeschlossen werden. Bei "NEIN" sollte das Item als nicht erledigt bleiben.

**Lösung:**
Wir haben den WebSocket-Handler aktualisiert, um sowohl auf das `REQUEST_FINAL_ITEM_CONFIRMATION` als auch auf das `last_item_confirmation_needed` Event zu reagieren. Die Bestätigungsabfrage wird nun korrekt angezeigt und leitet je nach Benutzerentscheidung entweder den API-Aufruf zum Abschließen der Liste ein oder setzt das Item zurück auf "nicht erledigt".

Zusätzlich haben wir die UI verbessert, um den Status der Liste (aktiv/inaktiv) anzuzeigen und die Möglichkeit hinzugefügt, eine neue Liste zu erstellen, wenn die aktuelle Liste abgeschlossen ist. Das `is_active`-Flag wird jetzt im Store gespeichert und bei allen API-Anfragen und WebSocket-Events aktualisiert.



### B-5: Offline aktualisierung nicht mehr möglich
**Status:** done
**Entdeckt am:** 2025-05-14
**Behoben am:** 
**Beschreibung:**
Wenn auf der Seite `/zettel` von einem Gerät über den Websocket items als is_purchased markiert werden und das zweite Gerät einen gesperrten Bildschirm hat oder die App nicht offen ist, kann es vorkommen, dass das zweite Gerät seinen Einkaufszettel NIE mehr aktualisiert wird. Auch eine aktualisierung zu erzwingen ist unmöglich (bspw mit schliessen der App oder Webseite). Die Items auf der Liste sind fortan nicht mehr synchron zwischen den verschiedenen Geräten.

**Reproduktion:**
1. Einkaufszettel mit mehreren Items öffnen
2. Gerät 2 bildschirm sperren
3. Gerät 1 einkaufsliste mit items aktualisieren
4. Gerät 2 bildschirm entsperren

**Erwartetes Verhalten:**
Die Einkaufsliste auf den Geräten ist nicht mehr synchron. Die Items wurden nicht auf Gerät 2 synchronisiert. Nur wenn beide Geräte ein aktiven Bildschrim haben werden die Items korrekt übertragen - das ist schlecht.

**Lösung:**
Bei jedem Aufruf der Seite muss eine Synchronisation neu ausgeführt werden.


### B-6: Automatisches ausloggen aus der App nach ein paar Tagen auf 1 Monat verlängern und auf bugs prüfen. Akutell wird nach 1 tag ausgeloggt.
**Status:** done
**Entdeckt am:** 2025-05-18
**Behoben am:** 
**Beschreibung:**



### B-7: Offline aktualisierung nicht mehr möglich
**Status:** open
**Entdeckt am:** 2025-05-23
**Behoben am:** 
**Beschreibung:**
Wenn auf der Seite `/zettel` von einem Gerät über den Websocket items als is_purchased markiert werden und das Gerät keine gute Internetverbindung hat, wird die Liste einfach nicht gar nicht mehr synchronisiert. Auch dann nicht, wenn das Gerät wieder im Internet ist. Dies führt zu asynchronen Listen und ist für die User sehr unschön.

**Reproduktion:**
1. Gerät 2 Einkaufszettel mit mehreren Items öffnen
2. Gerät 2 Internet verbindung wird schlecht
3. Gerät 2 einkaufsliste mit items aktualisieren
4. Gerät 1 Items werden nicht mehr aktualisiert

**Erwartetes Verhalten:**
Die Einkaufsliste auf den Geräten ist nicht mehr synchron wenn die Internetverbindung auf dem Smartphone schlecht wird. Die Items wurden nicht auf Gerät 1 synchronisiert. Nur wenn beide Geräte ein aktiven Bildschrim haben werden die Items korrekt übertragen - das ist schlecht.

**Lösung:**