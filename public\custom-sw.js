/**
 * Benutzerdefinierter Service Worker für Ordy PWA
 * 
 * Dieser Code wird in den generierten Service Worker eingefügt und
 * stellt sicher, dass Updates korrekt angewendet werden.
 */

// Auf SKIP_WAITING-Nachricht hören
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Beim Aktivieren alle Clients übernehmen
self.addEventListener('activate', (event) => {
  event.waitUntil(
    self.clients.claim()
  );
});
