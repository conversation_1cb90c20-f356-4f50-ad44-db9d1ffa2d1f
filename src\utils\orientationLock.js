/**
 * Robuste Orientierungssperre für mobile Geräte
 * Kombiniert verschiedene Ansätze für maximale Kompatibilität
 */

class OrientationLock {
  constructor() {
    this.isLocked = false;
    this.lockAttempts = 0;
    this.maxLockAttempts = 5;
    this.retryDelay = 1000; // 1 Sekunde

    this.init();
  }

  init() {
    // Sofort versuchen zu sperren
    this.lockToPortrait();

    // Event-Listener für verschiedene Orientierungsänderungen
    this.setupEventListeners();

    // Periodische Überprüfung (als Fallback)
    this.startPeriodicCheck();

    // CSS-Klasse für Landscape-Rotation verwalten
    this.manageCSSRotation();
  }

  setupEventListeners() {
    // Standard Screen Orientation API
    if (screen.orientation) {
      screen.orientation.addEventListener('change', () => {
        this.handleOrientationChange();
      });
    }

    // Fallback für ältere Browser
    window.addEventListener('orientationchange', () => {
      // Kurze Verzögerung, da orientationchange vor der tatsächlichen Änderung feuert
      setTimeout(() => {
        this.handleOrientationChange();
      }, 100);
    });

    // Resize-Event als zusätzlicher Fallback
    window.addEventListener('resize', () => {
      // Nur reagieren, wenn sich die Orientierung geändert hat
      setTimeout(() => {
        this.checkAndLockIfNeeded();
      }, 200);
    });

    // Visibility Change - erneut sperren, wenn App wieder sichtbar wird
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        setTimeout(() => {
          this.lockToPortrait();
        }, 500);
      }
    });

    // Page Focus - erneut sperren
    window.addEventListener('focus', () => {
      setTimeout(() => {
        this.lockToPortrait();
      }, 300);
    });
  }

  handleOrientationChange() {
    console.log('Orientierung geändert, versuche Portrait-Sperre...');

    // Kurze Verzögerung, um sicherzustellen, dass die Orientierung vollständig geändert wurde
    setTimeout(() => {
      this.lockToPortrait();
    }, 100);
  }

  checkAndLockIfNeeded() {
    const isLandscape = this.isCurrentlyLandscape();

    if (isLandscape && this.canUseLockAPI()) {
      console.log('Landscape-Modus erkannt, versuche zu sperren...');
      this.lockToPortrait();
    }
  }

  isCurrentlyLandscape() {
    // PRÄZISE Erkennung: Nur echten Landscape-Modus erkennen

    // 1. Screen Orientation API (zuverlässigste Methode)
    if (screen.orientation) {
      const angle = screen.orientation.angle;
      const isLandscapeAngle = angle === 90 || angle === 270;
      console.log(`Screen Orientation API: angle=${angle}, isLandscape=${isLandscapeAngle}`);
      return isLandscapeAngle;
    }

    // 2. Window Orientation (Fallback)
    if (typeof window.orientation !== 'undefined') {
      const isLandscapeAngle = Math.abs(window.orientation) === 90;
      console.log(`Window Orientation: angle=${window.orientation}, isLandscape=${isLandscapeAngle}`);
      return isLandscapeAngle;
    }

    // 3. Viewport-Dimensionen (letzter Fallback)
    const dimensionCheck = window.innerWidth > window.innerHeight;
    const isMobileLandscape = dimensionCheck && window.innerHeight <= 600;

    console.log(`Dimension Check: ${window.innerWidth}x${window.innerHeight}, isLandscape=${isMobileLandscape}`);

    return isMobileLandscape;
  }

  canUseLockAPI() {
    return screen.orientation && typeof screen.orientation.lock === 'function';
  }

  async lockToPortrait() {
    if (!this.canUseLockAPI()) {
      console.log('Screen Orientation Lock API nicht verfügbar');
      return false;
    }

    // Verhindere zu viele Versuche
    if (this.lockAttempts >= this.maxLockAttempts) {
      console.log('Maximale Anzahl von Lock-Versuchen erreicht');
      return false;
    }

    try {
      this.lockAttempts++;

      await screen.orientation.lock('portrait');

      console.log('Portrait-Orientierung erfolgreich gesperrt');
      this.isLocked = true;
      this.lockAttempts = 0; // Reset bei Erfolg

      return true;
    } catch (error) {
      console.warn(`Orientierungssperre fehlgeschlagen (Versuch ${this.lockAttempts}):`, error.message);

      // Bei bestimmten Fehlern erneut versuchen
      if (this.shouldRetry(error) && this.lockAttempts < this.maxLockAttempts) {
        setTimeout(() => {
          this.lockToPortrait();
        }, this.retryDelay);
      }

      return false;
    }
  }

  shouldRetry(error) {
    // Bestimmte Fehler rechtfertigen einen erneuten Versuch
    const retryableErrors = [
      'NotAllowedError', // Benutzer hat noch nicht interagiert
      'AbortError',      // Vorheriger Lock wurde abgebrochen
      'InvalidStateError' // Ungültiger Zustand
    ];

    return retryableErrors.some(errorType =>
      error.name === errorType || error.message.includes(errorType)
    );
  }

  startPeriodicCheck() {
    // Weniger aggressive periodische Überprüfung, da CSS-Rotation das Problem löst
    setInterval(() => {
      if (this.isCurrentlyLandscape() && this.canUseLockAPI()) {
        console.log('Periodische Überprüfung: Landscape erkannt, versuche zu sperren...');
        this.lockToPortrait();
      }
    }, 15000); // Alle 15 Sekunden statt 5 - weniger aggressiv
  }

  // Öffentliche Methode zum manuellen Sperren
  forceLock() {
    this.lockAttempts = 0; // Reset der Versuche
    return this.lockToPortrait();
  }

  // RADIKALER ANSATZ: Fixe Viewport-Breite für alle Landscape-Modi
  manageCSSRotation() {
    const updateRotation = () => {
      const isLandscape = this.isCurrentlyLandscape();

      if (isLandscape) {
        // Füge CSS-Klasse für Landscape-Rotation hinzu
        document.documentElement.classList.add('force-portrait-rotation');

        // WICHTIG: Setze IMMER eine fixe schmale Breite (wie iPhone SE)
        const FIXED_MOBILE_WIDTH = 375; // iPhone SE Portrait-Breite

        // Ändere das Viewport-Meta-Tag auf fixe schmale Breite
        this.setViewportMetaTag(FIXED_MOBILE_WIDTH);

        console.log(`Portrait-Simulation aktiviert:`);
        console.log(`  Real Landscape: ${window.innerWidth}x${window.innerHeight}`);
        console.log(`  Fixed Viewport: width=${FIXED_MOBILE_WIDTH}`);

      } else {
        // Entferne CSS-Klasse für Portrait-Modus
        document.documentElement.classList.remove('force-portrait-rotation');

        // Stelle ursprüngliches Viewport-Meta-Tag wieder her
        this.restoreViewportMetaTag();

        console.log('Portrait-Simulation deaktiviert');
      }
    };

    // Sofort ausführen
    updateRotation();

    // Bei Orientierungsänderungen aktualisieren
    const orientationHandler = () => {
      setTimeout(updateRotation, 100); // Kurze Verzögerung für Stabilität
    };

    if (screen.orientation) {
      screen.orientation.addEventListener('change', orientationHandler);
    }

    window.addEventListener('orientationchange', orientationHandler);
    window.addEventListener('resize', orientationHandler);
  }

  // Viewport-Meta-Tag dynamisch ändern
  setViewportMetaTag(width) {
    try {
      // Finde das bestehende Viewport-Meta-Tag
      let viewportMeta = document.querySelector('meta[name="viewport"]');

      // Speichere das ursprüngliche Meta-Tag
      if (!this.originalViewportMeta && viewportMeta) {
        this.originalViewportMeta = viewportMeta.getAttribute('content');
      }

      // Erstelle ein neues Meta-Tag, falls keines existiert
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.name = 'viewport';
        document.head.appendChild(viewportMeta);
        this.originalViewportMeta = 'width=device-width, initial-scale=1.0'; // Standard-Fallback
      }

      // Setze das neue Viewport mit der Portrait-Breite
      const newViewportContent = `width=${width}, initial-scale=1.0, user-scalable=no`;
      viewportMeta.setAttribute('content', newViewportContent);

      console.log(`Viewport Meta-Tag geändert: ${newViewportContent}`);

      // Trigger einen Reflow, damit die Änderung wirksam wird
      document.body.style.display = 'none';
      document.body.offsetHeight; // Trigger reflow
      document.body.style.display = '';

    } catch (error) {
      console.warn('Viewport-Meta-Tag-Änderung fehlgeschlagen:', error.message);
    }
  }

  // Stelle ursprüngliches Viewport-Meta-Tag wieder her
  restoreViewportMetaTag() {
    try {
      if (this.originalViewportMeta) {
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (viewportMeta) {
          viewportMeta.setAttribute('content', this.originalViewportMeta);
          console.log(`Viewport Meta-Tag wiederhergestellt: ${this.originalViewportMeta}`);

          // Trigger einen Reflow
          document.body.style.display = 'none';
          document.body.offsetHeight; // Trigger reflow
          document.body.style.display = '';
        }
        this.originalViewportMeta = null;
      }
    } catch (error) {
      console.warn('Viewport-Meta-Tag-Wiederherstellung fehlgeschlagen:', error.message);
    }
  }

  // Informationen über den aktuellen Zustand
  getStatus() {
    return {
      isLocked: this.isLocked,
      lockAttempts: this.lockAttempts,
      canUseLockAPI: this.canUseLockAPI(),
      isCurrentlyLandscape: this.isCurrentlyLandscape(),
      orientation: screen.orientation ? screen.orientation.angle : window.orientation,
      cssRotationActive: document.documentElement.classList.contains('force-portrait-rotation'),
      viewportMetaOverridden: !!this.originalViewportMeta,
      currentDimensions: `${window.innerWidth}x${window.innerHeight}`,
      currentViewportMeta: document.querySelector('meta[name="viewport"]')?.getAttribute('content') || 'none'
    };
  }
}

// Singleton-Instanz erstellen
const orientationLock = new OrientationLock();

// Exportiere sowohl die Klasse als auch die Instanz
export { OrientationLock, orientationLock };
export default orientationLock;
