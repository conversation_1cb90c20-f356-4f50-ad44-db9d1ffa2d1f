<template>
    <div 
        class="rounded-2xl bg-white h-auto w-32 p-3 antialiased imglinkcss bg-cover text-white"
    >
        <!-- up -->
        <div class="w-full h-8 pb-4 flex flex-row font-bold text-base ">
            {{ dayelement.day }}
        </div>

        <!-- select -->
        <div class="w-full flex flex-col pb-4">
            <!-- selected values -->
            <span class="text-xs uppercase font-extrabold font-OpenSans align-left text-left">
                <div v-for="eachday in dayelement.values" v-bind:key="eachday.id" class="">
                    <label class="inline-flex items-center py-1 checked:bg-sfgyellow-500">
                        <input type="checkbox" class="form-checkbox h-4 w-4" v-model="eachday.selected" :value='eachday.item'>
                        <span class="ml-2">{{ eachday.item }}</span>
                    </label>
                </div>
            </span>
        </div>

    </div>
</template>
<script setup>

    defineProps({
        dayelement: Object,
        index: Number,
    })

</script>
<style>

    .imglinkcss {
        background-image: v-bind('dayelement.imagelink');
    }

</style>

