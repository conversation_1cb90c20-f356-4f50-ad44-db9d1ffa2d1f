const PinterestToken = require('../models/pinterestTokenModel');
const helper = require('../utils/helper');

class PinterestTokenService {
  constructor() {
    this.cachedToken = null;
    this.cacheExpiry = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 Minuten Cache
  }

  /**
   * Holt den aktuellen Pinterest Access Token
   * @param {string} environment - Umgebung (development, preview, production)
   * @returns {string|null} Access Token oder null
   */
  async getAccessToken(environment = process.env.NODE_ENV || 'development') {
    try {
      // Prüfe Cache
      if (this.cachedToken && this.cacheExpiry && Date.now() < this.cacheExpiry) {
        return this.cachedToken.accessToken;
      }

      // Lade Token aus Datenbank
      const tokenRecord = await PinterestToken.getCurrentToken(environment);
      
      if (!tokenRecord) {
        helper.devConsole('[PinterestTokenService] No Pinterest token found in database');
        return null;
      }

      // Prüfe ob Token bald abläuft
      if (tokenRecord.isExpiringSoon()) {
        helper.devConsole('[PinterestTokenService] Token expires soon, should refresh');
        // Hier könnte automatisches Refresh implementiert werden
      }

      // Cache aktualisieren
      this.cachedToken = tokenRecord;
      this.cacheExpiry = Date.now() + this.cacheTimeout;

      return tokenRecord.accessToken;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error getting access token:', error.message);
      return null;
    }
  }

  /**
   * Holt den aktuellen Pinterest Refresh Token
   * @param {string} environment - Umgebung
   * @returns {string|null} Refresh Token oder null
   */
  async getRefreshToken(environment = process.env.NODE_ENV || 'development') {
    try {
      const tokenRecord = await PinterestToken.getCurrentToken(environment);
      return tokenRecord ? tokenRecord.refreshToken : null;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error getting refresh token:', error.message);
      return null;
    }
  }

  /**
   * Speichert neue Pinterest Tokens
   * @param {string} accessToken - Access Token
   * @param {string} refreshToken - Refresh Token
   * @param {string} scope - Token Scopes
   * @param {string} environment - Umgebung
   * @returns {Object} Gespeicherter Token-Record
   */
  async saveTokens(accessToken, refreshToken, scope, environment = process.env.NODE_ENV || 'development') {
    try {
      const tokenRecord = await PinterestToken.saveTokens(accessToken, refreshToken, scope, environment);
      
      // Cache leeren
      this.clearCache();
      
      helper.devConsole('[PinterestTokenService] Pinterest tokens saved successfully');
      return tokenRecord;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error saving tokens:', error.message);
      throw error;
    }
  }

  /**
   * Aktualisiert den Access Token
   * @param {string} newAccessToken - Neuer Access Token
   * @param {string} newRefreshToken - Neuer Refresh Token (optional)
   * @param {string} environment - Umgebung
   * @returns {Object} Aktualisierter Token-Record
   */
  async updateAccessToken(newAccessToken, newRefreshToken = null, environment = process.env.NODE_ENV || 'development') {
    try {
      const tokenRecord = await PinterestToken.updateAccessToken(newAccessToken, newRefreshToken, environment);
      
      // Cache leeren
      this.clearCache();
      
      helper.devConsole('[PinterestTokenService] Pinterest access token updated successfully');
      return tokenRecord;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error updating access token:', error.message);
      throw error;
    }
  }

  /**
   * Prüft ob Pinterest Tokens verfügbar sind
   * @param {string} environment - Umgebung
   * @returns {boolean} True wenn Tokens verfügbar sind
   */
  async hasValidTokens(environment = process.env.NODE_ENV || 'development') {
    try {
      const tokenRecord = await PinterestToken.getCurrentToken(environment);
      return !!tokenRecord && !!tokenRecord.accessToken && !!tokenRecord.refreshToken;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error checking token validity:', error.message);
      return false;
    }
  }

  /**
   * Holt alle Token-Informationen
   * @param {string} environment - Umgebung
   * @returns {Object|null} Token-Record oder null
   */
  async getTokenInfo(environment = process.env.NODE_ENV || 'development') {
    try {
      return await PinterestToken.getCurrentToken(environment);
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error getting token info:', error.message);
      return null;
    }
  }

  /**
   * Leert den Token-Cache
   */
  clearCache() {
    this.cachedToken = null;
    this.cacheExpiry = null;
  }

  /**
   * Prüft ob Token bald abläuft und gibt Warnung aus
   * @param {string} environment - Umgebung
   * @returns {boolean} True wenn Token bald abläuft
   */
  async checkTokenExpiry(environment = process.env.NODE_ENV || 'development') {
    try {
      const tokenRecord = await PinterestToken.getCurrentToken(environment);
      
      if (!tokenRecord) {
        return false;
      }

      if (tokenRecord.isExpiringSoon()) {
        helper.devConsole('[PinterestTokenService] WARNING: Pinterest token expires soon!', {
          expiresAt: tokenRecord.expiresAt,
          environment: environment
        });
        return true;
      }

      return false;
    } catch (error) {
      helper.devConsole('[PinterestTokenService] Error checking token expiry:', error.message);
      return false;
    }
  }
}

// Singleton-Instanz
const pinterestTokenService = new PinterestTokenService();

module.exports = pinterestTokenService;
