const express = require('express');
const pinterestOAuthController = require('../controllers/pinterestOAuthController');
// const authController = require('../controllers/authController'); // Temporarily commented out

const router = express.Router();

// @desc    Start Pinterest OAuth flow
// @route   GET /api/v1/pinterest/oauth/start
// @access  Public (for now, add auth later)
router.get('/start',
  pinterestOAuthController.startOAuth
);

// @desc    Handle Pinterest OAuth callback
// @route   GET /api/v1/pinterest/oauth/callback
// @access  Public (but validates state)
router.get('/callback',
  pinterestOAuthController.handleCallback
);

// @desc    Refresh Pinterest access token
// @route   POST /api/v1/pinterest/oauth/refresh
// @access  Public (for now, add auth later)
router.post('/refresh',
  pinterestOAuthController.refreshToken
);

// @desc    Test Pinterest integration
// @route   POST /api/v1/pinterest/test
// @access  Public (for now, add auth later)
router.post('/test',
  pinterestOAuthController.testIntegration
);

module.exports = router;
