<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ordy <PERSON><PERSON>end</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e60023;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #e60023;
        }
        .btn {
            display: inline-block;
            background: #e60023;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .btn:hover {
            background: #c8001a;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Section -->
        <div id="loginSection">
            <h1>🔐 Ordy Admin Backend</h1>
            <div class="step">
                <h3>Anmeldung erforderlich</h3>
                <p>Bitte melden Sie sich mit Ihrem Ordy-Account an:</p>
                <button id="loginBtn" class="btn">Mit Ordy anmelden</button>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div id="adminDashboard" style="display: none;">
            <h1>🛠️ Ordy Admin Backend</h1>

            <div class="step">
                <h3>Willkommen, <span id="adminName"></span>!</h3>
                <p>Admin-Berechtigungen: <span id="adminPermissions"></span></p>
                <button id="logoutBtn" class="btn" style="background: #dc3545;">Abmelden</button>
            </div>

        <div class="step">
            <h3>Schritt 1: Pinterest Autorisierung</h3>
            <p>Klicken Sie auf den Button unten, um Pinterest zu autorisieren:</p>
            <button id="startAuth" class="btn">Pinterest autorisieren</button>
        </div>

        <div class="step">
            <h3>Schritt 2: Automatische Token-Verarbeitung</h3>
            <p>Nach der Autorisierung wird automatisch:</p>
            <ul>
                <li>✅ Der Authorization Code extrahiert</li>
                <li>✅ Das Access Token angefordert</li>
                <li>✅ Das Token in der Datenbank gespeichert</li>
                <li>✅ Das automatische Publishing aktiviert</li>
            </ul>
        </div>

            <div id="status" class="status info" style="display: none;">
                <strong>Status:</strong> <span id="statusText">Bereit...</span>
            </div>

            <div class="step">
                <h3>📊 Publishing Status</h3>
                <button id="testPublish" class="btn" style="background: #28a745;">Publishing testen</button>
                <button id="checkToken" class="btn" style="background: #17a2b8;">Token Status prüfen</button>
            </div>
        </div>
    </div>

    <!-- Stytch SDK -->
    <script src="https://js.stytch.com/stytch.js"></script>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        let stytchClient;
        let authToken = null;

        // Initialize Stytch
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Stytch client (you'll need to add your public key)
            stytchClient = window.Stytch.init({
                public_token: 'public-token-test-123', // Replace with your actual Stytch public token
                environment: 'test' // or 'live' for production
            });

            // Check if user is already logged in
            checkAuthStatus();
        });

        // Check authentication status
        async function checkAuthStatus() {
            const token = localStorage.getItem('ordy_admin_token');
            if (token) {
                try {
                    const response = await fetch(`${API_BASE}/admin/verify`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });

                    if (response.ok) {
                        const userData = await response.json();
                        showAdminDashboard(userData.user);
                        authToken = token;
                    } else {
                        localStorage.removeItem('ordy_admin_token');
                        showLoginSection();
                    }
                } catch (error) {
                    console.error('Auth check failed:', error);
                    showLoginSection();
                }
            } else {
                showLoginSection();
            }
        }

        // Show login section
        function showLoginSection() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('adminDashboard').style.display = 'none';
        }

        // Show admin dashboard
        function showAdminDashboard(user) {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('adminDashboard').style.display = 'block';

            document.getElementById('adminName').textContent = user.firstName + ' ' + user.lastName;

            const permissions = [];
            if (user.adminPermissions.canAccessBackend) permissions.push('Backend');
            if (user.adminPermissions.canManagePinterest) permissions.push('Pinterest');
            if (user.adminPermissions.canManageMarketing) permissions.push('Marketing');
            if (user.adminPermissions.canViewAnalytics) permissions.push('Analytics');

            document.getElementById('adminPermissions').textContent = permissions.join(', ') || 'Keine';
        }

        // Status anzeigen
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            statusDiv.className = `status ${type}`;
            statusText.textContent = message;
            statusDiv.style.display = 'block';
        }

        // Login button
        document.getElementById('loginBtn').addEventListener('click', async () => {
            try {
                showStatus('Anmeldung wird geprüft...', 'info');

                // For now, prompt for token (in production, use proper Stytch flow)
                const token = prompt('Bitte geben Sie Ihr Ordy Auth Token ein:');
                if (!token) return;

                // Verify admin access
                const response = await fetch(`${API_BASE}/admin/verify`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const userData = await response.json();
                    localStorage.setItem('ordy_admin_token', token);
                    authToken = token;
                    showAdminDashboard(userData.user);
                    showStatus('Erfolgreich angemeldet!', 'success');
                } else {
                    const errorData = await response.json();
                    showStatus(`Anmeldung fehlgeschlagen: ${errorData.message}`, 'error');
                }
            } catch (error) {
                showStatus(`Anmeldung fehlgeschlagen: ${error.message}`, 'error');
            }
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            localStorage.removeItem('ordy_admin_token');
            authToken = null;
            showLoginSection();
            showStatus('Erfolgreich abgemeldet', 'info');
        });

        // Pinterest Autorisierung starten
        document.getElementById('startAuth').addEventListener('click', async () => {
            try {
                showStatus('Generiere Pinterest Authorization URL...', 'info');

                const response = await fetch(`${API_BASE}/admin/pinterest/auth-url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showStatus('Weiterleitung zu Pinterest...', 'info');
                    // Öffne Pinterest OAuth in neuem Tab
                    window.open(data.data.authUrl, '_blank');
                } else {
                    showStatus(`Fehler: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`Netzwerkfehler: ${error.message}`, 'error');
            }
        });

        // Publishing testen
        document.getElementById('testPublish').addEventListener('click', async () => {
            try {
                showStatus('Teste Pinterest Publishing...', 'info');

                if (!authToken) {
                    showStatus('Bitte melden Sie sich zuerst an', 'error');
                    return;
                }

                const response = await fetch(`${API_BASE}/admin/marketing/auto-publish-pinterest`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showStatus('Pinterest Publishing erfolgreich!', 'success');
                } else {
                    showStatus(`Publishing Fehler: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`Publishing Fehler: ${error.message}`, 'error');
            }
        });

        // Token Status prüfen
        document.getElementById('checkToken').addEventListener('click', async () => {
            try {
                showStatus('Prüfe Token Status...', 'info');

                const response = await fetch(`${API_BASE}/admin/pinterest/token-status`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showStatus(`Token Status: ${data.message}`, 'success');
                } else {
                    showStatus('Kein gültiges Token gefunden', 'error');
                }
            } catch (error) {
                showStatus(`Token Check Fehler: ${error.message}`, 'error');
            }
        });

        // URL Parameter prüfen (falls von Pinterest Callback zurückgekehrt)
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');

        if (error) {
            showStatus(`Pinterest Autorisierung fehlgeschlagen: ${error}`, 'error');
        } else if (code) {
            showStatus('Pinterest Code erhalten, verarbeite automatisch...', 'info');

            // Automatische Token-Verarbeitung
            fetch(`${API_BASE}/oauth/pinterest/token?code=${code}`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showStatus('✅ Pinterest Token erfolgreich gespeichert! Automatisches Publishing ist jetzt aktiv.', 'success');
                } else {
                    showStatus(`Token Fehler: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                showStatus(`Token Verarbeitung fehlgeschlagen: ${error.message}`, 'error');
            });
        }
    </script>
</body>
</html>
