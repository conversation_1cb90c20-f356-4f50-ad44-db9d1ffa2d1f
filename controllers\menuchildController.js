const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper')
const Menu = require('../models/menuModel')
const menuController = require('./menuController')
const Menuchild = require('../models/menuchildModel')
const AppError = require('../utils/appError')
const { uploadFile, getFileStream } = require('../utils/awsStorage')
const stableIdManager = require('../utils/stableIdManager')
const { migrateLegacyMenuChild, detectLegacyFormat } = require('../utils/legacyMigrationBackend')

/////////////////////////////////////// ONE MENU CHILD /////////////////////////////////////////
// @POST /menu/child/one/:menuchildid
// empty Menü/Rezept/Reciept is created
exports.createOneMenuchild = catchAsync(async (req, res, next) => {

  // check if all data is available
  helper.devConsole("createOneMenuchild in menuchildController")
  helper.devConsole(req.body.menuchild)

  // Get userId from req.userId (set by previous middleware) or fallback to req.body.user_id
  const userId = req.userId || req.body.user_id;
  helper.devConsole(`Using userId: ${userId}`); // Log the found userId

  // KRITISCH: StableID-Management für neue MenuChild-Erstellung
  if (req.body.menuchild && req.body.menuchild.ingredients && Array.isArray(req.body.menuchild.ingredients)) {
    helper.devConsole("🔧 Processing ingredients for new MenuChild with StableID management...");

    // Für neue MenuChilds beginnen wir bei maxUsedStableId = 0
    const { updatedIngredients, newMaxUsedId } = stableIdManager.assignStableIds(
      req.body.menuchild.ingredients,
      0 // Neue MenuChilds starten bei 0
    );

    // Aktualisiere menuchild mit StableIDs
    req.body.menuchild.ingredients = updatedIngredients;
    req.body.menuchild.maxUsedStableId = newMaxUsedId;

    helper.devConsole(`✅ Assigned StableIDs for new MenuChild, maxUsedStableId: ${newMaxUsedId}`);
  }

  if (
      !userId || // Use the determined userId variable
      !req.body.menuchild || // Check if menuchild object exists
      !req.body.menuchild.parentId ||
      !req.body.menuchild.ingredients ||
      req.body.menuchild.seatCount === undefined || // Check for existence, even if 0
      req.body.menuchild.cookingTime === undefined || // Check for existence, even if 0
      !req.body.menuchild.preperation ||
      !req.body.menuchild.nutritions ||
      !req.body.menuchild.versions
  ){
      // Log which specific field might be missing for easier debugging
      let missingField = !userId ? 'userId' : !req.body.menuchild ? 'menuchild object' : !req.body.menuchild.parentId ? 'menuchild.parentId' : !req.body.menuchild.ingredients ? 'menuchild.ingredients' : req.body.menuchild.seatCount === undefined ? 'menuchild.seatCount' : req.body.menuchild.cookingTime === undefined ? 'menuchild.cookingTime' : !req.body.menuchild.preperation ? 'menuchild.preperation' : !req.body.menuchild.nutritions ? 'menuchild.nutritions' : 'menuchild.versions';
      helper.devConsole(`Missing field in createOneMenuchild: ${missingField}`);
      return next(new AppError(`Not every data was given at menuchildController.createOneMenuchild (Missing: ${missingField})`, 500));
  }

  // create childmenu
  const newMenuchild = await Menuchild.create(
    req.body.menuchild
  );

  //helper.devConsole(newMenuchild)

  req.body.menuchild._id = newMenuchild._id
  req.body.answerobject = newMenuchild

  //helper.devConsole(req.body.menuchild)

  // next
  next()

});

// @GET /menu/child/one/:menuchildid
exports.getOneMenuchild = catchAsync(async (req, res, next) => {
  if(
    !req.params.menuchildid
  ){
      next(new AppError('Not every data was given at menuchildController.getOneMenuchild', 500))
  }
  helper.devConsole("ingetOneMenuchild")
  // find one menu and automaticly return
  let menue = await Menuchild.findOne({_id: req.params.menuchildid})
  .populate([{
        path: 'ingredients.unit',
        model: 'Unit'
      },
      {
        path: 'ingredients.name',
        model: 'Grocery'
      }
  ])

  // 🔧 KRITISCH: Legacy-Migration für alte Rezepte
  if (menue) {
    const migrationInfo = detectLegacyFormat(menue);
    if (migrationInfo.isLegacy) {
      helper.devConsole(`🔄 Legacy recipe detected: ${migrationInfo.legacyType}`);
      helper.devConsole(`📋 Issues: ${migrationInfo.issues.join(', ')}`);

      try {
        const migratedMenuChild = await migrateLegacyMenuChild(menue.toObject());

        // Speichere migrierte Daten zurück in die Datenbank
        await Menuchild.updateOne(
          { _id: req.params.menuchildid },
          { $set: migratedMenuChild }
        );

        // Lade das migrierte MenuChild neu mit Populationen
        menue = await Menuchild.findOne({_id: req.params.menuchildid})
        .populate([{
              path: 'ingredients.unit',
              model: 'Unit'
            },
            {
              path: 'ingredients.name',
              model: 'Grocery'
            }
        ]);

        helper.devConsole('✅ Legacy recipe migrated and saved to database');
      } catch (migrationError) {
        helper.devConsole('❌ Legacy migration failed:', migrationError.message);
        // Fahre mit ursprünglichen Daten fort
      }
    }
  }

    // Send success response
    req.body.answerobject = menue

    // next
    next()
});

// @ UPDATE / @PATCH
// /menu/child/one/:menuchildid
exports.patchOneMenuchild = catchAsync(async (req, res, next) => {
    //helper.devConsole(req.body)
    //helper.devConsole(req.params.menuchildid)
    if(
      !req.params.menuchildid ||
      !req.body.menuchild
    ){
        next(new AppError('Not every data was given at menuchildController.getOneMenuchild', 500))
    }
    helper.devConsole("inPatchOneMenuchild")

    let updateObject = req.body.menuchild;

    // KRITISCH: StableID-Management für Zutaten
    if (updateObject.ingredients && Array.isArray(updateObject.ingredients)) {
        helper.devConsole("🔧 Processing ingredients with StableID management...");

        // Hole aktuelles MenuChild für maxUsedStableId
        const currentMenuChild = await Menuchild.findById(req.params.menuchildid);
        const currentMaxUsedId = currentMenuChild?.maxUsedStableId || 0;

        // Weise stabile IDs zu (nur für neue Zutaten)
        const { updatedIngredients, newMaxUsedId } = stableIdManager.assignStableIds(
            updateObject.ingredients,
            currentMaxUsedId
        );

        // Aktualisiere updateObject mit korrigierten Zutaten und maxUsedStableId
        updateObject = {
            ...updateObject,
            ingredients: updatedIngredients,
            maxUsedStableId: newMaxUsedId
        };

        // Validiere StableID-Konsistenz
        const validation = stableIdManager.validateStableIds({
            ingredients: updatedIngredients,
            maxUsedStableId: newMaxUsedId
        });

        if (!validation.isValid) {
            helper.devConsole("❌ StableID validation failed:", validation.errors);
            // Logge Warnung aber erlaube Update (da StableIDs im Controller bereits korrekt verwaltet werden)
            console.warn('⚠️ StableID validation failed but allowing update:', validation.errors);
        }

        if (validation.warnings.length > 0) {
            helper.devConsole("⚠️ StableID warnings:", validation.warnings);
        }

        helper.devConsole("✅ StableID management completed successfully");
    }

    helper.devConsole(updateObject)
    const menue = await Menuchild.updateOne({_id: req.params.menuchildid}, {$set: updateObject});
    helper.devConsole(menue)

    //next
    req.body.answerobject = updateObject
    next()

});

/////////////////////////////////////// //////////// /////////////////////////////////////////

////////////////////////////////////// MENUCHILD CONNECTION ///////////////////////////
// @ POST
// /one/child/:menuchildid
exports.addOneMenuchildToMenu = catchAsync(async (req, res, next) => {
  if(
      !req.body.menuchild
  ){
      next(new AppError('Not every data was given at menuchildController.addOneMenuchild', 500))
  }
  //helper.devConsole("menuchildController.addOneMenuchild")
  //helper.devConsole(req.body.menuchild)


  // set new object to update the menu parent object with new ID
  req.params.id = req.body.menuchild.parentId
  req.body.menu.menuchilds.push(
    {
      numberOfPersons: req.body.menuchild.seatCount,
      menuChildId: req.body.menuchild._id
    }
  )

  //req.body.data = "test"
  next()

});


//@CREATE
exports.recalculateMenuchild = catchAsync(async (req, res, next) => {
  helper.devConsole("menuchildController.recalculateMenuchild")
  helper.devConsole(req.body.menuchild.isStandard)

  if(
      !req.body.newPersonCount ||
      !req.body.menuchild.parentId ||
      !req.body.menuchild.ingredients ||
      !req.body.menuchild.seatCount ||
      !req.body.menuchild.cookingTime ||
      !req.body.menuchild.preperation ||
      !req.body.menuchild.nutritions ||
      !req.body.menuchild.versions ||
      !req.body.menuchild.isStandard
  ){
      next(new AppError('Not every data was given at menuchildController.createAndAddOneMenuchildToMenu', 500))
  }

  // check if parentId and numberOfPersons already exists
  const menu = await Menu.findOne({_id: req.body.menuchild.parentId, 'menuchilds.numberOfPersons': req.body.menuchild.seatCount})

})
// @CREATE
// /one/child/:menuchildid/createifnotexists
exports.createAndAddOneMenuchildToMenu = catchAsync(async (req, res, next) => {
  console.log("[createAndAddOneMenuchildToMenu] Starting...")
  console.log("[createAndAddOneMenuchildToMenu] Request body:", req.body.menuchild)

  // KRITISCH: Verbesserte Validierung - erlaubt leere Arrays für optionale Felder
  if(
      !req.body.user_id ||
      !req.body.menuchild.parentId ||
      !req.body.menuchild.ingredients ||
      req.body.menuchild.seatCount === undefined ||
      req.body.menuchild.cookingTime === undefined ||
      req.body.menuchild.preperation === undefined ||
      req.body.menuchild.nutritions === undefined ||
      req.body.menuchild.versions === undefined
  ){
      console.log('❌ Validation failed in createAndAddOneMenuchildToMenu:', {
          user_id: !!req.body.user_id,
          parentId: !!req.body.menuchild.parentId,
          ingredients: !!req.body.menuchild.ingredients,
          seatCount: req.body.menuchild.seatCount,
          cookingTime: req.body.menuchild.cookingTime,
          preperation: req.body.menuchild.preperation,
          nutritions: req.body.menuchild.nutritions,
          versions: req.body.menuchild.versions
      });
      next(new AppError('Not every data was given at menuchildController.createAndAddOneMenuchildToMenu', 500))
  }

  // check if parentId and numberOfPersons already exists
  const menu = await Menu.findOne({_id: req.body.menuchild.parentId, 'menuchilds.numberOfPersons': req.body.menuchild.seatCount})

  let newMenuchild;
  if(menu){
    //add found menu
    console.log("[createAndAddOneMenuchildToMenu] menuchild existiert")
    const menuchild = await Menuchild.findOne({parentId: req.body.menuchild.parentId, seatCount: req.body.menuchild.seatCount})
    req.body.menuchild = menuchild
    req.body.settings = { ...req.body.settings, menuchild_created: false }
  } else {
    console.log("[createAndAddOneMenuchildToMenu] menuchild existiert noch nicht")
    req.body.settings = { ...req.body.settings, menuchild_created: true }

    // PERFORMANCE: Optimized MenuChild creation
    console.log("[createAndAddOneMenuchildToMenu] Creating MenuChild optimized...");

    try {
      // Create MenuChild and update Menu in parallel for better performance
      const [newMenuchild, menuUpdateResult] = await Promise.all([
        Menuchild.create(req.body.menuchild),
        // Prepare menu update (will be executed after MenuChild creation)
        Menu.findById(req.body.menuchild.parentId)
      ]);

      console.log("[createAndAddOneMenuchildToMenu] MenuChild created successfully:", newMenuchild._id);

      // Now update the Menu with the new MenuChild reference
      const menuUpdate = await Menu.updateOne(
        { _id: req.body.menuchild.parentId },
        { $push: { menuchilds: { numberOfPersons: newMenuchild.seatCount, menuChildId: newMenuchild._id } } }
      );

      console.log("[createAndAddOneMenuchildToMenu] Menu updated successfully");

      // Store both results for response
      req.body.newMenuchild = newMenuchild;
      req.body.menuUpdateResult = menuUpdate;

    } catch (createError) {
      console.log("[createAndAddOneMenuchildToMenu] ERROR creating MenuChild:", createError.message);
      throw createError;
    }

    // Stelle sicher, dass die nächste Middleware alle erforderlichen Daten hat
    req.body.menuchild = {
      _id: newMenuchild._id,
      parentId: newMenuchild.parentId,
      seatCount: newMenuchild.seatCount,
      isStandard: newMenuchild.isStandard
    };
    //console.log(newMenuchild)

  }

  req.body.answerobject = newMenuchild

  console.log("[createAndAddOneMenuchildToMenu] Completed successfully")

  // next
  next()

});


// Used to update the actual choosed menuchild as standard (isStandard = true) when used by users
exports.updateRelatedMenuchildsIsStandardField = catchAsync(async (req, res, next) => {
  console.log("[updateRelatedMenuchildsIsStandardField] Starting...");
  // req.body.menuchild should contain _id of the menuchild to be set as standard,
  // and its parentId.
  const menuchildInfo = req.body.menuchild;

  if (!menuchildInfo || !menuchildInfo._id || !menuchildInfo.parentId) {
    return next(
      new AppError(
        "Required menuchild _id and parentId not provided to updateRelatedMenuchildsIsStandardField",
        400 // Bad Request
      )
    );
  }

  console.log("[updateRelatedMenuchildsIsStandardField] Setting other children to isStandard: false for parent:", menuchildInfo.parentId);
  // Set all other menuchilds with the same parentId to isStandard = false
  await Menuchild.updateMany(
    {
      parentId: menuchildInfo.parentId,
      _id: { $ne: menuchildInfo._id }, // Exclude the one we are about to set to true
    },
    {
      $set: { isStandard: false },
    }
  );

  console.log("[updateRelatedMenuchildsIsStandardField] Setting child to isStandard: true:", menuchildInfo._id);
  // Set the specified menuchild to isStandard = true and get the updated document
  const newStandardMenuchild = await Menuchild.findByIdAndUpdate(
    menuchildInfo._id,
    {
      $set: { isStandard: true },
    },
    { new: true } // Return the modified document
  );

  if (!newStandardMenuchild) {
    return next(
      new AppError(
        `Menuchild with ID ${menuchildInfo._id} not found, cannot set as standard.`,
        404 // Not Found
      )
    );
  }

  // Optional: Verify and log if parent Menu's numberOfPersons for this child matches its seatCount.
  // For now, we assume other parts of the system maintain this consistency.
  /*
  const parentMenu = await Menu.findById(newStandardMenuchild.parentId);
  if (parentMenu) {
    const entryInParent = parentMenu.menuchilds.find(e => e.menuChildId.equals(newStandardMenuchild._id));
    if (entryInParent && entryInParent.numberOfPersons !== newStandardMenuchild.seatCount) {
      helper.devConsole(`[WARN] Syncing numberOfPersons in Menu ${parentMenu._id} for child ${newStandardMenuchild._id}. Was ${entryInParent.numberOfPersons}, now ${newStandardMenuchild.seatCount}`);
      await Menu.updateOne(
        { _id: parentMenu._id, "menuchilds.menuChildId": newStandardMenuchild._id },
        { $set: { "menuchilds.$.numberOfPersons": newStandardMenuchild.seatCount } }
      );
    }
  }
  */

  req.body.answerobject = newStandardMenuchild; // Pass the new standard child to the next middleware/response
  console.log("[updateRelatedMenuchildsIsStandardField] Completed successfully");
  next();
});


//////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////// MENUCHILD INGREDIENTS ///////////////////////////
// @ POST
// @ DESC: This Route is to add one ingredient to an exisiting menuchild with ingredients
// /one/:menuid/child/:menuchildid/ingredient
exports.addOneIngredientToIngredients = catchAsync(async (req, res, next) => {
  helper.devConsole("addOneIngredientToIngredients in menuchildController")
  if(
      !req.body.menuchild.ingredients
  ){
      next(new AppError('Not every data was given at menuchildController.addOneIngredientToIngredients', 500))
  }

  // KRITISCH: StableID-Management für neue Zutat
  helper.devConsole("🔧 Adding ingredient with StableID management...");

  // Hole aktuelles MenuChild für maxUsedStableId und bestehende Zutaten
  const currentMenuChild = await Menuchild.findById(req.params.menuchildid);
  if (!currentMenuChild) {
    return next(new AppError('MenuChild not found', 404));
  }

  const currentIngredients = currentMenuChild.ingredients || [];
  const currentMaxUsedId = currentMenuChild.maxUsedStableId || 0;

  // Verarbeite neue Zutaten (kann Array oder einzelne Zutat sein)
  const newIngredients = Array.isArray(req.body.menuchild.ingredients)
    ? req.body.menuchild.ingredients
    : [req.body.menuchild.ingredients];

  // Füge neue Zutaten mit StableIDs hinzu
  let updatedIngredients = [...currentIngredients];
  let newMaxUsedId = currentMaxUsedId;

  for (const newIngredient of newIngredients) {
    const result = stableIdManager.addIngredientWithStableId(
      updatedIngredients,
      newIngredient,
      newMaxUsedId
    );
    updatedIngredients = result.updatedIngredients;
    newMaxUsedId = result.newMaxUsedId;
  }

  // Aktualisiere MenuChild mit allen Zutaten und neuer maxUsedStableId
  const updateResult = await Menuchild.updateOne(
    { _id: req.params.menuchildid },
    {
      $set: {
        ingredients: updatedIngredients,
        maxUsedStableId: newMaxUsedId
      }
    }
  );

  helper.devConsole(`✅ Added ${newIngredients.length} ingredient(s) with StableIDs, new maxUsedStableId: ${newMaxUsedId}`);
  helper.devConsole(updateResult)

  req.body.answerobject = {
    ...updateResult,
    addedCount: newIngredients.length,
    newMaxUsedStableId: newMaxUsedId
  }
  // next
  next()

});

// @ DELETE
// @ DESC: Remove one ingredient from an existing menuchild while preserving StableIDs
// /one/:menuid/child/:menuchildid/ingredient/:ingredientIndex
exports.removeOneIngredientFromIngredients = catchAsync(async (req, res, next) => {
  helper.devConsole("removeOneIngredientFromIngredients in menuchildController")

  const ingredientIndex = parseInt(req.params.ingredientIndex);
  if (isNaN(ingredientIndex) || ingredientIndex < 0) {
    return next(new AppError('Invalid ingredient index', 400));
  }

  // KRITISCH: StableID-Management für Zutaten-Löschung
  helper.devConsole("🗑️ Removing ingredient with StableID preservation...");

  // Hole aktuelles MenuChild
  const currentMenuChild = await Menuchild.findById(req.params.menuchildid);
  if (!currentMenuChild) {
    return next(new AppError('MenuChild not found', 404));
  }

  const currentIngredients = currentMenuChild.ingredients || [];
  const currentMaxUsedId = currentMenuChild.maxUsedStableId || 0;

  if (ingredientIndex >= currentIngredients.length) {
    return next(new AppError('Ingredient index out of range', 400));
  }

  // Entferne Zutat und behalte StableID-Konsistenz
  const { updatedIngredients, removedStableId } = stableIdManager.removeIngredientKeepStableIds(
    currentIngredients,
    ingredientIndex,
    currentMaxUsedId
  );

  // Aktualisiere MenuChild (maxUsedStableId bleibt gleich - IDs werden nie wiederverwendet)
  const updateResult = await Menuchild.updateOne(
    { _id: req.params.menuchildid },
    {
      $set: {
        ingredients: updatedIngredients
        // maxUsedStableId bleibt unverändert - gelöschte IDs werden nie wiederverwendet
      }
    }
  );

  helper.devConsole(`✅ Removed ingredient with stableId ${removedStableId}, maxUsedStableId stays: ${currentMaxUsedId}`);
  helper.devConsole(updateResult)

  req.body.answerobject = {
    ...updateResult,
    removedStableId,
    remainingCount: updatedIngredients.length,
    maxUsedStableId: currentMaxUsedId // Bleibt unverändert
  }

  next()
});

// @ DELETE
// @ DESC: Remove ingredient by stableId from ALL MenuChilds of a recipe
// /recipe/:parentId/ingredient/:stableId/global-delete
exports.removeIngredientFromAllMenuChilds = catchAsync(async (req, res, next) => {
  const { parentId, stableId } = req.params;
  const stableIdNum = parseInt(stableId);

  console.log(`[removeIngredientFromAllMenuChilds] Removing stableId ${stableIdNum} from all MenuChilds of recipe ${parentId}`);

  if (isNaN(stableIdNum) || stableIdNum <= 0) {
    return next(new AppError('Invalid stableId', 400));
  }

  try {
    // Finde alle MenuChilds dieses Rezepts
    const menuChilds = await Menuchild.find({ parentId: parentId });
    console.log(`[removeIngredientFromAllMenuChilds] Found ${menuChilds.length} MenuChilds for recipe ${parentId}`);

    let updatedCount = 0;

    // Entferne die Zutat aus allen MenuChilds
    for (const menuChild of menuChilds) {
      const originalLength = menuChild.ingredients.length;

      // Filtere die Zutat mit der stableId heraus
      const updatedIngredients = menuChild.ingredients.filter(ingredient => ingredient.stableId !== stableIdNum);

      if (updatedIngredients.length < originalLength) {
        // Zutat wurde gefunden und entfernt
        await Menuchild.updateOne(
          { _id: menuChild._id },
          { $set: { ingredients: updatedIngredients } }
        );
        updatedCount++;
        console.log(`[removeIngredientFromAllMenuChilds] Removed stableId ${stableIdNum} from MenuChild ${menuChild._id} (${menuChild.seatCount} persons)`);
      }
    }

    console.log(`[removeIngredientFromAllMenuChilds] Successfully removed stableId ${stableIdNum} from ${updatedCount} MenuChilds`);

    req.body.answerobject = {
      success: true,
      removedStableId: stableIdNum,
      updatedMenuChildsCount: updatedCount,
      message: `Ingredient removed from ${updatedCount} recipe variants`
    };

    next();
  } catch (error) {
    console.log(`[removeIngredientFromAllMenuChilds] Error:`, error);
    return next(new AppError('Failed to remove ingredient from all recipe variants', 500));
  }
});

// @ POST
// @ DESC: Add ingredient to ALL MenuChilds of a recipe with proper scaling
// /recipe/:parentId/ingredient/global-add
exports.addIngredientToAllMenuChilds = catchAsync(async (req, res, next) => {
  const { parentId } = req.params;
  const { basePersonCount } = req.body;

  console.log(`[addIngredientToAllMenuChilds] Adding ingredient to all MenuChilds of recipe ${parentId}`);
  console.log(`[addIngredientToAllMenuChilds] Request body:`, req.body);
  console.log(`[addIngredientToAllMenuChilds] Processed ingredients from middleware:`, req.body.menuchild?.ingredients);
  console.log(`[addIngredientToAllMenuChilds] Base person count:`, basePersonCount);

  // 🔧 KRITISCH: Prüfe verschiedene Datenstrukturen für Ingredients
  let processedIngredients = null;

  // Prüfe ob groceryAndUnitChecker die Ingredients verarbeitet hat
  if (req.body.menuchild?.ingredients && req.body.menuchild.ingredients.length > 0) {
    processedIngredients = req.body.menuchild.ingredients;
    console.log(`[addIngredientToAllMenuChilds] Using processed ingredients from middleware`);
  } else if (req.body.ingredients && req.body.ingredients.length > 0) {
    // Fallback: Direkte ingredients im body (für manuelle Verarbeitung)
    processedIngredients = req.body.ingredients;
    console.log(`[addIngredientToAllMenuChilds] Using direct ingredients from body (fallback)`);
  }

  if (!processedIngredients || processedIngredients.length === 0 || !basePersonCount) {
    console.log(`[addIngredientToAllMenuChilds] Missing data:`, {
      processedIngredients: processedIngredients,
      basePersonCount: basePersonCount,
      bodyKeys: Object.keys(req.body)
    });
    return next(new AppError('Processed ingredient data and base person count are required', 400));
  }

  // Verwende das erste (und einzige) verarbeitete Ingredient
  const ingredient = processedIngredients[0];

  try {
    // Finde alle MenuChilds dieses Rezepts
    const menuChilds = await Menuchild.find({ parentId: parentId });
    console.log(`[addIngredientToAllMenuChilds] Found ${menuChilds.length} MenuChilds for recipe ${parentId}`);

    let updatedCount = 0;
    let assignedStableId = null;

    // Bestimme die nächste verfügbare stableId über alle MenuChilds
    let globalMaxStableId = 0;
    menuChilds.forEach(menuChild => {
      const maxId = menuChild.maxUsedStableId || 0;
      if (maxId > globalMaxStableId) {
        globalMaxStableId = maxId;
      }
    });

    assignedStableId = globalMaxStableId + 1;
    console.log(`[addIngredientToAllMenuChilds] Assigned global stableId: ${assignedStableId}`);

    // Füge die Zutat zu allen MenuChilds hinzu mit entsprechender Skalierung
    for (const menuChild of menuChilds) {
      const personCount = menuChild.seatCount || basePersonCount;
      const scalingFactor = personCount / basePersonCount;

      // Skaliere die Menge entsprechend der Personenanzahl
      const scaledIngredient = {
        amount: ingredient.amount * scalingFactor,
        unit: ingredient.unit, // ObjectId
        name: ingredient.name, // ObjectId
        stableId: assignedStableId
      };

      // Füge die Zutat hinzu
      const updatedIngredients = [...(menuChild.ingredients || []), scaledIngredient];

      // Aktualisiere MenuChild
      await Menuchild.updateOne(
        { _id: menuChild._id },
        {
          $set: {
            ingredients: updatedIngredients,
            maxUsedStableId: assignedStableId
          }
        }
      );

      updatedCount++;
      console.log(`[addIngredientToAllMenuChilds] Added ingredient to MenuChild ${menuChild._id} (${personCount} persons, amount: ${scaledIngredient.amount})`);
    }

    console.log(`[addIngredientToAllMenuChilds] Successfully added ingredient with stableId ${assignedStableId} to ${updatedCount} MenuChilds`);

    req.body.answerobject = {
      success: true,
      assignedStableId: assignedStableId,
      updatedMenuChildsCount: updatedCount,
      basePersonCount: basePersonCount,
      message: `Ingredient added to ${updatedCount} recipe variants with scaling`
    };

    next();
  } catch (error) {
    console.log(`[addIngredientToAllMenuChilds] Error:`, error);
    return next(new AppError('Failed to add ingredient to all recipe variants', 500));
  }
});

// 🔧 KRITISCH: Dedizierte PersonCount-Route mit Ingredient-Skalierung
// @ PATCH
// @ DESC: Update person count and scale ingredients accordingly
// /child/:menuchildid/person-count
exports.updatePersonCount = catchAsync(async (req, res, next) => {
  const { menuchildid } = req.params;
  const { direction } = req.body;

  helper.devConsole(`[updatePersonCount] MenuChild: ${menuchildid}, Direction: ${direction}`);

  // Validierung
  if (!direction || !['up', 'down'].includes(direction)) {
    return next(new AppError('Invalid direction. Must be "up" or "down"', 400));
  }

  // Lade MenuChild mit populated ingredients
  const menuChild = await Menuchild.findById(menuchildid)
    .populate([
      {
        path: 'ingredients.unit',
        model: 'Unit'
      },
      {
        path: 'ingredients.name',
        model: 'Grocery'
      }
    ]);

  if (!menuChild) {
    return next(new AppError('MenuChild not found', 404));
  }

  const oldSeatCount = menuChild.seatCount || 1;
  let newSeatCount;

  if (direction === 'up') {
    newSeatCount = oldSeatCount + 1;
  } else {
    newSeatCount = Math.max(1, oldSeatCount - 1); // Minimum 1 Person
  }

  // Keine Änderung nötig
  if (newSeatCount === oldSeatCount) {
    req.body.answerobject = menuChild;
    return next();
  }

  helper.devConsole(`🔧 PersonCount change: ${oldSeatCount} → ${newSeatCount}`);

  // Berechne Skalierungsfaktor
  const scalingFactor = newSeatCount / oldSeatCount;

  // Skaliere alle Ingredient-Mengen
  if (menuChild.ingredients && menuChild.ingredients.length > 0) {
    for (let ingredient of menuChild.ingredients) {
      const oldAmount = ingredient.amount || 1;
      const newAmount = Math.round((oldAmount * scalingFactor) * 100) / 100; // Runde auf 2 Dezimalstellen

      helper.devConsole(`📊 Scaling ingredient "${ingredient.name?.name || 'Unknown'}": ${oldAmount} → ${newAmount}`);
      ingredient.amount = newAmount;
    }
  }

  // Aktualisiere seatCount
  menuChild.seatCount = newSeatCount;

  try {
    // Speichere MenuChild
    await menuChild.save();
    helper.devConsole(`✅ PersonCount successfully updated to ${newSeatCount}`);

    // Aktualisiere auch das Parent Menu
    await Menu.updateOne(
      {
        _id: menuChild.parentId,
        "menuchilds.menuChildId": menuChild._id
      },
      {
        $set: {
          "menuchilds.$.numberOfPersons": newSeatCount
        }
      }
    );

    helper.devConsole(`✅ Updated parent menu numberOfPersons to ${newSeatCount}`);

  } catch (saveError) {
    helper.devConsole(`❌ Error saving MenuChild: ${saveError.message}`);
    return next(new AppError(`Failed to update person count: ${saveError.message}`, 500));
  }

  // Send success response
  req.body.answerobject = menuChild;
  next();
});

// 🔧 KRITISCH: Global Delete Ingredient von ALLEN MenuChilds eines Rezepts
// @ DELETE
// @ DESC: Delete ingredient from ALL MenuChilds of a recipe by stableId
// /recipe/:parentId/ingredient/:stableId/global-delete
exports.deleteIngredientFromAllMenuChilds = catchAsync(async (req, res, next) => {
  const { parentId, stableId } = req.params;

  helper.devConsole(`[deleteIngredientFromAllMenuChilds] Recipe: ${parentId}, StableId: ${stableId}`);

  // Validierung
  if (!stableId || isNaN(parseInt(stableId))) {
    return next(new AppError('Valid stableId is required', 400));
  }

  const stableIdNumber = parseInt(stableId);

  try {
    // Finde alle MenuChilds des Rezepts
    const menuChilds = await Menuchild.find({ parentId: parentId });

    if (menuChilds.length === 0) {
      return next(new AppError('No recipe variants found', 404));
    }

    helper.devConsole(`[deleteIngredientFromAllMenuChilds] Found ${menuChilds.length} recipe variants`);

    let totalDeleted = 0;
    const updateResults = [];

    // Lösche Ingredient mit stableId aus allen MenuChilds
    for (const menuChild of menuChilds) {
      const originalCount = menuChild.ingredients ? menuChild.ingredients.length : 0;

      // Filtere Ingredients - entferne das mit der stableId
      if (menuChild.ingredients) {
        menuChild.ingredients = menuChild.ingredients.filter(ingredient =>
          ingredient.stableId !== stableIdNumber
        );
      }

      const newCount = menuChild.ingredients ? menuChild.ingredients.length : 0;
      const deletedCount = originalCount - newCount;

      if (deletedCount > 0) {
        await menuChild.save();
        totalDeleted += deletedCount;

        updateResults.push({
          menuChildId: menuChild._id,
          seatCount: menuChild.seatCount,
          deletedCount: deletedCount,
          remainingIngredients: newCount
        });

        helper.devConsole(`[deleteIngredientFromAllMenuChilds] Deleted ${deletedCount} ingredient(s) from MenuChild ${menuChild._id} (${menuChild.seatCount} persons)`);
      } else {
        updateResults.push({
          menuChildId: menuChild._id,
          seatCount: menuChild.seatCount,
          deletedCount: 0,
          remainingIngredients: newCount,
          note: 'Ingredient not found'
        });
      }
    }

    helper.devConsole(`[deleteIngredientFromAllMenuChilds] Global delete completed: ${totalDeleted} ingredients deleted from ${menuChilds.length} variants`);

    // Send success response
    req.body.answerobject = {
      success: true,
      message: `Ingredient with stableId ${stableIdNumber} deleted from all recipe variants`,
      data: {
        parentId: parentId,
        stableId: stableIdNumber,
        totalVariants: menuChilds.length,
        totalDeleted: totalDeleted,
        updateResults: updateResults
      }
    };

    next();

  } catch (error) {
    helper.devConsole(`[deleteIngredientFromAllMenuChilds] Error:`, error);
    return next(new AppError('Failed to delete ingredient from all recipe variants', 500));
  }
});