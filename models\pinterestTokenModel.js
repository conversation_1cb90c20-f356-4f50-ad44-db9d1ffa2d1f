const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const pinterestTokenSchema = new mongoose.Schema({
  // Eindeutige Identifikation für das Token-Set
  tokenIdentifier: {
    type: String,
    default: 'pinterest_oauth',
    required: true,
    unique: true
  },

  // OAuth Tokens
  accessToken: {
    type: String,
    required: true
  },

  refreshToken: {
    type: String,
    required: true
  },

  // Token Metadaten
  tokenType: {
    type: String,
    default: 'bearer'
  },

  scope: {
    type: String,
    default: 'boards:read,pins:write,pins:read'
  },

  // Zeitstempel
  issuedAt: {
    type: Date,
    default: Date.now,
    required: true
  },

  expiresAt: {
    type: Date,
    // Pinterest tokens expire after 30 days by default
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  },

  lastRefreshed: {
    type: Date,
    default: Date.now
  },

  // Status
  isActive: {
    type: Boolean,
    default: true
  },

  // Environment (development, preview, production)
  environment: {
    type: String,
    default: process.env.NODE_ENV || 'development',
    required: true
  }
}, {
  timestamps: true // Fügt createdAt und updatedAt automatisch hinzu
});

// Index für schnelle Abfragen
pinterestTokenSchema.index({ tokenIdentifier: 1, environment: 1, isActive: 1 });

// Methode zum Abrufen des aktuellen Tokens
pinterestTokenSchema.statics.getCurrentToken = async function(environment = process.env.NODE_ENV || 'development') {
  return await this.findOne({
    tokenIdentifier: 'pinterest_oauth',
    environment: environment,
    isActive: true
  }).sort({ lastRefreshed: -1 });
};

// Methode zum Speichern neuer Tokens
pinterestTokenSchema.statics.saveTokens = async function(accessToken, refreshToken, scope, environment = process.env.NODE_ENV || 'development') {
  console.log('[PinterestToken.saveTokens] Saving new tokens...', {
    accessToken: accessToken ? 'present' : 'missing',
    refreshToken: refreshToken ? 'present' : 'missing',
    scope: scope,
    environment: environment
  });

  // KRITISCH: Lösche alte Tokens komplett (nicht nur deaktivieren)
  // da sie möglicherweise falsche Scopes haben
  const deletedCount = await this.deleteMany(
    { tokenIdentifier: 'pinterest_oauth', environment: environment }
  );

  console.log('[PinterestToken.saveTokens] Deleted', deletedCount.deletedCount, 'old tokens with potentially incorrect scopes');

  // Erstelle neuen Token-Eintrag
  const newToken = await this.create({
    tokenIdentifier: 'pinterest_oauth',
    accessToken: accessToken,
    refreshToken: refreshToken,
    scope: scope,
    environment: environment,
    isActive: true,
    issuedAt: new Date(),
    lastRefreshed: new Date()
  });

  console.log('[PinterestToken.saveTokens] Token saved successfully with ID:', newToken._id);
  console.log('[PinterestToken.saveTokens] New token scopes:', scope);
  return newToken;
};

// Methode zur Scope-Validierung
pinterestTokenSchema.statics.validateScopes = async function(environment = process.env.NODE_ENV || 'development') {
  const requiredScopes = ['boards:read', 'boards:write', 'pins:read', 'pins:write', 'user_accounts:read'];

  const tokenRecord = await this.getCurrentToken(environment);
  if (!tokenRecord || !tokenRecord.scope) {
    return { valid: false, missing: requiredScopes, current: [] };
  }

  // KRITISCH: Pinterest API kann Scopes als Komma-getrennt ODER Leerzeichen-getrennt zurückgeben
  const currentScopes = tokenRecord.scope
    .split(/[,\s]+/)  // Split by comma OR space
    .map(s => s.trim())
    .filter(s => s.length > 0);  // Remove empty strings

  console.log('[PinterestToken.validateScopes] Raw scope string:', tokenRecord.scope);
  console.log('[PinterestToken.validateScopes] Parsed current scopes:', currentScopes);
  console.log('[PinterestToken.validateScopes] Required scopes:', requiredScopes);

  const missingScopes = requiredScopes.filter(scope => !currentScopes.includes(scope));

  return {
    valid: missingScopes.length === 0,
    missing: missingScopes,
    current: currentScopes,
    tokenId: tokenRecord._id
  };
};

// Methode zum Aktualisieren des Access Tokens
pinterestTokenSchema.statics.updateAccessToken = async function(newAccessToken, newRefreshToken = null, environment = process.env.NODE_ENV || 'development') {
  const updateData = {
    accessToken: newAccessToken,
    lastRefreshed: new Date()
  };

  if (newRefreshToken) {
    updateData.refreshToken = newRefreshToken;
  }

  return await this.findOneAndUpdate(
    {
      tokenIdentifier: 'pinterest_oauth',
      environment: environment,
      isActive: true
    },
    updateData,
    { new: true }
  );
};

// Methode zum Prüfen, ob Token bald abläuft (innerhalb der nächsten 7 Tage)
pinterestTokenSchema.methods.isExpiringSoon = function() {
  const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  return this.expiresAt < sevenDaysFromNow;
};

// Use the specific connection (connection1) to define the model
const PinterestToken = connection1.model('PinterestToken', pinterestTokenSchema);

module.exports = PinterestToken;
