<template>
    <!-- Innerer Container -->
    <div class="w-full mx-auto">
      <!--
      bg-white
      font-YesevaOne
      w-8/12
      xl:w-10/12
      2xl:w-7/12
      mx-auto
      rounded-xl
      flex
      flex-col
      py-3
      mt-36
      md:block
      min-w-44
      -->
      <ul v-if="userStore.user.id" class="
      bg-white
      font-YesevaOne
      w-full
      flex
      flex-row
      fixed          <!-- Fixiert die Navigation für mobile Geräte -->
      bottom-0       <!-- Positioniert das Element am unteren Rand -->
      left-0         <!-- Positioniert es links vom Bildschirm -->
      right-0        <!-- Positioniert es rechts vom Bildschirm -->
      pl-3
      pr-3            <!-- Fügt Padding hinzu für mobile Ansicht -->
      md:w-8/12
      md:relative    <!-- Setzt die Position wieder auf relativ für Tablets und Desktops -->
      md:block       <!-- <PERSON><PERSON><PERSON><PERSON>, dass es als Block-Element auf größeren Geräten angezeigt wird -->
      md:p-0         <!-- Padding entfernen für große Ansichten -->
      md:mt-36
      md:rounded-xl
      xl:w-10/12
      2xl:w-6/12
      mx-auto
      md:min-w-44
      z-50           <!-- Z-Index, um das Element über anderen Inhalten zu halten -->
      "
      >

        <li @click.prevent="routerRezeptePush" class="w-full">
          <button class="w-full md:pl-5 py-3 flex md:flex-row flex-col mx-auto">
            <img class="mx-auto md:ml-5 w-auto h-auto" src="../assets/icons/zahnraeder.png" alt="Kochbuch" style="width: 22px;height: 21px;" />
            <span class="mx-auto md:ml-2 w-full p-1 md:text-left text-xs">Kochbuch</span>
          </button>
        </li>
        <li @click.prevent="routerCreationPush" class="w-full">
          <button class="w-full md:pl-5 py-3 flex md:flex-row flex-col mx-auto" type="button">
            <img class="mx-auto md:ml-5 w-auto h-auto" src="../assets/icons/rezepte.png" alt="Zettel" style="width: 21px;height: 21px;" />
            <span class="mx-auto md:ml-2 w-full p-1 md:text-left text-xs">Zettel</span>
          </button>
        </li>
        <li @click.prevent="routerCookeasyPush" class="w-full">
          <button class="w-full md:pl-5 py-3 flex md:flex-row flex-col mx-auto">
            <img class="mx-auto md:ml-5 w-auto h-auto" src="../assets/icons/speicher.png" alt="Reste verwerten" style="width: 21px;height: 21px;" />
            <span class="mx-auto md:ml-2 w-full p-1 md:text-left text-xs">Lager</span>
          </button>
        </li>
        <li @click.prevent="routerUploadPush" class="w-full">
          <button class="w-full md:pl-5 py-3 flex md:flex-row flex-col mx-auto">
            <img class="mx-auto md:ml-5 w-auto" src="../assets/icons/imageupload.png" alt="Hochladen" style="width: 30px;height: 21px;" />
            <span class="mx-auto md:ml-2 w-full p-1 md:text-left text-xs">Generieren</span>
          </button>
        </li>

        <!--
        
        <li @click.prevent="routerRezeptePush">
          <button class="w-full pl-5 py-3 flex flex-row text-left">
            <img src="../assets/icons/rezepte.png" alt="meine rezepte" style="width: 21px;height: 21px;" />
            <span class="ml-4 w-auto">meine rezepte</span>
          </button>
        </li>
        <li v-if="userStore.user.id" @click.prevent="routerKuechentischPush">
          <button class="w-full pl-5 py-3 flex flex-row">
            <img src="../assets/icons/kuechentisch.png" alt="küchentisch" style="width: 21px;height: 21px;" />
            <span class="ml-4 w-auto">küchentisch</span>
          </button>
        </li>
        -->
      </ul>

    </div>
</template>
<script setup>
    import { ref } from 'vue'
    import { useRouter } from 'vue-router';
    import { useUserStore } from '../store/userStore';

    const router = useRouter()
    const userStore = useUserStore()

    const isActive = ref(true)

    const changeMenuState = () => {
      isActive.value = !isActive.value
    }

    const routerHomePush = () => {
      changeMenuState()
      router.push({ name: 'home'})
    }
    const routerWochenplanPush = () => {
      changeMenuState()
      router.push({ name: 'wochenplan'})
    }
    const routerCookeasyPush = () => {
      changeMenuState()
      router.push({ name: 'cookeasy'})
    }
    const routerUploadPush = () => {
      changeMenuState()
      router.push({ name: 'uploadMenu'})
    }
    const routerLoginPush = () => {
      changeMenuState()
      router.push({ name: 'login'})
    }
    const routerKuechentischPush = () => {
      changeMenuState()
      router.push({ name: 'kuechentisch'})
    }

    const routerLogoutPush = () => {
      changeMenuState()

      localStorage.removeItem('backend_session_token')
      localStorage.removeItem('session_token')
      localStorage.removeItem('user_id')
      
      userStore.user.id = null;
      userStore.user.img = null;
      userStore.user.sessionToken = null;
      userStore.user.firstName = null;
      userStore.user.lastName = null;
      userStore.user.email = null;

      router.push('/login')
    }
    const routerRezeptePush = () => {
      changeMenuState()
      router.push({ name: 'kochbuch'})
    }

    const routerCreationPush = () => {
      changeMenuState()
      router.push({ name: 'zettel'})
    }

</script>
<style>

#navbarNav {
    width: 100%;
}

.global-overlay {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(1,1,1,.77);
    z-index: 50;
}

@media only screen and (min-width: 992px) {
    #navbarNav {
        float: right;
    }
}


@media only screen and (max-width: 991px) {
    .navbar {
        z-index: 100;
    }

    #navbarNav {
        display: block;
        margin-left: 0;
        position: absolute;
        top: 100%;
        left: 0;
        padding: 20px;
        background: rgb(248, 249, 250);
    }
}

</style>