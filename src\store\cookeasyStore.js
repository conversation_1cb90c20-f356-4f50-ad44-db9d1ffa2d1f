import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useUserStore } from './userStore'
import dayjs from 'dayjs'
import { useHelperStore } from '../../utils/helper';
import { fixStableIdsAfterAIGeneration } from '@/utils/recipeUtils';

export const useCookeasyStore = defineStore('cookeasy', () => {

    const { setNotification } = useNotification();
    const router = useRouter();
    const userStore = useUserStore();
    const helper = useHelperStore()

    /* INITIALIZING VALUES */

    const loadingState = ref(false)
    const loadingStateMenuCreation = ref(false)
    const lastRequests = ref("");
    const cookeasyConversation = ref([])
    const cookeasyConversationGPT = ref([
        {
            "content": `Kannst du mir bitte drei (3) unterschiedlich schwierige Rezeptvorschläge
generieren? Das erste Rezept soll sehr einfach und für Anfänger geeignet,
das zweite Rezept soll von mittlerer Schwierigkeit für Fortgeschrittene und das dritte Rezept etwas anspruchsvoller
für Experten sein. Falls zu Zutaten bekommst, baue diese in die Rezeptvorschläge ein.
Bitte achte darauf, dass für
jedes Rezept eine kurze Anleitung als Fliesstext vorhanden ist. Kreiere für jeden Rezeptvorschlag
den Key "name", "zubereitungszeit" und eine kurze "zubereitungsanleitung". Verfasse die
Zubereitungsanleitung immer als Fliesstext und verwende keine Sonderzeichen. Jeder Rezeptvorschlag
ist ein JSON Objekt und im Key "recipes" enthalten, welcher ein Array ist:

recieps: [
{
"name": // string, Name des Rezeptes
"zubereitungszeit": // string, Zum Beispiel 4min, wenn das Rezept in 4 Minuten zubereitet ist
"zubereitungsanleitung": // string, Fliesstext welcher das Essen beschreibt
},
{
"name": // string, Name des Rezeptes
"zubereitungszeit": // string, Zum Beispiel 4min, wenn das Rezept in 4 Minuten zubereitet ist
"zubereitungsanleitung": // string, Fliesstext welcher das Essen beschreibt
}
,
...
]
`,
            "role": "system"
        }
    ]);

    /* INITIALIZING */
    /* FUNCTIONS */

    /* CONVERATION */

    const cleanConversation = async() => {
        lastRequests.value = ""
        cookeasyConversation.value = []
        cookeasyConversationGPT.value = []
    }

    const startOrCreateConversation = async () => {

        loadingState.value = true;

        ///one/
        try{

            /// DEFINE DATA OBJECTS USER
            const userMessageObjectUI = {
                "content": lastRequests.value,
                "role": "user",
                "datatypearray": false,
                "loading": false
            }

            const userMessageObjectGPT = {
                "content": 'json ' + lastRequests.value,
                "role": "user"
            }

            /// DEFINE DATA OBJECTS ASSISTANT
            const messageObjectAssistantUI = {
                "content": "",
                "role": "assistant",
                "datatypearray": true
            }

            const messageObjectAssistantGPT = {
                "content": "",
                "role": "assistant"
            }

            //console.log(messageObjectAssistantUI)
            //console.log(messageObjectAssistantGPT)

            /// CHECK IF TEXT WAS GIVEN
            if(!userMessageObjectUI.content){
                setNotification('Gib mindestens ein Nahrungsmittel an', 'alert')
                throw TypeError('Gib mindestens ein Nahrungsmittel an');
            }


            /// FILL GPT AND UI ARRAY
            cookeasyConversationGPT.value.push(userMessageObjectGPT)
            cookeasyConversation.value.push(userMessageObjectUI)

            // DELETE ON GPT ARRAY
            /*
            for (let i = 0; i < cookeasyConversationGPT.value.length; i++){
                if(cookeasyConversationGPT.value[i].datatypearray){
                    cookeasyConversationGPT.value[i].content = JSON.stringify(cookeasyConversationGPT.value[i].content)
                }
                delete cookeasyConversationGPT.value[i]["datatypearray"];
               //console.log(cookeasyConversationGPT.value[i])
            };
            */

            //console.log(cookeasyConversationGPT.value)

            /// CREATE PAYLOAD
            const payload = {
                'chat': cookeasyConversationGPT.value
            }

            /// SEND TO API (gptcontroller > createReciepsByIngridients)
            let res;
            try{
                res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/ingredients/toreciept', payload);
            } catch(err){
                // CATCH ERROR
                helper.devConsole(err)
                throw TypeError('Es hat etwas nicht funktioniert');
            }

            helper.devConsole("res.data")
            helper.devConsole(res.data)

            // --- Robust Response Handling ---
            let jsonData;
            try {
                // 1. Check if the expected path to content exists and is a string
                if (res?.data?.data?.data?.content && typeof res.data.data.data.content === 'string') {
                    // 2. Try parsing the JSON string
                    jsonData = JSON.parse(res.data.data.data.content);

                    // 3. Check if the parsed data has the expected 'recipes' array
                    if (!jsonData || !Array.isArray(jsonData.recipes)) {
                        helper.devConsole("Error: Parsed JSON does not contain 'recipes' array.", jsonData);
                        throw new Error("Unerwartetes Datenformat vom Server empfangen (fehlendes recipe Array).");
                    }

                    // If parsing and validation successful, prepare message objects
                    messageObjectAssistantUI.content = jsonData.recipes;
                    messageObjectAssistantGPT.content = JSON.stringify(jsonData.recipes); // Keep original string for GPT context

                } else {
                    helper.devConsole("Error: Invalid response structure or content type.", res.data);
                    throw new Error("Ungültige Antwortstruktur vom Server empfangen.");
                }

            } catch (parseError) {
                helper.devConsole("JSON Parse Error or structure validation failed:", parseError);
                // Set a specific error message for the user
                setNotification('Fehler beim Verarbeiten der Server-Antwort.', 'alert');
                // Exit the function here as we cannot proceed
                // The finally block will still reset loadingState
                return;
            }
            // --- End Robust Response Handling ---

            // APEND ANSWER TO GPT AND UI
            // Check the outer status as well
            if(res.data.status == "success"){
                cookeasyConversationGPT.value.push(messageObjectAssistantGPT)
                cookeasyConversation.value.push(messageObjectAssistantUI)
                // loadingState is reset in finally block
            } else {
                // Handle cases where status is not 'success' even if parsing worked initially
                helper.devConsole("API status was not 'success'", res.data);
                setNotification(res.data.message || 'Ein unerwarteter Server-Status wurde empfangen.', 'alert');
                // Exit - finally block will reset loading state
                return;
            }

            // SET DATATYPEARRAY (Should only run if success path was taken)
            for (let i = 0; i < cookeasyConversation.value.length; i++){
                // Check if content exists before accessing constructor
                if(cookeasyConversation.value[i].content && cookeasyConversation.value[i].content.constructor.name === "Array"){
                    cookeasyConversation.value[i].datatypearray = true
                } else {
                    cookeasyConversation.value[i].datatypearray = false
                }
            };

            // SET FINAL VARS
            //console.log(cookeasyConversationGPT.value)
            //console.log(cookeasyConversation.value)


        } catch (error){
            // Enhanced error handling
            helper.devConsole("Error in startOrCreateConversation:", error);
            // Provide user notification
            setNotification('Fehler beim Generieren der Rezeptvorschläge. Bitte versuche es erneut.', 'alert');
            // Reset conversation arrays potentially? Depending on desired behavior on error.
            // cleanConversation(); // Example: Uncomment to clear conversation on error
        } finally {
            // Ensure loading state is always reset, regardless of success or error
            loadingState.value = false;
            helper.devConsole("startOrCreateConversation finally block: loadingState set to false");
        }
    }




  const createReciept = async (index, index2) => {

    loadingStateMenuCreation.value = true;
    cookeasyConversation.value[index].content[index2].loading = true

    const payload = {}

    payload["user_id"] = userStore.user.id;
    payload["settings"] = { switch: 'forward'};
    payload["data"] = JSON.stringify(cookeasyConversation.value[index].content[index2]);
    //console.log(index)
    //console.log(index2)

    //console.log(cookeasyConversation.value[index].content[index2])
    //console.log(obj)
    try{
        const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/reciept/createbytext', payload);
        //console.log(res.data)
        if(res.data.success){
            // Set loading states to false upon successful creation
            loadingStateMenuCreation.value = false
            // Ensure the specific item still exists before accessing loading property
            if (cookeasyConversation.value[index]?.content?.[index2]) {
                cookeasyConversation.value[index].content[index2].loading = false
            }
            // Redirect to the edit page with the ID from the response
            router.push({ name: 'rezept_edit', params: { id: res.data.data._id }  })
        } else {
            // Handle cases where the API call was successful (2xx) but the business logic failed (success: false)
            helper.devConsole("createReciept: API success but business logic failed", res.data);
            // Optionally set a notification for the user
            setNotification(res.data.message || 'Rezept konnte nicht erstellt werden.', 'alert');
            // Reset loading states
            loadingStateMenuCreation.value = false;
            if (cookeasyConversation.value[index]?.content?.[index2]) {
                cookeasyConversation.value[index].content[index2].loading = false;
            }
        }

        } catch(err){
            helper.devConsole("Error in createReciept:", err);
            // Reset both loading states in case of error
            loadingStateMenuCreation.value = false;
            // Check if the specific item still exists before trying to modify it
            if (cookeasyConversation.value[index]?.content?.[index2]) {
                cookeasyConversation.value[index].content[index2].loading = false;
            }

            // Enhanced Error Notification Logic
            let userMessage = 'Ein unbekannter Fehler ist aufgetreten.';
            const messageType = 'alert';

            if (err.response) {
              // We have a response from the server (4xx, 5xx)
              const status = err.response.status;
              const backendMessage = err.response?.data?.message; // Try to get backend message

              if (status === 401) {
                // Let the global interceptor handle 401
                console.warn('createReciept: Received 401, interceptor should handle.');
                // No specific user notification here
              } else if (status >= 400 && status < 500) {
                // Other 4xx Client Errors (Operational)
                userMessage = backendMessage || `Fehlerhafte Anfrage (Code: ${status}).`;
                helper.devConsole(`Create Recipe Client Error (Status ${status}): Displaying message: ${userMessage}`);
                setNotification(userMessage, messageType);
              } else if (status >= 500) {
                // 5xx Server Errors (Programming/Unexpected)
                userMessage = 'Ups, da ist etwas schiefgelaufen. Dies passiert manchmal - bitte einfach nochmals probieren.';
                helper.devConsole(`Create Recipe Server Error (Status ${status}): Showing generic user-friendly message.`);
                setNotification(userMessage, messageType);
              }
            } else if (err.request) {
              // Request made but no response received (network error)
              userMessage = 'Netzwerkfehler beim Erstellen des Rezepts.';
              helper.devConsole('Create Recipe Network Error: Request made but no response.');
              setNotification(userMessage, messageType);
            } else {
              // Error setting up the request
              userMessage = 'Fehler beim Vorbereiten der Rezept-Erstellung.';
              helper.devConsole('Create Recipe Setup Error:', err.message);
              setNotification(userMessage, messageType);
            }
            // Note: No global error state setting here, relies on notification.
        }


    }

    return {
        // EXPORTET VALUES
        lastRequests,
        cookeasyConversation,
        cookeasyConversationGPT,
        loadingState,
        loadingStateMenuCreation,
        // EXPORTED FUNCTIONS
        cleanConversation,
        startOrCreateConversation,
        createReciept,

    };

});


