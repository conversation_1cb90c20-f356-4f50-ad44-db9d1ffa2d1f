| ID   | Beschreibung                                                                 | Status | Zugehörige Anforderung ID | Erstellt am  | Erledigt am |
| :--- | :--------------------------------------------------------------------------- | :----- | :------------------------ | :----------- | :---------- |
| F-15 | Implementierung: Einkaufszettel-Historie im Seitenbereich (Blockiert: Seite /zettel lädt nicht korrekt - 500 Server Error) | todo   | REQ-101                   | 2025-05-22   |             |
| F-16 | Design: UI für Einkaufszettel-Historie                                       | todo   | REQ-101                   | 2025-05-22   |             |
| F-17 | Funktion: Aktivierung historischer Einkaufszettel mit zugehörigen Daten      | todo   | REQ-101                   | 2025-05-22   |             |
| F-18 | Implementierung: Kategorisierung von Einkaufszettel-Artikeln                 | todo   | REQ-102                   | 2025-05-22   |             |
| F-19 | UI: Gruppierte Anzeige von Einkaufsartikeln nach Kategorien                  | todo   | REQ-102                   | 2025-05-22   |             |