# FRNT_TASKS.md

## Übersicht

Aktuelle Entwicklungsaufgaben für das `ordy-frontend`.

| ID   | Beschreibung                                                                 | Status | Zugehörige Anforderung ID | Erstellt am  | Erledigt am |
| :--- | :--------------------------------------------------------------------------- | :----- | :------------------------ | :----------- | :---------- |
| F-1  | Setup Basis-Vue-App                                                          | done   | -                         | 2023-01-01   | 2023-01-02  |
| F-2  | Implementiere Login-Interface                                                | done   | Auth-2                    | 2023-10-26   | 2024-07-30  |
| F-3  | Implementiere Google OAuth Interface                                         | done   | Auth-4                    | 2024-07-26   | 2024-07-30  |
| F-4  | Implementiere Stytch Auth Interface                                          | done   | Auth-5                    | 2024-07-26   | 2024-07-30  |
| F-5  | CRUD Interface für Menüs                                                     | done   | Menu-X                    | 2024-07-26   | 2024-07-30  |
| F-6  | CRUD Interface für Einkaufslisten                                            | done   | Shopping-Y                | 2024-07-26   | 2024-07-30  |
| F-7  | CRUD Interface für Küchentische                                              | done   | KitchenTable-Z            | 2024-07-26   | 2024-07-30  |
| F-8  | CRUD Interface für Wochenpläne                                               | done   | Weekplan-A                | 2024-07-26   | 2024-07-30  |
| F-9  | Implementiere GPT Integration Interface                                      | done   | GPT-B                     | 2024-07-26   | 2024-07-30  |
| F-10 | Implementiere Stripe Integration Interface                                   | done   | Payment-C                 | 2024-07-26   | 2024-07-30  |
| F-11 | Implementiere File Upload Interface                                          | done   | File-D                    | 2024-07-26   | 2024-07-30  |
| F-12 | Implementiere Assistant Interface                                            | done   | Assistant-E               | 2024-07-26   | 2024-07-30  |

## PAYWALL ENHANCEMENT TASKS - DEZEMBER 2024

### Anforderung 1: Nutzungsüberwachung verbessern
| F-13 | Realtime API Nutzung in Abonnement-Seite hinzufügen                         | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |
| F-14 | UI-Namen für alle 4 Nutzungstypen optimieren                                | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |
| F-15 | Verfügbares Guthaben numerisch anzeigen                                     | done   | REQ-PAY-004               | 2024-12-20   | 2024-12-20  |

### Anforderung 2: Abo-Management Fehler beheben
| F-16 | 401 Error beim Abo-Wechsel analysieren und beheben                          | done   | REQ-PAY-003               | 2024-12-20   | 2024-12-20  |
| F-17 | Logout-Problem bei Abo-Änderungen beheben                                   | done   | REQ-PAY-003               | 2024-12-20   | 2024-12-20  |

### Anforderung 3: Profilbild-Management
| F-18 | Profilbild Upload-Interface implementieren                                  | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |
| F-19 | KI-Profilbild-Generator Interface implementieren                            | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |
| F-20 | Profilbild-Vorschau und Bearbeitung implementieren                          | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |

### Anforderung 4: Automatische Profilbild-Generierung
| F-21 | Automatische Profilbild-Generierung bei Registrierung                       | done   | REQ-PAY-006               | 2024-12-20   | 2024-12-20  |

### UI/UX Verbesserungen
| F-22 | Abonnement-Seite Layout für 4 Nutzungstypen optimieren                      | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |
| F-23 | Nutzungsbalken mit Guthaben-Anzeige erweitern                               | done   | REQ-PAY-004               | 2024-12-20   | 2024-12-20  |
| F-24 | Error Handling für Abo-Management verbessern                                | todo   | REQ-PAY-003               | 2024-12-20   |             |
