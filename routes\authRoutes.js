const express = require('express');
const authController = require('../controllers/authController');
const paymentController = require('../controllers/paymentController');

const router = express.Router();

/**
 * @openapi
 * /api/v1/auth/payment/checkout/retrival:
 *   get:
 *     tags:
 *       - Auth & Payment
 *     summary: Verarbeitet das Ergebnis einer Stripe Checkout Session.
 *     description: Wird von Stripe nach einer Zahlung aufgerufen (Success/Cancel URL).
 *     parameters:
 *       - in: query
 *         name: session_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Die von Stripe zurückgegebene Session-ID.
 *     responses:
 *       '302':
 *         description: Redirect zum Frontend nach Verarbeitung.
 *       '400':
 *         description: Fehler bei der Verarbeitung der Stripe-Session.
 */
router.get('/payment/checkout/retrival', paymentController.retrival)

// @desc    Stripe Webhook endpoint
// @route   POST auth/payment/stripe_webhook
//router.post('/payment/stripe_webhook', express.raw({ type: 'application/json' }), paymentController.stripewebhook)

////////////////////////////// AUTH ACTIVE BENITH ////////////////////////////////

/**
 * @openapi
 * /api/v1/auth/reg:
 *   get:
 *     tags:
 *       - Auth & Payment
 *     summary: Authentifiziert einen OAuth-Token (z.B. von Stytch).
 *     description: >
 *       Nimmt einen OAuth-Token als Query-Parameter entgegen, authentifiziert ihn bei Stytch,
 *       erstellt ggf. einen neuen Benutzer in der lokalen DB oder loggt einen bestehenden ein
 *       und leitet dann zum Frontend weiter, wobei Sitzungsinformationen als Query-Parameter übergeben werden.
 *     parameters:
 *       - in: query
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Der OAuth-Authentifizierungstoken.
 *     responses:
 *       '302':
 *         description: Redirect zum Frontend mit Sitzungsparametern.
 *       '500':
 *         description: Fehler bei der Stytch-Authentifizierung oder Benutzererstellung.
 */
router.get('/reg', authController.auth)

/**
 * @openapi
 * /api/v1/auth/verify:
 *   post:
 *     tags:
 *       - Auth & Payment
 *     summary: Verifiziert einen Stytch-Sitzungstoken (Middleware).
 *     description: >
 *       Nimmt einen Stytch-Token aus dem Authorization-Header entgegen,
 *       authentifiziert ihn bei Stytch und hängt bei Erfolg Benutzerdaten an das `req`-Objekt an.
 *       Wird typischerweise als Middleware vor geschützten Routen verwendet.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       description: Optionaler Request Body, der von nachfolgenden Handlern verwendet wird.
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       '200': # Oder eher der Status der nachfolgenden Route
 *         description: Token erfolgreich verifiziert, Anfrage wird weitergeleitet.
 *       '401':
 *         description: Token ungültig oder fehlt.
 */
router.post('/verify', authController.verify) // Hinweis: Dies ist eine Middleware

/**
 * @openapi
 * /api/v1/auth/logout:
 *   get:
 *     tags:
 *       - Auth & Payment
 *     summary: Loggt den Benutzer aus.
 *     description: >
 *       Versucht, die aktuelle Stytch-Sitzung (basierend auf dem Token im Authorization-Header) zu widerrufen.
 *       Gibt auch bei Fehlern (z.B. wenn Sitzung bereits ungültig) einen Erfolg zurück.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: Logout erfolgreich (Sitzung bei Stytch widerrufen oder war bereits ungültig).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean, example: true }
 *                 message: { type: string, example: "Logout success" }
 */
router.get('/logout', authController.logout)

/**
 * @openapi
 * /api/v1/auth/userbyid:
 *   post:
 *     tags:
 *       - Auth & Payment
 *     summary: Ruft Benutzerdaten nach dem Login/Seiten-Reload ab.
 *     description: Verifiziert den Token, holt die zugehörigen Benutzerdaten aus der lokalen DB.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       description: Der Request Body wird von der verify-Middleware befüllt.
 *       required: false
 *     responses:
 *       '201':
 *         description: Benutzerdaten erfolgreich abgerufen.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status: { type: string, example: success }
 *                 success: { type: boolean, example: true }
 *                 data:
 *                    type: object
 *                    properties:
 *                      user: { $ref: '#/components/schemas/User' } # Annahme User-Schema
 *       '401':
 *         description: Nicht autorisiert.
 */
router.post('/userbyid', authController.verify, authController.getUserAfterReload, authController.sendanswer)

/**
 * @openapi
 * /api/v1/auth/userdata/bysearchstring/{searchstring}:
 *   get:
 *     tags:
 *       - Auth & Payment
 *     summary: Sucht Benutzer nach E-Mail oder Vornamen.
 *     description: Sucht öffentlich zugängliche Benutzer (externalAccess=true) nach Teilen der E-Mail oder des Vornamens.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: searchstring
 *         required: true
 *         schema:
 *           type: string
 *         description: Der Suchbegriff.
 *     responses:
 *       '201':
 *         description: Benutzer gefunden.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean, example: true }
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       email: { type: string }
 *                       extId: { type: string }
 *                       _id: { type: string, format: objectId }
 *       '401':
 *         description: Nicht autorisiert.
 */
router.get('/userdata/bysearchstring/:searchstring', authController.verify, authController.getUserDataByString)

/**
 * @openapi
 * /api/v1/auth/userdata:
 *   post:
 *     tags:
 *       - Auth & Payment
 *     summary: Ruft Benutzerdaten ab.
 *     description: Holt die vollständigen Daten des aktuell authentifizierten Benutzers.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '201':
 *         description: Benutzerdaten erfolgreich abgerufen.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean, example: true }
 *                 data: { $ref: '#/components/schemas/User' }
 *       '401':
 *         description: Nicht autorisiert.
 */
router.post('/userdata', authController.verify, authController.getUserData)

/**
 * @openapi
 * /api/v1/auth/userdata/update:
 *   post:
 *     tags:
 *       - Auth & Payment
 *     summary: Aktualisiert Benutzerdaten.
 *     description: Aktualisiert spezifische Felder des authentifizierten Benutzers.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id: # Wird diese ID wirklich benötigt oder aus dem Token genommen?
 *                 type: string
 *                 format: objectId
 *               payload:
 *                 type: object
 *                 description: Objekt mit den zu aktualisierenden Feldern und Werten.
 *                 example: { "firstName": "NeuerName", "defaultKitchentable": "neueId" }
 *     responses:
 *       '200': # Update gibt oft 200 OK zurück
 *         description: Benutzerdaten erfolgreich aktualisiert.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean, example: true }
 *                 data: { type: object } # Enthält Infos über das Update (z.B. nModified)
 *       '400':
 *         description: Fehler beim Aktualisieren.
 *       '401':
 *         description: Nicht autorisiert.
 */
router.post('/userdata/update', authController.verify, authController.updateUserData)

/**
 * @openapi
 * /api/v1/auth/me:
 *   delete:
 *     tags:
 *       - Auth & Payment
 *     summary: Löscht den eigenen Benutzeraccount.
 *     description: Löscht den Account des aktuell authentifizierten Benutzers aus der lokalen Datenbank und bei Stytch.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '204':
 *         description: Account erfolgreich gelöscht.
 *       '401':
 *         description: Nicht autorisiert.
 *       '500':
 *         description: Fehler beim Löschen des Accounts (Stytch oder DB).
 */
router.delete('/me', authController.verify, authController.deleteMe);

/**
 * @openapi
 * /api/v1/auth/payment/checkout/{priceid}/{userid}:
 *   get:
 *     tags:
 *       - Auth & Payment
 *     summary: Initialisiert eine Stripe Checkout Session.
 *     description: Erstellt eine Stripe Checkout Session für das angegebene Produkt (priceid) und den Benutzer.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: priceid
 *         required: true
 *         schema:
 *           type: string
 *         description: Die Preis-ID des Stripe-Produkts.
 *       - in: path
 *         name: userid
 *         required: true
 *         schema:
 *           type: string
 *           format: objectId
 *         description: Die ID des Benutzers, für den die Zahlung ist.
 *     responses:
 *       '201':
 *         description: Stripe Session erfolgreich erstellt.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status: { type: string, example: success }
 *                 success: { type: boolean, example: true }
 *                 data:
 *                   type: object # Das Stripe Session Objekt
 *       '401':
 *         description: Nicht autorisiert.
 *       '500':
 *         description: Fehler beim Erstellen der Stripe Session.
 */
router.get('/payment/checkout/:priceid/:userid', authController.verify, paymentController.checkout, authController.sendanswer)

//https://8c73-213-55-233-118.ngrok-free.app/api/v1/auth/payment/stripe_webhook
///api/v1/auth/microsoftcallback
//router.get('/microsoftcallback', authController.microsoftcallback)

// Route to refresh Stytch session token
router.post('/session/refresh', authController.refreshSession);

module.exports = router;

// Benötigte Schemata (Referenz auf User)
/**
 * @openapi
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         _id: { type: string, format: objectId }
 *         extId: { type: string }
 *         extAuthService: { type: string }
 *         firstName: { type: string }
 *         lastName: { type: string }
 *         email: { type: string, format: email }
 *         img: { type: string, format: url }
 *         defaultKitchentable: { type: string, format: objectId }
 *         externalAccess: { type: boolean }
 *         # ... weitere User-Felder ...
 */