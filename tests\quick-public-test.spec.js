import { test, expect } from '@playwright/test';

test.describe('Quick Public Pages Test', () => {
  
  test('All public pages have consistent navigation and work correctly', async ({ page }) => {
    // Test Homepage
    await page.goto('/new-homepage');
    await expect(page.locator('nav a[href="/new-homepage"]')).toContainText('Startseite');
    await expect(page.locator('nav a[href="/rezepte"]')).toContainText('Rezepte');
    await expect(page.locator('nav a[href="/blog"]')).toContainText('Blog');
    await expect(page.locator('nav a[href="/about"]')).toContainText('Über uns');
    
    // Test Recipe Explorer
    await page.locator('nav a[href="/rezepte"]').click();
    await expect(page).toHaveURL('/rezepte');
    await expect(page.locator('h1')).toContainText('Kostenlose Rezepte entdecken');
    await expect(page.locator('nav a[href="/rezepte"]')).toHaveClass(/text-ordypurple-100/);
    
    // Test search functionality
    await page.fill('input[placeholder*="Nach Rezepten"]', 'Hähnchen');
    await expect(page.locator('input[placeholder*="Nach Rezepten"]')).toHaveValue('Hähnchen');
    
    // Test Blog
    await page.locator('nav a[href="/blog"]').click();
    await expect(page).toHaveURL('/blog');
    await expect(page.locator('h1')).toContainText('Ordy Blog');
    await expect(page.locator('nav a[href="/blog"]')).toHaveClass(/text-ordypurple-100/);
    
    // Verify no author names in articles
    const authorElements = page.locator('article .text-sm.font-medium.text-gray-900');
    await expect(authorElements).toHaveCount(0);
    
    // Test article click
    await page.locator('article').first().click();
    await expect(page.locator('text=Artikel:')).toBeVisible();
    await page.locator('button:has-text("OK")').click();
    
    // Test About page
    await page.locator('nav a[href="/about"]').click();
    await expect(page).toHaveURL('/about');
    await expect(page.locator('h1')).toContainText('Über Ordy');
    await expect(page.locator('nav a[href="/about"]')).toHaveClass(/text-ordypurple-100/);
    
    // Verify key sections exist
    await expect(page.locator('h2:has-text("Mission")')).toBeVisible();
    await expect(page.locator('h2:has-text("Werte")')).toBeVisible();
    await expect(page.locator('h2:has-text("Team")')).toBeVisible();
    
    // Test navigation back to homepage
    await page.locator('nav a[href="/new-homepage"]').click();
    await expect(page).toHaveURL('/new-homepage');
    await expect(page.locator('nav a[href="/new-homepage"]')).toHaveClass(/text-ordypurple-100/);
  });

  test('Mobile navigation works correctly', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/new-homepage');
    
    // Open mobile menu
    await page.locator('button[class*="md:hidden"]').click();
    await expect(page.locator('div[class*="md:hidden"]')).toBeVisible();
    
    // Test mobile navigation links
    await expect(page.locator('div[class*="md:hidden"] a[href="/new-homepage"]')).toContainText('Startseite');
    await expect(page.locator('div[class*="md:hidden"] a[href="/rezepte"]')).toContainText('Rezepte');
    await expect(page.locator('div[class*="md:hidden"] a[href="/blog"]')).toContainText('Blog');
    await expect(page.locator('div[class*="md:hidden"] a[href="/about"]')).toContainText('Über uns');
  });

  test('All CTA buttons are present and functional', async ({ page }) => {
    // Homepage CTAs
    await page.goto('/new-homepage');
    await expect(page.locator('button:has-text("Kostenlos starten")')).toBeVisible();
    await expect(page.locator('button:has-text("Familie anmelden")')).toBeVisible();
    
    // Recipe Explorer CTAs
    await page.goto('/rezepte');
    await expect(page.locator('a[href="/login"]:has-text("Kostenlos anmelden")')).toBeVisible();
    
    // About page CTAs
    await page.goto('/about');
    await expect(page.locator('a[href="/login"]:has-text("Jetzt ausprobieren")')).toBeVisible();
    await expect(page.locator('a[href="/login"]:has-text("Jetzt kostenlos anmelden")')).toBeVisible();
  });

  test('Footer links work on all pages', async ({ page }) => {
    const pages = ['/new-homepage', '/rezepte', '/blog', '/about'];
    
    for (const pagePath of pages) {
      await page.goto(pagePath);
      
      // Check footer exists and has correct links
      await expect(page.locator('footer')).toBeVisible();
      await expect(page.locator('footer a[href="/rezepte"]')).toBeVisible();
      await expect(page.locator('footer a[href="/blog"]')).toBeVisible();
      await expect(page.locator('footer a[href="/about"]')).toBeVisible();
      await expect(page.locator('footer a[href="/legal/agb"]')).toBeVisible();
      await expect(page.locator('footer a[href="/legal/privacy-policy"]')).toBeVisible();
    }
  });

  test('Blog functionality improvements', async ({ page }) => {
    await page.goto('/blog');
    
    // Test category filtering
    await page.locator('button:has-text("Nachhaltigkeit")').click();
    await expect(page.locator('button:has-text("Nachhaltigkeit")')).toHaveClass(/bg-ordypurple-100/);
    
    // Test newsletter subscription
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.locator('button:has-text("Abonnieren")').click();
    await expect(page.locator('text=Danke für dein Interesse')).toBeVisible();
    await page.locator('button:has-text("OK")').click();
    
    // Verify articles have longer, more detailed content
    const firstArticle = page.locator('article').first();
    const articleText = await firstArticle.locator('p').textContent();
    expect(articleText.length).toBeGreaterThan(200); // Ensure articles are more detailed
  });
});
