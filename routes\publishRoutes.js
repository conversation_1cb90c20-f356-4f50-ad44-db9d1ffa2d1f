const express = require('express');
const publishController = require('../controllers/publishController');
const authController = require('../controllers/authController'); // For protecting the route

const router = express.Router();

// Protect all routes in this router using verifyServiceClient
router.use(authController.verifyServiceClient);

// POST /api/v1/marketing/publish/now
router.post(
    '/now',
    // authController.verifyServiceToken, // REMOVED - Middleware is now router-level
    publishController.publishNow
);

// POST /api/v1/marketing/publish/test-pinterest
router.post(
    '/test-pinterest',
    publishController.testPinterestPublish
);

// POST /api/v1/marketing/publish/reset-pinterest
router.post(
    '/reset-pinterest',
    publishController.resetPinterestCounts
);

// POST /api/v1/marketing/publish/setup-pinterest
router.post(
    '/setup-pinterest',
    publishController.setupPinterestOAuth
);

// POST /api/v1/marketing/publish/test-tiktok
router.post(
    '/test-tiktok',
    publishController.testTikTokPublish
);

// POST /api/v1/marketing/publish/test-instagram
router.post(
    '/test-instagram',
    publishController.testInstagramPublish
);

module.exports = router;