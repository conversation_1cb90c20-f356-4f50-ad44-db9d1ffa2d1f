const { default: mongoose } = require('mongoose');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const Grocerylist = require('../models/grocerylistModel');
const Grocery = require('../models/groceryModel');
const Weekplan = require('../models/weekplanModel');
const AppError = require('../utils/appError');
const dayjs = require('dayjs');
require('dayjs/locale/de')


// @ GET one Grocerylist by ID
// /grocerylist/one/:id
exports.getOneGrocerylist = catchAsync(async (req, res, next) => {
  helper.devConsole("getOneGrocerylist in grocerylistController");
  const grocerylist = await Grocerylist.findById(req.params.id);
  if (!grocerylist) {
    return next(new AppError('Grocerylist not found', 404));
  }

  // next
  req.body.answerobject = grocerylist
  next()
});

// @ POST a new Grocerylist
// /grocerylist/one/:id
exports.createGrocerylist = catchAsync(async (req, res, next) => {
  helper.devConsole("createGrocerylist in grocerylistController");
  helper.devConsole(req.body.grocerylist);
  if(
    !req.body.grocerylist.kitchentableId ||
    !req.body.grocerylist.startDate ||
    !req.body.grocerylist.endDate
  ){
      next(new AppError('Not every data was given at grocerylistController.createGrocerylist', 500))
  }

  const grocerylist = await Grocerylist.create(req.body.grocerylist);

  // next
  req.body.answerobject = grocerylist
  next()
});

// @ PATCH update a Grocerylist by ID
// /grocerylist/one/:id
exports.updateGrocerylist = catchAsync(async (req, res, next) => {
  helper.devConsole("updateGrocerylist in grocerylistController");
  helper.devConsole(req.body)
  const updateObject = req.body;
  const grocerylist = await Grocerylist.findByIdAndUpdate(req.params.id, updateObject, {
    new: true,
    runValidators: true
  });
  if (!grocerylist) {
    return next(new AppError('Grocerylist not found', 404));
  }

  // next
  req.body.answerobject = grocerylist
  next()
});

// @ DELETE a Grocerylist by ID
// /grocerylist/one/:id
exports.deleteGrocerylist = catchAsync(async (req, res, next) => {
  helper.devConsole("deleteGrocerylist in grocerylistController");
  if(
    !req.params.id
  ){
      next(new AppError('Not every data was given at grocerylistController.deleteGrocerylist', 500))
  }

  const grocerylist = await Grocerylist.findByIdAndDelete(req.params.id);

  // next
  req.body.answerobject = grocerylist
  next()
});

//////////////////////////

// @ GET all Grocerylists
// /grocerylist/all/:kitchentableid
exports.getAllGrocerylistsByKitchentableId = catchAsync(async (req, res, next) => {
  helper.devConsole("getAllGrocerylistsByKitchentableId in grocerylistController");
  if(
    !req.params.kitchentableId
  ){
      next(new AppError('Not every data was given at grocerylistController.getAllGrocerylistsByKitchentableId', 500))
  }

  const grocerylists = await Grocerylist.find({
    "kitchentableId" : req.params.kitchentableId
  });

  // next
  req.body.answerobject = grocerylists
  next()
});



// @POST api/v1/all/grocerys/bykitchentableid
// Get One List with Grocerys
exports.getAllGrocerysByKitchentableId = catchAsync(async (req, res, next) => {
  helper.devConsole("getAllGrocerysByKitchentableId in grocerylistController");
  helper.devConsole(req.body)
  if(
    !req.body.grocerylist.kitchentableId ||
    !req.body.grocerylist.startDate ||
    !req.body.grocerylist.endDate
  ){
      next(new AppError('Not every data was given at grocerylistController.getAllGrocerysByKitchentableId', 500))
  }
  //getGrocerysByKitchentableId
  // Schritt 1: Finde alle Weekplans, die den Kriterien entsprechen
  const weekplans = await Weekplan.find({
    kitchentableId: req.body.grocerylist.kitchentableId,
    date: {
      $gte: req.body.grocerylist.startDate,
      $lte: req.body.grocerylist.endDate
    },
  }).populate({
    path: 'menuId',
    populate: [
      {
        path: 'menuchilds.menuChildId',
        model: 'MenuChild',
        populate: [
          {
            path: 'ingredients.unit',
            model: 'Unit'
          },
          {
            path: 'ingredients.name',
            model: 'Grocery'
          }
        ]
      },
      {
        path: 'users.userId',
        model: 'User'
      }
    ]
  });

  helper.devConsole("weekplans")
  //console.log(weekplans)

 // Schritt 2: Extrahiere alle menuIds und numberOfPersons
  const numberOfPersonsMap = {};
  weekplans.forEach(wp => {
    numberOfPersonsMap[wp._id.toString()] = wp.numberOfPersons;
  });

  // Initialisiere das Array für alle Ingredients
  const allIngredientsArray = [];

  // Schritt 3 und 4: Filtere alle Menus und extrahiere alle ingredients innerhalb der gefilterten menuchilds
  const results = weekplans.map(wp => {
    const menu = wp.menuId.toObject();
    const numberOfPersons = numberOfPersonsMap[wp._id.toString()];

    const filteredMenuchilds = menu.menuchilds.filter(mc => {
      // Überprüft, ob das Menü kind existiert und ob die Sitzplatzanzahl der Anzahl der Personen entspricht
      return mc.menuChildId && mc.menuChildId.seatCount === numberOfPersons;
    });

    // Extrahiere alle ingredients innerhalb der gefilterten menuchilds
    filteredMenuchilds.forEach(mc => {
      mc.menuChildId.ingredients.forEach(ingredient => {
        allIngredientsArray.push({
          amount: ingredient.amount,
          name: ingredient.name.name,  // assuming ingredient.name points to Grocery
          unit: ingredient.unit.name,   // assuming ingredient.unit points to Unit
          menuId: wp.menuId._id,
          menuchildsId: mc.menuChildId._id,
          numberOfPersons: wp.numberOfPersons,
          date: wp.date
        });
      });
    });

    // Aktualisiere das Menü im jeweiligen Weekplan
    const updatedMenu = { ...menu, menuchilds: filteredMenuchilds };

    // Formatiere das Datum
    dayjs.locale('de');
    const formattedDate = dayjs(wp.date).format('dddd, D. MMMM');

    // Update `wp`-Objekt mit dem formatierten Datum und aktualisiertem Menü
    return { ...wp.toObject(), date: formattedDate, menuId: updatedMenu };
  });

  //console.log(allIngredientsArray);

  // Füge das ingredientsArray an req.body.answerobject hinzu
  req.body.answerobject = allIngredientsArray;

  //
  next()
});

// @ POST One grocery to grocerylist
// /grocerylist/one/:id/grocerylistupdate
exports.updateGrocerylistItems = catchAsync(async (req, res, next) => {
  helper.devConsole("updateGrocerylistItems in grocerylistController");
  helper.devConsole(req.body)
  helper.devConsole(req.params)
  if(
    !req.params.id ||
    !req.body.grocerylist.switch ||
    !req.body.grocerylist.locked ||
    !req.body.grocerylist.groceryListPassive ||
    !req.body.grocerylist.groceryListActive
  ){
      next(new AppError('Not every data was given at grocerylistController.updateGrocerylistItems', 500))
  }

  // 1. Extrahiere das Dokument, um die zu entfernenden Elemente basierend auf den Indizes zu bestimmen
  const doc = await Grocerylist.findById(req.params.id);

  if (!doc) {
    throw new Error('Document not found');
  }


  helper.devConsole(req.body.grocerylist.groceryListActive)
  helper.devConsole(req.body.grocerylist.groceryListPassive)

  const updatedList = await Grocerylist.findByIdAndUpdate(
    req.params.id,
    { $set:
      { groceryListActive: req.body.grocerylist.groceryListActive,
        groceryListPassive: req.body.grocerylist.groceryListPassive,
        locked: req.body.grocerylist.locked
      }
    },
    { new: true }  // Dies gibt das aktualisierte Dokument zurück
  );


  // next
  req.body.answerobject = updatedList
  next()


});


/* OLD CONTROLLER HANDLERS
exports.loadGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("appAuth in grocerylistController")
  try{



    //use axios as you normally would, but specify httpsAgent in the config

    res.status(201).json({
      status: 'success',
      data: {
        data: "data will come"
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

//@POST /grocerylist/one/bykitchentableid/:userid
exports.loadGroceryListItemByUserId = catchAsync(async (req, res, next) => {
  helper.devConsole("grocerylist by userid in grocerylistController")
  try{

    helper.devConsole(req.params.id)

    const grocerylist = await Grocerylist.find({
      creatorId: req.params.id
      })
      .sort({ createdAt : -1})

    //console.log(grocerylist)

    res.status(201).json({
      status: 'success',
      data: {
        data: grocerylist
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});



//@POST /grocerylist/one/:id
exports.createGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("createGroceryListItem in grocerylistController")
  try{
    helper.devConsole()
    //load relations from the user
    const weekplan = await Grocerylist.create({menu: req.body.menuid, user: req.body.userid, date: req.body.date, phase: req.body.phase});
    helper.devConsole(weekplan)
    //load all menues by id

    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        data: weekplan
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});


// @POST /grocerylist/many
exports.saveManyGroceryListItems = catchAsync(async (req, res, next) => {
  try{
    helper.devConsole("drin")
    //console.log(req.body.menus)

    const groceryListObject = {}
    groceryListObject.relatedMenus = []
    groceryListObject.groceryListActive = []

    groceryListObject.creatorId = req.body.id
    groceryListObject.startDate = req.body.dates[0]
    groceryListObject.endDate = req.body.dates[1]

    for (let i = 0; i < req.body.menus.length; i++){
      for (let o = 0; o < req.body.menus[i].length; o++){
        // menu (weekplan)
        groceryListObject.relatedMenus.push(req.body.menus[i][o]._id)

        // grocerylist
        for (let u = 0; u < req.body.menus[i][o].menu.zutaten.length; u++){
          helper.devConsole("---------- hi ----------")
          helper.devConsole(req.body.menus[i][o].plannedSeats)
          helper.devConsole(req.body.menus[i][o].menu.zutaten[u].menge)
          helper.devConsole(req.body.menus[i][o].menu.Personen)

          // recalculate the amount of food which is to buy
          //1 / 2 = 0.5
          // plannedSeats / reciept Personen = factor
          req.body.menus[i][o].menu.zutaten[u].menge = req.body.menus[i][o].menu.zutaten[u].menge * (req.body.menus[i][o].plannedSeats / req.body.menus[i][o].menu.Personen)
          //req.body.menus[i][o].menu.zutaten[u].menge = req.body.menus[i][o].menu.zutaten[u].menge * req.body.menus[i][o].plannedSeats
          helper.devConsole(req.body.menus[i][o].menu.zutaten[u].menge)
          groceryListObject.groceryListActive.push(req.body.menus[i][o].menu.zutaten[u])
        }
      }
    }

    //console.log(groceryListObject)

    const grocerylist = await Grocerylist.create(groceryListObject);

    res.status(201).json({
      status: 'success',
      data: grocerylist
    });

  } catch(err) {
    helper.devConsole(err)

    res.status(401).json({
      status: 'error',
      data: err
    });
  }

})

// @POST /grocerylist/create


// @POST /search/many
exports.searchGroceryListItems = catchAsync(async (req, res, next) => {
  helper.devConsole("searchWeekplans in weekplanController")
  //if(req.body.userid == '' || req.body.date == ''){AppError('Bitte userid und date objekt mitliefern', 500)}
  try{
    //load relations from the user
    const weekplan = await Grocerylist.find({
      date: {
          $gte: req.body.dates[0],
          $lte: req.body.dates[1]
      },
      user: req.body.userid
    }).sort({ date : 1});

    helper.devConsole(weekplan)

    // Send success response
    res.status(201).json({
      status: 'success',
      data: {
        weekplan
      }
    });

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});

// @patch /grocerylist/one/:id
exports.updateGroceryListItem = catchAsync(async (req, res, next) => {
  helper.devConsole("updateGroceryListItem in grocerylistController")
  //helper.devConsole(req.params)
  try{
    if(req.params.id){
      //load relations from the user
      const updateObject = req.body;
      const groceryListItem = await Grocerylist.updateOne({_id: req.params.id}, {$set: updateObject});
      helper.devConsole(groceryListItem)
      //load all menues by id

      // Send success response
      res.status(201).json({
        status: 'success',
        data: {
          data: groceryListItem
        }
      });

    }

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});


exports.deleteGroceryList = catchAsync(async (req, res, next) => {
  helper.devConsole("deleteWeekplan in weekplanController")
  helper.devConsole(req.params)
  try{
    if(req.params.id){
      //load relations from the user
      const grocerylistDelete = await Grocerylist.deleteOne({ "_id" : req.params.id });
      //console.log(grocerylistDelete)
      //load all menues by id

      // Send success response
      res.status(201).json({
        status: 'success',
        data: {
          data: grocerylistDelete
        }
      });

    }

  } catch(error){
    helper.devConsole(error)
    // Send response
    res.status(401).json({
      status: 'error',
      data: error
    });
  }

});
*/

// @ GET Search for grocery/ingredient by name
// /grocerylist/search/grocery/:name
exports.searchGroceryByName = catchAsync(async (req, res, next) => {
  helper.devConsole("searchGroceryByName in grocerylistController");

  const searchName = req.params.name;
  if (!searchName || !searchName.trim()) {
    return next(new AppError('Grocery name is required', 400));
  }

  try {
    // Search for grocery by name (case-insensitive, partial match)
    function escapeRegex(text) {
      return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    }

    const safeSearchTerm = escapeRegex(searchName.trim());

    // Search for exact match first, then partial matches
    const exactMatch = await Grocery.findOne({
      name: { $regex: `^${safeSearchTerm}$`, $options: 'i' }
    });

    if (exactMatch) {
      helper.devConsole(`Found exact match for "${searchName}":`, exactMatch.name);
      req.body.answerobject = exactMatch;
      return next();
    }

    // If no exact match, search for partial matches
    const partialMatches = await Grocery.find({
      name: { $regex: safeSearchTerm, $options: 'i' }
    }).limit(5); // Limit to 5 results

    if (partialMatches.length > 0) {
      helper.devConsole(`Found ${partialMatches.length} partial matches for "${searchName}"`);
      // Return the first partial match
      req.body.answerobject = partialMatches[0];
      return next();
    }

    // No matches found
    helper.devConsole(`No grocery found for "${searchName}"`);
    req.body.answerobject = null;
    next();

  } catch (error) {
    helper.devConsole(`Error searching for grocery "${searchName}":`, error);
    return next(new AppError('Error searching for grocery', 500));
  }
});