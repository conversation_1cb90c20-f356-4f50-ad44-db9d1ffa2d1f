<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold mb-4">Marketing-Workflow</h2>
    
    <div class="mb-6">
      <p class="text-gray-700 mb-2">
        Dieser Workflow erstellt automatisch Marketing-Inhalte für soziale Medien:
      </p>
      <ul class="list-disc pl-5 text-gray-600 mb-4">
        <li>Generiert einen Marketing-Text mit KI</li>
        <li>Wählt ein zufälliges Rezept aus</li>
        <li>Erstellt ein Video</li>
        <li><PERSON><PERSON><PERSON> das Video auf S3 hoch</li>
      </ul>
      <p class="text-sm text-gray-500 italic">
        Hinweis: Der Prozess kann einige Minuten dauern. Sie werden benachrichtigt, sobald er abgeschlossen ist.
      </p>
    </div>
    
    <div class="flex flex-col space-y-4">
      <button 
        @click="triggerWorkflow" 
        class="bg-ordypurple-100 text-white py-2 px-4 rounded-md hover:bg-ordypurple-200 transition-colors"
        :disabled="isLoading"
      >
        <span v-if="isLoading">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Workflow wird ausgeführt...
        </span>
        <span v-else>Marketing-Workflow starten</span>
      </button>
      
      <button 
        v-if="marketingContentId" 
        @click="goToUploadPage" 
        class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
      >
        Video auf soziale Medien hochladen
      </button>
    </div>
    
    <div v-if="marketingContentId" class="mt-6 p-4 bg-gray-50 rounded-md">
      <h3 class="font-medium text-gray-800 mb-2">Workflow-Status</h3>
      <p class="text-sm text-gray-600">Content-ID: {{ marketingContentId }}</p>
      <p class="text-sm text-gray-600" v-if="videoUrl">
        Video-URL: <a :href="videoUrl" target="_blank" class="text-blue-600 hover:underline">{{ videoUrl }}</a>
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import { useHelperStore } from '../../utils/helper';

const router = useRouter();
const { setNotification } = useNotification();
const helper = useHelperStore();

const isLoading = ref(false);
const marketingContentId = ref(null);
const videoUrl = ref(null);

// Funktion zum Auslösen des Marketing-Workflows
const triggerWorkflow = async () => {
  isLoading.value = true;
  
  try {
    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-workflow/trigger`,
      {},
      {
        timeout: 60000 // 60 Sekunden Timeout, da der Prozess länger dauern kann
      }
    );
    
    if (response.data && response.data.marketingContentId) {
      marketingContentId.value = response.data.marketingContentId;
      setNotification('Marketing-Workflow erfolgreich gestartet!', 'success');
      
      // Starte Polling, um den Status des Videos zu überprüfen
      checkVideoStatus();
    } else {
      setNotification('Marketing-Workflow gestartet, aber keine Content-ID erhalten.', 'info');
    }
  } catch (error) {
    helper.devConsole('Fehler beim Starten des Marketing-Workflows:', error);
    setNotification('Fehler beim Starten des Marketing-Workflows. Bitte versuchen Sie es später erneut.', 'alert');
  } finally {
    isLoading.value = false;
  }
};

// Funktion zum Überprüfen des Video-Status
const checkVideoStatus = async () => {
  if (!marketingContentId.value) return;
  
  try {
    const response = await axios.get(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-content/${marketingContentId.value}`
    );
    
    if (response.data && response.data.data && response.data.data.videoS3Url) {
      videoUrl.value = response.data.data.videoS3Url;
      setNotification('Video wurde erfolgreich erstellt!', 'success');
    } else {
      // Wenn das Video noch nicht verfügbar ist, erneut prüfen nach 30 Sekunden
      setTimeout(checkVideoStatus, 30000);
    }
  } catch (error) {
    helper.devConsole('Fehler beim Überprüfen des Video-Status:', error);
  }
};

// Funktion zum Navigieren zur Upload-Seite
const goToUploadPage = () => {
  router.push(`/marketing/upload?contentId=${marketingContentId.value}`);
};
</script>
