const menuchildController = require('../../controllers/menuchildController');
const stableIdManager = require('../../utils/stableIdManager');

console.log('🧪 Starting API Integration Tests...\n');

// Mock request and response objects
function createMockReq(body, params = {}) {
  return {
    body,
    params,
    userId: 'test-user-id'
  };
}

function createMockRes() {
  return {
    status: function(code) {
      this.statusCode = code;
      return this;
    },
    json: function(data) {
      this.data = data;
      return this;
    }
  };
}

function createMockNext() {
  return function(error) {
    if (error) {
      console.error('❌ Next called with error:', error.message);
      throw error;
    }
  };
}

// Test 1: StableID-Zuweisung bei MenuChild-Update
console.log('📋 Test 1: StableID-Zuweisung bei MenuChild-Update');

// Simuliere ein MenuChild-Update mit neuen Zutaten
const mockMenuChild = {
  ingredients: [
    { name: '<PERSON>hl', amount: 500, unit: 'g' }, // Neue Zutat ohne stableId
    { name: 'Milch', amount: 250, unit: 'ml', stableId: 2 }, // Bestehende Zutat
    { name: 'Eier', amount: 2, unit: 'Stück' } // Neue Zutat ohne stableId
  ],
  maxUsedStableId: 2
};

// Teste die StableID-Logik direkt
try {
  const { updatedIngredients, newMaxUsedId } = stableIdManager.assignStableIds(
    mockMenuChild.ingredients, 
    mockMenuChild.maxUsedStableId
  );
  
  console.log('✅ StableID assignment completed');
  console.log('📊 Results:');
  updatedIngredients.forEach((ingredient, index) => {
    console.log(`   ${index + 1}. ${ingredient.name} -> stableId: ${ingredient.stableId}`);
  });
  console.log(`   New maxUsedStableId: ${newMaxUsedId}`);
  
  // Validiere Ergebnisse
  if (updatedIngredients[0].stableId === 3 && 
      updatedIngredients[1].stableId === 2 && 
      updatedIngredients[2].stableId === 4 &&
      newMaxUsedId === 4) {
    console.log('✅ Test 1 PASSED: StableID assignment works correctly\n');
  } else {
    console.error('❌ Test 1 FAILED: Unexpected stableId assignments\n');
  }
} catch (error) {
  console.error('❌ Test 1 FAILED:', error.message, '\n');
}

// Test 2: Validierung der StableID-Konsistenz
console.log('📋 Test 2: StableID-Konsistenz-Validierung');

try {
  const validMenuChild = {
    ingredients: [
      { name: 'Mehl', stableId: 1 },
      { name: 'Milch', stableId: 2 },
      { name: 'Eier', stableId: 3 }
    ],
    maxUsedStableId: 3
  };
  
  const validation = stableIdManager.validateStableIds(validMenuChild);
  
  if (validation.isValid && validation.errors.length === 0) {
    console.log('✅ Test 2 PASSED: Valid MenuChild passes validation\n');
  } else {
    console.error('❌ Test 2 FAILED: Valid MenuChild failed validation\n');
  }
} catch (error) {
  console.error('❌ Test 2 FAILED:', error.message, '\n');
}

// Test 3: Fehlerhafte StableID-Erkennung
console.log('📋 Test 3: Fehlerhafte StableID-Erkennung');

try {
  const invalidMenuChild = {
    ingredients: [
      { name: 'Mehl', stableId: 1 },
      { name: 'Milch', stableId: 1 }, // Duplikat!
      { name: 'Eier', stableId: 3 }
    ],
    maxUsedStableId: 3
  };
  
  const validation = stableIdManager.validateStableIds(invalidMenuChild);
  
  if (!validation.isValid && validation.errors.length > 0) {
    console.log('✅ Test 3 PASSED: Invalid MenuChild correctly detected');
    console.log('📊 Detected errors:', validation.errors);
    console.log('');
  } else {
    console.error('❌ Test 3 FAILED: Invalid MenuChild not detected\n');
  }
} catch (error) {
  console.error('❌ Test 3 FAILED:', error.message, '\n');
}

// Test 4: Zutaten-Hinzufügung
console.log('📋 Test 4: Zutaten-Hinzufügung mit StableID');

try {
  const existingIngredients = [
    { name: 'Mehl', stableId: 1 },
    { name: 'Milch', stableId: 2 }
  ];
  
  const newIngredient = { name: 'Zucker', amount: 100, unit: 'g' };
  
  const result = stableIdManager.addIngredientWithStableId(
    existingIngredients, 
    newIngredient, 
    2
  );
  
  if (result.updatedIngredients.length === 3 && 
      result.assignedStableId === 3 && 
      result.newMaxUsedId === 3) {
    console.log('✅ Test 4 PASSED: Ingredient addition with StableID works');
    console.log(`📊 Added "${newIngredient.name}" with stableId: ${result.assignedStableId}\n`);
  } else {
    console.error('❌ Test 4 FAILED: Unexpected results from ingredient addition\n');
  }
} catch (error) {
  console.error('❌ Test 4 FAILED:', error.message, '\n');
}

// Test 5: Zutaten-Entfernung
console.log('📋 Test 5: Zutaten-Entfernung mit StableID-Erhaltung');

try {
  const ingredients = [
    { name: 'Mehl', stableId: 1 },
    { name: 'Milch', stableId: 2 },
    { name: 'Eier', stableId: 3 },
    { name: 'Zucker', stableId: 4 }
  ];
  
  // Entferne Zutat an Index 1 (Milch mit stableId 2)
  const result = stableIdManager.removeIngredientKeepStableIds(ingredients, 1, 4);
  
  if (result.updatedIngredients.length === 3 && 
      result.removedStableId === 2 &&
      result.updatedIngredients[0].stableId === 1 &&
      result.updatedIngredients[1].stableId === 3 &&
      result.updatedIngredients[2].stableId === 4) {
    console.log('✅ Test 5 PASSED: Ingredient removal preserves other stableIds');
    console.log(`📊 Removed ingredient with stableId: ${result.removedStableId}`);
    console.log('📊 Remaining stableIds:', result.updatedIngredients.map(i => i.stableId));
    console.log('');
  } else {
    console.error('❌ Test 5 FAILED: Unexpected results from ingredient removal\n');
  }
} catch (error) {
  console.error('❌ Test 5 FAILED:', error.message, '\n');
}

console.log('🎉 All API Integration tests completed!');
console.log('');
console.log('📋 Summary:');
console.log('✅ StableID assignment for new ingredients');
console.log('✅ StableID preservation for existing ingredients');
console.log('✅ StableID validation and error detection');
console.log('✅ Ingredient addition with automatic stableId');
console.log('✅ Ingredient removal with stableId preservation');
console.log('');
console.log('🚀 Backend StableID-System is ready for production!');
