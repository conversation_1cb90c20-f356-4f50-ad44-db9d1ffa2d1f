/**
 * Skript zur Kategorisierung bestehender Einkaufsartikel
 * 
 * Führe dieses Skript aus, um alle bestehenden Einkaufsartikel zu kategorisieren.
 * Verwendung: node scripts/categorizeExistingItems.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Lade Umgebungsvariablen
dotenv.config({ path: './.env' });

// Verbinde mit der Datenbank
mongoose.connect(process.env.DATABASE_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('Datenbankverbindung hergestellt');
}).catch(err => {
  console.error('Fehler bei der Datenbankverbindung:', err);
  process.exit(1);
});

// Lade Modelle
const ShoppingListItem = require('../models/shoppingListItem');

// Hilfsfunktion zur Kategorisierung von Artikeln
const categorizeItem = (name) => {
  name = name.toLowerCase();
  
  // Gemüse & Früchte
  if (/apfel|birne|banane|orange|zitrone|tomate|gurke|salat|karotte|zwiebel|knoblauch|paprika|broccoli|spinat|kohl|pilz/i.test(name)) {
    return 'Gemüse & Früchte';
  }
  
  // Brotwaren & Backwaren
  if (/brot|brötchen|gipfeli|kuchen|sandwich|toast|croissant|bagel|wecken|zopf/i.test(name)) {
    return 'Brotwaren & Backwaren';
  }
  
  // Milchprodukte & Molkereiprodukte
  if (/milch|joghurt|butter|rahm|käse|quark|sahne|schmand|frischkäse|mozzarella|parmesan/i.test(name)) {
    return 'Milchprodukte & Molkereiprodukte';
  }
  
  // Fleisch, Wurst & Fisch
  if (/fleisch|wurst|schinken|speck|fisch|lachs|thunfisch|hähnchen|rind|schwein|lamm|salami/i.test(name)) {
    return 'Fleisch, Wurst & Fisch';
  }
  
  // Tiefkühlprodukte
  if (/tiefkühl|tiefgekühlt|glacé|eis|pizza|tk/i.test(name)) {
    return 'Tiefkühlprodukte';
  }
  
  // Grundnahrungsmittel
  if (/mehl|zucker|salz|öl|essig|reis|pasta|nudel|spaghetti|konserve|dose|tomatenmark|gewürz/i.test(name)) {
    return 'Grundnahrungsmittel';
  }
  
  // Frühstück & Cerealien
  if (/müsli|cornflakes|flocken|marmelade|honig|nutella|aufstrich|sirup/i.test(name)) {
    return 'Frühstück & Cerealien';
  }
  
  // Süsswaren & Snacks
  if (/schokolade|bonbon|chips|nüsse|riegel|keks|süssigkeit|snack|popcorn/i.test(name)) {
    return 'Süsswaren & Snacks';
  }
  
  // Getränke
  if (/wasser|saft|bier|wein|getränk|cola|fanta|sprite|kaffee|tee|milch/i.test(name)) {
    return 'Getränke';
  }
  
  // Non-Food & Haushaltsartikel
  if (/putzmittel|waschmittel|seife|shampoo|toilettenpapier|küchenpapier|serviette|zahnpasta/i.test(name)) {
    return 'Non-Food & Haushaltsartikel';
  }
  
  // Fallback
  return 'Sonstiges';
};

// Hauptfunktion zur Kategorisierung aller Artikel
async function categorizeAllItems() {
  try {
    console.log('Starte Kategorisierung bestehender Einkaufsartikel...');
    
    // Finde alle Artikel ohne Kategorie
    const items = await ShoppingListItem.find({ category: { $exists: false } });
    console.log(`${items.length} Artikel ohne Kategorie gefunden.`);
    
    let updated = 0;
    for (const item of items) {
      const category = categorizeItem(item.name);
      item.category = category;
      await item.save();
      updated++;
      
      if (updated % 100 === 0) {
        console.log(`${updated} von ${items.length} Artikeln kategorisiert...`);
      }
    }
    
    console.log(`Kategorisierung abgeschlossen. ${updated} Artikel wurden kategorisiert.`);
    process.exit(0);
  } catch (error) {
    console.error('Fehler bei der Kategorisierung:', error);
    process.exit(1);
  }
}

// Führe die Kategorisierung aus
categorizeAllItems();