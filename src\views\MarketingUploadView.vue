<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Video auf soziale Medien hochladen</h1>
    
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-ordypurple-100"></div>
    </div>
    
    <div v-else-if="!contentData" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <p class="text-red-600">Kein gültiger Content gefunden. Bitte stellen Sie sicher, dass Sie eine gültige Content-ID angegeben haben.</p>
      <button 
        @click="goToTriggerPage" 
        class="mt-4 bg-ordypurple-100 text-white py-2 px-4 rounded-md hover:bg-ordypurple-200 transition-colors"
      >
        Zurück zum Marketing-Workflow
      </button>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Linke Spalte: Video-Vorschau und Inhaltsinformationen -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Video-Vorschau</h2>
        
        <div v-if="contentData.videoS3Url" class="mb-6">
          <video 
            :src="contentData.videoS3Url" 
            controls 
            class="w-full h-auto rounded-md"
          ></video>
        </div>
        <div v-else class="bg-gray-100 rounded-md p-4 mb-6 text-center">
          <p class="text-gray-500">Video wird noch generiert...</p>
        </div>
        
        <div class="mb-4">
          <h3 class="font-medium text-gray-800 mb-2">Rezept</h3>
          <p class="text-gray-700">{{ contentData.recipeName }}</p>
        </div>
        
        <div class="mb-4">
          <h3 class="font-medium text-gray-800 mb-2">Beschreibung</h3>
          <p class="text-gray-700">{{ contentData.socialMediaTextOhneLink || contentData.textOne }}</p>
        </div>
      </div>
      
      <!-- Rechte Spalte: Upload-Optionen -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Auf soziale Medien hochladen</h2>
        
        <div class="space-y-4">
          <!-- Instagram -->
          <div class="border border-gray-200 rounded-md p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <svg class="w-6 h-6 mr-2 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
                <span class="font-medium">Instagram</span>
              </div>
              <span v-if="contentData.published_insta" class="text-green-600 text-sm">
                Bereits veröffentlicht
              </span>
              <button 
                v-else
                @click="publishToSocialMedia('instagram')" 
                :disabled="publishingPlatform === 'instagram'"
                class="bg-ordypurple-100 text-white py-1 px-3 rounded-md hover:bg-ordypurple-200 transition-colors text-sm"
              >
                <span v-if="publishingPlatform === 'instagram'">Wird hochgeladen...</span>
                <span v-else>Jetzt veröffentlichen</span>
              </button>
            </div>
          </div>
          
          <!-- Facebook -->
          <div class="border border-gray-200 rounded-md p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span class="font-medium">Facebook</span>
              </div>
              <span v-if="contentData.published_fb" class="text-green-600 text-sm">
                Bereits veröffentlicht
              </span>
              <button 
                v-else
                @click="publishToSocialMedia('facebook')" 
                :disabled="publishingPlatform === 'facebook'"
                class="bg-ordypurple-100 text-white py-1 px-3 rounded-md hover:bg-ordypurple-200 transition-colors text-sm"
              >
                <span v-if="publishingPlatform === 'facebook'">Wird hochgeladen...</span>
                <span v-else>Jetzt veröffentlichen</span>
              </button>
            </div>
          </div>
          
          <!-- TikTok -->
          <div class="border border-gray-200 rounded-md p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <svg class="w-6 h-6 mr-2 text-black" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                </svg>
                <span class="font-medium">TikTok</span>
              </div>
              <span v-if="contentData.published_tiktok" class="text-green-600 text-sm">
                Bereits veröffentlicht
              </span>
              <button 
                v-else
                @click="publishToSocialMedia('tiktok')" 
                :disabled="publishingPlatform === 'tiktok'"
                class="bg-ordypurple-100 text-white py-1 px-3 rounded-md hover:bg-ordypurple-200 transition-colors text-sm"
              >
                <span v-if="publishingPlatform === 'tiktok'">Wird hochgeladen...</span>
                <span v-else>Jetzt veröffentlichen</span>
              </button>
            </div>
          </div>
          
          <!-- Pinterest -->
          <div class="border border-gray-200 rounded-md p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <svg class="w-6 h-6 mr-2 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.162-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.401.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.354-.629-2.758-1.379l-.749 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.607 0 11.985-5.365 11.985-11.987C23.97 5.39 18.592.026 11.985.026L12.017 0z"/>
                </svg>
                <span class="font-medium">Pinterest</span>
              </div>
              <span v-if="contentData.published_pinterest" class="text-green-600 text-sm">
                Bereits veröffentlicht
              </span>
              <button 
                v-else
                @click="publishToSocialMedia('pinterest')" 
                :disabled="publishingPlatform === 'pinterest'"
                class="bg-ordypurple-100 text-white py-1 px-3 rounded-md hover:bg-ordypurple-200 transition-colors text-sm"
              >
                <span v-if="publishingPlatform === 'pinterest'">Wird hochgeladen...</span>
                <span v-else>Jetzt veröffentlichen</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import useNotification from '../../modules/notificationInformation';
import { useHelperStore } from '../../utils/helper';

const route = useRoute();
const router = useRouter();
const { setNotification } = useNotification();
const helper = useHelperStore();

const contentId = ref(route.query.contentId);
const contentData = ref(null);
const isLoading = ref(true);
const publishingPlatform = ref(null);

// Beim Laden der Komponente den Content laden
onMounted(async () => {
  if (!contentId.value) {
    isLoading.value = false;
    return;
  }
  
  await loadContentData();
});

// Funktion zum Laden der Content-Daten
const loadContentData = async () => {
  isLoading.value = true;
  
  try {
    const response = await axios.get(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-content/${contentId.value}`
    );
    
    if (response.data && response.data.data) {
      contentData.value = response.data.data;
    } else {
      setNotification('Fehler beim Laden der Content-Daten.', 'alert');
    }
  } catch (error) {
    helper.devConsole('Fehler beim Laden der Content-Daten:', error);
    setNotification('Fehler beim Laden der Content-Daten.', 'alert');
  } finally {
    isLoading.value = false;
  }
};

// Funktion zum Veröffentlichen auf sozialen Medien
const publishToSocialMedia = async (platform) => {
  if (!contentData.value || !contentData.value.videoS3Url) {
    setNotification('Kein Video zum Hochladen verfügbar.', 'alert');
    return;
  }
  
  publishingPlatform.value = platform;
  
  try {
    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-content/publish`,
      {
        contentId: contentId.value,
        platform: platform
      }
    );
    
    if (response.data && response.data.success) {
      setNotification(`Erfolgreich auf ${getPlatformName(platform)} veröffentlicht!`, 'success');
      // Aktualisiere die Content-Daten
      await loadContentData();
    } else {
      setNotification(`Fehler beim Veröffentlichen auf ${getPlatformName(platform)}.`, 'alert');
    }
  } catch (error) {
    helper.devConsole(`Fehler beim Veröffentlichen auf ${platform}:`, error);
    setNotification(`Fehler beim Veröffentlichen auf ${getPlatformName(platform)}.`, 'alert');
  } finally {
    publishingPlatform.value = null;
  }
};

// Hilfsfunktion für Plattformnamen
const getPlatformName = (platform) => {
  const names = {
    instagram: 'Instagram',
    facebook: 'Facebook',
    tiktok: 'TikTok',
    pinterest: 'Pinterest'
  };
  
  return names[platform] || platform;
};

// Funktion zum Navigieren zur Trigger-Seite
const goToTriggerPage = () => {
  router.push('/marketing');
};
</script>
