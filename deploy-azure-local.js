/**
 * Azure Local Deployment Simulation Script
 * 
 * Dieses Skript kombiniert alle Schritte, um ein lokales Azure-Deployment zu simulieren:
 * 1. Überprüfen der Umgebungsvariablen
 * 2. Erstellen der erforderlichen Dateien
 * 3. Build-Prozess
 * 4. Starten der Anwendung
 * 5. Testen der Anwendung
 */

'use strict';

const { execSync, spawn } = require('child_process');
const fs = require('fs');
// Path-Modul wird importiert aber nicht verwendet
// const path = require('path');

// Konfiguration
const ENV = process.argv[2] || 'preview'; // 'preview' oder 'production'
if (!['preview', 'production'].includes(ENV)) {
  console.error('Fehler: Umgebung muss entweder "preview" oder "production" sein.');
  process.exit(1);
}

console.log(`\n🚀 Starting Azure local deployment simulation for ${ENV} environment\n`);

// 1. Umgebungsvariablen überprüfen
console.log('🔍 Checking environment variables...');
try {
  execSync(`node azure-env-validator.js`, { stdio: 'inherit' });
} catch (error) {
  console.error('Environment validation failed:', error.message);
  process.exit(1);
}

// 2. web.config überprüfen
if (!fs.existsSync('./web.config')) {
  console.log('ℹ️ web.config file not found. Creating it...');
  
  const webConfigContent = `<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <webSocket enabled="false" />
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <rule name="StaticContent">
          <action type="Rewrite" url="public{REQUEST_URI}"/>
        </rule>
        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="server.js"/>
        </rule>
      </rules>
    </rewrite>
    <security>
      <requestFiltering removeServerHeader="true">
        <hiddenSegments>
          <remove segment="bin"/>
        </hiddenSegments>
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
      </customHeaders>
    </httpProtocol>
    <httpErrors existingResponse="PassThrough" />
  </system.webServer>
</configuration>`;
  
  fs.writeFileSync('./web.config', webConfigContent);
  console.log('✅ web.config file created.');
}

// 3. Abhängigkeiten installieren
console.log('\n📦 Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully.');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// 4. Anwendung starten in einem separaten Prozess
console.log('\n🚀 Starting the application in Azure-like environment...');
console.log(`Environment: ${ENV}`);

let appProcess;

try {
  // Umgebungsvariablen für den Kindprozess
  const envVars = {
    ...process.env,
    NODE_ENV: ENV,
    WEBSITE_SITE_NAME: ENV === 'production' ? 'ordy-prd' : 'ordy-tst',
    WEBSITE_HOSTNAME: ENV === 'production' ? 'ordy-prd.azurewebsites.net' : 'ordy-tst-d8cfc0bzchbqd9ha.switzerlandnorth-01.azurewebsites.net',
    WEBSITE_INSTANCE_ID: 'local-simulation'
  };
  
  // Server in einem separaten Prozess starten
  appProcess = spawn('node', ['server.js'], { 
    env: envVars,
    stdio: 'inherit'
  });
  
  // Event-Handler für den Prozess
  appProcess.on('error', (err) => {
    console.error('❌ Failed to start application:', err.message);
    process.exit(1);
  });
  
  // Warten, um sicherzustellen, dass der Server startet
  console.log('⏳ Waiting for server to start...');
  setTimeout(() => {
    // 5. Tests ausführen in einem separaten Prozess
    console.log('\n🧪 Running tests against the deployed application...');
    try {
      execSync('node azure-test-app.js', { stdio: 'inherit' });
      console.log('\n✅ Local Azure deployment simulation completed successfully.');
    } catch (error) {
      console.error('\n❌ Tests failed:', error.message);
    } finally {
      // Cleanup: App-Prozess beenden
      console.log('\n🧹 Cleaning up...');
      if (appProcess) {
        appProcess.kill();
      }
      console.log('✅ Application process terminated.');
    }
  }, 5000); // 5 Sekunden warten
  
} catch (error) {
  console.error('❌ Error during deployment simulation:', error.message);
  if (appProcess) {
    appProcess.kill();
  }
  process.exit(1);
}

// Event-Handler für das Hauptskript
process.on('SIGINT', () => {
  console.log('\n👋 Received termination signal. Cleaning up...');
  if (appProcess) {
    appProcess.kill();
  }
  process.exit(0);
}); 