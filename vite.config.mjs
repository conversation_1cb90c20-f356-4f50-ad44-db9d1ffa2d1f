import { fileURLToPath, URL } from 'node:url'
//import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { resolve } from 'path';

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite';
import vueDevTools from 'vite-plugin-vue-devtools'
import { VitePWA } from 'vite-plugin-pwa'

// Read package.json
const __dirname = fileURLToPath(new URL('.', import.meta.url));
const packageJson = JSON.parse(readFileSync(resolve(__dirname, 'package.json'), 'utf-8'));

export default ({ mode }) => {
    const env = loadEnv(mode, process.cwd());

    // import.meta.env.VITE_NAME available here with: process.env.VITE_NAME
    // import.meta.env.VITE_PORT available here with: process.env.VITE_PORT

    return defineConfig({
      plugins: [
        vue(),
        tailwindcss(),
        VitePWA({
          registerType: 'prompt',
          includeAssets: ['favicon.ico', 'robots.txt', 'apple-touch-icon.png'],
          manifest: false, // Wir verwenden die externe manifest.json
          injectRegister: 'auto',
          devOptions: {
            enabled: true
          },
          strategies: 'generateSW',
          // Wir verwenden die einfachere generateSW-Strategie statt injectManifest
          // um Probleme mit Dateipfaden zu vermeiden
          workbox: {
            globPatterns: ['**/*.{js,css,html,ico,png,svg,jpg,jpeg,gif,webp,woff,woff2,ttf,eot}'],
            navigateFallback: 'index.html',
            navigateFallbackDenylist: [/^\/api/, /^\/admin/],
            skipWaiting: true,
            clientsClaim: true,
            cleanupOutdatedCaches: true,
            maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5 MB
            // Eindeutige Cache-ID für jedes Deployment
            cacheId: 'ordy-' + Date.now().toString(),
            runtimeCaching: [
              {
                urlPattern: /^https:\/\/([a-z0-9-]+\.)?ordy\.ch/,
                handler: 'NetworkFirst',
                options: {
                  networkTimeoutSeconds: 5,
                  cacheName: 'api-cache',
                  expiration: {
                    maxEntries: 50,
                    maxAgeSeconds: 1 * 24 * 60 * 60, // 1 Tag statt 30 Tage
                  },
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                },
              },
              {
                urlPattern: /\.(?:png|jpg|jpeg|svg|gif)$/,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'images-cache',
                  expiration: {
                    maxEntries: 100,
                    maxAgeSeconds: 30 * 24 * 60 * 60, // 30 Tage
                  },
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                },
              },
              {
                urlPattern: /\.(?:js|css)$/,
                handler: 'NetworkFirst', // Änderung von StaleWhileRevalidate zu NetworkFirst
                options: {
                  cacheName: 'static-resources',
                  expiration: {
                    maxEntries: 50,
                    maxAgeSeconds: 1 * 24 * 60 * 60, // 1 Tag statt 7 Tage
                  },
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                },
              },
              {
                urlPattern: /^https:\/\/fonts\.googleapis\.com/,
                handler: 'StaleWhileRevalidate',
                options: {
                  cacheName: 'google-fonts-stylesheets',
                  expiration: {
                    maxEntries: 10,
                    maxAgeSeconds: 60 * 60 * 24 * 365, // 1 Jahr
                  },
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                },
              },
              {
                urlPattern: /^https:\/\/fonts\.gstatic\.com/,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'google-fonts-webfonts',
                  expiration: {
                    maxEntries: 30,
                    maxAgeSeconds: 60 * 60 * 24 * 365, // 1 Jahr
                  },
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                },
              }
            ]
          }
        }),
        // Include this only in development mode
        ...(mode === 'development' ? [vueDevTools()] : []),
      ],
      define: {
        'import.meta.env.VITE_APP_VERSION': JSON.stringify(packageJson.version)
      },
      build: {
        sourcemap: env.VITE_ENV !== 'production', // Enable source maps only in non-production mode
        outDir: 'dist', // Specify output directory
        minify: env.VITE_ENV === 'production' ? 'terser' : false,
        terserOptions: env.VITE_ENV === 'production' ? {
          compress: {
            drop_console: true, // Remove console.log in production
            drop_debugger: true, // Remove debugger statements
          },
        } : {},
        rollupOptions: {
            // You can set manual chunking strategy if necessary
            output: {
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        return id.toString().split('node_modules/')[1].split('/')[0].toString(); // Group node_modules into chunks
                    }
                }
            }
        }
    },
    resolve: {
      alias: {
          '@': fileURLToPath(new URL('./src', import.meta.url)) // Set alias for import paths
        }
    }
    });
}