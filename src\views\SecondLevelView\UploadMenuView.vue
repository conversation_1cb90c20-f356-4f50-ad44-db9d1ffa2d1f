<template>
  <!-- Column Builder-->
  <div class="w-full flex md:flex-row flex-col px-6">

    <!-- Middle Container -->
    <div class="md:w-3/4 md:pr-12 w-full">
      <!-- Head-->
      <div class="w-full flex flex-row mt-10">
        <h1 class="w-full first-letter:md:w-11/12 h-auto pb-3 text-xl leading-[3rem]">Neues Rezept erfassen</h1>
        <div class="invisible w-0 md:visible md:w-1/12">
          <svg width="55" class="text float-right" height="13" viewBox="0 0 55 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="27.5" cy="6.5" r="6.5" fill="white"/>
            <circle cx="48.5" cy="6.5" r="6.5" fill="white"/>
          </svg>
        </div>
      </div>
      <p class="w-full md:w-1/2">Über Text, Bild, URL oder mit dem Generator in ein paar Sekunden zum nächsten Rezept...</p>
      <!-- Head-->


      <!-- component switcher -->
      <div class="relative flex items-center w-full h-12 bg-default-button-bg mt-12 rounded-2xl pb-4 overflow-hidden">
        <div class="absolute inset-0 h-full rounded-2xl bg-white transition-all duration-300 ease-in-out"
             :style="{ width: '25%', left: `${((tempweekplanStore.switchSettingsComponentButton - 1) * 25)}%` }">
        </div>
        
        <button @click="tempweekplanStore.setActiveButton(1)" 
                class="z-10 w-1/4 h-12 text-[11px] pt-[16px] font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Mit<br />Quelle
        </button>
        <button @click="tempweekplanStore.setActiveButton(2)" 
                class="z-10 w-1/4 h-12 text-[11px] pt-[16px] font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Mit<br />Text
        </button>
        <button @click="tempweekplanStore.setActiveButton(3)" 
                class="z-10 w-1/4 h-12 text-[11px] pt-[16px] font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Mit<br />Bild
        </button>
        <button @click="tempweekplanStore.setActiveButton(4)" 
                class="z-10 w-1/4 h-12 text-[11px] pt-[16px] font-YesevaOne rounded-2xl bg-transparent focus:outline-none">
          Mit<br />Generator
        </button>
      </div>
      <!-- component switcher -->

      <!-- ========= Content Sections für Tabs ========= -->

      <!-- Upload FROM URL -->
      <div class="w-full mt-12 space-y-2" v-if="tempweekplanStore.switchSettingsComponentButton == 1">
        <h3>Aus Quellen ein Rezept erstellen</h3>
        <p class="text-xs text-gray-600 mb-4">Wähle eine private URL für ein neues Rezept aus.</p>
        <input v-model="tempweekplanStore.recieptUrl" class="w-full md:w-2/3 px-6 rounded-lg h-10 bg-white" placeholder="https://www.."  />
        <click-shadow-button @click.prevent="tempweekplanStore.createURLreciept()"  
          :element="{'buttondescription': 'Rezept erstellen', 'active': 'false', 'iconneeded': false}" 
          :index="1"  
        />
      </div>

      <!-- Upload FROM TEXT -->
      <div class="w-full mt-12 space-y-2" v-if="tempweekplanStore.switchSettingsComponentButton == 2">
        <h3>Aus einem Text ein Rezept erstellen</h3>
        <p class="text-xs text-gray-600 mb-4">Kopiere aus deinen Notizen oder einem anderen Dokument ein Rezept.</p>
        <textarea v-model="tempweekplanStore.recieptText" class="w-full md:w-2/3 mt-2 px-6 py-2 rounded-lg h-24 bg-white" placeholder="Schweinsbraten | Grossmutters rezept...."  ></textarea>
        <click-shadow-button @click.prevent="tempweekplanStore.createURLText()"  
          :element="{'buttondescription': 'Rezept erstellen', 'active': 'false', 'iconneeded': false}" 
          :index="1"  
        />
      </div>

      <!-- Upload FROM IMAGE -->
      <div class="w-full mt-12 space-y-2" v-if="tempweekplanStore.switchSettingsComponentButton == 3">
        <h3>Aus einem Bild ein Rezept erstellen</h3>
        <p class="text-xs text-gray-600 mb-4">Erstelle aus einem Screenshot oder einem Bild ein Rezept.</p>
        <div class="flex w-full md:w-2/3 justify-center">
          <div v-if="!tempweekplanStore.isLoading" class="rounded-lg shadow-xl bg-white w-full">
              <div class="m-4">
                  <div>
                    <LiveCamera />
                  </div>
                  <div class="flex items-center justify-center w-full mt-6">
                      <label class="flex flex-col w-full h-20 border-4 border-dashed hover:bg-gray-100 hover:border-gray-300 rounded-xl">
                          <div v-if="!tempweekplanStore.imageIsLoading" class="flex flex-row items-center justify-center pt-4">
                              <svg xmlns="http://www.w3.org/2000/svg"
                                  class="h-8 w-1/3 text-gray-400 group-hover:text-gray-600" viewBox="0 0 20 20"
                                  fill="currentColor">
                                  <path fill-rule="evenodd"
                                      d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                      clip-rule="evenodd" />
                              </svg>
                              <p class="w-2/3 pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600">
                                  Aus Album auswählen</p>
                          </div>
                          <div v-if="tempweekplanStore.imageIsLoading" class="w-full h-20">
                            <img src="../../assets/icons/reload.png" class="w-8 h-8 my-auto mt-6 mx-auto justify-center anmiate animate-spin" />
                          </div>
                          <input type="file" ref="fileInput" class="opacity-0" @change="handleFileUpload" />
                      </label>
                  </div>
              </div>
              <div v-if="tempweekplanStore.tempImage" class="flex flex-col md:flex-row items-center px-5 w-full gap-4 md:gap-12 pb-6 mx-auto">
                <div>
                  <button 
                  @click="tempweekplanStore.clearImage()"
                      class="w-full md:w-auto items-center justify-center mx-auto md:mx-0 h-auto mt-4 md:mt-8 
                      rounded-2xl bg-[#A37DFF] pt-3 pb-3 px-4
                      shadow-custom shadow-[#E0ADFF]"
                  >
                      <span class="font-YesevaOne text-sm text-white">Löschen</span>
                  </button>
                </div>
                <div class="w-full md:w-auto mt-4 md:mt-0">
                  <click-shadow-button @click.prevent="tempweekplanStore.createIMAGEreciept(), scrollToDiv('targetDiv')"
                    :element="{'buttondescription': 'Rezept erstellen', 'active': 'false', 'iconneeded': false}" 
                    :index="1"
                    class="w-full" 
                  />
                </div>
              </div>
          </div>
        </div>
      </div>

      <!-- Upload MIT GENERATOR (Neue Komponente) -->
      <div v-if="tempweekplanStore.switchSettingsComponentButton == 4" class="w-full mt-12">
          <RecipeGenerator />
      </div>
      <!-- Upload MIT GENERATOR ENDE -->

      <!-- ========= Gemeinsamer Bereich für Ladeanzeige und Ergebnisse ========= -->

      <!-- Ladeanzeige (alle Tabs) -->
      <div v-if="tempweekplanStore.isLoading && tempweekplanStore.switchSettingsComponentButton != 4" class="flex flex-nowrap lg:flex-wrap gap-5 pr-5 pb-12 mt-12 w-full h-auto">
          <div class="h-128 w-full md:w-114 rounded-2xl bg-gradient-to-r from-ordypurple-200 via-ordypink-100 to-ordypurple-200 background-animate">
               <div class="h-20 float-right mt-6 mr-6 w-20 rounded-2xl bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 background-animate"></div>
               <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
               <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
               <div class="h-20 float-right mt-6 mr-6 w-full p-10 rounded-2xl bg-gradient-to-r from-gray-90 via-gray-200 to-gray-90 background-animate"></div>
          </div>
      </div>

      <!-- Ergebnisliste (alle Tabs) -->
      <div ref="targetDiv" class="flex flex-wrap gap-5 pr-5 pb-12 mt-2 w-full h-auto">
          <div 
              class="mt-5 rounded-xl h-128 w-80"  
              :class="(tempweekplanStore.isLoading)?'opacity-50 pointer-events-none':''" 
              v-for="(menue, idx) in tempweekplanStore.tempweekplanmenu" 
              v-bind:key="menue?._id || menue?.name || idx" >
              <menu-card 
                  class="bg-sfgyellow-500" 
                  :menuPlanType="{ addToMenu: false, editMenu: false, none: false }" 
                  :weekplanParentData="weekplanParentData" 
                  :element="menue" 
                  :index="idx" 
              />
          </div>
      </div>
      <!-- Ergebnisliste Ende -->

    </div> <!-- Ende Middle Container -->

    <!-- Right Container (Leer) -->
    <div class="md:w-1/4 w-full md:p-10 md:mt-12 pb-32">
      <!-- Bleibt leer -->
    </div>

  </div> <!-- Ende Column Builder -->
</template>

<script setup>
import { onMounted, onBeforeUnmount, onUpdated, reactive, ref, onUnmounted } from 'vue';
import axios from 'axios';
import heic2any from 'heic2any';
import menuCard from '../../components/menuCard.vue'
import preferenceCard from '../../components/preferenceCard.vue'
import dayCard from '../../components/dayCard.vue'
import useNotification from '../../../modules/notificationInformation';
import { useMenuStore } from '@/store/menuStore';
import { useUserStore } from '@/store/userStore'
import { useWeekplanStore } from '@/store/weekplanStore'
import { useTempWeekplanStore } from '@/store/weekplanStore'
import { useAboStore } from '../../store/aboStore';
import { useHelperStore } from '../../../utils/helper';
import clickShadowButton from '../../components/clickShadowButton.vue'
import LiveCamera from './LiveCamera.vue';
import Pica from 'pica';
import RecipeGenerator from '@/components/RecipeGenerator.vue';

  const userStore = useUserStore();
  const aboStore = useAboStore()
  const weekplanStore = useWeekplanStore();
  const tempweekplanStore = useTempWeekplanStore();
  const { setNotification } = useNotification();
  const helper = useHelperStore();

  const weekplanParentData = {
    plannedSeats: null
  }
  
  const targetDiv = ref(null);
  const fileInput = ref(null);

  // --- Logik aus SearchMenuView ---
  const searchText = ref("Rezeptvorschläge generieren") 
  // const isLoadingStep2 = ref(false) // Ersetzt durch tempweekplanStore.isLoading

  const datasetPreferences = reactive([
    {
        boxid: 1,
        name: "Anzahl Personen",
        preferencevalue: "1", // Standardwert
        items: null, // Nicht benötigt für Input
        type: "number"
    },
    {
        boxid: 2,
        name: "Vegane Rezepte",
        preferencevalue: "nein", // Standardwert
        items: ["egal", "ja", "nein"],
        type: "select"
    },
    // Weitere Präferenzen hier hinzufügen...
  ]);

  const createMenu = async () => {
      helper.devConsole("createMenu called (now for Generator)");
      
      // Stelle sicher, dass mindestens eine Präferenz (Personenanzahl) vorhanden ist
      const anzahlPersonenPref = datasetPreferences.find(p => p.boxid === 1);
      if (!anzahlPersonenPref || !anzahlPersonenPref.preferencevalue || parseInt(anzahlPersonenPref.preferencevalue) <= 0) {
          setNotification('Bitte gib eine gültige Anzahl Personen an.', 'alert');
          return;
      }

      let preferencesString = "";
      datasetPreferences.forEach(pref => {
          // Füge nur Präferenzen hinzu, die einen gültigen Wert haben und nicht "egal" sind
          if (pref.preferencevalue && pref.preferencevalue !== 'egal') {
               // Einfache Konkatenation für den Prompt, ggf. anpassen
               preferencesString += `${pref.name}: ${pref.preferencevalue}, `;
          }
      });
      // Entferne das letzte Komma und Leerzeichen
      preferencesString = preferencesString.replace(/,\s*$/, '');

      helper.devConsole("Preferences String:", preferencesString);

      // Verwende die createWeekplanFromGenerator Funktion aus dem Store
      await tempweekplanStore.createWeekplanFromGenerator(preferencesString);
      // Optional: Nach Generierung zu den Ergebnissen scrollen
      scrollToDiv('targetDiv'); // targetDiv wird jetzt auch für Generator-Ergebnisse verwendet
  };
  // --- Ende Logik aus SearchMenuView ---


  // Methode um zum Div zu scrollen
  const scrollToDiv = (refName) => {
    const element = targetDiv.value; // Holen Sie sich das Element mit der Referenz
    if (element) {
      const top = element.offsetTop - 100; // Kleiner Offset nach oben
      setTimeout(() => {
        window.scrollTo({ top, behavior: 'smooth' });
      }, 100);
    } else {
      console.warn('Ziel-Div zum Scrollen nicht gefunden.');
    }
  };


  const handleFileUpload = async (event) => {
        tempweekplanStore.tempImage = null;
        tempweekplanStore.imageIsLoading = true;

        const file = event.target.files[0];
        let fileType = '';

        if (file) {
            helper.devConsole("in file true");
            helper.devConsole(file.type);

            // Bestimmung des Dateityps
            if (!file.type) {
                helper.devConsole("image has no file type");
                const fileName = file.name;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                if (fileExtension === 'heic') {
                    fileType = 'image/heic';
                }
            } else {
                helper.devConsole("image has file type");
                fileType = file.type;
            }

            const isHeic = fileType === 'image/heic' || fileType === 'image/heif';

            try {
                let convertedFile;

                // Konvertierung von HEIC in JPEG
                if (isHeic) {
                    helper.devConsole("is heic");
                    convertedFile = await heic2any({
                        blob: file,
                        toType: 'image/jpeg',
                        quality: 1 // Hohe Qualität
                    });
                    tempweekplanStore.tempImageBlob = convertedFile
                } else {
                    helper.devConsole("is jpeg");
                    convertedFile = file; // JPEG-Datei bleibt unverändert
                    tempweekplanStore.tempImageBlob = convertedFile
                }
                
                tempweekplanStore.tempImage = URL.createObjectURL(convertedFile);
                tempweekplanStore.imageIsLoading = false;

            } catch (error) {
                tempweekplanStore.imageIsLoading = false;
                console.error('Fehler bei der Umwandlung des Bildes:', error);
                setNotification('Fehler bei der Bildumwandlung', 'error');
            }
        }

        // Setzen Sie das Dateiauswahlfeld zurück
        fileInput.value.value = '';
    };

  // Lifecycle Hook, um bei Verlassen der Seite die temporären Daten zu löschen
  onUnmounted(() => {
      tempweekplanStore.clearFoundMenus();
      tempweekplanStore.tempImage = null;
      tempweekplanStore.tempImageBlob = null;
      // Ggf. weitere Resets hier
  });

</script>
<style scoped>

.background-animate {
    background-size: 400%;

    -webkit-animation: AnimationName 3s ease infinite;
    -moz-animation: AnimationName 3s ease infinite;
    animation: AnimationName 3s ease infinite;
  }

  @keyframes AnimationName {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

.shake {
  animation: shake 3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(10px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-10px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(15px, 0, 0);
  }
}

</style>