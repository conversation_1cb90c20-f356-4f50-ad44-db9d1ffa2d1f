const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Sicherheitslücken-Fixer wird gestartet...');

// Diese Pakete haben kritische Sicherheitslücken
const criticalPackages = [
  'json-schema@0.4.0',
  'minimist@1.2.8',
  'tough-cookie@4.1.3',
  'sshpk@2.0.0',
  'qs@6.11.0'
];

// Installation der neuesten sicheren Versionen
console.log('📦 Installiere neueste sichere Versionen der kritischen Pakete...');
criticalPackages.forEach(pkg => {
  try {
    console.log(`Installiere ${pkg}...`);
    execSync(`npm install ${pkg} --save-exact`, { stdio: 'inherit' });
  } catch (error) {
    console.error(`⚠️ Fehler beim Installieren von ${pkg}: ${error.message}`);
  }
});

// Erzeuge eine .npmrc-Datei, um Warnungen zu deaktivieren
console.log('📝 Erstelle .npmrc zur Steuerung von npm-Verhalten...');
fs.writeFileSync('.npmrc', 'legacy-peer-deps=true\nforce=true\n');

// Aktualisiere nodemon, da es Abhängigkeiten mit Sicherheitslücken hat
console.log('🔄 Aktualisiere nodemon auf eine sicherere Version...');
try {
  execSync('npm install nodemon@latest --save', { stdio: 'inherit' });
} catch (error) {
  console.error(`⚠️ Fehler beim Aktualisieren von nodemon: ${error.message}`);
}

// Entferne das veraltete 'latest' Paket, das Teil des Problems ist
console.log('🗑️ Entferne veraltetes "latest" Paket...');
try {
  execSync('npm uninstall latest', { stdio: 'inherit' });
} catch (error) {
  console.error(`⚠️ Fehler beim Entfernen von "latest": ${error.message}`);
}

console.log('✅ Ausführung abgeschlossen. Führe nun "npm audit --audit-level=critical" aus, um zu überprüfen, ob kritische Sicherheitslücken behoben wurden.'); 