const mongoose = require('mongoose')
const { connection1 } = require('./../db.js');
const helper = require('../utils/helper')
const kitchentableModel = require('./kitchentableModel.js')

const GrocerylistSchema = new mongoose.Schema({
    kitchentableId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Kitchentable',
        required: true
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    locked: {
        type: Boolean,
        default: false
    },
    groceryListActive: {
        type: Array,
        default: []
    },
    groceryListPassive: {
        type: Array,
        default: []
    },
    createdAt: {
        type: String,
        default: Date.now()
    }
})

/*
GrocerylistSchema.pre(/^find/, function(next){
    helper.devConsole("pre on weekplan find executed")
    this.populate('menu')
    next()
});
*/




module.exports = connection1.model('Grocerylist', GrocerylistSchema)