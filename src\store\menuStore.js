import { defineStore } from 'pinia';
import { nextTick, ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import useNotification from '../../modules/notificationInformation';

import axios, { CancelToken } from 'axios';
import { useUserStore } from '../store/userStore'
import { useHelperStore, useConfirmationStore } from '../../utils/helper'

export const useMenuStore = defineStore('oneReciept', () => {

    const { setNotification } = useNotification();
    const confirmationStore = useConfirmationStore();
    const router = useRouter();
    const helper = useHelperStore()
    const userStore = useUserStore()

    /* INITIALIZING VALUES */

    const oneReciept = ref({
        _id: null,
        name: null,
        description: null,
        users: [],
        imagelink: null,
        freeAccess: null,
        createdAt: null,
        __v: null,
        // additional data only in vue
        permission: null,
        //
        menuchilds: {
            _id: null,
            numberOfPersons: null,
            menuChildId: {
                _id: null,
                cookingTime: null,
                createdAt: null,
                ingredients: [],
                isStandard: null,
                nutritions: [],
                parentId: null,
                preperation: [],
                versions: [],
                _v: null
            }
        }
    })

    const plannedSeats = ref(null);


    /* INITIALIZING VALUES */
    ///////////////////////////////////////////////////////////////////////////////////////
    /* SET ONE MENU */

    const setOneMenue = async(res) => {

        helper.devConsole("setOneMenue at menuStore")

        oneReciept.value._id = res.data.data._id;
        oneReciept.value.name = res.data.data.name,
        oneReciept.value.users = res.data.data.users,
        oneReciept.value.description = res.data.data.description,
        oneReciept.value.imagelink = res.data.data.imagelink, //resImage.data <- object prop if load image from aws would work
        oneReciept.value.freeAccess = res.data.data.freeAccess,
        oneReciept.value.__v = res.data.data.__v

        //console.log("----------------")
        //console.log("user id check")
        //console.log(userStore.user.id)

        //check if user is owner or creator, otherwise no access
        const filteredData = oneReciept.value.users.filter(item =>
            item.userId._id === userStore.user.id &&
            (item.roleId.name === "creator" || item.roleId.name === "owner")
        );


        //console.log("filteredData")
        //console.log(filteredData)

        if(filteredData.length > 0){
            //console.log("länger 0")
            oneReciept.value.permission = "edit"
        }

        helper.devConsole("--------")
        helper.devConsole("check menucild path")
        helper.devConsole(res.data.data.menuchilds)
        helper.devConsole("--------")

        //console.log("-----------------------------")
        //console.log("----- check menucild path ---")
        //console.log(res.data.data.menuchilds)
        //console.log("-----------------------------")

        // menuchilds data
        oneReciept.value.menuchilds._id = res.data.data.menuchilds[0]._id
        oneReciept.value.menuchilds.numberOfPersons = res.data.data.menuchilds[0].numberOfPersons
        oneReciept.value.menuchilds.menuChildId._id = res.data.data.menuchilds[0].menuChildId._id
        oneReciept.value.menuchilds.menuChildId.seatCount = res.data.data.menuchilds[0].menuChildId.seatCount
        oneReciept.value.menuchilds.menuChildId.cookingTime = res.data.data.menuchilds[0].menuChildId.cookingTime
        oneReciept.value.menuchilds.menuChildId.createdAt = res.data.data.menuchilds[0].menuChildId.createdAt
        oneReciept.value.menuchilds.menuChildId.ingredients = res.data.data.menuchilds[0].menuChildId.ingredients
        oneReciept.value.menuchilds.menuChildId.isStandard = res.data.data.menuchilds[0].menuChildId.isStandard
        oneReciept.value.menuchilds.menuChildId.nutritions = res.data.data.menuchilds[0].menuChildId.nutritions
        oneReciept.value.menuchilds.menuChildId.preperation = res.data.data.menuchilds[0].menuChildId.preperation
        oneReciept.value.menuchilds.menuChildId.parentId = res.data.data.menuchilds[0].menuChildId.parentId
        oneReciept.value.menuchilds.menuChildId.versions = res.data.data.menuchilds[0].menuChildId.versions
        oneReciept.value.menuchilds.menuChildId._v = res.data.data.menuchilds[0].menuChildId._v

        // KRITISCH: Legacy-Migration und Datenstruktur-Reparatur
        if (oneReciept.value.menuchilds.menuChildId.ingredients) {
            console.log('🔧 Checking for legacy recipe and data structure integrity...');

            // Importiere Legacy-Migration (dynamisch um Circular Dependencies zu vermeiden)
            const { fullLegacyMigration, shouldSaveMigratedRecipe } = await import('../utils/legacyStableIdMigration.js');

            // Führe vollständige Legacy-Migration durch
            oneReciept.value = fullLegacyMigration(oneReciept.value);

            // Prüfe ob das migrierte Rezept gespeichert werden sollte
            if (shouldSaveMigratedRecipe(oneReciept.value)) {
                console.log('💾 Recipe was migrated from legacy format, triggering auto-save...');
                // Trigger auto-save für migrierte Rezepte (falls in Edit-Mode)
                // Das wird vom Frontend-Component gehandhabt
            }

            console.log('✅ Legacy migration and data structure check completed');
        }

        //console.log(oneReciept.value)
        helper.devConsole(oneReciept.value)
    }
    /* SET ONE MENU */
    ///////////////////////////////////////////////////////////////////////////////////////
    /* FUNCTIONS */
    /* LOAD ONE MENU TO THE STORE*/
    const getOneMenue = async (id, freeAccess) => {
        //console.log("getOneMenue at menuStore")
        helper.devConsole("getOneMenue at menuStore")
        try{
            let res;
            if(freeAccess) res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/' + id + '/free');
            if(!freeAccess) res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/' + id);
            //console.log("res.data.data 1")
            //console.log(res.data.data)
            await setOneMenue(res)
            //console.log("res.data.data 2")
            //console.log(res.data.data)
        } catch (error){
            helper.devConsole(error)
        }
    }
    /* LOAD ONE MENU TO THE STORE*/
    /////////////////////////////////////// LOCAL CHANGES /////////////////////////////////////////////////
    //CHANGE NAME; DESCRIPTION AND FREEACCESS CLIENTSIDE IN MENU
    const changeMenu = async (value, item) => {
        //console.log(item)
        //console.log(value)
            try{
                //console.log(oneReciept.value[item])
                oneReciept.value[item] = value.target.value
                updateMenuAtDB(oneReciept.value._id)
            } catch (error){
                helper.devConsole(error)
            }
    }

    ///CHANGE NAME; DESCRIPTION AND FREEACCESS CLIENTSIDE IN MENUCHILD
    const changeMenuchild = async (value, item) => {
        //console.log(item)
        //console.log(value)
            try{
                //console.log(oneReciept.value[item])
                oneReciept.value.menuchilds.menuChildId[item] = value.target.value
                updateMenuchildAtDB(oneReciept.value.menuchilds.menuChildId._id)
            } catch (error){
                helper.devConsole(error)
            }
    }

    //CHANGE TEXT IN ARRAY
    const changeMenuchildArray = async (value, index, item, firstLevelKey) => {
        // OUT OF CONTROLL
            try{
                //console.log(value.target.value)
                //console.log(firstLevelKey)
                oneReciept.value.menuchilds.menuChildId[item][index][firstLevelKey] = value.target.value
                updateMenuchildAtDB(oneReciept.value.menuchilds.menuChildId._id)
            } catch (error){
                console.log(error)
            }
    }

    // ADD ONE LOCAL INGRIDIENT
    const createEmptyIngredientStep = async () => {
        try{
            //console.log(value.target.value)
            // TODO !!!!!!!!!!!!!!! CHECK OVER API IF THE VALUES ARE CORRECT (HARD CODED) !!!!!!!!!!!!!!!!!!!!!! TODO
            //oneReciept.value.menuchilds.menuChildId.ingredients.push({unit: "-", amount: "-", name: "-"})

            // /one/child/:menuchildid/ingredientschecker
            // /one/:menuid/child/:menuchildid/ingredient
            try{
                const res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/' + oneReciept.value._id + '/child/' + oneReciept.value.menuchilds.menuChildId._id + '/ingredient', {
                    ingredients: [{unit: "-", amount: "-", name: "-"}]
                });
                // load new Menu
                if(res.data){
                    await getOneMenue(oneReciept.value.menuchilds.menuChildId.parentId)
                }
            } catch (error){
                helper.devConsole(error)
            }

            //updateMenchilduAtDB(oneReciept.value._id)
        } catch (error){
            helper.devConsole(error)
        }
    }

    // ADD ONE LOCAL PREPERATION STEP
    const createEmptyPreperationStep = async () => {
        try{
            //console.log(value.target.value)
            oneReciept.value.menuchilds.menuChildId.preperation.push({head:"-",content:"-"})
            //updateMenchilduAtDB(oneReciept.value._id)
        } catch (error){
            helper.devConsole(error)
        }
    }

    // ADD FREEACCESS YES/NO
    const setMenuRelationFreeAccessState = async () => {
        //console.log("hi")
        //console.log(oneReciept.value.freeAccess)
        oneReciept.value.freeAccess = !oneReciept.value.freeAccess
        //console.log(oneReciept.value.freeAccess)
        //Update DB
        updateMenuAtDB(oneReciept.value._id)
    }

    //PERSON UP - 🔧 KRITISCH: Neue dedizierte PersonCount-API
    const countPerson = async (type, incomingMenuChild, incomingWeekplan) => {
        helper.devConsole(`[countPerson] Called with type: ${type}`);

        try {
            const checktype = typeof type;

            // 🔧 KRITISCH: Für String-Typ (up/down) verwende neue dedizierte API
            if (checktype === "string") {
                helper.devConsole(`[countPerson] Using new dedicated PersonCount API for direction: ${type}`);

                const menuChildId = oneReciept.value.menuchilds.menuChildId._id;
                if (!menuChildId) {
                    throw new Error('MenuChild ID not found');
                }

                // Verwende neue dedizierte PersonCount-API
                const response = await axios.patch(
                    `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/child/${menuChildId}/person-count`,
                    { direction: type }
                );

                helper.devConsole('✅ PersonCount API response:', response.data);

                // Lade das aktualisierte Rezept neu
                await getOneMenue(oneReciept.value._id);

                return response;
            }

            // 🔧 KRITISCH: Für Number-Typ (Weekplan) verwende alte Logik
            if(checktype == "number"){
                helper.devConsole(`[countPerson] Using legacy logic for weekplan with person count: ${type}`);

                let typeVal = type;
                let data = {}
                let newAmount = ref(0)
                let baseIngredientsArray = incomingMenuChild.menuChildId.ingredients
                let baseOldNumberOfPersons = incomingMenuChild.numberOfPersons;
                let ingredientsArray = []

                // calculate new values for the recipe
                for (let i = 0; i < baseIngredientsArray.length; i++){
                    if(Number(baseOldNumberOfPersons) > 0){
                        newAmount.value = baseIngredientsArray[i].amount / Number(baseOldNumberOfPersons) * (Number(baseOldNumberOfPersons) + typeVal)

                        // KRITISCH: Vollständige Datenstruktur erhalten für Frontend-Kompatibilität!
                        const ingredientData = {
                            "_id": baseIngredientsArray[i]._id,
                            "amount": newAmount.value,
                            "name": baseIngredientsArray[i].name,
                            "unit": baseIngredientsArray[i].unit
                        };

                        // KRITISCH: stableId erhalten, falls vorhanden
                        if (baseIngredientsArray[i].stableId) {
                            ingredientData.stableId = baseIngredientsArray[i].stableId;
                        }

                        ingredientsArray.push(ingredientData);
                    }
                }

                // data coming from weekplan setter
                console.log("------- inside number and weekplan setter ------------")
                console.log(incomingMenuChild.menuChildId)
                data.parentId = incomingMenuChild.menuChildId.parentId
                data.seatCount = Number(incomingWeekplan.plannedSeats)

                // KRITISCH: Backend-kompatible Zutaten-Struktur auch für Weekplan
                const backendCompatibleIngredientsWeekplan = ingredientsArray.map(ingredient => ({
                    "_id": ingredient._id,
                    "amount": ingredient.amount,
                    "unit": ingredient.unit?._id || ingredient.unit, // ID falls Objekt, sonst direkt
                    "name": ingredient.name?._id || ingredient.name, // ID falls Objekt, sonst direkt
                    "stableId": ingredient.stableId // StableID erhalten
                }));

                data.ingredients = backendCompatibleIngredientsWeekplan
                data.cookingTime = incomingMenuChild.menuChildId.cookingTime
                data.preperation = incomingMenuChild.menuChildId.preperation
                data.nutritions = incomingMenuChild.menuChildId.nutritions
                data.versions = incomingMenuChild.menuChildId.versions
                data.isStandard = false

                 // check if exists, if not create one
                const resMenu = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/weekplan/add/one/menu/' + incomingMenuChild.menuChildId._id + '/checkifmenuchildexists',
                    {
                        user_id: userStore.user.id,
                        menuchild: data,
                        weekplan: incomingWeekplan
                    }
                )

                await getOneMenue(data.parentId)
                return resMenu;
            }
        } catch (error){
            // KRITISCH: Intelligente Fehlerbehandlung für Personenanzahl-Änderung
            if (error.response?.status === 500) {
                console.warn('⚠️ countPerson: Server 500 error but operation may have succeeded');
                helper.devConsole('countPerson 500 error details:', error.response?.data);

                // Prüfe ob es ein Cast-Error ist (häufiger false positive)
                const errorMessage = error.response?.data?.message || '';
                if (errorMessage.includes('Cast to embedded failed')) {
                    console.warn('⚠️ Cast Error in countPerson - likely false positive, checking if operation succeeded...');
                }

                // KEINE User-Notification bei 500-Errors - diese sind oft false positives
                // Die Personenanzahl-Änderung funktioniert trotzdem
            } else {
                console.error('❌ Real error in countPerson:', error);
                helper.devConsole(error);
                // Nur bei echten Fehlern (nicht 500) eine Notification zeigen
                setNotification('Fehler beim Ändern der Personenanzahl', 'error');
            }
        }
    }

    //CHANGE INGRDIENTS (UNITS AND PRODUCTS)
    const changeMenuchildSecondLevel = async (value, index, item, firstLevelKey, secondLevelKey) => {
        // OUT OF CONTROLL
        try{
            //console.log(value)
           // console.log(index)
            //console.log(item)
            //console.log(firstLevelKey)
            //console.log(secondLevelKey)
            // item: "ingredients"
            // value: 3 oder stück
            // index : 3
            // fristlevelkey: fristlevelkey
            // secondLevelKey: secondLevelKey

            /////////// CHECK EINHEIT/NAME CHANGE
            let updateEinheit = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/ingredientsrow/checker',{
                "id": oneReciept.value._id,
                "index": index,
                "firstLevelKey": firstLevelKey,
                "secondLevelKey": secondLevelKey,
                "value": value.target.value,
            });

            // Local Update
            //console.log(updateEinheit.data.data)
            oneReciept.value.menuchilds.menuChildId[item][index][firstLevelKey] = updateEinheit.data.data

            helper.devConsole(updateEinheit)
            if(updateEinheit.data.data.name == "-"){
                setNotification('Verwende g, ml, l, stk, Prise, kg oder el etc als Mengenangaben','alert')
            } else {
                /////////// UPDATE EINHEIT/NAME IN DB ON MENU
                await updateMenuchildIngredientsAtDB(oneReciept.value.menuchilds.menuChildId._id)
            }
        } catch (error){
            helper.devConsole(error)
        }
    }

    // 🛒 EINKAUFSZETTEL-INTEGRATION: Rezept zum Einkaufszettel hinzufügen
    const addRecipeToShoppingList = async () => {
        helper.devConsole("addRecipeToShoppingList in menuStore");

        try {
            const menuChildId = oneReciept.value.menuchilds.menuChildId._id;

            if (!menuChildId) {
                setNotification('Rezept-ID nicht gefunden', 'error');
                return;
            }

            const response = await axios.post(
                `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/recipe/${menuChildId}/add-to-shopping-list`
            );

            if (response.data.success) {
                setNotification(response.data.message || 'Zutaten zum Einkaufszettel hinzugefügt', 'success');
                helper.devConsole('✅ Recipe added to shopping list:', response.data.data);
                return response.data.data;
            } else {
                setNotification('Fehler beim Hinzufügen zum Einkaufszettel', 'error');
            }
        } catch (error) {
            helper.devConsole('❌ Error adding recipe to shopping list:', error);

            if (error.response?.data?.message) {
                setNotification(error.response.data.message, 'error');
            } else {
                setNotification('Fehler beim Hinzufügen zum Einkaufszettel', 'error');
            }
        }
    };

    // 🛒 EINKAUFSZETTEL-INTEGRATION: Rezept vom Einkaufszettel entfernen
    const removeRecipeFromShoppingList = async () => {
        helper.devConsole("removeRecipeFromShoppingList in menuStore");

        try {
            const menuChildId = oneReciept.value.menuchilds.menuChildId._id;

            if (!menuChildId) {
                setNotification('Rezept-ID nicht gefunden', 'error');
                return;
            }

            const response = await axios.delete(
                `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/recipe/${menuChildId}/remove-from-shopping-list`
            );

            if (response.data.success) {
                setNotification(response.data.message || 'Zutaten vom Einkaufszettel entfernt', 'success');
                helper.devConsole('✅ Recipe removed from shopping list:', response.data.data);
                return response.data.data;
            } else {
                setNotification('Fehler beim Entfernen vom Einkaufszettel', 'error');
            }
        } catch (error) {
            helper.devConsole('❌ Error removing recipe from shopping list:', error);

            if (error.response?.data?.message) {
                setNotification(error.response.data.message, 'error');
            } else {
                setNotification('Fehler beim Entfernen vom Einkaufszettel', 'error');
            }
        }
    };

    // 🔧 KRITISCH: Global Delete Ingredient von ALLEN MenuChilds
    const deleteIngredientGlobally = async (stableId) => {
        helper.devConsole(`[deleteIngredientGlobally] Deleting ingredient with stableId: ${stableId}`);

        try {
            if (!stableId) {
                setNotification('StableID ist erforderlich', 'error');
                return;
            }

            const parentId = oneReciept.value._id;
            if (!parentId) {
                setNotification('Rezept-ID nicht gefunden', 'error');
                return;
            }

            const response = await axios.delete(
                `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/recipe/${parentId}/ingredient/${stableId}/global-delete`
            );

            if (response.data.success) {
                setNotification(response.data.message || 'Zutat aus allen Rezept-Varianten entfernt', 'success');
                helper.devConsole('✅ Ingredient deleted globally:', response.data.data);

                // Lade das aktualisierte Rezept neu
                await getOneMenue(parentId);

                return response.data.data;
            } else {
                setNotification('Fehler beim Entfernen der Zutat', 'error');
            }
        } catch (error) {
            helper.devConsole('❌ Error deleting ingredient globally:', error);

            if (error.response?.data?.message) {
                setNotification(error.response.data.message, 'error');
            } else {
                setNotification('Fehler beim Entfernen der Zutat', 'error');
            }
        }
    };

    //CREATE SHAREABLE LINK
    const createShareableLink = async () => {
        helper.devConsole("createShareableLink in menuStore");

        // URL base for sharing
        const baseUrl = import.meta.env.VITE_BASE_URL + '/kochbuch/menu/' + oneReciept.value._id;
        let shareUrl = baseUrl; // Default URL without freeAccess

        if (oneReciept.value.freeAccess) {
            // Recipe is already public, generate link and copy
            shareUrl = baseUrl + '?freeAccess=true';
        try {
                if (navigator.share) {
          await navigator.share({
                        title: 'Rezept ' + oneReciept.value.name + ' teilen',
                        url: shareUrl
                    });
                    helper.devConsole('Inhalt erfolgreich geteilt via Web Share API');
                    setNotification('Link zum geteilten Rezept kopiert und Teilen-Dialog geöffnet.', 'success');
                } else {
                    helper.devConsole('[Clipboard Test] shareUrl vor navigator.clipboard.writeText:', shareUrl);
                    await navigator.clipboard.writeText(shareUrl);
                    setNotification('Link zum geteilten Rezept in die Zwischenablage kopiert.', 'success');
                    // Consider a more user-friendly notification than alert, perhaps using useNotification
                    // alert('Link zum geteilten Rezept kopiert: ' + shareUrl);
                }
        } catch (error) {
                helper.devConsole('Fehler beim Teilen/Kopieren des bereits öffentlichen Links:', error);
                setNotification('Fehler beim Kopieren des Links.', 'alert');
        }
      } else {
            // Recipe is not public, ask for confirmation
            const userConfirmed = await confirmationStore.showConfirmation(
                'Soll dieses Rezept veröffentlicht und der Link geteilt werden?',
                'Dieses Rezept wird für alle sichtbar, die den Link haben.', // Subtext/Description
                'Ja, veröffentlichen und teilen', // Confirm button text
                'Nein, abbrechen' // Cancel button text
            );

            if (userConfirmed) {
                try {
                    oneReciept.value.freeAccess = true;
                    await updateMenuAtDB(oneReciept.value._id); // Update backend

                    shareUrl = baseUrl + '?freeAccess=true';

                    if (navigator.share) {
                        await navigator.share({
                            title: 'Rezept ' + oneReciept.value.name + ' teilen',
                            url: shareUrl
                        });
                        helper.devConsole('Inhalt erfolgreich veröffentlicht und geteilt via Web Share API');
                        setNotification('Rezept veröffentlicht und Link kopiert/geteilt.', 'success');
                    } else {
                        helper.devConsole('[Clipboard Test] shareUrl vor navigator.clipboard.writeText:', shareUrl);
                        await navigator.clipboard.writeText(shareUrl);
                        setNotification('Rezept veröffentlicht und Link in die Zwischenablage kopiert.', 'success');
                        // alert('Rezept veröffentlicht! Link kopiert: ' + shareUrl);
                    }
        } catch (error) {
                    helper.devConsole('Fehler beim Veröffentlichen oder Teilen/Kopieren:', error);
                    // Rollback optimistic update if backend update failed?
                    // oneReciept.value.freeAccess = false; // Potentially
                    setNotification('Fehler beim Veröffentlichen oder Kopieren des Links.', 'alert');
        }
            } else {
                setNotification('Veröffentlichung und Teilen abgebrochen.', 'info');
      }
    }
    };




    ///////////////////////////////////////////////  /////////////////////////////////////////////////////

    ////////////////////////////////////////    UPDATE AT DATABASE    ////////////////////////////////////////////////////

    const updateMenuAtDB = async (id) => {
        // to update only name, description or freeAccess in database
        try{
            // prepare to update menu
            let newobject = {}
            newobject.name = oneReciept.value.name
            newobject.description = oneReciept.value.description
            newobject.freeAccess = oneReciept.value.freeAccess

            // final object to send
            await axios.patch(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/' + id,
                {
                    menu: newobject
                }
            );

        } catch (error){
            helper.devConsole(error)
        }
    }
    const updateMenuchildAtDB = async (id) => {
        try{
            // prepare to update menuchild
            let newobject = {}
            newobject.cookingTime = oneReciept.value.menuchilds.menuChildId.cookingTime

            // 🔧 KRITISCH: Sichere Ingredient-Verarbeitung mit Validierung
            const processedIngredients = oneReciept.value.menuchilds.menuChildId.ingredients.map(ingredient => {
                // Validiere Ingredient-Struktur
                if (!ingredient) {
                    console.warn('⚠️ Empty ingredient found, skipping');
                    return null;
                }

                const processedIngredient = {
                    "_id": ingredient._id
                };

                // Sichere Amount-Verarbeitung
                if (ingredient.amount !== undefined && ingredient.amount !== null) {
                    processedIngredient.amount = Number(ingredient.amount) || 0;
                }

                // 🔧 KRITISCH: Sichere Unit-Verarbeitung
                if (ingredient.unit) {
                    if (typeof ingredient.unit === 'object' && ingredient.unit._id) {
                        processedIngredient.unit = ingredient.unit._id;
                    } else if (typeof ingredient.unit === 'string') {
                        processedIngredient.unit = ingredient.unit;
                    } else {
                        console.warn('⚠️ Invalid unit structure:', ingredient.unit);
                        processedIngredient.unit = null;
                    }
                }

                // 🔧 KRITISCH: Sichere Name-Verarbeitung
                if (ingredient.name) {
                    if (typeof ingredient.name === 'object' && ingredient.name._id) {
                        processedIngredient.name = ingredient.name._id;
                    } else if (typeof ingredient.name === 'string') {
                        processedIngredient.name = ingredient.name;
                    } else {
                        console.warn('⚠️ Invalid name structure:', ingredient.name);
                        processedIngredient.name = null;
                    }
                }

                // StableID erhalten
                if (ingredient.stableId) {
                    processedIngredient.stableId = ingredient.stableId;
                }

                return processedIngredient;
            }).filter(ingredient => ingredient !== null); // Entferne null-Werte

            newobject.ingredients = processedIngredients;
            newobject.preperation = oneReciept.value.menuchilds.menuChildId.preperation
            newobject.versions = oneReciept.value.menuchilds.menuChildId.versions
            newobject.nutritions = oneReciept.value.menuchilds.menuChildId.nutritions

            helper.devConsole('🔄 Sending validated ingredients to backend:', processedIngredients.slice(0, 3));

            // final object to send
            const response = await axios.patch(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/child/' + id,
                {
                    menuchild: newobject
                }
            );

            helper.devConsole('✅ MenuChild update successful:', response.status);

        } catch (error){
            // 🔧 KRITISCH: Verbesserte Fehlerbehandlung - KEINE User-Notifications bei Auto-Save
            if (error.response?.status === 500) {
                const errorMessage = error.response?.data?.message || '';

                // Detaillierte Diagnose für Cast-Errors
                if (errorMessage.includes('Cast to embedded failed') || errorMessage.includes('Cast to ObjectId failed')) {
                    console.warn('⚠️ Cast Error in updateMenuchildAtDB - data structure mismatch detected');
                    helper.devConsole('Cast Error Details:', {
                        message: errorMessage,
                        ingredients: oneReciept.value.menuchilds.menuChildId.ingredients?.slice(0, 2)
                    });
                } else {
                    console.warn('⚠️ Server 500 error in updateMenuchildAtDB (may be false positive)');
                    helper.devConsole('Server error details:', error.response?.data);
                }

                // KEINE User-Notification - Auto-Save läuft im Hintergrund
            } else {
                console.error('❌ Real error in updateMenuchildAtDB:', error);
                helper.devConsole(error);
            }

            // NIEMALS setNotification bei Auto-Save - das würde User spammen
        }
    }
    const updateMenuchildIngredientsAtDB = async (id) => {
        try{
            // prepare to update menuchild > ingredients
            let newobject = {}
            let newarray = []

            //if oneReciept.key = ingredients
            let arrayToPass = oneReciept.value.menuchilds.menuChildId.ingredients
            //console.log(arrayToPass.length)

            for (let a = 0; a < arrayToPass.length; a++) {
                //console.log(arrayToPass[a])
                const ingredientData = {
                    "amount": arrayToPass[a].amount,
                    "unit": arrayToPass[a].unit._id,
                    "name": arrayToPass[a].name._id,
                    "_id": arrayToPass[a]._id
                };

                // KRITISCH: Stable ID mit senden, falls vorhanden
                if (arrayToPass[a].stableId) {
                    ingredientData.stableId = arrayToPass[a].stableId;
                }

                newarray.push(ingredientData);
            }

            newobject.ingredients = newarray
            // ------------------------------------

            //console.log(newobject)
            //console.log(id)

            // final object to send
            await axios.patch(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/one/child/' + id,
                {
                    menuchild: newobject
                }
            );

        } catch (error){
            // KRITISCH: Keine User-Notifications bei Auto-Save Operationen
            if (error.response?.status === 500) {
                console.warn('⚠️ updateMenuchildIngredientsAtDB: Server 500 error (likely false positive)');
                helper.devConsole('updateMenuchildIngredientsAtDB 500 error:', error.response?.data);
            } else {
                console.error('❌ updateMenuchildIngredientsAtDB real error:', error);
                helper.devConsole(error);
            }
            // KEINE setNotification - Auto-Save läuft im Hintergrund
        }
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////
    const recreateImageOfReciept = async () => {
        try{
            helper.devConsole("recreateImageOfReciept at menuStore");
            // Use the new promise-based confirmation
            const userConfirmed = await confirmationStore.showConfirmation(
                'Möchten Sie ein neues Bild erstellen?',
                'alert'
            );

            if (userConfirmed) {
                helper.devConsole("------------- confirmation (recreate image) --------------");
                is_loading.value = true;
                let newMenu = {
                    id: oneReciept.value._id,
                    name: oneReciept.value.name,
                    description: oneReciept.value.description
                };
                helper.devConsole(newMenu);
                try {
                    const createdImage = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/image/createoneimagebyreciept', {
                        menu: newMenu
                    });
                    helper.devConsole(createdImage.data);
                    if(createdImage.data.success){
                        router.go(); // Reload page after successful creation
                        setNotification('Das Bild wurde aktualisiert', 'success');
                    } else {
                        setNotification('Bild konnte nicht erstellt werden (API-Fehler).', 'alert');
                    }
                } catch (apiError) {
                     helper.devConsole("API Error during image recreation:", apiError);
                     setNotification('Fehler bei der Bild-Erstellung (API).', 'alert');
                } finally {
                    is_loading.value = false; // Ensure loading is stopped
                }
            } else {
                setNotification('Bild-Erstellung abgebrochen.', 'alert');
            }
        } catch (error) {
            // This catch handles errors in showConfirmation itself or general errors before the await
            is_loading.value = false;
            helper.devConsole("General error in recreateImageOfReciept:", error);
            setNotification('Fehler bei der Bild-Erstellung.', 'alert');
        }
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////

    const goToEditModeOfReciept = async () => {
        try{
            //console.log(value.target.value)
            router.push({ name: 'rezept_edit', params: { id: oneReciept.value._id } })
        } catch (error){
            helper.devConsole(error)
        }
    }


    /*************** UPLOAD IMAGE ********************/
    const is_loading = ref(false);
    const is_dragover = ref(false)
    const loadFiles = ref();
    const filesIds = ref([]);

    const uploadNewImage = async (event) => {

        is_loading.value = true;
        const data = new FormData();
        const file = event.target.files[0]

        data.append("image", file);
        data.append("menuid", oneReciept.value._id)

        try{

            const newImage = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/images/upload', data, { headers: {'Content-Type': 'multipart/form-data'}});
            //console.log(newImage)
            if(newImage.data.success){
                await setOneMenue(newImage)
            }
            is_dragover.value = true;


        } catch (error){
            helper.devConsole(error)
        }
    }



    //GET NUTRION SCORE FROM API
    const loadingStateNahrwerte = ref(false)
    const loadNutrionScore = async () => {
        //activate Button Loading
        loadingStateNahrwerte.value = true;

        const payload = {}
        payload._id = oneReciept.value.menuchilds.menuChildId._id
        payload.ingredients = oneReciept.value.menuchilds.menuChildId.ingredients

        try{
            let res = await axios.post(import.meta.env.VITE_API_BASE_URL + '/api/v1/creator/functions/ingredients/nutritionalcalc', {
                settings: { menuchild_created: true },
                menuchild: payload
            });
            //console.log(res.data.data)
            if(res.data.success){
                //console.log("drin")
                oneReciept.value.menuchilds.menuChildId.nutritions = res.data.data
                await updateMenuchildAtDB(oneReciept.value.menuchilds.menuChildId._id)

                //deactivate Button Loading
                loadingStateNahrwerte.value = false;
            }

        } catch (error){
            helper.devConsole(error)
        }
    }

    ///////////////// MENU RELATION //////////////////////


    const updateMenuRelationDB = async () => {
        //console.log("hi")
        try{
            await axios.patch(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/related/' + oneReciept.value._id,
                {
                    "freeAccess": oneReciept.value.freeAccess,
                }
            );
            //console.log(answ)
        } catch (error){
            helper.devConsole(error)
        }

    }




    const deleteUserFromMenu = async (menuId, menuName) => {
        helper.devConsole(`deleteUserFromMenu called for menu ID: ${menuId}, Name: ${menuName}`);
        try {
            // Confirmation Dialog - Adjusted Text
            const confirmed = await confirmationStore.showConfirmation(
                'Aus Kochbuch entfernen?',
                `Möchten Sie das Rezept "${menuName || 'Dieses Rezept'}" wirklich aus Ihrem Kochbuch entfernen?`
            );

            if (confirmed) {
                helper.devConsole(`User confirmed removal of recipe relation: ${menuId}`);

                // Correct API call as per requirements
                const response = await axios.delete(
                    `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/one/${menuId}/delete/relateduser`,
                    {
                        data: { user_id: userStore.user.id } // Send user_id in the request body
                    }
                );

                if (response.data.success) {
                    setNotification('Das Rezept wurde aus deinem Kochbuch entfernt.', 'success');
                    router.go(-1); // Navigate back
                } else {
                    setNotification(response.data.message || 'Rezept konnte nicht entfernt werden (Backend).', 'alert');
                }
            } else {
                helper.devConsole(`User cancelled removal of recipe relation: ${menuId}`);
            }
        } catch (err) {
            helper.devConsole("Error during deleteUserFromMenu:", err);
            let userMessage = 'Fehler beim Entfernen des Rezepts aus dem Kochbuch.';
            if (err.response) {
                userMessage = err.response.data?.message || `Serverfehler (${err.response.status}).`;
            } else if (err.request) {
                userMessage = 'Netzwerkfehler. Bitte Verbindung prüfen.';
            } else {
                userMessage = 'Fehler beim Senden der Anfrage.';
            }
            setNotification(userMessage, 'alert');
        }
    }

    ///////////////// MENU RELATION //////////////////////

    const deleteRecipe = async (menuId, menuName) => {
        helper.devConsole(`deleteRecipe called for ID: ${menuId}, Name: ${menuName}`);
        try {
            const confirmed = await confirmationStore.showConfirmation(
                'Rezept löschen?', // Corrected Title
                `Möchten Sie das Rezept "${menuName || 'Dieses Rezept'}" wirklich dauerhaft löschen?` // Corrected Message
            );

            if (confirmed) {
                helper.devConsole(`User confirmed deletion for recipe: ${menuId}`);
                // Call the API endpoint to delete the menu
                // Assuming the endpoint is DELETE /api/v1/menu/one/:id
                const response = await axios.delete(`${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/one/${menuId}`);

                if (response.data.success) { // Check if the backend confirms success
                    setNotification('Rezept erfolgreich gelöscht.', 'success');
                    // Navigate back or to the recipe list
                    // Example: Navigate back one step in history
                    router.go(-1);
                    // Or navigate to a specific route:
                    // router.push({ name: 'kochbuch' });
                } else {
                    // Handle cases where the API call succeeded but deletion failed logically on the backend
                    setNotification(response.data.message || 'Rezept konnte nicht gelöscht werden (Backend).', 'alert');
                }
            } else {
                helper.devConsole(`User cancelled deletion for recipe: ${menuId}`);
            }
        } catch (err) {
            helper.devConsole("Error during deleteRecipe:", err);
            let userMessage = 'Fehler beim Löschen des Rezepts.';
            if (err.response) {
                userMessage = err.response.data?.message || `Serverfehler (${err.response.status}).`;
            } else if (err.request) {
                userMessage = 'Netzwerkfehler. Bitte Verbindung prüfen.';
            } else {
                userMessage = 'Fehler beim Senden der Löschanfrage.';
            }
            setNotification(userMessage, 'alert');
        }
    };

    return {
        // EXPORTET VALUES
        oneReciept,
        // EXPORTED FUNCTIONS
        setOneMenue,
        getOneMenue,
        countPerson,
        changeMenu,
        changeMenuchild,
        changeMenuchildArray,
        changeMenuchildSecondLevel,
        updateMenuAtDB,
        updateMenuchildAtDB,
        updateMenuchildIngredientsAtDB,
        createEmptyIngredientStep,
        createEmptyPreperationStep,
        goToEditModeOfReciept,
        uploadNewImage,
        setMenuRelationFreeAccessState,
        updateMenuRelationDB,
        deleteUserFromMenu,
        createShareableLink,
        recreateImageOfReciept,
        is_loading,
        loadFiles,
        filesIds,
        is_dragover,
        plannedSeats,
        loadNutrionScore,
        loadingStateNahrwerte,
        deleteRecipe,
        // 🛒 EINKAUFSZETTEL-INTEGRATION
        addRecipeToShoppingList,
        removeRecipeFromShoppingList,
        // 🔧 INGREDIENT-MANAGEMENT
        deleteIngredientGlobally,
    };

});


export const useMenuesStore = defineStore('reciepts', () => {

    const { setNotification } = useNotification();
    const userStore = useUserStore();
    const router = useRouter();
    const helper = useHelperStore()

    /* INITIALIZING VALUES */
    const myReciepts = ref([]);
    const allReciepts = ref([]);

    let loadMenusTokenSource = ref(null);
    const isLoadLocked = ref(false);

    const searchMyReciepts = ref("");
    const searchAllReciepts = ref("");
    const recipeScope = ref('kitchentable');
    const currentPage = ref(0);
    const limit = ref(9); // Backend default limit is 9 recipes per page
    const isLoadingMore = ref(false);
    const hasMoreData = ref(true);
    const hasLoadedOnce = ref(false); // Tracks if the initial load has completed

    const filterCreatedAt = ref('sort=-createdAt')
    const filterCookingTime = ref('')

    const filterBoxTime = ref([
        { name: 'filter_time_all_basic.png', active: true, filter: '' },
        { name: 'filter_time_15_active.png', active: false, filter: '&cookingTime[lte]=15' },
        { name: 'filter_time_25_active.png', active: false, filter: '&cookingTime[lte]=25' },
        { name: 'filter_time_35_active.png', active: false, filter: '&cookingTime[lte]=35' },
        { name: 'filter_time_45_active.png', active: false, filter: '&cookingTime[lte]=45' },
        { name: 'filter_time_gt45_active.png', active: false, filter: '&cookingTime[gte]=45' },
    ]);

    const filterBoxDate = ref([
        { name: 'filter_new_basic.png', active: false, filter: 'sort=createdAt' },
        { name: 'filter_new_active.png', active: true, filter: 'sort=-createdAt' },
    ]);

    // Filter states
    const isDateFilterActive = ref(false);
    const isTimeFilterActive = ref(false);
    const isShoppingListFilterActive = ref(false);

    // --- MODIFIED: Filter Toggle Functions now trigger load directly ---
    const toggleDateFilter = () => {
      helper.devConsole("Toggling Date Filter");
      const activeIndex = filterBoxDate.value.findIndex(f => f.active);
      if (activeIndex !== -1) {
          filterBoxDate.value[activeIndex].active = false;
          const nextIndex = (activeIndex + 1) % filterBoxDate.value.length;
          filterBoxDate.value[nextIndex].active = true;
          // Set the global filter value
          filterCreatedAt.value = filterBoxDate.value[nextIndex].filter;
          helper.devConsole(`Date Filter set to: ${filterCreatedAt.value}. Triggering reload.`);
          // Trigger load if user data is available
          if (userStore.user.id && userStore.user.defaultKitchentable) {
                currentPage.value = 0;
                hasMoreData.value = true;
                loadAllMenusByUser(userStore.user.id, userStore.user.defaultKitchentable, false);
          } else {
                helper.devConsole("Toggle Date Filter: User data not ready, load will happen via watcher later.");
          }
      }
    };

    const toggleTimeFilter = () => {
        helper.devConsole("Toggling Time Filter");
        const activeIndex = filterBoxTime.value.findIndex(f => f.active);
        if (activeIndex !== -1) {
            filterBoxTime.value[activeIndex].active = false;
            const nextIndex = (activeIndex + 1) % filterBoxTime.value.length;
            filterBoxTime.value[nextIndex].active = true;
            // Set the global filter value
            filterCookingTime.value = filterBoxTime.value[nextIndex].filter;
            helper.devConsole(`Time Filter set to: ${filterCookingTime.value}. Triggering reload.`);
            // Trigger load if user data is available
             if (userStore.user.id && userStore.user.defaultKitchentable) {
                currentPage.value = 0;
                hasMoreData.value = true;
                loadAllMenusByUser(userStore.user.id, userStore.user.defaultKitchentable, false);
            } else {
                 helper.devConsole("Toggle Time Filter: User data not ready, load will happen via watcher later.");
            }
        }
    };

    // --- Action to toggle filter and reload ---
    const toggleShoppingListFilterAndReload = () => {
        isShoppingListFilterActive.value = !isShoppingListFilterActive.value;
        helper.devConsole(`Toggled shopping list filter to: ${isShoppingListFilterActive.value}. Reloading...`);
        const currentUserId = userStore.user.id;
        const currentTableId = recipeScope.value === 'kitchentable' ? userStore.user.defaultKitchentable : null;
        loadAllMenusByUser(currentUserId, currentTableId, { isFreshLoad: true });
    };

    // FUNCTIONS MYRECIEPTS
    // Search for recipes
    const searchForRecieps = async () => {
        helper.devConsole("FUNC searchForRecieps triggerd");
        if (userStore.user.id && userStore.user.defaultKitchentable) {
            currentPage.value = 0;
            hasMoreData.value = true;
            await loadAllMenusByUser(userStore.user.id, userStore.user.defaultKitchentable, false);
        } else {
            helper.devConsole("Search Triggered: User data not ready, load will happen via watcher later.");
        }
    }

    //LOAD ALL MENUS BY USER ID (mit Paginierung & Cancellation & Load Lock)
    const loadAllMenusByUser = async (userId, kitchentableId, options = {}) => {
        const {
            loadMore = false,
            isFreshLoad = !loadMore
        } = options;

        // Determine filter state BEFORE logging or making decisions
        const applyShoppingListFilter = isShoppingListFilterActive.value;

        helper.devConsole(`loadAllMenusByUser called: {userId: ${userId}, kitchentableId: ${kitchentableId || 'none'}, currentScope: ${recipeScope.value}, loadMore: ${loadMore}, isFreshLoad: ${isFreshLoad}, isLoading: ${isLoadingMore.value}, shoppingListFilter: ${applyShoppingListFilter}}`);

        if (!userId) {
            helper.devConsole("loadAllMenusByUser: Aborted, no userId provided.");
            resetLoadState(); // Use helper function
            return;
        }

        if (isFreshLoad && activeRequestController.value) {
            helper.devConsole("loadAllMenusByUser: Fresh load requested, cancelling previous request...");
            activeRequestController.value.abort();
            activeRequestController.value = null;
            isLoadingMore.value = false;
        } else if (isLoadingMore.value) {
            helper.devConsole("loadAllMenusByUser: Aborted, already loading data.");
            return;
        }

        isLoadingMore.value = true;
        let pageToUse = 0; // Variable to hold the page number for the API call

        if (isFreshLoad) {
            hasLoadedOnce.value = false;
            currentPage.value = 0; // Reset page state for fresh load
            myReciepts.value = [];
            hasMoreData.value = true;
            pageToUse = 0; // Use page 0 for the fresh load call
            helper.devConsole(`Fresh load: Reset currentPage to 0. API call will use page=${pageToUse}.`);
        } else { // This is a loadMore call
            pageToUse = currentPage.value; // Use the current page state for loadMore
             helper.devConsole(`Load more: API call will use page=${pageToUse}.`);
        }

        const controller = new AbortController();
        activeRequestController.value = controller;

        try {
            // Use kitchentableId if scope is kitchentable and ID exists, otherwise 'none'
            const tableIdSegment = recipeScope.value === 'kitchentable' && kitchentableId ? kitchentableId : 'none';

            // Base URL with path segments
            const baseURL = `${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/complete/allbyuserid/${userId}/kitchentableid/${tableIdSegment}`;

            // Build query parameters object dynamically
            const queryParams = {
                scope: recipeScope.value, // Always include scope
                page: pageToUse,          // Always include page
                // limit: limit.value      // Limit seems optional based on API, removing for now unless needed
            };

            // Add sort parameter - Use filterCreatedAt ref which defaults to sort=-createdAt
            if (filterCreatedAt.value) {
                 const sortParam = filterCreatedAt.value.split('=');
                 if(sortParam.length === 2) {
                    queryParams[sortParam[0]] = sortParam[1];
                 }
            } else {
                 // Default sort if ref is somehow empty
                 queryParams.sort = '-createdAt';
            }

            // Add search string if present
            if (searchMyReciepts.value) {
                queryParams.searchstring = searchMyReciepts.value;
            }

            // Add cooking time filter if present (e.g., '&cookingTime[lte]=25')
            if (filterCookingTime.value) {
                // Extract key and value from filter string like '&key=value'
                const timeFilterMatch = filterCookingTime.value.match(/&([^=]+)=(.+)/);
                if (timeFilterMatch && timeFilterMatch.length === 3) {
                    queryParams[timeFilterMatch[1]] = timeFilterMatch[2];
                }
            }

            // Add shopping list filter if active
            if (applyShoppingListFilter) {
                queryParams.filterOnShoppingList = 'true';
            }


            helper.devConsole(`>>> Calling API: ${baseURL} with params:`, queryParams);

            // Use axios params object for cleaner encoding
            const response = await axios.get(baseURL, {
                params: queryParams,
                signal: controller.signal
            });

            helper.devConsole(">>> API Response Data (loadAllMenusByUser):", response.data);

            // Check if response is successful and data.menus is an array
            if (response.data && response.data.success && response.data.data && Array.isArray(response.data.data.menus)) {
                const newRecipes = response.data.data.menus; // Use the menus array
                helper.devConsole(">>> Processed Menus (newRecipes):", newRecipes);

                if (isFreshLoad) {
                    myReciepts.value = newRecipes;
                } else {
                    myReciepts.value.push(...newRecipes);
                }

                // Increment page state AFTER a successful call (both fresh and loadMore)
                if (loadMore) {
                   currentPage.value++;
                   helper.devConsole(`Load more successful, incremented currentPage to ${currentPage.value}`);
                } else if (isFreshLoad) {
                   currentPage.value = 1; // Set to 1 after successful fresh load (next page will be 1)
                   helper.devConsole(`Fresh load successful, set currentPage to ${currentPage.value}`);
                }

                // Adjust pagination check based on the length of the received menus array
                if (newRecipes.length < limit.value || newRecipes.length === 0) {
                    hasMoreData.value = false;
                    helper.devConsole(`No more data to load. Received ${newRecipes.length} recipes, limit is ${limit.value}`);
                } else {
                    hasMoreData.value = true;
                    helper.devConsole(`More data available. Received ${newRecipes.length} recipes, limit is ${limit.value}`);
                }
            } else {
                helper.devConsole("loadAllMenusByUser: No data found or unexpected format (response.data.data.menus was not an array).");
                if (isFreshLoad) myReciepts.value = [];
                hasMoreData.value = false;
            }

        } catch (error) {
            if (axios.isCancel(error)) {
                helper.devConsole('Load request cancelled.');
            } else {
                console.error('Error loading user menus:', error);
                setNotification('Fehler beim Laden der Rezepte.', 'alert');
                hasMoreData.value = false;
                if (isFreshLoad) myReciepts.value = [];
            }
        } finally {
            if (activeRequestController.value === controller) {
                 activeRequestController.value = null;
            }
            isLoadingMore.value = false;
            if (isFreshLoad) {
                hasLoadedOnce.value = true;
            }
            helper.devConsole(`loadAllMenusByUser finished, loading state set to ${isLoadingMore.value}. hasLoadedOnce: ${hasLoadedOnce.value}`);
        }
    }

    // Explizite Funktion zum Laden weiterer Rezepte
    const loadMoreRecipes = async () => {
        if (userStore.user.id) {
            // Verwende die richtige kitchentableId basierend auf dem aktuellen Scope
            const kitchentableId = recipeScope.value === 'kitchentable' ? userStore.user.defaultKitchentable : null;
            await loadAllMenusByUser(userStore.user.id, kitchentableId, { loadMore: true, isFreshLoad: false });
        } else {
            helper.devConsole("Cannot load more recipes: User data not available.");
        }
    }

    /////////////////////////////////////////////////////////////////////////////
    // FUNCTIONS MYRECIEPTS
    // Load all reciept names
    //searchForAllFreeAccessRecieps
    const searchForAllFreeAccessRecieps = async () => {
        console.log("/menu/related/severalusers/public")
        try{
            const answ = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/complete/all/freeaccess');
            //console.log(answ.data.data)
            allReciepts.value = answ.data.data
        } catch (error){
            helper.devConsole(error)
        }
    }


    // Find specific reciept names by name
    const searchForOpenReciepsByName = async () => {
        //"/menu/related/severalusers/public/:menusearchstring"
        try{
            const answ = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/menu/related/severalusers/public/' + searchAllReciepts.value);
            //console.log(answ.data.data.result)
            allReciepts.value = answ.data.data.result
        } catch (error){
            helper.devConsole(error)
        }

        if(searchAllReciepts.value == ""){
            await searchForAllFreeAccessRecieps()
        }
    }

    // Action to manually update the shopping list status of a menu in the list
    const setMenuZettelStatus = (menuId, status) => {
        if (myReciepts.value) {
            const menuIndex = myReciepts.value.findIndex(menu => menu._id === menuId);
            if (menuIndex !== -1) {
                // Update both properties for compatibility
                myReciepts.value[menuIndex].isZettel = status;
                myReciepts.value[menuIndex].isOnActiveShoppingList = status;
                helper.devConsole(`Updated shopping list status for menu ${menuId} to ${status} in menuStore.`);
            } else {
                helper.devConsole(`Menu ${menuId} not found in myReciepts to update shopping list status.`);
            }
        }
    };

    // --- ADD THIS LINE ---
    const activeRequestController = ref(null); // AbortController for cancelling requests

    return {
        myReciepts,
        allReciepts,
        searchMyReciepts,
        searchAllReciepts,
        filterBoxDate,
        filterBoxTime,
        filterCreatedAt,
        filterCookingTime,
        // MyReciepts
        searchForRecieps,
        loadAllMenusByUser,
        loadMoreRecipes,
        // AllReciepts
        searchForAllFreeAccessRecieps,
        searchForOpenReciepsByName,
        recipeScope,
        isLoadingMore,
        hasMoreData,
        setMenuZettelStatus,
        // Filter Actions
        toggleDateFilter,
        toggleTimeFilter,
        isShoppingListFilterActive,
        toggleShoppingListFilterAndReload,
        hasLoadedOnce, // Export the new ref
    }
});


export const useRelationMenueStore = defineStore('menurelations', () => {

    const { setNotification } = useNotification();

    /* INITIALIZING VALUES */
    const menurelations = ref();

    //GET ALL MENUS WHICH BELONG TO ONE USER
    const setMenuRelation = async (object) => {

        try{
            //let res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/menu/related/oneuser/' + userId);
            //myReciepts.value = res.data.data.data;
            console.log(object)

        } catch (error){
            console.log(error)
        }
    }


    return {
        menurelations,
        setMenuRelation
    }
});

