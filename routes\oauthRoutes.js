const express = require('express');
const oauthController = require('../controllers/oauthController');

const router = express.Router();

// POST /api/v1/oauth/token - Endpoint to obtain an access token
router.post('/token', oauthController.createToken);

// Pinterest OAuth routes
// POST /api/v1/oauth/pinterest/auth-url - Generate Pinterest authorization URL
router.post('/pinterest/auth-url', oauthController.pinterestAuthUrl);

// POST /api/v1/oauth/pinterest/token - Exchange authorization code for access token
router.post('/pinterest/token', oauthController.pinterestToken);

// GET /api/v1/oauth/pinterest/token - Exchange authorization code for access token (via query params)
router.get('/pinterest/token', oauthController.pinterestToken);

// GET /api/v1/oauth/pinterest/token-status - Check Pinterest token status
router.get('/pinterest/token-status', oauthController.pinterestTokenStatus);

// Alternative route for your specific URL pattern
router.post('/pinterest/auth-url', (req, res, next) => {
  // If code is provided as query parameter, redirect to token endpoint
  if (req.query.code) {
    return oauthController.pinterestToken(req, res, next);
  }
  // Otherwise, generate auth URL
  return oauthController.pinterestAuthUrl(req, res, next);
});

// GET /api/v1/oauth/pinterest/callback - Pinterest OAuth callback (for better UX)
router.get('/pinterest/callback', (req, res) => {
  const { code, state, error } = req.query;

  if (error) {
    return res.status(400).send(`
      <html>
        <body>
          <h1>Pinterest Authorization Failed</h1>
          <p>Error: ${error}</p>
          <p>Please try again.</p>
        </body>
      </html>
    `);
  }

  if (code) {
    return res.status(200).send(`
      <html>
        <body>
          <h1>Pinterest Authorization Successful!</h1>
          <p><strong>Authorization Code:</strong> <code>${code}</code></p>
          <p>Copy this code and use it in the token exchange API call:</p>
          <pre>
curl -X POST http://localhost:8080/api/v1/oauth/pinterest/token \\
  -H "Content-Type: application/json" \\
  -d '{"code": "${code}"}'
          </pre>
          <p>State: ${state}</p>
        </body>
      </html>
    `);
  }

  return res.status(400).send(`
    <html>
      <body>
        <h1>Invalid Pinterest Callback</h1>
        <p>No authorization code received.</p>
      </body>
    </html>
  `);
});

// KRITISCH: Pinterest OAuth Callback Route für /auth/pinterest/callback
// GET /auth/pinterest/callback - Pinterest OAuth callback (alternative URL)
router.get('/callback', async (req, res) => {
  const { code, state, error } = req.query;

  if (error) {
    return res.status(400).send(`
      <html>
        <body>
          <h1>Pinterest Authorization Failed</h1>
          <p>Error: ${error}</p>
          <p>Please try again.</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);
  }

  if (code) {
    // KRITISCH: Direkte API-Integration für Token-Exchange
    try {
      console.log('[Pinterest Callback] Received authorization code:', code);

      // Direkte Pinterest API-Integration
      const axios = require('axios');

      // Prüfe verschiedene mögliche Environment Variable Namen
      const clientId = process.env.PINTEREST_APP_ID ||
                      process.env.PINTEREST_CLIENT_ID ||
                      process.env.PINTEREST_APP_KEY;

      const clientSecret = process.env.PINTEREST_APP_SECRET ||
                          process.env.PINTEREST_CLIENT_SECRET ||
                          process.env.PINTEREST_APP_SECRET_KEY;

      // KRITISCH: Redirect URI muss exakt mit der Auth URL übereinstimmen
      const redirectUri = process.env.PINTEREST_REDIRECT_URI ||
                         process.env.PINTEREST_CALLBACK_URL ||
                         'http://localhost:8080/auth/pinterest/callback';

      console.log('[Pinterest Callback] CRITICAL: Checking redirect URI consistency...');
      console.log('[Pinterest Callback] Using redirect URI:', redirectUri);
      console.log('[Pinterest Callback] This MUST match the URI used in auth URL generation!');

      const tokenData = {
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: redirectUri,
        client_id: clientId,
        client_secret: clientSecret
      };

      console.log('[Pinterest Callback] Environment variables check:', {
        PINTEREST_APP_ID: process.env.PINTEREST_APP_ID ? `${process.env.PINTEREST_APP_ID.substring(0, 10)}...` : 'MISSING',
        PINTEREST_CLIENT_ID: process.env.PINTEREST_CLIENT_ID ? `${process.env.PINTEREST_CLIENT_ID.substring(0, 10)}...` : 'MISSING',
        PINTEREST_APP_KEY: process.env.PINTEREST_APP_KEY ? `${process.env.PINTEREST_APP_KEY.substring(0, 10)}...` : 'MISSING',
        PINTEREST_APP_SECRET: process.env.PINTEREST_APP_SECRET ? `${process.env.PINTEREST_APP_SECRET.substring(0, 10)}...` : 'MISSING',
        PINTEREST_CLIENT_SECRET: process.env.PINTEREST_CLIENT_SECRET ? `${process.env.PINTEREST_CLIENT_SECRET.substring(0, 10)}...` : 'MISSING',
        PINTEREST_REDIRECT_URI: process.env.PINTEREST_REDIRECT_URI || 'MISSING',
        PINTEREST_CALLBACK_URL: process.env.PINTEREST_CALLBACK_URL || 'MISSING',
        finalClientId: clientId ? `${clientId.substring(0, 10)}...` : 'MISSING',
        finalClientSecret: clientSecret ? `${clientSecret.substring(0, 10)}...` : 'MISSING',
        finalRedirectUri: redirectUri
      });

      // Validiere dass alle erforderlichen Credentials vorhanden sind
      if (!clientId || !clientSecret) {
        console.error('[Pinterest Callback] Missing required credentials:', {
          clientId: !!clientId,
          clientSecret: !!clientSecret
        });

        return res.status(400).send(`
          <html>
            <head>
              <title>Pinterest Configuration Error</title>
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .error { color: red; }
              </style>
            </head>
            <body>
              <h1 class="error">Pinterest Configuration Error ❌</h1>
              <p><strong>Missing Pinterest API Credentials</strong></p>
              <p>Client ID: ${clientId ? '✅ Present' : '❌ Missing'}</p>
              <p>Client Secret: ${clientSecret ? '✅ Present' : '❌ Missing'}</p>
              <p>Please configure the following environment variables:</p>
              <ul style="text-align: left; display: inline-block;">
                <li>PINTEREST_APP_ID or PINTEREST_CLIENT_ID</li>
                <li>PINTEREST_APP_SECRET or PINTEREST_CLIENT_SECRET</li>
              </ul>
              <script>
                setTimeout(() => {
                  window.close();
                }, 10000);
              </script>
            </body>
          </html>
        `);
      }

      console.log('[Pinterest Callback] Exchanging code for token with data:', {
        grant_type: tokenData.grant_type,
        code: code.substring(0, 10) + '...',
        redirect_uri: tokenData.redirect_uri,
        client_id: tokenData.client_id ? `${tokenData.client_id.substring(0, 10)}...` : 'missing',
        client_secret: tokenData.client_secret ? `${tokenData.client_secret.substring(0, 10)}...` : 'missing'
      });

      // Pinterest API v5 - Versuche verschiedene Formate
      console.log('[Pinterest Callback] Trying Pinterest API with Basic Auth...');

      // Erstelle Basic Auth Header
      const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

      // Versuche zuerst mit Basic Auth und form-urlencoded (Standard OAuth2)
      const qs = require('querystring');
      const formData = qs.stringify({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: redirectUri
      });

      console.log('[Pinterest Callback] Sending with Basic Auth and form data:', formData);

      let response;
      try {
        response = await axios.post('https://api.pinterest.com/v5/oauth/token', formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${basicAuth}`,
            'Accept': 'application/json'
          }
        });
      } catch (basicAuthError) {
        console.log('[Pinterest Callback] Basic Auth failed, trying JSON with client credentials in body...');

        // Fallback: JSON mit client credentials im body
        response = await axios.post('https://api.pinterest.com/v5/oauth/token', tokenData, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
      }

      console.log('[Pinterest Callback] Pinterest API response:', {
        status: response.status,
        hasAccessToken: !!response.data.access_token,
        hasRefreshToken: !!response.data.refresh_token,
        scope: response.data.scope
      });

      if (response.data && response.data.access_token) {
        // Save token to database
        const PinterestToken = require('../models/pinterestTokenModel');

        console.log('[Pinterest Callback] Saving tokens to database...');
        const savedToken = await PinterestToken.saveTokens(
          response.data.access_token,
          response.data.refresh_token,
          response.data.scope
        );

        console.log('[Pinterest Callback] Token saved successfully with ID:', savedToken._id);

        return res.status(200).send(`
          <html>
            <head>
              <title>Pinterest Connected</title>
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .success { color: green; }
              </style>
            </head>
            <body>
              <h1 class="success">Pinterest Connected Successfully! ✅</h1>
              <p>Your Pinterest account has been linked to Ordy.</p>
              <p><strong>Token ID:</strong> ${savedToken._id}</p>
              <p>You can now close this window.</p>
              <script>
                setTimeout(() => {
                  window.close();
                }, 3000);
              </script>
            </body>
          </html>
        `);
      } else {
        throw new Error('No access token received from Pinterest API');
      }

    } catch (error) {
      console.error('[Pinterest Callback] Token exchange failed:', error.response?.data || error.message);

      return res.status(400).send(`
        <html>
          <head>
            <title>Pinterest Connection Failed</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .error { color: red; }
            </style>
          </head>
          <body>
            <h1 class="error">Pinterest Connection Failed ❌</h1>
            <p><strong>Error:</strong> ${error.response?.data?.error_description || error.message}</p>
            <p><strong>Details:</strong> ${JSON.stringify(error.response?.data || {}, null, 2)}</p>
            <p>Please try again.</p>
            <script>
              setTimeout(() => {
                window.close();
              }, 5000);
            </script>
          </body>
        </html>
      `);
    }
  }

  return res.status(400).send(`
    <html>
      <body>
        <h1>Invalid Pinterest Callback</h1>
        <p>No authorization code received.</p>
        <script>
          setTimeout(() => {
            window.close();
          }, 3000);
        </script>
      </body>
    </html>
  `);
});

module.exports = router;