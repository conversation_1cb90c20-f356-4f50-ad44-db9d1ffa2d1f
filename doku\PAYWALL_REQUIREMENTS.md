# PAYWALL ENHANCEMENT REQUIREMENTS

## Übersicht
Detaillierte Anforderungen für die Verbesserung der Paywall-Funktionalität in der Ordy-App.

## REQ-PAY-001: Nutzungsüberwachung erweitern

### Beschreibung
Der User soll seine Nutzung der KI in der Abonnement-Seite überwachen können, inklusive der neuen Realtime API (Assistant).

### Aktueller Zustand
- Tracking für: Menucreations, Cookeasy, Menuuploads
- Fehlend: Realtime API (Assistant) Nutzung

### Anforderungen
1. **Realtime API Tracking hinzufügen**
   - Neue Felder im User Model: `nrRealtimeApiCalls`, `nrRealtimeApiCalls_active`
   - Tracking bei jeder Assistant-Session
   - Integration in bestehende Abo-Logik

2. **UI-Namen optimieren**
   - Menucreations → "KI-Menüerstellung"
   - Cookeasy → "KI-Kochassistent" 
   - Menuuploads → "Rezept-Uploads"
   - Realtime API → "Sprach-Assistent"

### Technische Details
- Backend: User Model <PERSON>, Tracking in assistantStore.js
- Frontend: aboCard.vue erwei<PERSON>, aboStore.js anpassen

---

## REQ-PAY-003: Abo-Management Fehler beheben

### Beschreibung
Ein neues Abo zu buchen oder zu ändern führt aktuell zu einem 401 error vom Backend und zum logout im frontend.

### Problem-Analyse
- `paymentController.js` Zeile 210: Bei Fehlern wird 401 Status zurückgegeben
- 401 führt im Frontend zum automatischen Logout
- Session wird invalidiert bei Abo-Änderungen

### Lösungsansatz
1. **Error Handling verbessern**
   - 401 nur bei echten Auth-Fehlern verwenden
   - Für Stripe-Fehler: 400 oder 500 verwenden
   - Detaillierte Fehlermeldungen

2. **Session Management korrigieren**
   - Session bei Abo-Änderungen beibehalten
   - Stripe-Fehler nicht als Auth-Fehler behandeln

### Technische Umsetzung
- Backend: `paymentController.js` Error Handling überarbeiten
- Frontend: Error Handling in aboStore.js verbessern

---

## REQ-PAY-004: Verfügbares Guthaben anzeigen

### Beschreibung
Neben der Nutzung (Balken) sollen auch die verfügbaren Guthaben je nach Abo in Zahlen stehen.

### Aktueller Zustand
- Nur Balken-Darstellung der Nutzung
- Keine numerische Anzeige des verfügbaren Guthabens

### Anforderungen
1. **Numerische Anzeige**
   - Format: "X von Y verwendet" oder "Y - X verfügbar"
   - Für alle 4 Nutzungstypen
   - Responsive Design

2. **Abo-spezifische Limits**
   - Dynamische Anzeige je nach Abo-Typ
   - Echtzeit-Updates bei Nutzung

### UI-Design
```
KI-Menüerstellung    [████████░░] 8/10 verfügbar
KI-Kochassistent     [██████░░░░] 6/10 verfügbar
Rezept-Uploads       [███░░░░░░░] 3/10 verfügbar
Sprach-Assistent     [█████░░░░░] 5/10 verfügbar
```

---

## REQ-PAY-005: Profilbild-Management

### Beschreibung
In den Settings soll jeder User ein Profilbild ändern/hochladen oder per KI generieren können.

### Funktionen
1. **Profilbild Upload**
   - Datei-Upload (JPEG, PNG, HEIC)
   - Bildgrößen-Validierung
   - Automatische Komprimierung
   - S3 Storage Integration

2. **KI-Profilbild-Generator**
   - Prompt-basierte Generierung
   - Verschiedene Stile auswählbar
   - Vorschau vor Speicherung
   - DALL-E Integration

3. **Profilbild-Verwaltung**
   - Aktuelles Bild anzeigen
   - Ändern/Löschen
   - Vorschau-Funktionalität

### Technische Umsetzung
- Backend: Neue API Endpoints, S3 Integration, DALL-E Integration
- Frontend: Upload-Interface, KI-Generator UI, Bildvorschau

---

## REQ-PAY-006: Automatische Profilbild-Generierung

### Beschreibung
Wenn ein neuer User erstellt wird, soll automatisch ein Profilbild per KI erstellt werden.

### Funktionalität
1. **Automatische Generierung**
   - Bei User-Registrierung
   - Basierend auf Vorname/Nachname
   - Fallback auf generische Avatare

2. **Background Processing**
   - Asynchrone Generierung
   - Keine Verzögerung der Registrierung
   - Retry-Mechanismus bei Fehlern

3. **Default Handling**
   - Placeholder während Generierung
   - Fallback bei KI-Fehlern
   - User kann später ändern

### Technische Details
- Trigger: User Creation Hook
- Processing: Background Job/Queue
- Storage: S3 Bucket
- Fallback: Default Avatar System

---

## Implementierungs-Prioritäten

### Phase 1 (Kritisch)
1. REQ-PAY-003: Abo-Management Fehler beheben
2. REQ-PAY-001: Realtime API Tracking

### Phase 2 (Wichtig)  
3. REQ-PAY-004: Guthaben-Anzeige
4. REQ-PAY-005: Profilbild Upload

### Phase 3 (Nice-to-have)
5. REQ-PAY-005: KI-Profilbild-Generator
6. REQ-PAY-006: Automatische Generierung

## Testing-Strategie

### Backend Tests
- Unit Tests für neue API Endpoints
- Integration Tests für Stripe-Integration
- S3 Upload Tests

### Frontend Tests
- Component Tests für UI-Änderungen
- E2E Tests für Abo-Management
- Upload-Funktionalität Tests

### User Acceptance Tests
- Abo-Wechsel ohne Logout
- Profilbild Upload/Generierung
- Nutzungsanzeige Korrektheit
