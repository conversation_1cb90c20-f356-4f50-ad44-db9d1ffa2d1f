const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const ServiceClient = require('../models/serviceClientModel');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const helper = require('../utils/helper');
const mongoose = require('mongoose');
const axios = require('axios');

const jwtSecret = process.env.JWT_SECRET;
const serviceTokenExpiresInSeconds = 3600; // 1 hour

// Function to sign a token specifically for a service client
const signServiceClientToken = (serviceName, clientId) => {
  const payload = {
    service: serviceName, // Use the service name in the payload
    sub: clientId,        // Use clientId as the subject
    // Add iat (issued at) automatically by the library
  };
  return jwt.sign(payload, jwtSecret, {
    expiresIn: `${serviceTokenExpiresInSeconds}s` // e.g., '3600s'
  });
};

/**
 * @description Handles the OAuth 2.0 Client Credentials Grant flow.
 * Allows registered service clients to exchange their credentials (client_id, client_secret)
 * for a short-lived bearer access token (JWT).
 *
 * --- Setup Instructions ---
 *
 * 1. Create Service Client in Database:
 *    - Manually add an entry to the 'serviceclients' collection in MongoDB.
 *    - Fields required:
 *      - name (String): Descriptive name for the client service (e.g., "Reporting Service").
 *      - clientId (String): A unique ID for the client.
 *          Generate using: node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
 *      - clientSecret (String): The *hashed* client secret.
 *          Generate a plain secret: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
 *          Hash the plain secret (using bcryptjs, cost 12):
 *            node -e "const bcrypt = require('bcryptjs'); console.log(bcrypt.hashSync('YOUR_PLAIN_SECRET_HERE', 12))"
 *          Store the resulting hash in this field.
 *    - Example Document Structure:
 *      {
 *        "name": "MeinReportingService",
 *        "clientId": "a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6",
 *        "clientSecret": "$2a$12$DeinBcryptHashVomPlainSecret.....",
 *        "_id": ObjectId("..."),
 *        "createdAt": ISODate("...)"
 *      }
 *
 * 2. Client Authentication Flow:
 *    a) Request Token:
 *       - Send a POST request to: /api/v1/oauth/token
 *       - Set Header: Content-Type: application/x-www-form-urlencoded
 *       - Send Body (form-urlencoded):
 *         - grant_type=client_credentials
 *         - client_id=<YourClientID>
 *         - client_secret=<Your_PLAIN_Secret>
 *       - Successful Response (200 OK):
 *         {
 *           "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *           "token_type": "Bearer",
 *           "expires_in": 3600
 *         }
 *    b) Call Protected API Endpoints:
 *       - Include the obtained 'access_token' in the Authorization header for subsequent requests:
 *         Authorization: Bearer <access_token>
 *       - Cache the token until it expires (check 'expires_in'). Request a new token when needed.
 */
exports.createToken = catchAsync(async (req, res, next) => {
  const functionName = 'oauthController.createToken';
  // helper.devConsole(`[${functionName}] Start`);

  const { grant_type, client_id, client_secret } = req.body;

  // 1. Validate input
  if (grant_type !== 'client_credentials') {
    // helper.devConsole(`[${functionName}] Invalid grant_type: ${grant_type}`);
    return next(new AppError('Unsupported grant_type', 400));
  }

  if (!client_id || !client_secret) {
    // helper.devConsole(`[${functionName}] Missing client_id or client_secret`);
    return next(new AppError('client_id and client_secret are required', 400));
  }

  // Log Mongoose connection state RIGHT BEFORE the query
  helper.devConsole(`[${functionName}] Mongoose connection readyState: ${mongoose.connection.readyState}`); // 0: disconnected, 1: connected, 2: connecting, 3: disconnecting

  // 2. Find the client by clientId, explicitly selecting the clientSecret
  // helper.devConsole(`[${functionName}] Finding client with ID: ${client_id}`);
  const serviceClient = await ServiceClient.findOne({ clientId: client_id }).select('+clientSecret');

  // 3. Check if client exists and secret is correct
  if (!serviceClient || !(await serviceClient.correctSecret(client_secret, serviceClient.clientSecret))) {
    // helper.devConsole(`[${functionName}] Invalid client_id or client_secret`);
    // Use a generic error message for security (do not reveal which one was wrong)
    return next(new AppError('Invalid client credentials', 401)); // 401 Unauthorized
  }

  // helper.devConsole(`[${functionName}] Client validated: ${serviceClient.name}`);

  // 4. Generate the access token
  const accessToken = signServiceClientToken(serviceClient.name, serviceClient.clientId);
  // helper.devConsole(`[${functionName}] Access token generated for service: ${serviceClient.name}`);

  // 5. Send the token response (OAuth 2.0 standard format)
  res.status(200).json({
    access_token: accessToken,
    token_type: 'Bearer',
    expires_in: serviceTokenExpiresInSeconds
  });
});

// Pinterest OAuth endpoints
exports.pinterestAuthUrl = catchAsync(async (req, res, next) => {
  helper.devConsole('[oauthController.pinterestAuthUrl] Generating Pinterest auth URL...');

  const clientId = process.env.PINTEREST_CLIENT_ID || process.env.PINTEREST_APP_ID;
  // KRITISCH: Redirect URI muss mit Callback Route übereinstimmen
  const redirectUri = process.env.PINTEREST_REDIRECT_URI || process.env.PINTEREST_CALLBACK_URL || 'http://localhost:8080/auth/pinterest/callback';

  if (!clientId) {
    return next(new AppError('Pinterest Client ID not configured', 400));
  }

  // Pinterest OAuth Scopes - ERWEITERT für vollständige API-Funktionalität
  const scopes = [
    'boards:read',      // Boards lesen
    'boards:write',     // Boards erstellen/bearbeiten
    'pins:read',        // Pins lesen (KRITISCH - war fehlend!)
    'pins:write',       // Pins erstellen/bearbeiten
    'user_accounts:read' // User-Account Informationen
  ].join(',');

  console.log('[oauthController.pinterestAuthUrl] Using scopes:', scopes);

  const authUrl = `https://www.pinterest.com/oauth/?` +
    `client_id=${clientId}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `response_type=code&` +
    `scope=${encodeURIComponent(scopes)}&` +
    `state=pinterest_oauth_setup`;

  res.status(200).json({
    status: 'success',
    data: {
      authUrl: authUrl,
      clientId: clientId,
      redirectUri: redirectUri
    }
  });
});

exports.pinterestToken = catchAsync(async (req, res, next) => {
  helper.devConsole('[oauthController.pinterestToken] Exchanging code for token...');

  // Accept code from both body and query parameters
  const code = req.body.code || req.query.code;

  if (!code) {
    return next(new AppError('Authorization code is required (provide as body.code or query.code)', 400));
  }

  helper.devConsole('[oauthController.pinterestToken] Received code:', code.substring(0, 10) + '...');

  const clientId = process.env.PINTEREST_CLIENT_ID || process.env.PINTEREST_APP_ID;
  const clientSecret = process.env.PINTEREST_CLIENT_SECRET || process.env.PINTEREST_APP_SECRET;
  // KRITISCH: Redirect URI muss mit Callback Route übereinstimmen
  const redirectUri = process.env.PINTEREST_REDIRECT_URI || process.env.PINTEREST_CALLBACK_URL || 'http://localhost:8080/auth/pinterest/callback';

  if (!clientId || !clientSecret) {
    return next(new AppError('Pinterest credentials not configured', 400));
  }

  try {
    // Pinterest API expects form-urlencoded data
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('code', code);
    formData.append('redirect_uri', redirectUri);
    formData.append('client_id', clientId);
    formData.append('client_secret', clientSecret);

    helper.devConsole('[oauthController.pinterestToken] Sending request to Pinterest with:', {
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: redirectUri,
      client_id: clientId ? 'present' : 'missing',
      client_secret: clientSecret ? 'present' : 'missing'
    });

    // Debug: Log the actual form data being sent
    console.log('[PINTEREST DEBUG] FormData contents:', formData.toString());

    const response = await axios.post('https://api.pinterest.com/v5/oauth/token', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    helper.devConsole('[oauthController.pinterestToken] Pinterest API response:', {
      status: response.status,
      data: response.data
    });

    if (response.data && response.data.access_token) {
      // Save token to database using the correct model method
      const PinterestToken = require('../models/pinterestTokenModel');

      console.log('[oauthController.pinterestToken] About to save tokens to database...');
      const savedToken = await PinterestToken.saveTokens(
        response.data.access_token,
        response.data.refresh_token,
        response.data.scope
      );

      helper.devConsole('[oauthController.pinterestToken] Token saved successfully with ID:', savedToken._id);

      res.status(200).json({
        status: 'success',
        message: 'Pinterest token saved successfully',
        data: {
          tokenType: response.data.token_type,
          expiresIn: response.data.expires_in,
          scope: response.data.scope,
          tokenId: savedToken._id
        }
      });
    } else {
      helper.devConsole('[oauthController.pinterestToken] Invalid Pinterest API response:', response.data);
      return next(new AppError('Invalid response from Pinterest API', 400));
    }
  } catch (error) {
    helper.devConsole('[oauthController.pinterestToken] Error:', error.response?.data || error.message);
    return next(new AppError(`Failed to exchange code for token: ${error.response?.data?.message || error.message}`, 400));
  }
});

// Pinterest Token Status Check
exports.pinterestTokenStatus = catchAsync(async (req, res, next) => {
  helper.devConsole('[oauthController.pinterestTokenStatus] Checking Pinterest token status...');

  try {
    const PinterestToken = require('../models/pinterestTokenModel');
    console.log('[pinterestTokenStatus] Searching for current token...');

    const tokenRecord = await PinterestToken.getCurrentToken();
    console.log('[pinterestTokenStatus] Token search result:', {
      found: !!tokenRecord,
      hasAccessToken: tokenRecord?.accessToken ? 'present' : 'missing',
      isActive: tokenRecord?.isActive,
      environment: tokenRecord?.environment,
      tokenId: tokenRecord?._id
    });

    if (tokenRecord && tokenRecord.accessToken) {
      const now = new Date();
      const isExpiringSoon = tokenRecord.isExpiringSoon ? tokenRecord.isExpiringSoon() : false;

      // KRITISCH: Scope-Validierung prüfen
      const scopeValidation = await PinterestToken.validateScopes();
      console.log('[pinterestTokenStatus] Scope validation result:', scopeValidation);

      res.status(200).json({
        status: 'success',
        message: scopeValidation.valid ? 'Pinterest token is active with correct scopes' : 'Pinterest token is active but missing required scopes',
        data: {
          hasToken: true,
          isActive: tokenRecord.isActive,
          isExpiringSoon: isExpiringSoon,
          lastRefreshed: tokenRecord.lastRefreshed,
          scope: tokenRecord.scope,
          environment: tokenRecord.environment,
          tokenId: tokenRecord._id,
          scopeValidation: scopeValidation
        }
      });
    } else {
      console.log('[pinterestTokenStatus] No valid token found in database');
      res.status(200).json({
        status: 'success',
        message: 'No Pinterest token found',
        data: {
          hasToken: false
        }
      });
    }
  } catch (error) {
    console.error('[oauthController.pinterestTokenStatus] Error:', error.message);
    helper.devConsole('[oauthController.pinterestTokenStatus] Error:', error.message);
    return next(new AppError(`Failed to check token status: ${error.message}`, 500));
  }
});