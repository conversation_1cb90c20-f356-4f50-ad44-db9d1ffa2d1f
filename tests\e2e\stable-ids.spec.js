const { test, expect } = require('@playwright/test');

test.describe('StableID System E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigiere zur Login-Seite oder direkt zur App
    await page.goto('http://localhost:5173');
    
    // Warte auf die App-Initialisierung
    await page.waitForLoadState('networkidle');
    
    // Falls Login erforderlich ist, hier implementieren
    // await page.fill('[data-testid="email"]', '<EMAIL>');
    // await page.fill('[data-testid="password"]', 'password');
    // await page.click('[data-testid="login-button"]');
  });

  test('Rezept erstellen und StableIDs testen', async ({ page }) => {
    console.log('🧪 Test: Rezept erstellen und StableIDs testen');
    
    // Navigiere zur Rezept-Erstellung
    await page.goto('http://localhost:5173/kochbuch');
    await page.waitForLoadState('networkidle');
    
    // Klicke auf "Neues Rezept" oder ähnlichen Button
    const newRecipeButton = page.locator('[data-testid="new-recipe"], .new-recipe-btn, button:has-text("Neues Rezept")').first();
    if (await newRecipeButton.isVisible()) {
      await newRecipeButton.click();
    } else {
      // Fallback: Navigiere direkt zur Rezept-Erstellung
      await page.goto('http://localhost:5173/kochbuch/menu/new');
    }
    
    await page.waitForLoadState('networkidle');
    
    // Fülle Rezept-Grunddaten aus
    const titleInput = page.locator('input[placeholder*="Titel"], input[name="title"], [data-testid="recipe-title"]').first();
    if (await titleInput.isVisible()) {
      await titleInput.fill('StableID Test Rezept');
    }
    
    // Füge erste Zutat hinzu
    console.log('   📋 Füge erste Zutat hinzu: Mehl');
    const addIngredientBtn = page.locator('button:has-text("Zutat hinzufügen"), [data-testid="add-ingredient"], .add-ingredient').first();
    if (await addIngredientBtn.isVisible()) {
      await addIngredientBtn.click();
    }
    
    // Fülle Zutat-Details aus
    const ingredientNameInput = page.locator('input[placeholder*="Zutat"], input[name*="ingredient"], [data-testid="ingredient-name"]').last();
    if (await ingredientNameInput.isVisible()) {
      await ingredientNameInput.fill('Mehl');
    }
    
    const amountInput = page.locator('input[placeholder*="Menge"], input[name*="amount"], [data-testid="ingredient-amount"]').last();
    if (await amountInput.isVisible()) {
      await amountInput.fill('500');
    }
    
    // Speichere das Rezept
    const saveButton = page.locator('button:has-text("Speichern"), [data-testid="save-recipe"], .save-btn').first();
    if (await saveButton.isVisible()) {
      await saveButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    console.log('   ✅ Erstes Rezept mit einer Zutat erstellt');
    
    // Füge zweite Zutat hinzu
    console.log('   📋 Füge zweite Zutat hinzu: Milch');
    if (await addIngredientBtn.isVisible()) {
      await addIngredientBtn.click();
    }
    
    const secondIngredientName = page.locator('input[placeholder*="Zutat"], input[name*="ingredient"], [data-testid="ingredient-name"]').last();
    if (await secondIngredientName.isVisible()) {
      await secondIngredientName.fill('Milch');
    }
    
    const secondAmountInput = page.locator('input[placeholder*="Menge"], input[name*="amount"], [data-testid="ingredient-amount"]').last();
    if (await secondAmountInput.isVisible()) {
      await secondAmountInput.fill('250');
    }
    
    // Speichere erneut
    if (await saveButton.isVisible()) {
      await saveButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    console.log('   ✅ Zweite Zutat hinzugefügt');
    
    // Prüfe, ob Zutaten-Buttons mit IDs vorhanden sind
    const ingredientButtons = page.locator('[data-stable-id], .ingredient-button, button:has-text("${ID:")');
    const buttonCount = await ingredientButtons.count();
    
    if (buttonCount >= 2) {
      console.log(`   ✅ ${buttonCount} Zutaten-Buttons gefunden`);
    } else {
      console.log(`   ⚠️ Nur ${buttonCount} Zutaten-Buttons gefunden`);
    }
    
    // Teste Zutaten-Löschung
    console.log('   📋 Teste Zutaten-Löschung');
    const deleteButtons = page.locator('button:has-text("Löschen"), [data-testid="delete-ingredient"], .delete-ingredient').first();
    if (await deleteButtons.isVisible()) {
      await deleteButtons.click();
      
      // Bestätige Löschung falls Dialog erscheint
      const confirmButton = page.locator('button:has-text("Bestätigen"), button:has-text("Ja"), [data-testid="confirm-delete"]').first();
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
      }
      
      await page.waitForLoadState('networkidle');
      console.log('   ✅ Zutat gelöscht');
    }
    
    // Füge neue Zutat hinzu (sollte nicht die gelöschte ID wiederverwenden)
    console.log('   📋 Füge neue Zutat hinzu: Eier');
    if (await addIngredientBtn.isVisible()) {
      await addIngredientBtn.click();
    }
    
    const thirdIngredientName = page.locator('input[placeholder*="Zutat"], input[name*="ingredient"], [data-testid="ingredient-name"]').last();
    if (await thirdIngredientName.isVisible()) {
      await thirdIngredientName.fill('Eier');
    }
    
    const thirdAmountInput = page.locator('input[placeholder*="Menge"], input[name*="amount"], [data-testid="ingredient-amount"]').last();
    if (await thirdAmountInput.isVisible()) {
      await thirdAmountInput.fill('2');
    }
    
    // Finale Speicherung
    if (await saveButton.isVisible()) {
      await saveButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    console.log('   ✅ Dritte Zutat hinzugefügt');
    
    // Prüfe finale Zutaten-Anzahl
    const finalButtonCount = await ingredientButtons.count();
    console.log(`   📊 Finale Anzahl Zutaten-Buttons: ${finalButtonCount}`);
    
    // Test erfolgreich wenn mindestens 2 Zutaten vorhanden sind
    expect(finalButtonCount).toBeGreaterThanOrEqual(2);
    
    console.log('   🎉 StableID Test erfolgreich abgeschlossen');
  });

  test('Rezept zu Einkaufszettel hinzufügen', async ({ page }) => {
    console.log('🧪 Test: Rezept zu Einkaufszettel hinzufügen');
    
    // Navigiere zu einem bestehenden Rezept
    await page.goto('http://localhost:5173/kochbuch');
    await page.waitForLoadState('networkidle');
    
    // Klicke auf das erste verfügbare Rezept
    const firstRecipe = page.locator('.recipe-card, [data-testid="recipe-item"], .menu-item').first();
    if (await firstRecipe.isVisible()) {
      await firstRecipe.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Suche nach "Zum Einkaufszettel hinzufügen" Button
    const addToShoppingListBtn = page.locator(
      'button:has-text("Einkaufszettel"), button:has-text("Zum Zettel"), [data-testid="add-to-shopping-list"]'
    ).first();
    
    if (await addToShoppingListBtn.isVisible()) {
      await addToShoppingListBtn.click();
      await page.waitForLoadState('networkidle');
      
      console.log('   ✅ Rezept zum Einkaufszettel hinzugefügt');
      
      // Navigiere zum Einkaufszettel
      await page.goto('http://localhost:5173/zettel');
      await page.waitForLoadState('networkidle');
      
      // Prüfe, ob Zutaten im Einkaufszettel vorhanden sind
      const shoppingItems = page.locator('.shopping-item, [data-testid="shopping-item"], .zettel-item');
      const itemCount = await shoppingItems.count();
      
      console.log(`   📊 ${itemCount} Artikel im Einkaufszettel gefunden`);
      expect(itemCount).toBeGreaterThan(0);
      
      console.log('   🎉 Einkaufszettel-Integration erfolgreich');
    } else {
      console.log('   ⚠️ "Zum Einkaufszettel" Button nicht gefunden');
    }
  });

  test('Platzhalter im Rezepttext testen', async ({ page }) => {
    console.log('🧪 Test: Platzhalter im Rezepttext');
    
    // Navigiere zu einem Rezept mit Zubereitungstext
    await page.goto('http://localhost:5173/kochbuch');
    await page.waitForLoadState('networkidle');
    
    const firstRecipe = page.locator('.recipe-card, [data-testid="recipe-item"], .menu-item').first();
    if (await firstRecipe.isVisible()) {
      await firstRecipe.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Suche nach Platzhaltern im Text
    const placeholders = page.locator('text=/\\$\\{ID:\\d+\\}/, [data-placeholder-id], .ingredient-placeholder');
    const placeholderCount = await placeholders.count();
    
    console.log(`   📊 ${placeholderCount} Platzhalter im Text gefunden`);
    
    if (placeholderCount > 0) {
      // Klicke auf den ersten Platzhalter
      await placeholders.first().click();
      console.log('   ✅ Platzhalter-Interaktion getestet');
    }
    
    // Prüfe, ob Zutaten-Buttons vorhanden sind
    const ingredientButtons = page.locator('.ingredient-button, [data-stable-id]');
    const buttonCount = await ingredientButtons.count();
    
    console.log(`   📊 ${buttonCount} Zutaten-Buttons gefunden`);
    
    if (buttonCount > 0) {
      console.log('   🎉 Platzhalter-System funktioniert');
    } else {
      console.log('   ⚠️ Keine Zutaten-Buttons gefunden');
    }
  });
});
