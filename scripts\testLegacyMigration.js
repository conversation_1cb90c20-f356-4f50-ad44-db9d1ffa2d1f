/**
 * Test Script für Legacy-Migration
 * Erstellt Legacy-Rezepte zum Testen der Migration
 */

const mongoose = require('mongoose');
const Menuchild = require('../models/menuchildModel');
const Menu = require('../models/menuModel');
const Unit = require('../models/unitModel');
const Grocery = require('../models/groceryModel');

// Verbindung zur Datenbank
async function connectDB() {
    try {
        await mongoose.connect(process.env.DATABASE_CONNECTION_STRING);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
        process.exit(1);
    }
}

// Erstelle Legacy-Test-Daten
async function createLegacyTestData() {
    console.log('🔧 Creating legacy test data...');

    // Finde oder erstelle Standard-Units und Groceries
    const defaultUnit = await Unit.findOne({ name: '-' }) || await Unit.create({ name: '-' });
    const gramUnit = await Unit.findOne({ name: 'g' }) || await Unit.create({ name: 'g' });
    const literUnit = await Unit.findOne({ name: 'l' }) || await Unit.create({ name: 'l' });

    const milchGrocery = await Grocery.findOne({ name: 'Milch' }) || await Grocery.create({ name: 'Milch' });
    const mehlGrocery = await Grocery.findOne({ name: 'Mehl' }) || await Grocery.create({ name: 'Mehl' });
    const eierGrocery = await Grocery.findOne({ name: 'Eier' }) || await Grocery.create({ name: 'Eier' });

    // Legacy Format 1: Sehr alte "zutaten" Struktur
    const legacyMenuChild1 = {
        parentId: new mongoose.Types.ObjectId().toString(),
        seatCount: 4,
        cookingTime: 30,
        isStandard: true,
        // ❌ LEGACY: Alte "zutaten" Struktur mit Strings
        zutaten: [
            {
                name: 'Milch',
                menge: '500',
                einheit: 'ml'
            },
            {
                name: 'Mehl',
                menge: '250',
                einheit: 'g'
            }
        ],
        // ❌ LEGACY: Alte "zubereitung" Struktur
        zubereitung: [
            {
                head: 'Schritt 1',
                content: 'Milch erhitzen'
            }
        ],
        nutritions: [
            {
                name: 'Protein',
                amount: '15',  // ❌ String statt Number
                unit: 'g'
            }
        ]
    };

    // Legacy Format 2: Fehlende StableIDs
    const legacyMenuChild2 = {
        parentId: new mongoose.Types.ObjectId().toString(),
        seatCount: 2,
        cookingTime: 45,
        isStandard: true,
        // ❌ LEGACY: Ingredients ohne StableIDs
        ingredients: [
            {
                amount: 250,
                unit: mehlGrocery._id,
                name: mehlGrocery._id
                // ❌ Fehlende stableId
            },
            {
                amount: 2,
                unit: defaultUnit._id,
                name: eierGrocery._id
                // ❌ Fehlende stableId
            }
        ],
        // ❌ LEGACY: Fehlende maxUsedStableId
        preperation: [
            {
                head: 'Zubereitung',
                content: 'Mehl und Eier vermengen'
            }
        ],
        nutritions: [
            {
                name: 'Kohlenhydrate',
                amount: '45',  // ❌ String statt Number
                unit: 'g'
            }
        ]
    };

    // Legacy Format 3: String-Amounts
    const legacyMenuChild3 = {
        parentId: new mongoose.Types.ObjectId().toString(),
        seatCount: 3,
        cookingTime: 20,
        isStandard: true,
        ingredients: [
            {
                amount: '1.5',  // ❌ String statt Number
                unit: literUnit._id,
                name: milchGrocery._id,
                stableId: 1
            }
        ],
        maxUsedStableId: 1,
        preperation: [
            {
                head: 'Erhitzen',
                content: 'Milch langsam erhitzen'
            }
        ],
        nutritions: [
            {
                name: 'Fett',
                amount: '8.5',  // ❌ String statt Number
                unit: 'g'
            }
        ]
    };

    try {
        // Erstelle Legacy MenuChilds
        const savedLegacy1 = await Menuchild.create(legacyMenuChild1);
        const savedLegacy2 = await Menuchild.create(legacyMenuChild2);
        const savedLegacy3 = await Menuchild.create(legacyMenuChild3);

        console.log('✅ Legacy test data created:');
        console.log(`📋 Legacy Format 1 (zutaten): ${savedLegacy1._id}`);
        console.log(`📋 Legacy Format 2 (no stableIds): ${savedLegacy2._id}`);
        console.log(`📋 Legacy Format 3 (string amounts): ${savedLegacy3._id}`);

        // Erstelle auch Test-Menus
        const testMenu1 = await Menu.create({
            name: 'Legacy Test Rezept 1',
            description: 'Test für sehr alte zutaten Struktur',
            menuchilds: [{
                numberOfPersons: 4,
                menuChildId: savedLegacy1._id
            }],
            users: [],
            imagelink: ' ',
            freeAccess: false
        });

        const testMenu2 = await Menu.create({
            name: 'Legacy Test Rezept 2',
            description: 'Test für fehlende StableIDs',
            menuchilds: [{
                numberOfPersons: 2,
                menuChildId: savedLegacy2._id
            }],
            users: [],
            imagelink: ' ',
            freeAccess: false
        });

        const testMenu3 = await Menu.create({
            name: 'Legacy Test Rezept 3',
            description: 'Test für String-Amounts',
            menuchilds: [{
                numberOfPersons: 3,
                menuChildId: savedLegacy3._id
            }],
            users: [],
            imagelink: ' ',
            freeAccess: false
        });

        console.log('✅ Test menus created:');
        console.log(`🍽️ Menu 1: ${testMenu1._id}`);
        console.log(`🍽️ Menu 2: ${testMenu2._id}`);
        console.log(`🍽️ Menu 3: ${testMenu3._id}`);

        return {
            menuChild1: savedLegacy1._id,
            menuChild2: savedLegacy2._id,
            menuChild3: savedLegacy3._id,
            menu1: testMenu1._id,
            menu2: testMenu2._id,
            menu3: testMenu3._id
        };

    } catch (error) {
        console.error('❌ Error creating legacy test data:', error);
        throw error;
    }
}

// Hauptfunktion
async function main() {
    console.log('🧪 Legacy Migration Test Script');
    console.log('================================');

    await connectDB();
    
    const testData = await createLegacyTestData();
    
    console.log('\n🎯 Test Instructions:');
    console.log('1. Öffnen Sie diese URLs im Browser:');
    console.log(`   http://localhost:5174/kochbuch/menu/${testData.menu1}`);
    console.log(`   http://localhost:5174/kochbuch/menu/${testData.menu2}`);
    console.log(`   http://localhost:5174/kochbuch/menu/${testData.menu3}`);
    console.log('\n2. Prüfen Sie die Backend-Logs auf Migration-Meldungen');
    console.log('\n3. Prüfen Sie, ob die Rezepte korrekt angezeigt werden');
    console.log('\n4. Öffnen Sie auch die Edit-Versionen:');
    console.log(`   http://localhost:5174/kochbuch/menu/edit/${testData.menu1}`);
    console.log(`   http://localhost:5174/kochbuch/menu/edit/${testData.menu2}`);
    console.log(`   http://localhost:5174/kochbuch/menu/edit/${testData.menu3}`);

    console.log('\n✅ Legacy test data ready for testing!');
    
    await mongoose.disconnect();
}

// Script ausführen
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { createLegacyTestData };
