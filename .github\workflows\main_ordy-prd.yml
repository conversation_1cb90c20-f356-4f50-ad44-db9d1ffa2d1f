# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: ordy-prd | PRODUCTION

on:
  push:
    branches:
      - prd
  workflow_dispatch:

env:
  NODE_ENV: production

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checout Source
        uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'

      - name: npm install, build, and test
        run: npm install

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'ordy-prd'
          publish-profile: ${{ secrets.AZURE_WEBAPP_PRD_PUBLISH_PROFILE }}
          package: .