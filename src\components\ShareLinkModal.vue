<template>
  <!-- Overlay -->
  <div 
    class="fixed inset-0 z-40 bg-black bg-opacity-50 flex items-center justify-center"
    @click.self="$emit('close')" <!-- Close on overlay click -->
  >
    <!-- Modal Content -->
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
      <!-- <PERSON><PERSON> Header -->
      <h3 class="text-lg font-semibold mb-4 font-['Yeseva_One']">Küchentisch teilen</h3>
      
      <!-- Explanatory Text -->
      <p class="text-sm font-['Open_Sans'] mb-4">Sende diesen Link an Personen, die du zu diesem Küchentisch einladen möchtest:</p>
      
      <!-- Link Display and Copy Button -->
      <div class="flex items-center space-x-2 mb-6">
        <input 
          type="text" 
          :value="shareUrl" 
          readonly 
          class="flex-grow p-2 border border-gray-300 rounded bg-gray-100 text-sm font-mono truncate"
          ref="shareInput"
        />
        <button 
          @click="copyLink"
          class="bg-[#a37dff] text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors duration-200 text-sm font-['Yeseva_One']"
        >
          {{ copyButtonText }}
        </button>
      </div>
      
      <!-- Close Button -->
      <div class="text-right">
        <button 
          @click="$emit('close')"
          class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors duration-200 text-sm font-['Open_Sans'] font-semibold"
        >
          Fertig
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import useNotification from '../../modules/notificationInformation'; // Assuming notification module path

const props = defineProps({
  shareUrl: {
    type: String,
    required: true
  }
});

defineEmits(['close']);

const { setNotification } = useNotification();
const copyButtonText = ref('Kopieren');
const shareInput = ref(null); // Ref for the input element

const copyLink = async () => {
  if (!navigator.clipboard) {
    // Fallback for older browsers or insecure contexts (http)
    try {
      shareInput.value.select();
      document.execCommand('copy');
      copyButtonText.value = 'Kopiert!';
      setNotification('Link in Zwischenablage kopiert (Fallback)', 'success');
      setTimeout(() => copyButtonText.value = 'Kopieren', 2000);
    } catch (err) {
      console.error('Fallback: Oops, unable to copy', err);
      setNotification('Fehler beim Kopieren des Links', 'error');
      copyButtonText.value = 'Fehler';
      setTimeout(() => copyButtonText.value = 'Kopieren', 2000);
    }
    return;
  }

  try {
    await navigator.clipboard.writeText(props.shareUrl);
    copyButtonText.value = 'Kopiert!';
    setNotification('Link in Zwischenablage kopiert', 'success');
    setTimeout(() => copyButtonText.value = 'Kopieren', 2000); // Reset button text after 2 seconds
  } catch (err) {
    console.error('Failed to copy: ', err);
    setNotification('Fehler beim Kopieren des Links', 'error');
    copyButtonText.value = 'Fehler';
      setTimeout(() => copyButtonText.value = 'Kopieren', 2000);
  }
};
</script>

<style scoped>
/* Add fallback fonts if needed, similar to KuechentischView */
.font-\[\'Yeseva_One\'\] {
  font-family: 'Yeseva One', cursive;
}
.font-\[\'Open_Sans\'\] {
  font-family: 'Open Sans', sans-serif;
}
.font-mono {
  font-family: monospace;
}
</style> 