<template>
  <div 
    class="fixed inset-0 z-50 flex items-center justify-center" 
    style="background-color: rgba(0, 0, 0, 0.1);"
  >
    <div class="bg-white p-8 rounded-lg shadow-xl max-w-md w-4/5 md:w-3/5 lg:w-2/5">
      <h2 class="text-2xl font-bold mb-6 text-center">Profil vervollständigen</h2>
      <p class="text-sm text-gray-600 mb-6 text-center">
        Bitte vervollständige dein Profil, bevor du fortfährst.
      </p>
      <form @submit.prevent="saveProfile">
        <div class="mb-4">
          <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">Vorname</label>
          <input
            type="text"
            id="firstname"
            v-model="formData.firstname"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
        <div class="mb-4">
          <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">Nachname</label>
          <input
            type="text"
            id="lastname"
            v-model="formData.lastname"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
        <div class="mb-6">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">E-Mail</label>
          <input
            type="email"
            id="email"
            v-model="formData.email"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
          <p class="mt-2 text-xs text-gray-500">
            Sollte die selbe Mail wie die bei der Registrierung sein.
          </p>
        </div>
        <button
          type="submit"
          class="w-full bg-[#A37DFF] hover:bg-[#8f6dcc] text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#A37DFF]"
          :disabled="isSaving"
        >
          {{ isSaving ? 'Speichern...' : 'Speichern' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useUserStore } from '../store/userStore'; // Adjusted path assuming components/store structure
import useNotification from '../../modules/notificationInformation';

const props = defineProps({
  initialUser: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['close']);

const userStore = useUserStore();
const { setNotification } = useNotification();
const isSaving = ref(false);

// Use reactive for the form data to easily manage multiple fields
const formData = reactive({
  firstname: '',
  lastname: '',
  email: '',
});

// Use onMounted again to set initial values ONLY ONCE
onMounted(() => {
  console.log("CompleteProfilePopup: onMounted triggered. InitialUser:", JSON.parse(JSON.stringify(props.initialUser)));
  // Set initial values only if they exist in the prop
  formData.firstname = props.initialUser?.firstName || '';
  formData.lastname = props.initialUser?.lastName || '';
  formData.email = props.initialUser?.email || '';
  console.log("CompleteProfilePopup: formData initialized in onMounted:", JSON.parse(JSON.stringify(formData)));
});

const saveProfile = async () => {
  console.log("saveProfile called. formData:", JSON.parse(JSON.stringify(formData)));
  // Basic validation
  if (!formData.firstname || !formData.email) {
    console.log("Validation failed.");
    setNotification('Bitte fülle mindestens Vorname und E-Mail aus.', 'alert');
    return;
  }
  console.log("Validation passed. Setting isSaving = true");
  isSaving.value = true;
  try {
    console.log("Calling userStore.updateUserProfileDetails...");
    // Assuming the store action exists and handles the API call and state update
    await userStore.updateUserProfileDetails({ 
      firstName: formData.firstname,
      lastName: formData.lastname,
      email: formData.email,
    });
    console.log("userStore.updateUserProfileDetails finished successfully.");
    setNotification('Profil erfolgreich aktualisiert.', 'success');
    emit('close'); // Close the popup on success
  } catch (error) {
    console.error("Error caught in saveProfile:", error);
    // Error handling is done within the store action, but we could add specific UI feedback here if needed
    // Keep the popup open on error? Or close it? Let's keep it open for now.
    // setNotification is called within the store action on error
  } finally {
    console.log("Finally block reached. Setting isSaving = false");
    isSaving.value = false;
  }
};
</script>

<style scoped>
/* Add any specific styling if needed */
</style> 