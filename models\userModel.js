const mongoose = require('mongoose')
const { connection1 } = require('./../db.js');
const helper = require('../utils/helper')
const { ObjectId } = require('mongodb')

const UserSchema = new mongoose.Schema({
    extId: {
        type: String
    },
    extAuthService: {
        type: String,
    },
    firstName: {
        type: String,
    },
    lastName: {
        type: String,
    },
    email: {
        type: String,
        default: ''
    },
    img: {
        type: String,
        default: "default.png"
    },
    profileImageUpdatedAt: {
        type: Date,
        default: Date.now
    },
    profileImageSource: {
        type: String,
        enum: ['default', 'uploaded', 'ai_generated'],
        default: 'default'
    },
    createdAt: {
        type: String,
        default: Date.now()
    },
    // External Accessable or not
    defaultKitchentable: {
        type: String,
        default: ''
    },
    externalAccess: {
        type: Boolean,
        default: false
    },
    // Profilespecific Data
    weight: {
        type: Number,
        default: 0
    },
    height: {
        type: Number,
        default: 0
    },
    age: {
        type: Number,
        default: 0
    },
    protDaily: {
        type: Number,
        default: 0
    },
    kcalDaily: {
        type: Number,
        default: 0
    },
    fatDaily: {
        type: Number,
        default: 0
    },
    sex: {
        type: Number,
        default: 0
    },
    activness: {
        type: Number,
        default: 0
    },
    summDaily: {
        type: Number,
        default: 0
    },
    weekplanmode: {
        type: Number,
        default: 0
    },
    gdpr: {
        type: Number,
        default: 0
    },
    install_apple_app: {
        type: Boolean,
        default: false
    },
    pwa_install_prompts: {
        count: {
            type: Number,
            default: 0
        },
        max_count: {
            type: Number,
            default: 4
        },
        disabled: {
            type: Boolean,
            default: false
        },
        last_shown: {
            type: Date,
            default: null
        }
    },
    address: {
        city: {
            type: String,
            default: ''
        },
        country: {
            type: String,
            default: ''
        },
        line1: {
            type: String,
            default: ''
        },
        postal_code_state: {
            type: String,
            default: ''
        }
    },
    bookedAbo: {
        extStripeSessionId: {
            type: String,
            default: ''
        },
        extStripeCustomerId: {
            type: String,
            default: ''
        },
        extStripePriceId: {
            type: String,
            default: ''
        },
        extStripeSubscriptionId: {
            type: String,
            default: ''
        },
        type: {
            type: Number,
            default: 0
        },
        nrMenucreationCalls: { // number of contingent
            type: Number,
            default: 5
        },
        nrCookeasyCalls: { // number of contingent
            type: Number,
            default: 8
        },
        nrMenuuploadCalls: { // number of contingent
            type: Number,
            default: 3
        },
        nrRealtimeApiCalls: { // number of contingent for realtime API (assistant)
            type: Number,
            default: 10
        },

    },
    bookedAboUsage: {
        nrMenucreationCalls: { // number of usage
            type: Number,
            default: 0
        },
        nrMenucreationCalls_active: {
            type: Boolean,
            default: true
        },
        nrCookeasyCalls: { // number of usage
            type: Number,
            default: 0
        },
        nrCookeasyCalls_active: {
            type: Boolean,
            default: true
        },
        nrMenuuploadCalls: { // number of usage
            type: Number,
            default: 0
        },
        nrMenuuploadCalls_active: {
            type: Boolean,
            default: true
        },
        nrRealtimeApiCalls: { // number of usage for realtime API (assistant)
            type: Number,
            default: 0
        },
        nrRealtimeApiCalls_active: {
            type: Boolean,
            default: true
        }
    },
    explanation_trail: {
        type: Number,
        default: 0
    },
    // Admin permissions
    adminPermissions: {
        isAdmin: {
            type: Boolean,
            default: false
        },
        canAccessBackend: {
            type: Boolean,
            default: false
        },
        canManagePinterest: {
            type: Boolean,
            default: false
        },
        canManageMarketing: {
            type: Boolean,
            default: false
        },
        canViewAnalytics: {
            type: Boolean,
            default: false
        },
        permissions: [{
            type: String,
            enum: ['backend_access', 'pinterest_management', 'marketing_management', 'analytics_view', 'user_management']
        }]
    }
})

/*
UserSchema.pre(/^find/, function(next){
    helper.devConsole("pre on weekplan find executed")
    this.populate('menu')
    next()
});
*/




module.exports = connection1.model('User', UserSchema)