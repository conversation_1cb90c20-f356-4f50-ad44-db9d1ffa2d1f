const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const facebookTokenSchema = new mongoose.Schema({
  // Eindeutige Identifikation für das Token-Set
  tokenIdentifier: {
    type: String,
    default: 'facebook_oauth',
    required: true,
    unique: true
  },

  // OAuth Tokens
  accessToken: {
    type: String,
    required: true
  },

  refreshToken: {
    type: String,
    required: false // Facebook tokens don't always have refresh tokens
  },

  // Token Metadaten
  tokenType: {
    type: String,
    default: 'bearer'
  },

  scope: {
    type: String,
    default: 'pages_manage_posts,pages_read_engagement,instagram_basic,instagram_content_publish'
  },

  // Facebook Page Information
  pageId: {
    type: String,
    required: false
  },

  pageName: {
    type: String,
    required: false
  },

  // Instagram Business Account Information
  instagramBusinessAccountId: {
    type: String,
    required: false
  },

  // Zeitstempel
  issuedAt: {
    type: Date,
    default: Date.now,
    required: true
  },

  expiresAt: {
    type: Date,
    // Facebook tokens typically expire after 60 days
    default: () => new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)
  },

  lastRefreshed: {
    type: Date,
    default: Date.now
  },

  // Status
  isActive: {
    type: Boolean,
    default: true
  },

  // Environment (development, preview, production)
  environment: {
    type: String,
    default: process.env.NODE_ENV || 'development',
    required: true
  }
}, {
  timestamps: true // Fügt createdAt und updatedAt automatisch hinzu
});

// Index für schnelle Abfragen
facebookTokenSchema.index({ tokenIdentifier: 1, environment: 1, isActive: 1 });

// Methode zum Abrufen des aktuellen Tokens
facebookTokenSchema.statics.getCurrentToken = async function(environment = process.env.NODE_ENV || 'development') {
  return await this.findOne({
    tokenIdentifier: 'facebook_oauth',
    environment: environment,
    isActive: true
  }).sort({ lastRefreshed: -1 });
};

// Methode zum Speichern neuer Tokens
facebookTokenSchema.statics.saveTokens = async function(accessToken, refreshToken, scope, pageId, pageName, instagramBusinessAccountId, environment = process.env.NODE_ENV || 'development') {
  console.log('[FacebookToken.saveTokens] Saving new tokens...', {
    accessToken: accessToken ? 'present' : 'missing',
    refreshToken: refreshToken ? 'present' : 'missing',
    scope: scope,
    pageId: pageId,
    pageName: pageName,
    instagramBusinessAccountId: instagramBusinessAccountId,
    environment: environment
  });

  // Lösche alte Tokens komplett
  const deletedCount = await this.deleteMany(
    { tokenIdentifier: 'facebook_oauth', environment: environment }
  );

  console.log('[FacebookToken.saveTokens] Deleted', deletedCount.deletedCount, 'old tokens');

  // Erstelle neuen Token-Eintrag
  const newToken = await this.create({
    tokenIdentifier: 'facebook_oauth',
    accessToken: accessToken,
    refreshToken: refreshToken,
    scope: scope,
    pageId: pageId,
    pageName: pageName,
    instagramBusinessAccountId: instagramBusinessAccountId,
    environment: environment,
    isActive: true,
    issuedAt: new Date(),
    lastRefreshed: new Date()
  });

  console.log('[FacebookToken.saveTokens] Token saved successfully with ID:', newToken._id);
  console.log('[FacebookToken.saveTokens] New token scopes:', scope);
  return newToken;
};

// Methode zum Deaktivieren alter Tokens
facebookTokenSchema.statics.deactivateOldTokens = async function(environment = process.env.NODE_ENV || 'development') {
  const result = await this.updateMany(
    {
      tokenIdentifier: 'facebook_oauth',
      environment: environment,
      isActive: true
    },
    {
      isActive: false,
      lastRefreshed: new Date()
    }
  );

  console.log('[FacebookToken.deactivateOldTokens] Deactivated', result.modifiedCount, 'old tokens');
  return result;
};

// Methode zum Aktualisieren des Access Tokens
facebookTokenSchema.statics.updateAccessToken = async function(newAccessToken, newRefreshToken = null, environment = process.env.NODE_ENV || 'development') {
  const updateData = {
    accessToken: newAccessToken,
    lastRefreshed: new Date()
  };

  if (newRefreshToken) {
    updateData.refreshToken = newRefreshToken;
  }

  return await this.findOneAndUpdate(
    {
      tokenIdentifier: 'facebook_oauth',
      environment: environment,
      isActive: true
    },
    updateData,
    { new: true }
  );
};

// Methode zum Prüfen, ob Token bald abläuft (innerhalb der nächsten 7 Tage)
facebookTokenSchema.methods.isExpiringSoon = function() {
  const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  return this.expiresAt < sevenDaysFromNow;
};

// Methode zum Prüfen, ob Token abgelaufen ist
facebookTokenSchema.methods.isExpired = function() {
  return this.expiresAt < new Date();
};

// Use the specific connection (connection1) to define the model
const FacebookToken = connection1.model('FacebookToken', facebookTokenSchema);

module.exports = FacebookToken;
