<template>
    
    <div class="w-full rounded-3xl bg-white h-auto mt-14">
        <!-- Head -->
         <div class="w-full relative rounded-3xl bg-black h-16"></div>
         <div class="w-full relative -mt-14 rounded-3xl bg-ordypink-200 h-44 pr-8 md:pr-2 pl-8 md:pl-4">
            <h1 class="w-full text-left align-bottom text-[5rem] text-white leading-3 mt-3 font-YesevaOne pt-12 pb-6 pr-3">{{ element.name }}</h1>

            <ul class="mt-4 text-xs list-disc pr-8 md:pr-2 pl-8 md:pl-4 text-left" v-html="element.litext">
            </ul>

            <div class="relative bg-ordypurple-200 w-12 h-12 rounded-full pt-2 float-right">
                <img src="../assets/icons/gift.png" class="mx-auto my-auto" />
            </div>
         </div>
        <!-- Head -->
         <p class="h-24 p-8 font-YesevaOne text-tiny pt-12">{{ element.description }}</p>


        <!-- Footer But<PERSON> -->
        <div class="w-full relative mb-0 p-4">
            <div class="relative bg-ordypurple-200 w-12 ml-3 h-12 rounded-full pt-3">
                <img src="../assets/icons/info.png" class="mx-auto my-auto" />
            </div>
            <button v-if="element.active" class="rounded-3xl bg-black w-full h-16 text-right text-white -mt-8" :class="element.active ? 'bg-green-500' : 'bg-black'">
                <p class="align-bottom mb-0 pb-0 mt-3 font-YesevaOne text-xl pt-5 pr-3"><span>{{ element.value }}<b class="text-xs">CHF</b> Active</span></p>
            </button>
            <button v-else @click="aboStore.checkout(element.priceId)" class="rounded-3xl bg-black w-full h-16 text-right text-white -mt-8" :class="element.active ? 'bg-green-500' : 'bg-black'">
                <p class="align-bottom mb-0 pb-0 mt-3 font-YesevaOne text-xl pt-5 pr-3"><span>{{ element.value }}<b class="text-xs">CHF</b> Go</span></p>
            </button>
        </div>
        <!-- Footer Button -->
    </div>

</template>
<script setup>
import { reactive, ref, toRefs } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import { useMenuStore, useMenuesStore } from '../store/menuStore'
import { useUserStore  } from '../store/userStore';
import { useAboStore  } from '../store/aboStore';
import Agb from '../components/agb.vue';

const { setNotification } = useNotification();
const route = useRoute();
const router = useRouter();
const store = useMenuStore();
const menuesStore = useMenuesStore();
const userStore = useUserStore()
const aboStore = useAboStore()

    const props = defineProps({
            element: Object,
            index: Number,
        })
    
    const { element } = toRefs(props);

</script>