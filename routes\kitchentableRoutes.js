const express = require('express');
const authController = require('../controllers/authController');
const kitchentableController = require('../controllers/kitchentableController');
// Require the shopping list controller module at the top
const shoppingListController = require('../controllers/shoppingListController');

const router = express.Router();

// @desc    Single Kitchentable Object
// @route    GET /api/v1/kitchentable/:id
router
.route('/:id')
.get(authController.verify, kitchentableController.getKitchenTableObjectById)
.post(authController.verify, kitchentableController.createKitchenTableObject)
.delete(authController.verify, kitchentableController.deleteKitchenTableById, authController.sendanswer)

// @desc    Get active shopping list for a kitchentable
// @route   GET /api/v1/kitchentable/:kitchentableId/shopping-list/active
router
  .route('/:kitchentableId/shopping-list/active')
  // Access the controller function normally
  .get(authController.verify, shoppingListController.getActiveShoppingList)

// @desc    Get shopping list history for a kitchentable
// @route   GET /api/v1/kitchentable/:kitchentableId/shopping-lists/history
router
  .route('/:kitchentableId/shopping-lists/history')
  .get(authController.verify, shoppingListController.getShoppingListHistory)

// @desc    User Management Inside Kitchentable
// @route   /api/v1/kitchentable/user/:id (Old route for user management)
// router // Commented out old route block
// .route('/user/:id')
// .patch(authController.verify, kitchentableController.updateKitchenTableObject, authController.sendanswer)
// .get(authController.verify, kitchentableController.getKitchenTableByUserId)
// .delete(authController.verify, kitchentableController.deleteKitchenTableObject, authController.sendanswer)

// @desc    Add/Update user role in a specific kitchentable
// @route   PATCH /api/v1/kitchentable/:id/members
router
  .route('/:id/members') // :id is kitchentableId
  .patch(authController.verify, kitchentableController.updateKitchenTableObject, authController.sendanswer) // Using existing controller for adding/patching

// @desc    Remove a specific member from a specific kitchentable
// @route   DELETE /api/v1/kitchentable/:id/members/:memberId
router
  .route('/:id/members/:memberId') // :id is kitchentableId, :memberId is the userId to remove
  .delete(authController.verify, kitchentableController.deleteKitchenTableMember, authController.sendanswer) // Changed controller name for clarity

// @desc    Several Kitchentable Objects
// @route    GET /api/v1/kitchentable/all/related/:userid
router.route('/all/related/:userid')
.get(authController.verify, kitchentableController.getAllKitchenTablesById, authController.sendanswer)

// @desc    Check if user is related to a kitchentable
// @route    GET /api/v1/kitchentable/all/related/:userid
/*
router.route('/check/user/:userid')
.get(authController.verify, kitchentableController.getAllKitchenTablesById, authController.sendanswer)
*/

module.exports = router;