import { test, expect } from '@playwright/test';

test('Debug <PERSON><PERSON><PERSON>hl-Buttons', async ({ page }) => {
  console.log('🧪 Starte Debug-Test...');
  
  // Überwache Console-Logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🔍') || text.includes('📦') || text.includes('⚠️') || text.includes('❌')) {
      console.log(`[FRONTEND] ${text}`);
    }
  });
  
  // Überwache Requests
  page.on('request', request => {
    if (request.url().includes('createifnotexists')) {
      console.log(`[REQUEST] ${request.method()} ${request.url()}`);
    }
  });
  
  page.on('response', response => {
    if (response.url().includes('createifnotexists')) {
      console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
    }
  });
  
  // Navigiere zur App
  await page.goto('http://localhost:5173');
  await page.waitForLoadState('networkidle');
  
  console.log(`📍 URL: ${page.url()}`);
  
  // Prüfe ob Login erforderlich
  if (page.url().includes('/login')) {
    console.log('🔐 Login erforderlich - Test übersprungen');
    return;
  }
  
  // Navigiere zu Kochbuch
  await page.goto('http://localhost:5173/kochbuch');
  await page.waitForLoadState('networkidle');
  
  // Finde erstes Rezept
  const firstRecipe = page.locator('a[href*="/kochbuch/menu/"]').first();
  
  if (await firstRecipe.count() === 0) {
    console.log('❌ Keine Rezepte gefunden');
    return;
  }
  
  await firstRecipe.click();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  console.log(`📍 Rezept-URL: ${page.url()}`);
  
  // Suche + Button
  const plusButton = page.locator('button:has-text("+")').first();
  
  if (await plusButton.count() === 0) {
    console.log('❌ + Button nicht gefunden');
    return;
  }
  
  console.log('✅ + Button gefunden');
  
  // Klicke + Button
  await plusButton.click();
  await page.waitForTimeout(5000);
  
  console.log('🎯 Test abgeschlossen');
});
