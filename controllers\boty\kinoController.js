const catchAsync = require('../../utils/catchAsync');
var helper = require('../../utils/helper');
const Menu = require('../../models/menuModel');
//const MenuRelations = require('../../models/menuRelationModel');
const Jimp = require("jimp");
const { uploadFileByLink, getFileStream } = require('../../utils/awsStorage');
const fs = require('fs')
const axios = require('axios')
const moment = require('moment')



// @GET /functions/kinnovations/getfreecalendarslots
exports.getAllAirtableRows = catchAsync(async (req, res, next) => {

  helper.devConsole("get All Airtables")

  try {

    let response = await axios('https://api.airtable.com/v0/appst6N86kwlcmXyS/tblOs2KeNgIo0vIzN', {
      method: 'get',
      headers: { 'Authorization': `Bearer ${process.env.AIRT_TOKEN}`}
    });

    //console.log(response.data.records)
    
    req.body.request = response.data.records

    next()


  } catch(err) {
    helper.devConsole(err.message)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

})


// @POST /functions/kinnovations/getfreecalendarslots
exports.createAirtableRow = catchAsync(async (req, res, next) => {

  helper.devConsole("create Airtable")

  try {

    //console.log(req.body.name)
    //console.log(req.body.emails)
    //console.log(req.body.starttime)
    //console.log("2024-03-19T23:00:00.000Z")

    const timestamp = moment().utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
    //console.log(timestamp)
    const starttime = moment(req.body.starttime).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')

    let response = await axios('https://api.airtable.com/v0/appst6N86kwlcmXyS/tblOs2KeNgIo0vIzN', {
      method: 'post',
      headers: { 'Authorization': `Bearer ${process.env.AIRT_TOKEN}`},
      data: {
        "records": [
          {
            "fields": {
              "Name": `${req.body.name}`,
              "Email": `${req.body.emails}`,
              "Kundenname": "-",
              "Boty": true,
              "Erstellungsdatum": `${timestamp}`,
              "BotyTermin": `${starttime}`,
              "Typ": "Potentieller Kunde"
            }
          }
        ]
      }
    });

    next()


  } catch(err) {
    helper.devConsole(err.message)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

})

// @GET /functions/kinnovations/getAirtableRows
exports.responsePhoneRequest = catchAsync(async (req, res, next) => {

  helper.devConsole("response Phone Request")

  try {

    await exports.getAllAirtableRows(req, res, next);

    let responseMessage = ""
    const newName = req.body.fristname + " " + req.body.lastname

    // CHECK IF USER IS AVAIABLE
    for(let i = 0; i < req.body.request.length; i++){
      
      // IF Name is in table
      if(req.body.request[i].fields.Name == newName){
        
        // IF Type Familie
        if(req.body.request[i].fields.Typ == "Familie"){
          responseMessage = `Du gehörst zur Familie ${req.body.fristname}. Ruf Dominic direkt privat auf 079 841 83 05 an.`
        }

        // IF Type Kunde
        if(req.body.request[i].fields.Typ == "Kunde"){
          responseMessage = `Hallo ${req.body.fristname}. Schön dass du versucht uns zu erreichen. Du erreichst Dominic Kunz auf 079 841 83 05.`
        }

        // IF Type Potentieller Kunde
        if(req.body.request[i].fields.Typ == "Potentieller Kunde"){
          responseMessage = `Hallo ${req.body.fristname}. Schön dass du versuchst uns zu erreichen. Du solltest in den nächsten Tagen ein Meeting mit k-innovations haben. Nach diesem Meeting wirst du in der Lage sein, uns auch telefonisch zu erreichen. Falls kein Meeting geplant ist, kannst du dies auch hier machen.`
        }

      }
    }

    if(req.body.fristname === "Max" && req.body.lastname === "Mustermann" || req.body.lastname === "Muster" || req.body.lastname === "Müller"){
      responseMessage = `Kannst du mir deinen Vor- und Nachname mitteilen, damit ich nachschauen kann?`
    }

    // IF No Type or Person was found
    if(responseMessage === ""){
      responseMessage = `Hallo ${req.body.fristname} ${req.body.lastname}. Schön dass du versuchst uns zu erreichen. Wir kennen dich noch gar nicht und würden zuerst gerne ein digitales Kennenlernen organisieren. Schreibe kurz, ob Interesse an einem Meeting digital besteht.`
    }



    res.status(200).json({
      status: 'success',
      data: responseMessage
    });


  } catch(err) {
    helper.devConsole(err.message)
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }

})


// @POST /functions/kinnovations/getfreecalendarslots
exports.freeslot = catchAsync(async (req, res, next) => {
  try {

    helper.devConsole("inside function free slots")

      // Replace {access-token} with your actual access-token
      const freeSlots = [];

      /*
MS_CLIENTSECRET=
MS_TENANTID=
MS_APPID
      */

      // Get MS Access Token
      let response1 = await axios(`https://login.microsoftonline.com/${process.env.MS_TENANTID}/oauth2/v2.0/token`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          'client_id' : `${process.env.MS_APPID}`,
          'scope' : 'https://graph.microsoft.com/.default',
          'client_secret': `${process.env.MS_CLIENTSECRET}`,
          'grant_type': 'client_credentials'
        }
      });

      let token = response1.data.access_token
      //console.log(token)

      // Replace the url with your actual request url
      let response = await axios('https://graph.microsoft.com/v1.0/solutions/bookingBusinesses/<EMAIL>/appointments', {
          method: 'GET',
          headers: { 'Authorization': `Bearer ${token}` }
      });

      //console.log(response.data.value)
      const appointments = response.data.value;

      const slots = [];

      for (let i = 1; i <= 5; i++) {
          const startDate = moment().utcOffset('+01:00', true).add(i, 'days');
          startDate.hours(13);
          startDate.minutes(30);
          startDate.seconds(0);
          let tempDate = startDate.clone();
          while (tempDate.format('HH:mm') !== '15:30') {
            slots.push(tempDate.format());
            tempDate = tempDate.add(30, 'minutes');
          }
        }
      
        for (let slot of slots) {
          //console.log(slot)
          let isFree = true;
          const slotTimeStart = moment(slot);
          const slotTimeEnd = moment(slot).add(30, 'minutes');

          // Überprüfen Sie, ob das Slot am Wochenende liegt.
          if (slotTimeStart.day() === 0 || slotTimeStart.day() === 6) {
            isFree = false;
            //console.log("Slot ist am Wochenende.")
          }
          
          if(isFree){
            for (let appointment of appointments) {
                //console.log("check appointment")
        
                const appointmentStart = moment.utc(appointment.startDateTime.dateTime).utcOffset('+00:00', true);
                const appointmentEnd = moment.utc(appointment.endDateTime.dateTime).utcOffset('+00:00', true);


                
                // Falls der Slot zwischen Start und Ende eines bestehenden Meetings stattfindet
                if (slotTimeStart.isBetween(appointmentStart, appointmentEnd, 'minutes', "[)")) {
                    isFree = false;
                    //console.log("false1")
                    break;
                }
        
                // Wenn das Ende des freien Slots zwischen das Start und Ende des Meetings fällt
                if (slotTimeEnd.isBetween(appointmentStart, appointmentEnd, 'minutes', "(]")) {
                    isFree = false;
                    //console.log("false2")
                    break;
                }
            }
          }

          if (isFree) {
              let baseslot = {}
              moment.locale('de-ch');
              baseslot.day = moment(slot).format('dddd');
              baseslot.timeslot = moment(slot).format('LT');
              baseslot.duration = "30'"
              baseslot.selected = false
              baseslot.slot = slot
              freeSlots.push(baseslot);
          }
      }

      res.status(200).json({
        status: 'success',
        'data': freeSlots
      });
  } catch(err) {
    //console.log(err);
    res.status(500).json({
      status: 'error',
      'message': err.message
    });
  }
});

// @POST /functions/kinnovations/createmeeting
exports.createmeeting = catchAsync(async (req, res, next) => {
  helper.devConsole("createmeeting in kinoController")
  //console.log(req.body.emails)
  //console.log(req.body.name)
  //console.log(req.body.starttime)

  const starttime = moment(req.body.starttime).utc().format('YYYY-MM-DDTHH:mm:ss.SSSSSSS[Z]');
  //console.log(starttime)
  const endtime = moment(req.body.starttime).add(30, 'minutes').utc().format('YYYY-MM-DDTHH:mm:ss.SSSSSSS[Z]')
  //console.log(endtime)
  const dataobject = {
    "additionalInformation": "",
    "isLocationOnline": true,
    "customerTimeZone": "",
    "serviceId": "0795ca3b-2143-4190-9236-8e7877c7b06f",
    "serviceName": "k-innovations | 30-Minuten-Kennenlernen",
    "duration": "PT30M",
    "preBuffer": "PT0S",
    "postBuffer": "PT0S",
    "priceType": "undefined",
    "price": 0,
    "serviceNotes": "Wir freuen uns.",
    "optOutOfCustomerEmail": false,
    "staffMemberIds": [
        "6a431797-718a-45ab-9316-537703f648af"
    ],
    "smsNotificationsEnabled": false,
    "maximumAttendeesCount": 1,
    "filledAttendeesCount": 1,
    "startDateTime": {
        "dateTime": starttime,
        "timeZone": "UTC"
    },
    "endDateTime": {
        "dateTime": endtime,
        "timeZone": "UTC"
    },
    "serviceLocation": {
        "displayName": "",
        "locationEmailAddress": "",
        "locationUri": "",
        "locationType": "default",
        "uniqueId": null,
        "uniqueIdType": null,
        "address": {
            "street": "",
            "city": "",
            "state": "",
            "countryOrRegion": "",
            "postalCode": ""
        },
        "coordinates": {
            "altitude": 0,
            "latitude": 0,
            "longitude": 0,
            "accuracy": 0,
            "altitudeAccuracy": 0
        }
    },
    "reminders": [],
    "customers": [
        {
            "@odata.type": "#microsoft.graph.bookingCustomerInformation",
            "customerId": "1e29608c-f82e-4255-830a-02b8a87ecf9f",
            "name": req.body.name,
            "emailAddress": req.body.emails,
            "phone": "",
            "timeZone": "",
            "notes": "",
            "location": {
                "displayName": "",
                "locationEmailAddress": "",
                "locationUri": "",
                "locationType": "default",
                "uniqueId": null,
                "uniqueIdType": null,
                "address": {
                    "street": "",
                    "city": "",
                    "state": "",
                    "countryOrRegion": "",
                    "postalCode": ""
                },
                "coordinates": {
                    "altitude": 0,
                    "latitude": 0,
                    "longitude": 0,
                    "accuracy": 0,
                    "altitudeAccuracy": 0
                }
            },
            "customQuestionAnswers": []
        }
    ]
  }

  try{

  req.body.starttime = starttime;

  // Get MS Access Token
  let response1 = await axios(`https://login.microsoftonline.com/${process.env.MS_TENANTID}/oauth2/v2.0/token`, {
    method: 'POST',
    headers: { 
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: {
      'client_id' : `${process.env.MS_APPID}`,
      'scope' : 'https://graph.microsoft.com/.default',
      'client_secret': `${process.env.MS_CLIENTSECRET}`,
      'grant_type': 'client_credentials'
    }
  });

  let token = response1.data.access_token
  //console.log(token)
  
  // Replace the url with your actual request url
  let response = await axios('https://graph.microsoft.com/v1.0/solutions/bookingBusinesses/<EMAIL>/appointments', {
    method: 'post',
    headers: { 'Authorization': `Bearer ${token}` },
    data: dataobject
  });
  

  if(response.status == 201){
    exports.createAirtableRow(req, res, next);
  }

    res.status(200).json({
      success: true,
      data: "did it"
    });

  } catch(err){
    helper.dev(err)
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
})

// @POST /functions/kinnovations/basic
exports.kinnovations = catchAsync(async (req, res, next) => {

  helper.devConsole("chat in kinoController")

  //console.log(req.body.data)

  try{

    const tools = [
        {
            type: "function",
            "function": {
              "name": "get_free_slots",
              "description": "Book a meeting in the calendar.",
              "parameters": {
                "type": "object",
                "properties": {
                  "intent": {
                    "type": "string",
                    "enum": ["meeting_request", "contact_request"],
                    "description": "The specific intention of the user's request."
                  },
                  "keywords": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "List of keywords indicating a request for meeting or contact."
                  },
                  "email": {
                    "type": "string",
                    "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
                    "description": "Email address in a valid format."
                  }
                  },
                  required: ["type"],
              },
            }
        },
        {
          type: "function",
          "function": {
            "name": "get_telefon_proof",
            "description": "Phone call or telephone call requested.",
            "parameters": {
              "type": "object",
              "properties": {
                "fristname": {
                  "type": "string",
                  "description": "First name in plain text based on the user's message."
                },
                "lastname": {
                  "type": "string",
                  "description": "Last name in plain text based on the user's message."
                },
                },
                required: ["fristname", "lastname"],
            },
          }
        }
      ];


    const { OpenAI } = require("openai");

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo-0125",
      messages: req.body.data,
      tools: tools,
      tool_choice: "auto"
    });

    //console.log("test durch?")
    //console.log(completion.choices[0])

    /*

    let responseMessage = await JSON.parse(completion.choices[0].message)

    const toolCalls = responseMessage.tool_calls;
    helper.devConsole(responseMessage.tool_calls)

    if(responseMessage.tool_calls){
      helper.devConsole("toolCalls true")
      helper.devConsole(toolCalls)
    }
    */


    res.status(200).json({
      success: true,
      data: completion.choices[0].message
    });
 
  } catch(err){
    helper.dev(err)
    res.status(500).json({
      success: false,
      error: err.message
    });
  }

});