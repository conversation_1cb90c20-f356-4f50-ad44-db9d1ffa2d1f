import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5174';

test.describe('Public Pages Navigation and Functionality', () => {
  
  test('Homepage navigation is consistent', async ({ page }) => {
    await page.goto(`${BASE_URL}/`);

    // Check if page loads
    await expect(page).toHaveTitle(/Ordy/);

    // Check navigation items
    await expect(page.locator('nav a[href="/"]')).toContainText('Startseite');
    await expect(page.locator('nav a[href="/rezepte"]')).toContainText('Rezepte');
    await expect(page.locator('nav a[href="/blog"]')).toContainText('Blog');
    await expect(page.locator('nav a[href="/about"]')).toContainText('Über uns');

    // Check CTA button
    await expect(page.locator('nav button')).toContainText('Kostenlos starten');

    // Test mobile menu
    await page.setViewportSize({ width: 375, height: 667 });
    await page.locator('button[class*="md:hidden"]').click();
    await expect(page.locator('div[class*="md:hidden"] a[href="/"]')).toContainText('Startseite');
  });

  test('Recipe Explorer page works correctly', async ({ page }) => {
    await page.goto(`${BASE_URL}/rezepte`);
    
    // Check navigation consistency
    await expect(page.locator('nav a[href="/"]')).toContainText('Startseite');
    await expect(page.locator('nav a[href="/rezepte"]')).toHaveClass(/text-ordypurple-100/);
    
    // Check page content
    await expect(page.locator('h1')).toContainText('Kostenlose Rezepte entdecken');
    
    // Test search functionality
    await page.fill('input[placeholder*="Nach Rezepten"]', 'Hähnchen');
    await expect(page.locator('input[placeholder*="Nach Rezepten"]')).toHaveValue('Hähnchen');
    
    // Test ingredient filters
    await page.locator('button:has-text("Hähnchen")').first().click();
    await expect(page.locator('button:has-text("Hähnchen")').first()).toHaveClass(/bg-ordypurple-100/);
    
    // Check if recipes are displayed
    await expect(page.locator('.grid .transform')).toHaveCount(6); // Should show mock recipes
    
    // Test advanced filters
    await page.locator('button:has-text("Mehr Filter")').click();
    await expect(page.locator('select')).toHaveCount(3);
  });

  test('Blog page functionality', async ({ page }) => {
    await page.goto(`${BASE_URL}/blog`);
    
    // Check navigation consistency
    await expect(page.locator('nav a[href="/blog"]')).toHaveClass(/text-ordypurple-100/);
    
    // Check page content
    await expect(page.locator('h1')).toContainText('Ordy Blog');
    
    // Check featured article
    await expect(page.locator('h2')).toContainText('Wie KI die Zukunft des Kochens revolutioniert');
    
    // Test featured article click
    await page.locator('button:has-text("Artikel lesen")').first().click();
    await expect(page.locator('text=Artikel:')).toBeVisible();
    await page.locator('button:has-text("OK")').click();
    
    // Check category filters
    await page.locator('button:has-text("Nachhaltigkeit")').click();
    await expect(page.locator('button:has-text("Nachhaltigkeit")')).toHaveClass(/bg-ordypurple-100/);
    
    // Check articles grid - should have articles without author names
    const articles = page.locator('article');
    await expect(articles).toHaveCount(6);
    
    // Verify no author names are displayed in articles
    await expect(page.locator('article .text-sm.font-medium.text-gray-900')).toHaveCount(0);
    
    // Test article click
    await articles.first().click();
    await expect(page.locator('text=Artikel:')).toBeVisible();
    await page.locator('button:has-text("OK")').click();
    
    // Test newsletter subscription
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.locator('button:has-text("Abonnieren")').click();
    await expect(page.locator('text=Danke für dein Interesse')).toBeVisible();
    await page.locator('button:has-text("OK")').click();
  });

  test('About page content and navigation', async ({ page }) => {
    await page.goto(`${BASE_URL}/about`);
    
    // Check navigation consistency
    await expect(page.locator('nav a[href="/about"]')).toHaveClass(/text-ordypurple-100/);
    
    // Check page content
    await expect(page.locator('h1')).toContainText('Über Ordy');
    
    // Check mission section
    await expect(page.locator('h2:has-text("Mission")')).toBeVisible();
    await expect(page.locator('text=Kochen soll Freude bereiten')).toBeVisible();
    
    // Check values section
    await expect(page.locator('h2:has-text("Werte")')).toBeVisible();
    await expect(page.locator('h3:has-text("Nachhaltigkeit")')).toBeVisible();
    await expect(page.locator('h3:has-text("Familie")')).toBeVisible();
    await expect(page.locator('h3:has-text("Innovation")')).toBeVisible();
    
    // Check team section (should not show actual photos)
    await expect(page.locator('h2:has-text("Team")')).toBeVisible();
    await expect(page.locator('h3:has-text("KI & Technologie")')).toBeVisible();
    
    // Check technology section
    await expect(page.locator('h2:has-text("Technologie")')).toBeVisible();
    await expect(page.locator('text=OpenAI GPT-4')).toBeVisible();
    
    // Check impact section
    await expect(page.locator('h2:has-text("Impact")')).toBeVisible();
    await expect(page.locator('text=50+')).toBeVisible();
    await expect(page.locator('text=10.000+')).toBeVisible();
  });

  test('Cross-page navigation works correctly', async ({ page }) => {
    // Start at homepage
    await page.goto(`${BASE_URL}/new-homepage`);
    
    // Navigate to recipes
    await page.locator('nav a[href="/rezepte"]').click();
    await expect(page).toHaveURL(`${BASE_URL}/rezepte`);
    await expect(page.locator('h1')).toContainText('Kostenlose Rezepte');
    
    // Navigate to blog
    await page.locator('nav a[href="/blog"]').click();
    await expect(page).toHaveURL(`${BASE_URL}/blog`);
    await expect(page.locator('h1')).toContainText('Ordy Blog');
    
    // Navigate to about
    await page.locator('nav a[href="/about"]').click();
    await expect(page).toHaveURL(`${BASE_URL}/about`);
    await expect(page.locator('h1')).toContainText('Über Ordy');
    
    // Navigate back to homepage
    await page.locator('nav a[href="/new-homepage"]').click();
    await expect(page).toHaveURL(`${BASE_URL}/new-homepage`);
    await expect(page.locator('h1')).toContainText('Kochen war noch nie so einfach');
  });

  test('Mobile responsiveness', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Test homepage mobile
    await page.goto(`${BASE_URL}/new-homepage`);
    await page.locator('button[class*="md:hidden"]').click();
    await expect(page.locator('div[class*="md:hidden"]')).toBeVisible();
    
    // Test blog mobile
    await page.goto(`${BASE_URL}/blog`);
    await expect(page.locator('h1')).toBeVisible();
    
    // Test recipes mobile
    await page.goto(`${BASE_URL}/rezepte`);
    await expect(page.locator('input[placeholder*="Nach Rezepten"]')).toBeVisible();
    
    // Test about mobile
    await page.goto(`${BASE_URL}/about`);
    await expect(page.locator('h1')).toBeVisible();
  });

  test('All CTA buttons work', async ({ page }) => {
    // Test homepage CTA
    await page.goto(`${BASE_URL}/new-homepage`);
    await expect(page.locator('button:has-text("Kostenlos starten")')).toBeVisible();
    
    // Test recipe explorer CTA
    await page.goto(`${BASE_URL}/rezepte`);
    await expect(page.locator('a[href="/login"]:has-text("Kostenlos anmelden")')).toBeVisible();
    
    // Test about page CTA
    await page.goto(`${BASE_URL}/about`);
    await expect(page.locator('a[href="/login"]:has-text("Jetzt ausprobieren")')).toBeVisible();
  });

  test('Footer links work on all pages', async ({ page }) => {
    const pages = ['/new-homepage', '/rezepte', '/blog', '/about'];
    
    for (const pagePath of pages) {
      await page.goto(`${BASE_URL}${pagePath}`);
      
      // Check footer exists
      await expect(page.locator('footer')).toBeVisible();
      
      // Check footer links
      await expect(page.locator('footer a[href="/rezepte"]')).toBeVisible();
      await expect(page.locator('footer a[href="/blog"]')).toBeVisible();
      await expect(page.locator('footer a[href="/about"]')).toBeVisible();
      await expect(page.locator('footer a[href="/legal/agb"]')).toBeVisible();
      await expect(page.locator('footer a[href="/legal/privacy-policy"]')).toBeVisible();
    }
  });
});
