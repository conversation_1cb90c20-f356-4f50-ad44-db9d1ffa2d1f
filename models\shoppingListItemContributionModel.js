const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const shoppingListItemContributionSchema = new mongoose.Schema({
  shopping_list_item_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ShoppingListItem',
    required: true,
    index: true
  },
  recipe_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu', // Reference the 'Menu' model (used as Recipe)
    required: true,
    index: true
  },
  contributed_quantity: {
    // Using Mixed type to allow both Numbers and Strings
    type: mongoose.Schema.Types.Mixed,
    required: true
  }
}, {
  timestamps: true
});

// Compound index for faster lookup when removing recipe contributions
shoppingListItemContributionSchema.index({ recipe_id: 1, shopping_list_item_id: 1 }); 

// Use the imported connection to create the model
const ShoppingListItemContribution = connection1.model('ShoppingListItemContribution', shoppingListItemContributionSchema);

module.exports = ShoppingListItemContribution; 