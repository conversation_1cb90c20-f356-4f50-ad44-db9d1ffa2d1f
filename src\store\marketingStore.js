import { defineStore } from 'pinia';
import { ref } from 'vue';
// import apiClient from '@/services/apiClient'; // Removed apiClient import
import axios from 'axios'; // Added axios import
import { useHelperStore } from '../../utils/helper';
import useNotification from '../../modules/notificationInformation';

export const useMarketingStore = defineStore('marketing', () => {
  const content = ref(null); // Holds the latest marketing content object or null
  const isLoading = ref(false);
  const error = ref(null);

  const helper = useHelperStore();
  const { setNotification } = useNotification();

  async function fetchLatestContent() {
    helper.devConsole('[MarketingStore] Fetching latest marketing content...');
    isLoading.value = true;
    error.value = null;
    content.value = null; // Reset content before fetching

    try {
      // Construct full URL and use axios directly
      const fullUrl = `${import.meta.env.VITE_API_BASE_URL}/api/v1/marketing-content`;
      helper.devConsole('[MarketingStore] Calling GET:', fullUrl); // Log the full URL
      const response = await axios.get(fullUrl); 

      if (response.data && response.data.status === 'success') {
        if (response.data.data) {
          content.value = response.data.data;
          helper.devConsole('[MarketingStore] Content fetched successfully:', content.value);
          helper.devConsole('[MarketingStore] videoS3Url from API:', content.value?.videoS3Url);
        } else {
          // Success, but no content generated yet
          content.value = null;
          helper.devConsole('[MarketingStore] Successfully fetched, but no marketing content available (data is null).');
        }
      } else {
        // Handle cases where API response structure is unexpected
        throw new Error(response.data?.message || 'Unexpected response structure from marketing content API.');
      }
    } catch (err) {
      console.error('[MarketingStore] Error fetching marketing content:', err);
      const message = err.response?.data?.message || err.message || 'Fehler beim Laden des Marketing-Inhalts.';
      error.value = message;
      setNotification(message, 'alert');
      content.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    content,
    isLoading,
    error,
    fetchLatestContent,
  };
}); 