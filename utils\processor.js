// processor.js
class PCMEncodeProcessor extends AudioWorkletProcessor {
    constructor() {
        super();
    }

    process(inputs, outputs) {
        // Überprüfen der verfügbaren Eingaben
        const input = inputs[0];
        if (input.length > 0) {
            const channel = input[0]; // Greift auf den ersten Kanal zu
            const samples = new Int16Array(channel.length);
            for (let i = 0; i < channel.length; i++) {
                // Konvertiere Float32Array (-1 bis 1) zu Int16Array
                samples[i] = Math.max(-1, Math.min(1, channel[i])) * 0x7FFF;
            }
            // Postet die PCM16-kodierten Daten
            this.port.postMessage(samples.buffer);
        }
        return true;
    }
}

registerProcessor('pcm-encode-processor', PCMEncodeProcessor);