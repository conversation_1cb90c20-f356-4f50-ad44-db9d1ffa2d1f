import { defineStore } from 'pinia';
import { ref, watch, onMounted, onUnmounted } from 'vue';
import axios, { CancelToken } from 'axios';
import { useUserStore } from './userStore'; // To get the kitchentableId
import useNotification from '../../modules/notificationInformation'; // For error handling
import { useHelperStore, useConfirmationStore } from '../../utils/helper'; // For dev console logging & confirmation
import {
  saveOfflineAction,
  getOfflineActions,
  markActionAsSynced,
  cleanupSyncedActions,
  updateLastSyncTimestamp,
  isSyncNeeded,
  useOfflineSyncManager,
  saveLocalStateSnapshot,
  getLocalStateSnapshot,
  clearLocalStateSnapshot
} from '../services/offlineSync';
import { useNetworkMonitor } from '../services/networkMonitor';
import { mergeShoppingListData } from '../services/conflictResolution';

// Define localStorage key
const OFFLINE_QUEUE_KEY = 'offlineShoppingListActions';

export const useActiveShoppingListStore = defineStore('activeShoppingList', () => {
  const items = ref([]); // Holds the ShoppingListItems of the active list
  const listId = ref(null); // Holds the ID of the active shopping list itself
  const listCreatedAt = ref(null); // Holds the creation date of the active list
  const listName = ref(''); // Holds the name of the active shopping list
  const isActive = ref(false); // Flag um zu speichern, ob die Liste aktiv ist oder abgeschlossen
  const isCompleted = ref(false); // Flag um zu speichern, ob die Liste endgültig abgeschlossen ist
  const isLoading = ref(false);
  const error = ref(null);
  const wsInstance = ref(null);
  const isConnected = ref(false);
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;
  const initialReconnectDelay = 1000; // 1 second
  const maxReconnectDelay = 30000; // 30 seconds
  let reconnectTimer = null;
  let intentionallyDisconnected = false;
  const isOnline = ref(navigator.onLine); // Browser online status
  let visibilityChangeHandler = null; // Für die Behandlung von Sichtbarkeitsänderungen

  const userStore = useUserStore();
  const { setNotification } = useNotification();
  const helper = useHelperStore();
  const confirmationStore = useConfirmationStore(); // Instantiate confirmation store
  const networkMonitor = useNetworkMonitor(); // Netzwerküberwachung
  const offlineSyncManager = useOfflineSyncManager(); // Offline-Synchronisierung

  // Verbesserte Funktion zur Behandlung von Netzwerkstatusänderungen
  function handleNetworkStatusChange(isNetworkOnline) {
    helper.devConsole(`Network status changed: ${isNetworkOnline ? 'Online' : 'Offline'}`);

    // Nur aktualisieren, wenn sich der Status geändert hat
    if (isOnline.value !== isNetworkOnline) {
      const wasOnline = isOnline.value;
      isOnline.value = isNetworkOnline;

      if (!isNetworkOnline && wasOnline) {
        // Wir gehen offline - speichere den aktuellen Zustand
        helper.devConsole('Going offline - saving local state snapshot');
        saveLocalStateSnapshot(items.value, listId.value);
      } else if (isNetworkOnline && !wasOnline) {
        // Wir kommen online - versuche die Verbindung wiederherzustellen
        helper.devConsole('Coming online - checking connection status...');
        checkAndRestoreConnection();
      }
    }
  }

  // Verbesserte Funktion zur Behandlung von Sichtbarkeitsänderungen (App im Vordergrund/Hintergrund)
  async function handleVisibilityChange() {
    if (document.visibilityState === 'visible') {
      helper.devConsole('App is now visible (foreground). Checking connection status...');

      // Kurze Verzögerung, um sicherzustellen, dass das Gerät Zeit hatte, die Netzwerkverbindung wiederherzustellen
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Überprüfen und wiederherstellen der Verbindung
      await checkAndRestoreConnection();
    }
  }

  // Initialisierung
  onMounted(() => {
    // Netzwerkstatus-Listener hinzufügen
    const removeNetworkListener = networkMonitor.addStatusListener(handleNetworkStatusChange);

    // Sichtbarkeitsänderungs-Listener hinzufügen
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Sync-Callback registrieren
    const removeSyncCallback = offlineSyncManager.registerSyncCallback(syncOfflineActions);

    // Cleanup-Funktion speichern
    visibilityChangeHandler = () => {
      removeNetworkListener();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      removeSyncCallback();
    };
  });

  // Cleanup beim Unmounten
  onUnmounted(() => {
    if (visibilityChangeHandler) {
      visibilityChangeHandler();
    }
  });

  // --- Helper functions for localStorage queue ---
  const getOfflineQueue = () => {
    try {
      const storedQueue = localStorage.getItem(OFFLINE_QUEUE_KEY);
      return storedQueue ? JSON.parse(storedQueue) : [];
    } catch (e) {
      console.error("Error reading offline queue from localStorage:", e);
      return []; // Return empty array on error
    }
  };

  const setOfflineQueue = (queue) => {
    try {
      localStorage.setItem(OFFLINE_QUEUE_KEY, JSON.stringify(queue));
    } catch (e) {
      console.error("Error writing offline queue to localStorage:", e);
    }
  };

  const addToOfflineQueue = (action) => {
    const queue = getOfflineQueue();
    queue.push(action);
    setOfflineQueue(queue);
    helper.devConsole("Added action to offline queue:", action, "New queue length:", queue.length);
  };

  const clearOfflineQueue = () => {
    try {
      localStorage.removeItem(OFFLINE_QUEUE_KEY);
      helper.devConsole("Offline queue cleared.");
    } catch (e) {
      console.error("Error clearing offline queue from localStorage:", e);
    }
  };
  // -----------------------------------------------

  // --- Watch for Kitchentable ID to fetch list automatically ---
  watch(() => userStore.user.defaultKitchentable, (newKitchentableId, oldKitchentableId) => {
    helper.devConsole(`Kitchentable ID changed from ${oldKitchentableId} to ${newKitchentableId}, checking if list needs fetching.`);
    if (newKitchentableId && !listId.value) {
        helper.devConsole(`Fetching active shopping list automatically for new kitchentable ID.`);
        fetchActiveList();
    }
    // Optional: Handle case where kitchentable changes and listId *is* set (refetch? clear?)
    // else if (newKitchentableId && listId.value) {
    //    // Decide if refetching is needed for the new table
    //    fetchActiveList();
    // }
  }, { immediate: true }); // immediate: true runs the watcher once on store initialization

  // --- Watch for WebSocket connection status ---
  watch(() => isConnected.value, (newValue, oldValue) => {
    if (oldValue === true && newValue === false) {
        // Transitioned from Online to Offline - Clear timer and notifications
        helper.devConsole('Connection lost, clearing offline timer. No notification shown to avoid user confusion.');
        clearReconnectTimer();
        // No notification shown for offline status - silent handling
    } else if (oldValue === false && newValue === true) {
        // Transitioned from Offline to Online - Clear timer and notifications
        helper.devConsole('Connection restored, clearing offline timer.');
        // Timer already cleared at the beginning of the watch
        // No notification shown for online status - silent handling
    }
  });

  // --- NEW: Watch for listId to connect WebSocket ---
  watch(listId, (newId, oldId) => {
    helper.devConsole(`[Watcher listId] Changed from ${oldId} to ${newId}. isConnected: ${isConnected.value}, wsInstance: ${wsInstance.value ? 'exists' : 'null'}`); // Log watcher trigger
    if (newId && !isConnected.value && !wsInstance.value) {
      helper.devConsole(`[Watcher listId] Condition met. Calling connectWebSocket.`); // Log condition met
      connectWebSocket();
    }
     // Optional: Handle disconnect if listId becomes null (e.g., list finished/deleted)
    // else if (!newId && oldId && (isConnected.value || wsInstance.value)) {
    //   helper.devConsole('List ID is null, disconnecting WebSocket.');
    //   disconnectWebSocket();
    // }
  });

  // --- Actions ---

  async function fetchActiveList() {
    const kitchentableId = userStore.user.defaultKitchentable;
    if (!kitchentableId) {
      error.value = 'Kein Küchentisch ausgewählt.';
      // Only show notification if likely online, otherwise WS handling is primary
      // No notification here if offline, watcher handles persistent message
      console.error("fetchActiveList: No kitchentableId found in userStore.");
      return;
    }

    isLoading.value = true;
    error.value = null;
    helper.devConsole(`Fetching active shopping list for kitchentable: ${kitchentableId}`);

    // Prüfe, ob wir online sind, bevor wir versuchen zu laden
    if (!networkMonitor.getIsOnline()) {
      helper.devConsole('Device is offline. Skipping fetchActiveList and keeping current local state.');
      isLoading.value = false;
      // Behalte den aktuellen lokalen Zustand bei, anstatt ihn zu löschen
      return;
    }

    try {
      const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${kitchentableId}/shopping-list/active`);

      if (response.data && response.data.success && response.data.data) {
        // Assuming the API returns the list object containing an 'items' array and the list '_id'
        items.value = response.data.data.items || [];
        listId.value = response.data.data._id || null; // Store the list ID
        listCreatedAt.value = response.data.data.createdAt || null; // Store createdAt
        listName.value = response.data.data.name || ''; // Store the list name
        isActive.value = response.data.data.is_active !== undefined ? response.data.data.is_active : true; // Speichere is_active, Standard: true
        isCompleted.value = response.data.data.is_completed || false; // Speichere is_completed Status
        helper.devConsole(`Fetched ${items.value.length} items for list ${listId.value}, created at ${listCreatedAt.value}, is_active: ${isActive.value}`);

        // ---> TRY CONNECTING DIRECTLY AFTER FETCH <---
        if (listId.value && !isConnected.value && !wsInstance.value) {
          helper.devConsole(`[fetchActiveList] List fetched successfully. Attempting WebSocket connection.`);
          connectWebSocket();
        }
        // --------------------------------------------

      } else {
         // Handle cases where the API call succeeds but returns no data (e.g., no active list exists)
        items.value = [];
        listId.value = null;
        listCreatedAt.value = null; // Clear createdAt
        isActive.value = false; // Liste nicht aktiv, wenn keine Daten zurückkommen
        isCompleted.value = false; // Clear completed status
        helper.devConsole(`No active shopping list found or unexpected response structure for kitchentable: ${kitchentableId}`);
      }
    } catch (err) {
      console.error('Error fetching active shopping list:', err);
      const isNetworkError = err.code === 'ERR_NETWORK' || !err.response;

      // Verbesserte Offline-Behandlung
      if (isNetworkError) {
        // Netzwerkfehler - keine störende Benachrichtigung anzeigen
        error.value = 'Offline-Modus: Verwende lokale Daten.';
        helper.devConsole('Fetch failed due to network error. Using local data if available.');

        // Behalte den aktuellen lokalen Zustand bei, anstatt ihn zu löschen
        // Nur wenn wir noch gar keine Daten haben, setzen wir leere Werte
        if (!listId.value && items.value.length === 0) {
          items.value = [];
          listId.value = null;
          listCreatedAt.value = null;
          isActive.value = false;
        }
      } else {
        // Andere Fehler (Server-Fehler, etc.) - zeige Benachrichtigung nur wenn online
        if (networkMonitor.getIsOnline()) {
          const defaultMessage = 'Fehler beim Laden des Einkaufszettels.';
          let specificMessage = defaultMessage;
          if (err.response?.data?.message && typeof err.response.data.message === 'string') {
              specificMessage = err.response.data.message;
          }
          error.value = specificMessage; // Set reactive error state
          setNotification(specificMessage, 'alert'); // Show notification for other errors

          // Bei Server-Fehlern leeren wir die Daten
          items.value = [];
          listId.value = null;
          listCreatedAt.value = null;
          isActive.value = false;
        } else {
          // Auch bei anderen Fehlern im Offline-Modus: lokale Daten behalten
          error.value = 'Offline-Modus: Verwende lokale Daten.';
          helper.devConsole('Non-network error while offline. Keeping local data.');
        }
      }
    } finally {
      isLoading.value = false;
    }
  }

  // --- WebSocket Actions ---

  function clearReconnectTimer() {
      if (reconnectTimer) {
          clearTimeout(reconnectTimer);
          reconnectTimer = null;
      }
  }

  function scheduleReconnect() {
      clearReconnectTimer(); // Clear existing timer if any

      if (reconnectAttempts.value >= maxReconnectAttempts) {
          helper.devConsole(`WebSocket: Max reconnect attempts (${maxReconnectAttempts}) reached. Giving up.`);
          // Keine Benachrichtigung bei dauerhaftem Verbindungsfehler
          // Stille Fehlerbehandlung, da zu viele Benachrichtigungen störend sein können
          reconnectAttempts.value = 0; // Reset for potential manual reconnect later
          return;
      }

      // Exponential backoff with jitter
      const delay = Math.min(
          initialReconnectDelay * Math.pow(2, reconnectAttempts.value) + Math.random() * 1000,
          maxReconnectDelay
      );

      reconnectAttempts.value++;
      helper.devConsole(`WebSocket: Scheduling reconnect attempt ${reconnectAttempts.value}/${maxReconnectAttempts} in ${delay.toFixed(0)}ms`);

      reconnectTimer = setTimeout(() => {
          helper.devConsole('WebSocket: Attempting reconnect...');
          connectWebSocket(); // Try to connect again
      }, delay);
  }

  function connectWebSocket() {
    // Reset intentional disconnect flag on new connection attempt
    intentionallyDisconnected = false;

    // Überprüfen, ob wir bereits verbunden sind oder eine Verbindung aufbauen
    if (isConnected.value) {
      helper.devConsole('WebSocket already connected.');
      return;
    }

    // Wenn eine WebSocket-Instanz existiert, überprüfen wir ihren Status
    if (wsInstance.value) {
      const readyState = wsInstance.value.readyState;

      if (readyState === WebSocket.CONNECTING) {
        helper.devConsole('WebSocket is already in the process of connecting. Waiting...');
        return;
      } else if (readyState === WebSocket.OPEN) {
        helper.devConsole('WebSocket is already open, but isConnected flag was false. Correcting...');
        isConnected.value = true;
        return;
      } else {
        // CLOSING oder CLOSED
        helper.devConsole(`WebSocket instance exists but is in state ${readyState}. Cleaning up before reconnecting.`);
        try {
          wsInstance.value.close();
        } catch (e) {
          // Ignorieren, da wir sowieso eine neue Instanz erstellen
        }
        wsInstance.value = null;
      }
    }

    // Überprüfen, ob wir online sind
    if (!isOnline.value) {
      helper.devConsole('Device is offline. Cannot connect WebSocket.');
      return;
    }

    // Get kitchentableId from userStore
    const kitchentableId = userStore.user.defaultKitchentable;
    const token = localStorage.getItem('session_token'); // Use session_token instead

    // Basic checks
    if (!kitchentableId) {
        console.error('WebSocket: Cannot connect without kitchentableId.');
        helper.devConsole(">>> connectWebSocket aborted: Missing kitchentableId"); // More detailed log
        return;
    }
    if (!token) {
        console.error('WebSocket: Cannot connect without authentication token.');
        helper.devConsole(">>> connectWebSocket aborted: Missing session_token"); // More detailed log
        return;
    }

    // Construct the WebSocket URL (ensure base URL is correctly defined, e.g., from env variables)
    const baseWsUrl = import.meta.env.VITE_API_BASE_WS_URL || 'ws://localhost:8080'; // Example: Use env var or default
    const wsUrl = `${baseWsUrl}/ws/shopping-list/${kitchentableId}?token=${token}`;

    helper.devConsole(`>>> Connecting WebSocket to: ${wsUrl}`); // Enhanced log
    helper.devConsole(`>>> Using kitchentableId: ${kitchentableId}, token: ${token ? 'exists' : 'missing!'}`); // Log critical values

    // Timeout für die Verbindung setzen
    const connectionTimeout = setTimeout(() => {
      if (wsInstance.value && wsInstance.value.readyState === WebSocket.CONNECTING) {
        helper.devConsole('WebSocket connection attempt timed out. Cleaning up...');
        try {
          wsInstance.value.close();
        } catch (e) {
          // Ignorieren
        }
        wsInstance.value = null;

        // Reconnect-Logik auslösen
        if (!intentionallyDisconnected) {
          scheduleReconnect();
        }
      }
    }, 10000); // 10 Sekunden Timeout

    // Neue WebSocket-Instanz erstellen
    wsInstance.value = new WebSocket(wsUrl);

    wsInstance.value.onopen = async () => {
      helper.devConsole('>>> WebSocket onopen triggered. Connection successful.'); // Log onopen
      isConnected.value = true; // This will trigger the watcher to remove offline msg
      reconnectAttempts.value = 0;
      clearReconnectTimer();

      // Clear potential previous internal WebSocket error state on successful connect
      if (error.value && error.value.startsWith('WebSocket-Verbindung')) {
         error.value = null;
      }

      await syncOfflineActions();
    };

    wsInstance.value.onmessage = function(event) { // Explicit function expression
      console.log('[WS Raw Data]:', event.data);
      console.log('[WS Raw Data Type]:', typeof event.data);

      try {
        const message = JSON.parse(event.data);
        console.log('[WS Parsed Message]:', message);

        if (message && message.event === 'zettel_updated') {
          const payload = message.data;
          console.log('[WS zettel_updated Payload (before check)]:', payload);
          console.log('[WS zettel_updated Payload Type (before check)]:', typeof payload);
          if (payload === null) {
            items.value = [];
            listId.value = null;
            listCreatedAt.value = null;
            isActive.value = false; // Liste nicht aktiv bei null-Payload
            isCompleted.value = false; // Clear completed status
            console.log('[WS Processed]: List removed (payload is null).');
          } else if (typeof payload === 'object' && payload !== null) {
            // KRITISCHER FIX: Intelligente Merge-Logik für bereits erledigte Items
            const serverItems = payload.items || [];
            const currentItems = items.value || [];

            // Merge-Logik: Behalte lokale "is_purchased" Status bei, wenn sie neuer sind
            const mergedItems = serverItems.map(serverItem => {
              const localItem = currentItems.find(local => local._id === serverItem._id);

              if (localItem && localItem.is_purchased && !serverItem.is_purchased) {
                // Lokales Item ist als gekauft markiert, Server-Item nicht
                // Prüfe, ob es eine lokale Offline-Aktion gibt
                const offlineActions = getOfflineActions();
                const recentPurchaseAction = offlineActions.find(action =>
                  action.type === 'updateItemPurchasedStatus' &&
                  action.payload.itemId === serverItem._id &&
                  action.payload.isPurchased === true &&
                  !action.synced
                );

                if (recentPurchaseAction) {
                  helper.devConsole(`[MERGE] Behalte lokalen gekauft-Status für Item ${serverItem._id} (${serverItem.name}) - Offline-Aktion gefunden`);
                  return { ...serverItem, is_purchased: true };
                } else {
                  helper.devConsole(`[MERGE] Übernehme Server-Status für Item ${serverItem._id} (${serverItem.name}) - Keine lokale Offline-Aktion gefunden`);
                }
              }

              return serverItem;
            });

            items.value = mergedItems;
            listId.value = payload._id || null;
            listCreatedAt.value = payload.createdAt || null;
            listName.value = payload.name || ''; // Update list name
            isActive.value = payload.is_active !== undefined ? payload.is_active : true; // Speichere is_active, Standard: true
            isCompleted.value = payload.is_completed || false; // Speichere is_completed Status
            console.log(`[WS Processed]: List updated with ${mergedItems.length} items (${mergedItems.filter(i => i.is_purchased).length} purchased). is_active: ${isActive.value}, is_completed: ${isCompleted.value}`);
          } else {
            console.log('[WS ERROR]: Invalid zettel_updated payload received. Payload:', payload);
            console.log('[WS ERROR]: Type of invalid payload:', typeof payload);
          }
        } else if (message && message.event === 'zettel_finished') {
          // Spezielles Event für abgeschlossene Listen
          helper.devConsole('[WS Processed]: Received zettel_finished event', message.data);

          // Liste als inaktiv und abgeschlossen markieren
          isActive.value = false;
          isCompleted.value = true;

          // Erfolgsmeldung anzeigen
          setNotification('Einkaufszettel erfolgreich abgeschlossen!', 'success');

          // Optional: Aktualisieren der Listendetails, falls vom Backend mitgeliefert
          if (message.data && typeof message.data === 'object') {
            if (message.data._id) listId.value = message.data._id;
            if (message.data.items) items.value = message.data.items;
            if (message.data.is_completed !== undefined) isCompleted.value = message.data.is_completed;
          }
        } else if (message && message.event === 'item_status_updated') {
          const { itemId, isPurchased } = message.data || {};
          helper.devConsole(`[WS Processed]: Received item_status_updated for item ${itemId} to ${isPurchased}`);
          if (itemId && typeof isPurchased === 'boolean') {
            const itemIndex = items.value.findIndex(item => item._id === itemId);
            if (itemIndex !== -1) {
              items.value[itemIndex].is_purchased = isPurchased;
              helper.devConsole(`[WS Local Update]: Updated item ${itemId} status to ${isPurchased}`);
            } else {
              helper.devConsole(`[WS WARN]: item_status_updated received for unknown item ID: ${itemId}`);
              fetchActiveList();
            }
          } else {
             helper.devConsole('[WS ERROR]: Invalid item_status_updated payload:', message.data);
          }
        } else if (message && message.event === 'zettel_error') {
           console.log('[WS Processed]: Received zettel_error event.');
        } else if (message && message.event === 'REQUEST_FINAL_ITEM_CONFIRMATION' || message && message.event === 'last_item_confirmation_needed') {
          helper.devConsole(`[WS Processed]: Received ${message.event} event`, message.data);

          // Direkte Nutzung des confirmationStore entsprechend der Backend-Anweisungen
          confirmationStore.showConfirmation(
            'Liste abschließen?',
            message.data.message || 'Dies ist das letzte Item. Möchten Sie die Liste abschließen?',
            'JA',
            'NEIN'
          ).then(confirmed => {
            if (confirmed) {
              // Benutzer hat mit JA bestätigt - Liste abschließen
              finishListWithLastItem(message.data.itemId);
            } else {
              // Benutzer hat mit NEIN abgelehnt - Item zurücksetzen
              revertItemStatus(message.data.itemId);
            }
          });
        } else if (message && message.event === 'shopping_list_name_updated') {
          helper.devConsole('[WS INFO]: Received shopping_list_name_updated event:', message.data);
          // Update list name in store
          if (message.data.listId === listId.value) {
            listName.value = message.data.name;
            helper.devConsole(`Updated list name to: ${message.data.name}`);
          }
        } else if (message && message.event === 'zettel_finished') {
          helper.devConsole('[WS INFO]: Received zettel_finished event:', message.data);
          // Update store to reflect completed status
          if (message.data.listId === listId.value) {
            isCompleted.value = true;
            isActive.value = false;
            helper.devConsole(`Marked list ${message.data.listId} as completed`);
          }
        } else {
            console.log('[WS INFO]: Received message with unknown or missing event type:', message?.event);
        }
      } catch (e) {
        console.error('[WS JSON Parse Error]:', e);
        console.error('[WS Raw Data on Parse Error]:', event.data);
      }
    };

    wsInstance.value.onclose = (event) => {
      helper.devConsole(`>>> WebSocket onclose triggered: Code=${event.code}, Reason='${event.reason}', WasClean=${event.wasClean}`); // Enhanced log
      // Setting isConnected to false here will trigger the watcher
      if (isConnected.value) { // Only set if it was previously true
        isConnected.value = false;
      }
      wsInstance.value = null;
      clearReconnectTimer();

      // Only attempt reconnect if disconnected unintentionally
      if (!intentionallyDisconnected) {
          helper.devConsole('WebSocket: Unintentional disconnect detected, scheduling reconnect...');
          scheduleReconnect();
      } else {
          helper.devConsole('WebSocket: Intentional disconnect, no reconnect attempt.');
          intentionallyDisconnected = false;
      }
    };

    wsInstance.value.onerror = (err) => {
      console.error('WebSocket error event:', err);
      helper.devConsole('>>> WebSocket onerror triggered.', err); // Enhanced log
      // --- REMOVED: No notification directly from onerror ---
      // Just log the error. The onclose event following this will handle setting
      // isConnected to false, which then triggers the watcher for the offline message.
      // --------------------------------------------------------
      // Setting isConnected false here might be redundant as onclose usually follows,
      // but can be kept for robustness in case onclose doesn't fire.
      // if (isConnected.value) { isConnected.value = false; }
    };
  }

  function disconnectWebSocket() {
    helper.devConsole('WebSocket: Intentional disconnect.');
    intentionallyDisconnected = true; // Set flag before closing
    clearReconnectTimer();
    if (wsInstance.value) {
      wsInstance.value.close(1000, 'User initiated disconnect'); // Code 1000 for normal closure
      wsInstance.value = null; // Explicitly nullify after closing attempt
    }
    // Explicitly set connected to false and remove offline message if present
    if (isConnected.value) {
        isConnected.value = false;
    }
  }

  // Verbesserte Funktion zur Überprüfung und Wiederherstellung der WebSocket-Verbindung
  async function checkAndRestoreConnection() {
    helper.devConsole('Überprüfe WebSocket-Verbindungsstatus...');

    // Wenn wir offline sind, können wir nichts tun
    if (!networkMonitor.getIsOnline()) {
      helper.devConsole('Gerät ist offline. Kann WebSocket-Verbindung nicht wiederherstellen.');
      return false;
    }

    // Wenn keine Liste aktiv ist, brauchen wir keine Verbindung
    if (!listId.value) {
      helper.devConsole('Keine aktive Einkaufsliste. WebSocket-Verbindung nicht erforderlich.');
      return false;
    }

    // Überprüfen, ob wir wirklich verbunden sind
    const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

    // Prüfen, ob eine Synchronisierung erforderlich ist
    const needsSync = offlineSyncManager.hasPendingSync() || isSyncNeeded();
    if (needsSync) {
      helper.devConsole('Offline-Aktionen müssen synchronisiert werden.');
    }

    if (isReallyConnected) {
      helper.devConsole('WebSocket-Verbindung ist gesund.');

      // Wenn wir verbunden sind und Offline-Aktionen haben, synchronisieren wir diese
      if (needsSync) {
        helper.devConsole('Synchronisiere Offline-Aktionen, da die Verbindung wiederhergestellt wurde...');
        await syncOfflineActions();
      }

      return true;
    }

    // Wenn wir hier sind, ist die Verbindung nicht in Ordnung
    helper.devConsole(`WebSocket-Verbindung ist nicht gesund. isConnected=${isConnected.value}, wsInstance=${wsInstance.value ? 'exists' : 'null'}, readyState=${wsInstance.value ? wsInstance.value.readyState : 'N/A'}`);

    // Wenn wir nicht absichtlich getrennt wurden, versuchen wir, die Verbindung wiederherzustellen
    if (!intentionallyDisconnected) {
      helper.devConsole('Versuche, die WebSocket-Verbindung wiederherzustellen...');

      // Bestehende WebSocket-Verbindung bereinigen, falls vorhanden
      if (wsInstance.value) {
        try {
          wsInstance.value.close();
        } catch (e) {
          // Ignorieren, da wir sowieso eine neue Instanz erstellen
        }
        wsInstance.value = null;
      }

      // Neue Verbindung herstellen
      connectWebSocket();

      // Warten, bis die Verbindung hergestellt ist (maximal 5 Sekunden)
      if (needsSync) {
        let attempts = 0;
        const maxAttempts = 10;
        const delay = 500; // 500ms zwischen den Versuchen

        while (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, delay));

          const connectionRestored = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;
          if (connectionRestored) {
            helper.devConsole('Verbindung wiederhergestellt. Synchronisiere Offline-Aktionen...');
            await syncOfflineActions();

            // Liste neu laden, um sicherzustellen, dass wir den aktuellen Zustand haben
            await fetchActiveList();

            break;
          }

          attempts++;
        }

        if (attempts >= maxAttempts) {
          helper.devConsole('Konnte die Verbindung nicht wiederherstellen. Offline-Aktionen bleiben in der Warteschlange.');

          // Trotzdem versuchen, die Liste zu laden, um lokale Änderungen anzuzeigen
          await fetchActiveList();
        }
      }

      return true; // Wir haben versucht, die Verbindung wiederherzustellen
    } else {
      helper.devConsole('WebSocket wurde absichtlich getrennt. Keine Wiederverbindung.');
      return false;
    }
  }

  function sendMessage(message) {
    // Überprüfen, ob wir wirklich verbunden sind
    const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

    if (!isReallyConnected) {
      console.error('WebSocket is not connected or not in OPEN state. Cannot send message:', message);
      helper.devConsole(`WebSocket state check: isConnected=${isConnected.value}, wsInstance=${wsInstance.value ? 'exists' : 'null'}, readyState=${wsInstance.value ? wsInstance.value.readyState : 'N/A'}`);

      // Versuchen, die Verbindung wiederherzustellen, wenn wir online sind
      if (isOnline.value && (!wsInstance.value || wsInstance.value.readyState !== WebSocket.OPEN) && listId.value) {
        helper.devConsole('Attempting to reconnect WebSocket after sendMessage failure');
        connectWebSocket();
      }

      // Werfen eines Fehlers, damit der Aufrufer entscheiden kann, wie er damit umgehen möchte
      throw new Error('WebSocket is not connected or not in OPEN state');
    }

    try {
      wsInstance.value.send(JSON.stringify(message));
      return true; // Erfolgreicher Versand
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      helper.devConsole(`Error details for failed message: ${JSON.stringify(error)}`);

      // Wenn der Fehler auf ein Verbindungsproblem hindeutet, versuchen wir, die Verbindung wiederherzustellen
      if (isOnline.value) {
        helper.devConsole('Attempting to reconnect WebSocket after send error');
        connectWebSocket();
      }

      // Werfen des Fehlers, damit der Aufrufer entscheiden kann, wie er damit umgehen möchte
      throw error;
    }
  }

  // --- Item Modification Actions ---

  // Helper function to reset the list state
  function resetList() {
    helper.devConsole('Resetting active shopping list state.');
    items.value = [];
    listId.value = null;
    listCreatedAt.value = null;
    isActive.value = false; // Liste nicht aktiv bei Reset
    error.value = null;
    isLoading.value = false;
  }

  // --- Intelligente Sync Offline Actions Function mit Merge-Logik ---
  async function syncOfflineActions() {
    // Wenn wir offline sind, können wir nicht synchronisieren
    if (!networkMonitor.getIsOnline()) {
      helper.devConsole("Gerät ist offline. Kann Offline-Aktionen nicht synchronisieren.");
      return;
    }

    // Wenn keine aktive Liste vorhanden ist, können wir nicht synchronisieren
    if (!listId.value) {
      console.error("Kann Offline-Aktionen nicht synchronisieren: Keine aktive Listen-ID. Aktionen bleiben in der Warteschlange.");
      return;
    }

    // Prüfe, ob wir einen gültigen Session-Token haben
    const sessionToken = localStorage.getItem('session_token');
    if (!sessionToken) {
      helper.devConsole("Kein gültiger Session-Token gefunden. Überspringe Offline-Synchronisierung.");
      return;
    }

    helper.devConsole("=== STARTE VEREINFACHTE OFFLINE-SYNCHRONISIERUNG ===");

    // Verwende nur WebSocket-basierte Synchronisierung, wenn verbunden
    const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

    if (isReallyConnected) {
      helper.devConsole("WebSocket ist verbunden. Verwende WebSocket-basierte Synchronisierung.");
      await performWebSocketSync();
    } else {
      helper.devConsole("WebSocket ist nicht verbunden. Verwende HTTP-basierte Synchronisierung.");
      await performNormalSync();
    }

    helper.devConsole("=== OFFLINE-SYNCHRONISIERUNG ABGESCHLOSSEN ===");
  }

  // Neue WebSocket-basierte Synchronisierung
  async function performWebSocketSync() {
    const offlineActions = getOfflineActions();

    if (offlineActions.length === 0) {
      helper.devConsole("Keine Offline-Aktionen für WebSocket-Sync gefunden.");
      return;
    }

    helper.devConsole(`Synchronisiere ${offlineActions.length} Aktionen über WebSocket...`);

    for (let i = 0; i < offlineActions.length; i++) {
      const action = offlineActions[i];

      // Überspringe bereits synchronisierte Aktionen
      if (action.synced) continue;

      // Überspringe Aktionen für andere Listen
      if (action.listId && action.listId !== listId.value) continue;

      try {
        await sendOfflineActionViaWebSocket(action);
        markActionAsSynced(i);
        helper.devConsole(`WebSocket-Aktion erfolgreich synchronisiert: ${action.type}`);

        // Kurze Pause zwischen den Aktionen
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Fehler beim Synchronisieren der WebSocket-Aktion ${action.type}:`, error);
        // Behalte die Aktion in der Warteschlange für den nächsten Versuch
      }
    }

    // Bereinige synchronisierte Aktionen
    cleanupSyncedActions();
    updateLastSyncTimestamp();
  }

  // Hilfsfunktion für normale Synchronisierung (Fallback)
  async function performNormalSync() {
    helper.devConsole("Führe normale Synchronisierung durch...");

    const queue = getOfflineQueue();
    const offlineActions = getOfflineActions();

    if (queue.length === 0 && offlineActions.length === 0) {
      helper.devConsole("Keine Offline-Aktionen zum Synchronisieren gefunden.");
      return;
    }

    // Alte Queue-Synchronisierung
    if (queue.length > 0) {
      try {
        const origin = new URL(import.meta.env.VITE_API_BASE_URL).origin;
        const url = `${origin}/api/shopping-lists/${listId.value}/sync-offline-changes`;

        // Erstelle eine spezielle Axios-Instanz für Offline-Sync, die nicht den globalen Interceptor verwendet
        const offlineAxios = axios.create();

        // Füge manuell den Authorization-Header hinzu
        const sessionToken = localStorage.getItem('session_token');
        const headers = {};
        if (sessionToken) {
          headers['Authorization'] = `Bearer ${sessionToken}`;
        }

        const response = await offlineAxios.post(url, queue, { headers });

        if (response.data && response.data.success && response.data.data) {
          items.value = response.data.data.items || [];
          listId.value = response.data.data._id || null;
          listCreatedAt.value = response.data.data.createdAt || null;
          isActive.value = response.data.data.is_active !== undefined ? response.data.data.is_active : true;
          clearOfflineQueue();
          helper.devConsole("Alte Queue erfolgreich synchronisiert");
        }
      } catch (err) {
        console.error("Fehler bei der normalen Synchronisierung:", err);

        // Bei 401-Fehlern die Queue nicht leeren, sondern für späteren Versuch behalten
        if (err.response && err.response.status === 401) {
          helper.devConsole("401 Unauthorized bei Offline-Sync. Behalte Queue für späteren Versuch.");
        } else {
          helper.devConsole("Anderer Fehler bei Offline-Sync:", err.message);
        }
      }
    }

    // Neue Aktionen über WebSocket senden
    if (offlineActions.length > 0 && isConnected.value) {
      for (let i = 0; i < offlineActions.length; i++) {
        const action = offlineActions[i];
        if (action.synced || (action.listId && action.listId !== listId.value)) continue;

        try {
          await sendOfflineActionViaWebSocket(action);
          markActionAsSynced(i);
        } catch (error) {
          console.error(`Fehler beim Senden der Aktion ${action.type}:`, error);
        }
      }

      cleanupSyncedActions();
      updateLastSyncTimestamp();
    }
  }

  // Hilfsfunktion zum Senden von Offline-Aktionen über WebSocket
  async function sendOfflineActionViaWebSocket(action) {
    switch (action.type) {
      case 'updateItemPurchasedStatus':
        sendMessage({
          event: 'update_item_purchase_status',
          payload: {
            itemId: action.payload.itemId,
            isPurchased: action.payload.isPurchased
          }
        });
        break;
      case 'addManualItem':
        sendMessage({
          event: 'add_manual_item',
          payload: {
            name: action.payload.name,
            quantity: action.payload.quantity,
            unit: action.payload.unit,
            category: action.payload.category
          }
        });
        break;
      case 'deleteManualItem':
        sendMessage({
          event: 'delete_manual_item',
          payload: { itemId: action.payload.itemId }
        });
        break;
      case 'addRecipe':
        sendMessage({
          event: 'add_recipe',
          payload: { recipeId: action.payload.recipeId }
        });
        break;
      case 'removeRecipe':
        sendMessage({
          event: 'remove_recipe',
          payload: { recipeId: action.payload.recipeId }
        });
        break;
    }
  }

  // Hilfsfunktion zum Senden der gemergten Daten an den Server
  async function sendMergedDataToServer(mergedItems) {
    try {
      helper.devConsole("Sende gemergte Daten an den Server...");

      // Erstelle eine Payload mit den gemergten Items
      const payload = {
        items: mergedItems,
        mergeTimestamp: Date.now(),
        source: 'offline_merge'
      };

      const origin = new URL(import.meta.env.VITE_API_BASE_URL).origin;
      const url = `${origin}/api/shopping-lists/${listId.value}/merge-offline-state`;

      // Erstelle eine spezielle Axios-Instanz für Offline-Sync
      const offlineAxios = axios.create();

      // Füge manuell den Authorization-Header hinzu
      const sessionToken = localStorage.getItem('session_token');
      const headers = {};
      if (sessionToken) {
        headers['Authorization'] = `Bearer ${sessionToken}`;
      }

      const response = await offlineAxios.post(url, payload, { headers });

      if (response.data && response.data.success) {
        helper.devConsole("Gemergte Daten erfolgreich an den Server gesendet");

        // Aktualisiere den lokalen Zustand mit der Server-Antwort, falls verfügbar
        if (response.data.data && response.data.data.items) {
          items.value = response.data.data.items;
        }
      } else {
        throw new Error("Unerwartete Server-Antwort beim Senden der gemergten Daten");
      }
    } catch (error) {
      console.error("Fehler beim Senden der gemergten Daten an den Server:", error);

      // Bei 401-Fehlern nicht über WebSocket senden
      if (error.response && error.response.status === 401) {
        helper.devConsole("401 Unauthorized beim Senden der gemergten Daten. Überspringe WebSocket-Fallback.");
        return;
      }

      // Fallback: Sende die Änderungen über WebSocket
      helper.devConsole("Fallback: Sende Änderungen über WebSocket");
      if (isConnected.value) {
        sendMessage({
          event: 'sync_merged_state',
          payload: { items: mergedItems }
        });
      }
    }
  }
  // --- END: Intelligente Sync Offline Actions Function ---

  // Funktion zum Abschließen der Liste mit dem letzten Item
  async function finishListWithLastItem(itemId) {
    helper.devConsole('User confirmed to finish list with last item', itemId);
    try {
      // Korrigierter Endpunkt gemäß Backend-Anforderungen
      const endpoint = `${import.meta.env.VITE_API_BASE_URL}/api/shopping-list-items/${itemId}/confirm-and-finish`;
      helper.devConsole('[Final Item Confirmation] Calling endpoint:', endpoint);

      const response = await axios.post(endpoint);
      helper.devConsole('[Final Item Confirmation] API response:', response.data);

      if (response.data && response.data.success) {
        // Setzen des Liste-Status auf inaktiv (wird normalerweise durch WebSocket aktualisiert)
        isActive.value = false;

        setNotification('Einkaufszettel erfolgreich abgeschlossen!', 'success');
        // Die Liste sollte über WebSocket-Updates aktualisiert werden
      } else {
        setNotification(response.data.message || 'Fehler beim Abschließen des Einkaufszettels.', 'alert');
      }
    } catch (err) {
      console.error('Error calling confirm-and-finish:', err);
      helper.devConsole('[Final Item Confirmation] API error:', err);
      setNotification('Fehler beim Kommunizieren mit dem Server zum Abschließen des Zettels.', 'alert');
    }
  }

  // Funktion zum Zurücksetzen des Item-Status (wurde abgelehnt)
  function revertItemStatus(itemId) {
    helper.devConsole('User declined to finish list, reverting item status', itemId);

    // Item via WebSocket zurücksetzen auf "nicht erledigt"
    sendMessage({
      event: 'update_item_purchase_status',
      payload: {
        itemId: itemId,
        isPurchased: false
      }
    });

    setNotification('Aktion abgebrochen. Das Item wurde nicht als erledigt markiert.', 'info');
  }

  // --- MODIFIED: updateItemPurchasedStatus ---
  async function updateItemPurchasedStatus(itemId, isPurchased) {
    if (!listId.value) {
      console.error("updateItemPurchasedStatus: No active list ID.");
      setNotification('Fehler: Kein aktiver Einkaufszettel gefunden.', 'alert');
      return;
    }

    helper.devConsole(`Attempting to update item ${itemId} purchased status to ${isPurchased} for list ${listId.value}`);
    const action = {
      type: 'UPDATE_PURCHASE_STATUS',
      payload: { listId: listId.value, itemId, isPurchased },
      timestamp: Date.now(),
      id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}` // Unique ID for offline tracking
    };

    // Überprüfen, ob wir wirklich verbunden sind (robuste Prüfung)
    const isReallyConnected = networkMonitor.getIsOnline() &&
                              isConnected.value &&
                              wsInstance.value &&
                              wsInstance.value.readyState === WebSocket.OPEN;

    if (isReallyConnected) {
      // ONLINE: Send via WebSocket
      try {
        sendMessage({
          event: 'update_item_purchase_status',
          payload: { itemId, isPurchased }
        });
        // NO OPTIMISTIC UI UPDATE HERE FOR ONLINE MODE
        // The UI will update upon receiving 'item_status_updated' or 'REQUEST_FINAL_ITEM_CONFIRMATION' via WebSocket
        helper.devConsole(`Successfully sent update_item_purchase_status via WebSocket for item ${itemId}`);
      } catch (error) {
        console.error('Error sending update_item_purchase_status message:', error);
        // Fallback zur Offline-Behandlung bei WebSocket-Fehler
        handleOfflineItemUpdate(action, itemId, isPurchased);
      }
    } else {
      // OFFLINE oder Verbindungsprobleme
      helper.devConsole(`Device is offline or not connected. Handling item ${itemId} update offline.`);
      handleOfflineItemUpdate(action, itemId, isPurchased);
    }
  }

  // Hilfsfunktion für Offline-Item-Updates
  function handleOfflineItemUpdate(action, itemId, isPurchased) {
    // Add to offline queue
    addToOfflineQueue(action);

    // Speichere auch in der neuen Offline-Aktionen-Struktur
    saveOfflineAction({
      type: 'updateItemPurchasedStatus',
      payload: { itemId, isPurchased },
      listId: listId.value
    });

    // Optimistic UI update for OFFLINE mode
    const itemIndex = items.value.findIndex(item => item._id === itemId);
    if (itemIndex !== -1) {
        // WICHTIG: Verwende das richtige Feld-Format (is_purchased mit Unterstrich)
        items.value[itemIndex].is_purchased = isPurchased;
        items.value[itemIndex].purchasedAt = isPurchased ? new Date().toISOString() : null;
        items.value[itemIndex].updatedAt = new Date().toISOString();

        helper.devConsole(`Optimistically updated item ${itemId} to ${isPurchased ? 'purchased' : 'not purchased'}`);
        helper.devConsole(`Item ${itemId} is_purchased field set to:`, items.value[itemIndex].is_purchased);
    } else {
        console.warn(`Item ${itemId} not found for optimistic update`);
    }

    // Aktualisiere den lokalen Snapshot
    saveLocalStateSnapshot(items.value, listId.value);
  }

  async function finishActiveList(listIdToFinish) {
    if (!listIdToFinish) {
      console.error("finishActiveList: No list ID provided.");
      setNotification('Fehler: Keine Einkaufszettel-ID zum Abschließen angegeben.', 'alert');
      return;
    }
    helper.devConsole(`Attempting to finish list ${listIdToFinish}`);

    const action = {
      type: 'FINISH_LIST',
      payload: { listId: listIdToFinish },
      timestamp: Date.now(),
      id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}` // Unique ID for offline tracking
    };

    if (isConnected.value) {
        // ONLINE: Send via WebSocket
        sendMessage({
            event: 'finish_shopping_list'
            // No payload needed if the backend knows the list from the connection context
        });
        // Assume backend broadcast triggers UI update via 'zettel_updated' with null payload
    } else {
        // OFFLINE: Add to queue
        addToOfflineQueue(action);
        // Optimistic UI update: Clear the list
        resetList();
    }
    // No explicit HTTP call here anymore, relies on WS or offline queue
  }

  async function revertPurchase(itemIdToRevert) {
    if (!listId.value) {
        console.error("revertPurchase: No active list ID.");
        setNotification('Fehler: Kein aktiver Einkaufszettel gefunden.', 'alert');
        return;
    }

    helper.devConsole(`Attempting to revert purchase for item ${itemIdToRevert} on list ${listId.value}`);
    const action = {
        type: 'REVERT_PURCHASE',
        payload: { listId: listId.value, itemId: itemIdToRevert },
        timestamp: Date.now(),
        id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}`
    };

    if (isConnected.value) {
        // ONLINE: Send via WebSocket
        sendMessage({
            event: 'revert_item_purchase',
            payload: { itemId: itemIdToRevert }
        });
    } else {
        // OFFLINE: Add to queue
        addToOfflineQueue(action);
        // Optimistic UI update
        const itemIndex = items.value.findIndex(item => item._id === itemIdToRevert);
        if (itemIndex !== -1) {
            // WICHTIG: Verwende das richtige Feld-Format (is_purchased mit Unterstrich)
            items.value[itemIndex].is_purchased = false;
            items.value[itemIndex].purchasedAt = null;
            items.value[itemIndex].updatedAt = new Date().toISOString();
            // Optionally add a visual indicator for offline update
        }
    }
    // No explicit HTTP call here anymore, relies on WS or offline queue
  }

  async function addManualItem(newItem) {
      if (!listId.value) {
        console.error("addManualItem: No active list ID.");
        // Keine Benachrichtigung, da dies ein interner Fehler ist, der nicht vom Benutzer verursacht wurde
        return;
      }
      if (!newItem || typeof newItem.name !== 'string' || !newItem.name.trim()) {
          console.error("addManualItem: Invalid item data provided.", newItem);
          // Keine Benachrichtigung, da dies ein interner Fehler ist, der nicht vom Benutzer verursacht wurde
          return;
      }

      // Prüfen, ob die Liste abgeschlossen ist
      if (isCompleted.value) {
          helper.devConsole('Cannot add manual item to completed list');
          setNotification('Abgeschlossene Listen können nicht bearbeitet werden', 'error');
          return;
      }

      helper.devConsole(`Attempting to add manual item: "${newItem.name}" to list ${listId.value}`);

      // Prepare payload for both WS and offline queue
      const itemPayload = {
          name: newItem.name.trim(),
          // Include quantity and unit if provided and valid, otherwise backend defaults
          quantity: newItem.quantity && !isNaN(parseFloat(newItem.quantity)) ? parseFloat(newItem.quantity) : undefined,
          unit: typeof newItem.unit === 'string' && newItem.unit.trim() ? newItem.unit.trim() : undefined,
          category: typeof newItem.category === 'string' && newItem.category.trim() ? newItem.category.trim() : 'Sonstiges'
      };

      // Remove undefined properties
      Object.keys(itemPayload).forEach(key => itemPayload[key] === undefined && delete itemPayload[key]);

      const action = {
        type: 'ADD_MANUAL_ITEM',
        payload: { listId: listId.value, item: itemPayload },
        timestamp: Date.now(),
        // Generate a temporary client-side ID for optimistic UI update
        tempItemId: `temp_${Date.now()}_${Math.random().toString(36).substring(7)}`,
        id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}` // Unique ID for offline tracking
      };

      // Überprüfen, ob wir wirklich verbunden sind
      const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

      if (isReallyConnected) {
          // ONLINE: Send via WebSocket
          try {
              sendMessage({
                  event: 'add_manual_item',
                  payload: itemPayload
              });
              // Keine Benachrichtigung bei erfolgreicher Aktion
              // Stille Aktion, da der Benutzer sieht, dass der Artikel hinzugefügt wird
          } catch (error) {
              console.error('Error sending add_manual_item message:', error);
              // Fallback zur Offline-Warteschlange bei Fehler
              addToOfflineQueue(action);

              // Optimistische UI-Aktualisierung
              items.value.push({
                  _id: action.tempItemId, // Use temporary ID
                  name: itemPayload.name,
                  quantity: itemPayload.quantity || 1, // Use defaults or provided values
                  unit: itemPayload.unit || 'Stk',
                  category: itemPayload.category || 'Sonstiges',
                  is_purchased: false, // WICHTIG: Verwende das richtige Feld-Format
                  addedManually: true,
                  sourceRecipe: null,
                  createdAt: new Date().toISOString(), // Approximate time
                  updatedAt: new Date().toISOString(),
                  purchasedAt: null,
                  isTemporary: true // Flag for visual indication or special handling
              });

              // Keine Benachrichtigung, stattdessen stille Behandlung des Fehlers
              helper.devConsole('Silent handling of add_manual_item error, added to offline queue');
          }
      } else {
          // OFFLINE oder Verbindungsprobleme
          addToOfflineQueue(action);

          // Speichere auch in der neuen Offline-Aktionen-Struktur
          saveOfflineAction({
            type: 'addManualItem',
            payload: itemPayload,
            listId: listId.value
          });

          // Optimistische UI-Aktualisierung mit temporärer ID
          const newItem = {
              _id: action.tempItemId, // Use temporary ID
              name: itemPayload.name,
              quantity: itemPayload.quantity || 1, // Use defaults or provided values
              unit: itemPayload.unit || 'Stk',
              category: itemPayload.category || 'Sonstiges',
              is_purchased: false, // WICHTIG: Verwende das richtige Feld-Format
              addedManually: true,
              sourceRecipe: null,
              createdAt: new Date().toISOString(), // Approximate time
              updatedAt: new Date().toISOString(),
              purchasedAt: null,
              isTemporary: true // Flag for visual indication or special handling
          };

          items.value.push(newItem);

          // Aktualisiere den lokalen Snapshot
          saveLocalStateSnapshot(items.value, listId.value);

          // Keine Benachrichtigung bei Offline-Status oder Verbindungsproblemen
          // Stille Aktion, da zu viele Benachrichtigungen störend sein können

          // Versuchen, die WebSocket-Verbindung wiederherzustellen
          if (isOnline.value && (!wsInstance.value || wsInstance.value.readyState !== WebSocket.OPEN)) {
              helper.devConsole('Attempting to reconnect WebSocket after add_manual_item failure');
              connectWebSocket();
          }
      }
      // No explicit HTTP call here anymore
  }

  async function addRecipe(recipeId) {
      if (!listId.value) {
          console.error("addRecipe: No active list ID.");
          setNotification('Fehler: Kein aktiver Einkaufszettel gefunden, um Rezept hinzuzufügen.', 'alert');
          return;
      }
      if (!recipeId) {
          console.error("addRecipe: No recipe ID provided.");
          setNotification('Fehler: Keine Rezept-ID angegeben.', 'alert');
          return;
      }

      // Prüfen, ob die Liste abgeschlossen ist
      if (isCompleted.value) {
          helper.devConsole('Cannot add recipe to completed list');
          setNotification('Abgeschlossene Listen können nicht bearbeitet werden', 'error');
          return;
      }

      helper.devConsole(`Attempting to add recipe ${recipeId} to list ${listId.value}`);

      // Erweiterte Diagnose-Protokollierung
      helper.devConsole(`[Add Recipe Check] isConnected: ${isConnected.value}, wsInstance: ${wsInstance.value ? 'exists' : 'null'}, isOnline: ${isOnline.value}`);

      const action = {
          type: 'ADD_RECIPE',
          payload: { listId: listId.value, recipeId },
          timestamp: Date.now(),
          id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}`
      };

      // Überprüfen, ob wir wirklich verbunden sind
      const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

      if (isReallyConnected) {
          // ONLINE: Send via WebSocket
          try {
              sendMessage({
                  event: 'add_recipe',
                  payload: { recipeId }
              });
              // Keine Benachrichtigung bei erfolgreicher Aktion
              // Stille Aktion, da der Benutzer sieht, dass das Rezept hinzugefügt wird
          } catch (error) {
              console.error('Error sending add_recipe message:', error);
              // Fallback zur Offline-Warteschlange bei Fehler
              addToOfflineQueue(action);
              // Keine Benachrichtigung, stattdessen stille Behandlung des Fehlers
              helper.devConsole('Silent handling of add_recipe error, added to offline queue');
          }
      } else {
          // OFFLINE oder Verbindungsprobleme
          addToOfflineQueue(action);

          // Keine Benachrichtigung bei Offline-Status oder Verbindungsproblemen
          // Stille Aktion, da zu viele Benachrichtigungen störend sein können

          // Versuchen, die WebSocket-Verbindung wiederherzustellen
          if (isOnline.value && (!wsInstance.value || wsInstance.value.readyState !== WebSocket.OPEN)) {
              helper.devConsole('Attempting to reconnect WebSocket after add_recipe failure');
              connectWebSocket();
          }
      }
  }

  async function removeRecipe(recipeId) {
      if (!listId.value) {
          console.error("removeRecipe: No active list ID.");
          setNotification('Fehler: Kein aktiver Einkaufszettel gefunden, um Rezept zu entfernen.', 'alert');
          return;
      }
      if (!recipeId) {
          console.error("removeRecipe: No recipe ID provided.");
          setNotification('Fehler: Keine Rezept-ID zum Entfernen angegeben.', 'alert');
          return;
      }

      helper.devConsole(`Attempting to remove recipe ${recipeId} from list ${listId.value}`);

      // Erweiterte Diagnose-Protokollierung
      helper.devConsole(`[Remove Recipe Check] isConnected: ${isConnected.value}, wsInstance: ${wsInstance.value ? 'exists' : 'null'}, isOnline: ${isOnline.value}`);

      const action = {
          type: 'REMOVE_RECIPE',
          payload: { listId: listId.value, recipeId },
          timestamp: Date.now(),
          id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}`
      };

      // Überprüfen, ob wir wirklich verbunden sind
      const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

      if (isReallyConnected) {
          // ONLINE: Send via WebSocket
          try {
              sendMessage({
                  event: 'remove_recipe',
                  payload: { recipeId }
              });
              // Keine Benachrichtigung bei erfolgreicher Aktion
              // Stille Aktion, da der Benutzer sieht, dass das Rezept entfernt wird
          } catch (error) {
              console.error('Error sending remove_recipe message:', error);
              // Fallback zur Offline-Warteschlange bei Fehler
              addToOfflineQueue(action);

              // Optimistische UI-Aktualisierung
              const initialLength = items.value.length;
              items.value = items.value.filter(item => item.sourceRecipe !== recipeId);

              // Keine Benachrichtigung, stattdessen stille Behandlung des Fehlers
              helper.devConsole('Silent handling of remove_recipe error, added to offline queue');
          }
      } else {
          // OFFLINE oder Verbindungsprobleme
          addToOfflineQueue(action);

          // Optimistische UI-Aktualisierung: Entferne Artikel, die mit diesem Rezept verbunden sind
          const initialLength = items.value.length;
          items.value = items.value.filter(item => item.sourceRecipe !== recipeId);

          if (items.value.length < initialLength) {
              helper.devConsole(`Optimistically removed items for recipe ${recipeId}`);
          } else {
              helper.devConsole(`No items found for recipe ${recipeId} to remove optimistically.`);
          }

          // Keine Benachrichtigung bei Offline-Status oder Verbindungsproblemen
          // Stille Aktion, da zu viele Benachrichtigungen störend sein können

          // Versuchen, die WebSocket-Verbindung wiederherzustellen
          if (isOnline.value && (!wsInstance.value || wsInstance.value.readyState !== WebSocket.OPEN)) {
              helper.devConsole('Attempting to reconnect WebSocket after remove_recipe failure');
              connectWebSocket();
          }
      }
  }

  async function deleteManualItem(itemId) {
      if (!listId.value) {
          console.error("deleteManualItem: No active list ID.");
          // Keine Benachrichtigung, da dies ein interner Fehler ist, der nicht vom Benutzer verursacht wurde
          return;
      }
      if (!itemId) {
          console.error("deleteManualItem: No item ID provided.");
          // Keine Benachrichtigung, da dies ein interner Fehler ist, der nicht vom Benutzer verursacht wurde
          return;
      }

      // Find the item first to check if it was manually added
      const itemToDelete = items.value.find(item => item._id === itemId);
      if (!itemToDelete) {
          console.warn(`deleteManualItem: Item ${itemId} not found in list.`);
          // Don't show error to user, might have been removed already
          return;
      }

      // Decide if a confirmation is needed (e.g., if item is not temporary offline item)
      const needsConfirmation = !itemToDelete.isTemporary; // Example logic

      if (needsConfirmation) {
        const confirmed = await confirmationStore.showConfirmation(
          'Artikel löschen?',
          `Möchten Sie "${itemToDelete.name}" wirklich dauerhaft vom Einkaufszettel entfernen?`
        );
        if (!confirmed) {
          helper.devConsole(`Deletion of item ${itemId} cancelled by user.`);
          return; // Stop if user cancels
        }
      }

      helper.devConsole(`Attempting to delete manual item ${itemId} ("${itemToDelete.name}") from list ${listId.value}`);

       const action = {
          type: 'DELETE_MANUAL_ITEM',
          payload: { listId: listId.value, itemId },
          timestamp: Date.now(),
          id: `offline_${Date.now()}_${Math.random().toString(36).substring(7)}`
      };

      // Überprüfen, ob wir wirklich verbunden sind
      const isReallyConnected = isConnected.value && wsInstance.value && wsInstance.value.readyState === WebSocket.OPEN;

      if (isReallyConnected) {
          // ONLINE: Send via WebSocket
          try {
              sendMessage({
                  event: 'delete_manual_item',
                  payload: { itemId }
              });
              // Keine Benachrichtigung bei erfolgreicher Aktion
              // Stille Aktion, da der Benutzer sieht, dass der Artikel gelöscht wird
          } catch (error) {
              console.error('Error sending delete_manual_item message:', error);
              // Fallback zur Offline-Warteschlange bei Fehler
              addToOfflineQueue(action);

              // Optimistische UI-Aktualisierung
              const initialLength = items.value.length;
              items.value = items.value.filter(item => item._id !== itemId);

              // Keine Benachrichtigung, stattdessen stille Behandlung des Fehlers
              helper.devConsole('Silent handling of delete_manual_item error, added to offline queue');
          }
      } else {
          // OFFLINE oder Verbindungsprobleme
          addToOfflineQueue(action);

          // Optimistische UI-Aktualisierung: Entferne den Artikel
          const initialLength = items.value.length;
          items.value = items.value.filter(item => item._id !== itemId);

          if (items.value.length < initialLength) {
              helper.devConsole(`Optimistically removed item ${itemId}`);
          } else {
              console.warn(`Failed to optimistically remove item ${itemId}, not found?`);
          }

          // Keine Benachrichtigung bei Offline-Status oder Verbindungsproblemen
          // Stille Aktion, da zu viele Benachrichtigungen störend sein können

          // Versuchen, die WebSocket-Verbindung wiederherzustellen
          if (isOnline.value && (!wsInstance.value || wsInstance.value.readyState !== WebSocket.OPEN)) {
              helper.devConsole('Attempting to reconnect WebSocket after delete_manual_item failure');
              connectWebSocket();
          }
      }
       // No explicit HTTP call here anymore
  }

  // Test helper function to simulate receiving a last item confirmation event
  function simulateLastItemConfirmation(testItemId) {
    if (!listId.value) {
      helper.devConsole('[TEST] Cannot simulate last item confirmation without active list ID');
      return;
    }

    const itemId = testItemId || (items.value.length > 0 ? items.value[0]._id : 'test_item_id');
    const testData = {
      listId: listId.value,
      itemId: itemId,
      kitchentableId: userStore.user.defaultKitchentable,
      message: '[TEST] Möchten Sie dieses Test-Item als letztes abhaken und den Einkaufszettel abschließen? (Verwendet API: /api/shopping-list-items/' + itemId + '/confirm-and-finish)'
    };

    helper.devConsole('[TEST] Simulating last_item_confirmation_needed event', testData);

    // Direkt den WebSocket-Event simulieren
    confirmationStore.showConfirmation(
      'Liste abschließen?',
      testData.message,
      'JA',
      'NEIN'
    ).then(confirmed => {
      if (confirmed) {
        // Benutzer hat mit JA bestätigt
        finishListWithLastItem(testData.itemId);
      } else {
        // Benutzer hat mit NEIN abgelehnt
        revertItemStatus(testData.itemId);
      }
    });
  }

  // --- Getters ---
  // (Add getters later if needed, e.g., for computed properties)

  return {
    items,
    listId,
    listCreatedAt,
    listName, // Expose list name to components
    isActive, // Expose isActive flag to components
    isCompleted, // Expose isCompleted flag to components
    isLoading,
    error,
    isConnected,
    isOnline, // Expose online status
    fetchActiveList,
    connectWebSocket,
    disconnectWebSocket,
    checkAndRestoreConnection, // Neue Funktion zur Überprüfung und Wiederherstellung der Verbindung
    sendMessage,
    addManualItem,
    updateItemPurchasedStatus,
    addRecipe,
    removeRecipe,
    deleteManualItem,
    resetList,
    finishActiveList,
    revertPurchase,
    syncOfflineActions,
    // Add test function - exposed only in development environment
    ...(import.meta.env.DEV ? { simulateLastItemConfirmation } : {})
  };
});