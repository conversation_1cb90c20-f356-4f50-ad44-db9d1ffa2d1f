const { promisify } = require('util')
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const stytch = require('stytch');
const helper = require('../utils/helper')
const User = require('../models/userModel');
const Kitchentable = require('../models/kitchentableModel');
const Roles = require('../models/rolesModel');
const mongoose = require('mongoose')

////////////////////////// KITCHENTABLE OBJECT //////////////////////////////

// @desc     Standard GET Call by Id
// @route    GET /api/v1/kitchentable/:id
exports.getKitchenTableObjectById = catchAsync(async (req, res, next) => {

    helper.devConsole("/api/v1/kitchentable/:id")
    //console.log(req.params.id)
    
    const result = await Kitchentable.findOne({ '_id': req.params.id }).populate('members.roleId').populate('members.userId')

    //console.log("resulty dultsy")
    //console.log(result)

    res.status(201).json({
        success: true,
        data: result
    })
})

// create role
exports.createRole = catchAsync(async (req, res, next) => {
    const Roles = await Roles.create({
        name: "Owner"
    })
})


// @desc     Standard POST Call to Create one
// @route    POST /api/v1/kitchentable/:id
exports.createKitchenTableObject = catchAsync(async (req, res, next) => {
    helper.devConsole("POST /api/v1/kitchentable/")
    /* body needed:
    tableAddress_street
    tableAddress_plztown
    tableAddress_country
    req.body.data.userId
    */

    //helper.devConsole(req.params)
    helper.devConsole(req.body)

    if(!req.body.data.tableAddress_street ||
        !req.body.data.tableAddress_plztown ||
        !req.body.data.tableAddress_country ||
        !req.body.data.userId
    ){
        helper.devConsole("every data is given")
        next(new AppError('Not every data was given at createKitchenTableObject', 500))
    }

    // Create Document Kitchentable
    const kitchentablelist = await Kitchentable.create({
        tableAddress_street: req.body.data.tableAddress_street,
        tableAddress_plztown: req.body.data.tableAddress_plztown,
        tableAddress_country: req.body.data.tableAddress_country
    })

    // Update Document with Creator and Owner Role
    const kitchentableUpdate = await Kitchentable.updateOne(
        {_id: kitchentablelist._id},
        {members: [
            {
                roleId: "663f65d043c4a70258760812",
                userId: req.body.data.userId
            },
            {
                roleId: "663f824f981b50392aab7714",
                userId: req.body.data.userId
            }
        ]}
    )

    helper.devConsole("kitchentableUpdate")
    helper.devConsole(kitchentableUpdate)

    res.status(201).json({
        success: true,
        data: kitchentablelist
    })

})


// @desc     Standard PATCH Call to Update one
// @route    PATCH /api/v1/kitchentable/:id
    ///////////// OLD OLD OLD
    ///////////// OLD OLD OLD
    ///////////// OLD OLD OLD
    ///////////// OLD OLD OLD
exports.updateKitchenTableObject = catchAsync(async (req, res, next) => {
    helper.devConsole("DEPRACHED PATCH /api/v1/kitchentable/")
    helper.devConsole(req.body)

})

////////////////////////// END KITCHENTABLE OBJECT //////////////////////////////


///////////////////////////////// SEVERAL KITCHENTABLES ///////////////////////////////////////

// @desc     Standard Get all related Kitchentables for a specific user ID from the URL
// @route    GET /api/v1/kitchentable/all/related/:userid
exports.getAllKitchenTablesById = catchAsync(async (req, res, next) => {

        helper.devConsole("GET /api/v1/kitchentable/all/related/:userid - Using original logic with safe result storage")
        helper.devConsole(`Params received: ${JSON.stringify(req.params)}`);

        let userIdFromParam = req.params.userid;
        let userObjectId;

        // Convert param ID to ObjectId for query
        try {
            userObjectId = new mongoose.Types.ObjectId(userIdFromParam);
            helper.devConsole(`Converted userId ${userIdFromParam} to ObjectId: ${userObjectId}`);
        } catch (error) {
            helper.devConsole(`Error converting userIdFromParam to ObjectId: ${userIdFromParam}`);
            // Decide if this is a hard error or if we should proceed differently
            // For now, let's return an error for invalid ID format
            return next(new AppError('Invalid user ID format in URL.', 400));
        }
        
        // --- Database Query (using ObjectId from parameter) ---
        helper.devConsole(`Querying kitchentables where members.userId matches: ${userObjectId}`);
        const result = await Kitchentable.find({ 'members.userId': userObjectId })
                                       .populate('members.roleId')
                                       .populate('members.userId') // Populate user details if needed
        
        helper.devConsole(`Found ${result.length} kitchentables for user ${userIdFromParam}`);
        // helper.devConsole(JSON.stringify(result, null, 2)); // Log results if needed
        
        // --- Store result safely --- 
        // Ensure req.body exists (though it should from previous middleware)
        req.body = req.body || {}; 
        // Store results in a dedicated key, preserving other req.body content (like answerobject from verify)
        req.body.kitchentables = result; 
        
        helper.devConsole(`Stored results in req.body.kitchentables`);

        // We still need the authController.sendanswer at the end of the route definition
        // to send the response, which likely uses req.body.answerobject.
        // Let's copy the kitchentables into answerobject if it makes sense for sendanswer
        // Or adjust sendanswer later. For now, let's put it in both places to be safe, 
        // assuming sendanswer primarily looks at answerobject.
        if (req.body.answerobject) {
             req.body.answerobject.kitchentables = result;
        } else {
             req.body.answerobject = { kitchentables: result };
        }
        helper.devConsole(`Also copied results into req.body.answerobject.kitchentables`);


        next()
})

/////////////////////////// END SEVERAL KITCHENTABLES ///////////////////////////////////////

/////////////////////////////// KITCHENTABLE USERS ///////////////////////////////////////////

// @desc     Standard GET Call by Id
// @route    GET /api/v1/kitchentable/user/:id
exports.getKitchenTableByUserId = catchAsync(async (req, res, next) => {

    helper.devConsole("GET /api/v1/kitchentable/user/")
    //console.log(req.params)

    const kitchentablelist = await Kitchentable.find({
        //owner: req.params.id,
        //$or: { 
            "members":{
                $elemMatch:{"userId": req.params.id }
                },
        //}
        })
    
    //console.log(kitchentablelist)

    res.status(201).json({
        success: true,
        data: kitchentablelist
    })

})

// @desc     Standard PATCH Call to Update one
// @route    PATCH /api/v1/kitchentable/user/:id
exports.updateKitchenTableObject = catchAsync(async (req, res, next) => {
    try{
        helper.devConsole("PATCH /api/v1/kitchentable/:id/members")
        helper.devConsole("req.body")
        helper.devConsole(req.body)
        helper.devConsole("req.params")
        helper.devConsole(req.params)
        helper.devConsole("-------------- ------------------ ------------------")
        helper.devConsole(req.body.payload)
        helper.devConsole("-------------- ------------------ ------------------")

        //req.body.role ist member
        //req.body.user ist die user id um die es geht
        //req.params.id ist die kitchentable id um die es geht

        let respondString = "";
        let Kitchentableres;

        // load kitchentable to check if user is inside already
        const KitchentableCheck = await Kitchentable.find({_id: req.params.id})
        helper.devConsole("KitchentableCheck")
        helper.devConsole(KitchentableCheck)

        // load roles to check which id has specific role
        const roleCheck = await Roles.find({name: 'member'})
        helper.devConsole("roleCheck")
        helper.devConsole(roleCheck)


        const kitchentablestring = KitchentableCheck[0].members.toString()

        if(kitchentablestring.includes(req.body.user)){
            helper.devConsole("********* user schon drin********* ")
            respondString = "Person ist schon an diesem Küchentisch"
            Kitchentableres = []
        } else {
            helper.devConsole("********* user noch nicht drin ********* ")
            Kitchentableres = await Kitchentable.findByIdAndUpdate(
                { _id: req.params.id },
                {
                    $push: {
                        'members': {
                            'roleId': roleCheck[0]._id,
                            'userId': req.body.user
                        }
                    }
                },
                { new: true } // Option, um den aktualisierten Datensatz zurückzugeben
            ).populate('members.roleId') // Hier kannst du die entsprechenden Felder angeben, die du populaten möchtest
            .populate({
                path: 'members.userId',
                select: 'firstName email'
            });

            respondString = "Person wurde hinzugefügt"
            

            helper.devConsole("-------------------- KitchentableCheck ------------------")
            helper.devConsole(Kitchentableres)
            helper.devConsole("-------------------- KitchentableCheck ------------------")
        }


        //
        //console.log(Kitchentableres)
        req.body.answerobject = {}
        req.body.answerobject.text = respondString
        req.body.answerobject.data = Kitchentableres
        next()
        /*
        res.status(201).json({
            success: true,
            text: respondString,
            data: Kitchentableres
        })
            */

    } catch(err){
        helper.devConsole(err)
    }
})


// @desc     Removes a member from a specific kitchentable
// @route    DELETE /api/v1/kitchentable/:id/members/:memberId
// @access   Private (Requires auth, specific authorization check might be needed)
exports.deleteKitchenTableMember = catchAsync(async (req, res, next) => {
    helper.devConsole("--- deleteKitchenTableMember START ---");
    helper.devConsole(`Request Params: kitchentableId=${req.params.id}, memberId=${req.params.memberId}`);
    // helper.devConsole("Request Body:", req.body); // Body is not used anymore

    try{
        let respondString = "";
        let Kitchentableres;
        const kitchentableId = req.params.id;
        const memberIdToRemove = req.params.memberId;

        // --- Authorization Check (Placeholder) --- 
        // TODO: Implement proper authorization check.
        // Does the authenticated user have permission to remove members from this table (e.g., is owner/admin)?
        // const authenticatedUserId = req.body.answerobject?.user?._id;
        // const kitchentable = await Kitchentable.findById(kitchentableId);
        // if (!kitchentable || !kitchentable.members.some(m => m.userId.equals(authenticatedUserId) && ['owner', 'admin'].includes(m.roleId.name))) {
        //     return next(new AppError('Not authorized to remove members from this kitchentable.', 403));
        // }
        // --- End Authorization Check --- 

        const kitchentable = await Kitchentable.findById(kitchentableId);

        if (!kitchentable) {
            respondString = "Küchentisch nicht gefunden.";
            helper.devConsole(`Exit: ${respondString}`);
            req.body.answerobject = { message: respondString }; // Ensure answerobject exists for sendanswer
            helper.devConsole("--- deleteKitchenTableMember END (Kitchentable not found) ---");
            return next();
        }

        helper.devConsole("Members before pull:", kitchentable.members);

        // Check if memberIdToRemove is provided
        if (!memberIdToRemove) {
            respondString = "Fehlende Mitglieds-ID (memberId in URL).";
            helper.devConsole(`Exit: ${respondString}`);
            req.body.answerobject = { message: respondString }; // Ensure answerobject exists
            helper.devConsole("--- deleteKitchenTableMember END (Missing member ID) ---");
            return next(new AppError(respondString, 400)); // Use AppError for client errors
        }

        helper.devConsole(`Attempting to remove memberId: ${memberIdToRemove}`);

        respondString = "Person wurde versucht zu löschen."; // Default message

        // Ensure memberIdToRemove is a valid ObjectId before querying if needed, 
        // though $pull might handle non-matching types gracefully.

        Kitchentableres = await Kitchentable.findByIdAndUpdate(
            kitchentableId, // Use the ID directly
            {
                // Pull the element from the members array where the userId matches memberIdToRemove
                $pull: { members: { userId: memberIdToRemove } } 
            },
            { new: true } // Return the updated document
        ).populate('members.roleId')
         .populate({
                path: 'members.userId',
                select: 'firstName email _id' // Include _id for easier verification
            });

        if (Kitchentableres) {
             helper.devConsole("Members after pull attempt:", Kitchentableres.members);
             // Verify if the member is actually gone
             const memberStillExists = Kitchentableres.members.some(member => 
                 member.userId && member.userId._id && member.userId._id.toString() === memberIdToRemove.toString()
             );
             if (!memberStillExists) {
                 respondString = "Person wurde erfolgreich gelöscht.";
                 helper.devConsole("Result: Member successfully removed.");
             } else {
                 respondString = "Person konnte nicht gelöscht werden (ID nicht im Array gefunden oder $pull fehlgeschlagen).";
                 helper.devConsole("Result: Member removal failed (User ID possibly not found or $pull issue).");
             }
             // Prepare successful response data for sendanswer
             req.body.answerobject = { message: respondString, data: Kitchentableres }; 
        } else {
             // This case (findByIdAndUpdate returning null for an existing ID) is unlikely unless there's a concurrent delete.
             respondString = "Fehler: Küchentisch konnte nach dem Update nicht gefunden werden.";
             helper.devConsole("Result: findByIdAndUpdate returned null or failed during pull.");
             // Pass an error instead of just a message string
             return next(new AppError(respondString, 500)); 
        }

        helper.devConsole(`Final response message in answerobject: ${JSON.stringify(req.body.answerobject)}`);
        helper.devConsole("--- deleteKitchenTableMember END (Success/Handled) ---");
        next();

    } catch(err){
        helper.devConsole("!!! ERROR in deleteKitchenTableMember !!!:", err);
        helper.devConsole("--- deleteKitchenTableMember END (Caught Error) ---");
        // Pass error to the central error handler
        next(err);
    }
});

// @desc     Standard DELETE Call to delete one Kitchentable (or leave if not last member)
// @route    DELETE /api/v1/kitchentable/:id
// @access   Private (Requires auth, user must be a member)
exports.deleteKitchenTableById = catchAsync(async (req, res, next) => {
    const functionName = 'deleteKitchenTableById';
    helper.devConsole(`[${functionName}] START - Kitchentable ID: ${req.params.id}, User ID: ${req.user.id}`);

    const kitchentableId = req.params.id;
    const userId = req.user.id; // Get user ID from auth middleware (req.user assumed)

    // 1. Validate Input
    if (!mongoose.Types.ObjectId.isValid(kitchentableId)) {
        helper.devConsole(`[${functionName}] Invalid Kitchentable ID format: ${kitchentableId}`);
        return next(new AppError('Ungültige Kitchentable ID.', 400));
    }

    // 2. Find the Kitchentable and check membership
    let kitchentable;
    try {
        // Find by ID and check if the user is a member in one query
        kitchentable = await Kitchentable.findOne({ _id: kitchentableId });

        if (!kitchentable) {
            helper.devConsole(`[${functionName}] Kitchentable ${kitchentableId} not found.`);
            // Treat not found the same as deleted - idempotency
            req.body.answerobject = { message: "Küchentisch nicht gefunden oder bereits gelöscht.", status: 200 };
            return next();
            // return next(new AppError('Küchentisch nicht gefunden.', 404)); // Alternative: strict 404
        }

        // Check if the requesting user is actually a member
        const isMember = kitchentable.members.some(member => member.userId.toString() === userId.toString());
        if (!isMember) {
            helper.devConsole(`[${functionName}] User ${userId} is not a member of Kitchentable ${kitchentableId}.`);
            return next(new AppError('Nicht berechtigt, diesen Küchentisch zu verlassen oder zu löschen.', 403));
        }
        helper.devConsole(`[${functionName}] User ${userId} is a member. Member count: ${kitchentable.members.length}`);

    } catch (error) {
        helper.devConsole(`[${functionName}] Error finding Kitchentable ${kitchentableId}: ${error}`);
        return next(new AppError('Fehler beim Abrufen des Küchentisches.', 500));
    }

    // 3. Determine Action: Leave or Delete
    let responseMessage = "";
    let statusCode = 200; // Default to OK

    try {
        if (kitchentable.members.length > 1) {
            // --- LEAVE Case ---
            helper.devConsole(`[${functionName}] More than one member. User ${userId} will leave Kitchentable ${kitchentableId}.`);

            // Remove the user from the members array
            kitchentable.members = kitchentable.members.filter(member => member.userId.toString() !== userId.toString());
            await kitchentable.save();

            responseMessage = "Du hast den Küchentisch verlassen.";
            statusCode = 200;
            helper.devConsole(`[${functionName}] User ${userId} removed from members.`);

        } else if (kitchentable.members.length === 1 && kitchentable.members[0].userId.toString() === userId.toString()) {
            // --- DELETE Case (Last Member) ---
            helper.devConsole(`[${functionName}] Last member ${userId} deleting Kitchentable ${kitchentableId}.`);

            const deleteResult = await Kitchentable.findByIdAndDelete(kitchentableId);

            if (!deleteResult) {
                 // Should not happen if found earlier, but handle defensively
                 helper.devConsole(`[${functionName}] Kitchentable ${kitchentableId} was not found during deletion (concurrent modification?).`);
                 return next(new AppError('Küchentisch konnte nicht gelöscht werden (möglicherweise bereits gelöscht).', 404));
            }

            // TODO: Optional - Delete associated data like ShoppingLists here?
            // e.g., await ShoppingList.deleteMany({ kitchentable_id: kitchentableId });

            responseMessage = "Küchentisch wurde gelöscht.";
            statusCode = 200; // Or 204 No Content if no message needed
            helper.devConsole(`[${functionName}] Kitchentable ${kitchentableId} deleted.`);

        } else {
            // Should not happen if logic is correct, but catch edge cases
            helper.devConsole(`[${functionName}] Unexpected state for Kitchentable ${kitchentableId}. Members: ${kitchentable.members.length}, User: ${userId}.`);
            return next(new AppError('Unerwarteter Fehler beim Verarbeiten der Anfrage.', 500));
        }

        // 4. Update User's defaultKitchentable if necessary
        // Check if the kitchentable being left/deleted was the user's default
        const user = await User.findById(userId);
        if (user && user.defaultKitchentable && user.defaultKitchentable.toString() === kitchentableId.toString()) {
            helper.devConsole(`[${functionName}] Kitchentable ${kitchentableId} was default for User ${userId}. Setting default to null.`);
            user.defaultKitchentable = null;
            try {
                await user.save({ validateBeforeSave: false }); // Avoid potential validation issues if other fields are incomplete
                helper.devConsole(`[${functionName}] User ${userId} defaultKitchentable set to null.`);
            } catch (userSaveError) {
                 helper.devConsole(`[${functionName}] WARNING: Failed to update defaultKitchentable for User ${userId}: ${userSaveError}`);
                 // Log the error but proceed, as the primary action (leave/delete) succeeded.
                 // The user might need to manually update their default later if this fails.
            }
        } else {
             helper.devConsole(`[${functionName}] Kitchentable ${kitchentableId} was not default for User ${userId}, or user not found.`);
        }

        // 5. Prepare and send response
        // Use the standard response mechanism if available (e.g., req.body.answerobject for sendanswer middleware)
        req.body.answerobject = { message: responseMessage, status: statusCode };
        helper.devConsole(`[${functionName}] END - Response: ${JSON.stringify(req.body.answerobject)}`);
        next();

    } catch (error) {
        helper.devConsole(`[${functionName}] Error during leave/delete operation for Kitchentable ${kitchentableId}: ${error}`);
        return next(new AppError('Ein Fehler ist beim Verlassen oder Löschen des Küchentisches aufgetreten.', 500));
    }
});