# Azure Deployment Guide für Ordy API

Diese Anleitung erklärt, wie du Probleme beim Azure-Deployment diagnostizieren und beheben kannst.

## Überblick der Deployment-Konfiguration

Die Ordy API ist als Azure Web App konfiguriert. Die wichtigsten Dateien für das Azure-Deployment sind:

- **web.config**: Konfiguration für den IIS-Webserver in Azure
- **.deployment**: Anweisungen für Azure, wie die App gebaut und gestartet wird
- **start.js**: Startup-Skript, das vor dem Server ausgeführt wird und Log-Informationen sammelt
- **azure-startup.js**: Sammelt detaillierte Informationen zur Umgebung für Diagnose-Zwecke

## Lokales Testen des Azure-Deployments

Um ein Azure-Deployment lokal zu simulieren und zu testen, verwende folgende Skripte:

1. **azure-env-validator.js**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob alle erforderlichen Umgebungsvariablen gesetzt sind
   ```
   node azure-env-validator.js
   ```

2. **azure-local-build.js**: Simuliert den Build-Prozess auf Azure
   ```
   node azure-local-build.js preview   # Für Test-Umgebung
   node azure-local-build.js production # Für Produktionsumgebung
   ```

3. **deploy-azure-local.js**: Führt den kompletten Deployment-Prozess lokal aus
   ```
   node deploy-azure-local.js preview   # Für Test-Umgebung
   node deploy-azure-local.js production # Für Produktionsumgebung
   ```

## Zugriff auf die Logs in Azure

In der Azure Web App kannst du die Logs auf folgende Weise einsehen:

1. Gehe zum **Azure Portal**
2. Wähle deine **Web App** (ordy-tst oder ordy-prd)
3. Im linken Menü unter "Überwachung" auf **Protokollstreams** klicken
4. Wähle **Anwendungsprotokollierung** aus

Du kannst auch per FTP auf die Logs zugreifen. Die Zugangsdaten findest du im Azure Portal unter:
1. **Web App** auswählen
2. **Bereitstellungscenter** > **FTPS-Anmeldeinformationen** 

Die Log-Dateien befinden sich im Verzeichnis `/LogFiles/`.

## Häufige Probleme und Lösungen

### 1. Node.js-Version

Azure Web Apps verwenden standardmäßig eine feste Node.js-Version. Wenn du eine bestimmte Version verwenden möchtest, stelle sicher, dass sie mit der in `package.json` angegebenen Version übereinstimmt:

```json
"engines": {
  "node": "^22"
}
```

### 2. Fehler beim Starten der Anwendung

Wenn die App nicht startet, überprüfe:

- **Umgebungsvariablen**: Sind alle erforderlichen Umgebungsvariablen in den Azure-Einstellungen konfiguriert?
- **Datenbankverbindung**: Kann die App die MongoDB-Datenbank erreichen?
- **Portkonfiguration**: Azure erwartet, dass die App auf dem Port hört, der in der Umgebungsvariable `PORT` angegeben ist.

### 3. Probleme mit dem Webpack-Build

Bei Problemen mit dem Webpack-Build kannst du den Build-Prozess überspringen, indem du das build-Skript in `package.json` anpasst:

```json
"build": "echo \"Build process skipped for Azure deployment\""
```

### 4. Probleme mit dem Dateisystem

Azure Web Apps haben ein schreibgeschütztes Dateisystem. Stelle sicher, dass deine App keine Dateien schreibt, es sei denn in die folgenden Verzeichnisse:

- `/home/<USER>
- `/home/<USER>/wwwroot/logs`
- `/tmp`

### 5. Verbindungstimeouts

Wenn Verbindungen zur Datenbank oder zu externen Diensten unterbrochen werden, überprüfe:

- **Firewall-Einstellungen**: Erlaubt Azure, auf die externe Ressource zuzugreifen?
- **Netzwerkkonfiguration**: Sind VNet-Integration oder Hybrid-Verbindungen erforderlich?

## Deployment über GitHub Actions

Die Ordy API verwendet GitHub Actions für automatisierte Deployments:

- `.github/workflows/main_ordy-tst.yml`: Deployment zur Test-Umgebung
- `.github/workflows/main_ordy-prd.yml`: Deployment zur Produktionsumgebung

Diese Workflows werden ausgelöst, wenn Code in die entsprechenden Branches gepusht wird.

## Konfiguration der Azure Web App

Wichtige Einstellungen in der Azure Web App-Konfiguration:

1. **Anwendungseinstellungen**: Umgebungsvariablen für die App
2. **Allgemeine Einstellungen**: Stack-Version (Node) und Plattform (Windows/Linux)
3. **Bereitstellungssteckplätze**: Für A/B-Tests oder Staging-Deployments

## Support und weitere Hilfe

Bei anhaltenden Problemen wende dich an:
- Dominic Kunz (Backend-Entwickler)
- Azure Support-Team
- GitHub Actions-Dokumentation: https://docs.github.com/en/actions 