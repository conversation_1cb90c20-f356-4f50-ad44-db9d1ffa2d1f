# Ordy Menu System - Anforderungen & Spezifikation

## 📋 Übersicht
Dieses Dokument definiert die Anforderungen an das Menü- und Zutaten-System von Ordy. Es dient als zentrale Referenz für alle Entwicklungsarbeiten.

## 🎯 Kernfunktionalitäten

### 1. Rezept-Management
- **Dynamische Personenanzahl**: Rezepte können für verschiedene Personenanzahlen erstellt werden
- **MenuChild-System**: Jede Personenanzahl-Variante ist ein separates MenuChild
- **Standard-Variante**: Eine Variante ist immer als Standard markiert (isStandard: true)
- **Automatische Skalierung**: Zutatenmengen werden automatisch an Personenanzahl angepasst
- **PersonCount Buttons**: Funktionieren in ALLEN Menü-Bereichen:
  - `/kochbuch/menu/xx` (Anzeige-Modus)
  - `/kochbuch/menu/edit/xxx` (Edit-Modus)

### 2. Zutaten-System
- **Stable IDs**: Jede Zutat hat eine permanente ID (stableId) innerhalb eines Rezepts
- **Globale Operationen**: Zutaten können zu allen MenuChilds gleichzeitig hinzugefügt/entfernt werden
- **Automatische Kategorisierung**: Neue Zutaten werden automatisch kategorisiert
- **Eindeutige IDs**: Jede stableId existiert nur einmal pro Rezept
- **Konsistente Datenstruktur**: ObjectIds für unit und name, Numbers für amount

### 3. Text-Integration
- **Platzhalter-System**: Zutaten können in Zubereitungsschritten referenziert werden (${ID:1})
- **Dynamische Mengen**: Platzhalter zeigen automatisch skalierte Mengen an
- **Konsistenz**: Gelöschte Zutaten werden aus allen Texten entfernt

## 🔧 Technische Anforderungen

### A. Datenintegrität
1. **StableID-Konsistenz**: IDs werden nie wiederverwendet, auch nach Löschung nicht
2. **Globale Synchronisation**: Änderungen an Zutaten betreffen alle MenuChilds
3. **Referenz-Integrität**: Gelöschte Zutaten werden aus allen Texten entfernt
4. **Eindeutigkeit**: Keine doppelten stableIDs innerhalb eines Rezepts

### B. Performance
1. **Auto-Save Optimierung**: Speichern nur bei onBlur, nicht bei jedem Tastendruck
2. **Batch-Operationen**: Globale Änderungen in einer Transaktion
3. **Effiziente Skalierung**: Mengen-Berechnung ohne Rundungsfehler

### C. Benutzerfreundlichkeit
1. **Intuitive UI**: Klare Unterscheidung zwischen lokalen und globalen Operationen
2. **Fehlerbehandlung**: Graceful Fallbacks bei API-Fehlern
3. **Konsistente UX**: Einheitliche Bedienung über alle Funktionen

## 📊 Neue Anforderungen (Chat-basiert)

### 1. Generator-Kompatibilität
**Anforderung**: Aus /wochenplan/upload (4 Generatoren) müssen immer korrekte Menüs/Rezepte entstehen
- **Details**: AI-generierte Rezepte müssen vollständige Datenstrukturen haben
- **Kritisch**: Alle Pflichtfelder müssen gefüllt sein
- **Validation**: Automatische Prüfung der generierten Daten

### 2. Legacy-Kompatibilität
**Anforderung**: Alte Menüs/Rezepte müssen korrekt angezeigt und aktualisiert werden
- **Migration**: Automatische Aktualisierung alter Datenstrukturen
- **Backward Compatibility**: Unterstützung für alte Formate
- **Graduelle Migration**: Schrittweise Aktualisierung ohne Datenverlust

### 3. Einkaufszettel-Integration
**Anforderung**: Zutaten müssen zum /zettel hinzugefügt und entfernt werden können
- **Bidirektionale Sync**: Änderungen in beide Richtungen
- **Kategorisierung**: Automatische Sortierung nach Kategorien
- **Mengen-Aggregation**: Gleiche Zutaten werden zusammengefasst

### 4. Automatische Kategorisierung
**Anforderung**: Neue Zutaten müssen automatisch kategorisiert werden
- **AI-Integration**: Intelligente Erkennung von Zutat und Kategorie
- **Eindeutigkeit**: Eine stableID nur einmal pro Rezept
- **Validation**: Prüfung auf Duplikate

### 5. Globale Löschung
**Anforderung**: Zutaten müssen aus allen MenuChilds entfernt werden
- **Cascade Delete**: Automatische Entfernung aus allen Varianten
- **Text-Cleanup**: Entfernung aus Zubereitungsschritten
- **Konsistenz**: Keine verwaisten Referenzen

## 🏗️ Aktuelle Architektur

### Frontend (Vue.js)
```
/views/SecondLevelView/MenuDetailsEdit.vue
├── Zutat-Management
├── Auto-Save (onBlur)
├── Globale/Lokale Operationen
└── Text-Platzhalter

/store/menuStore.js
├── API-Calls
├── State Management
└── Error Handling
```

### Backend (Node.js/Express)
```
/controllers/menuchildController.js
├── CRUD-Operationen
├── StableID-Management
├── Globale Operationen
└── Skalierung

/models/menuchildModel.js
├── Schema-Definition
├── Validation
└── Middleware
```

### Middleware
```
/controllers/unitController.js
├── groceryAndUnitChecker
├── Automatische Erstellung
└── Datenvalidierung
```

## 📈 Datenmodell

### MenuChild Schema
```javascript
{
  parentId: ObjectId,           // Referenz zum Hauptrezept
  seatCount: Number,           // Personenanzahl
  ingredients: [{
    amount: Number,            // Menge (skaliert)
    unit: ObjectId,           // Einheit-Referenz
    name: ObjectId,           // Zutat-Referenz
    stableId: Number          // Permanente ID
  }],
  maxUsedStableId: Number,    // Höchste verwendete ID
  isStandard: Boolean,        // Standard-Variante
  preperation: [Object],      // Zubereitungsschritte
  nutritions: [Object]        // Nährwerte
}
```

### StableID-System
- **Eindeutigkeit**: Pro Rezept nur einmal
- **Permanenz**: Nie wiederverwendet
- **Konsistenz**: Über alle MenuChilds synchron
- **Referenzierung**: In Texten als ${ID:X}

## 🎨 UI/UX Prinzipien
- **Minimale Notifications**: Nur bei echten Fehlern
- **Auto-Save onBlur**: Performance-optimiert
- **Globale Bestätigung**: Klare Unterscheidung lokaler/globaler Operationen
- **Konsistente Icons**: Einheitliche Bedienung
- **Responsive Design**: Mobile-optimiert

---
*Letzte Aktualisierung: Januar 2025*
*Version: 1.0*
