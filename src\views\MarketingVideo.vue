<template>
  <div ref="wrapperRef" class="h-screen w-screen flex justify-center items-center bg-black overflow-hidden">
    <!-- Hauptcontainer für Lade/Fehler/Inhalt -->
    <div v-if="isLoading || marketingStore.isLoading" class="text-center text-gray-400">
      <p>Lade Marketing-Animation...</p>
      <!-- Optional: Spinner -->
    </div>
    <div v-else-if="error || marketingStore.error" class="text-center text-red-500">
      <p><PERSON><PERSON> beim <PERSON>: {{ error || marketingStore.error }}</p>
    </div>
    <!-- Eigentlicher Animations-Container -->
    <div
      v-if="panels.length > 0"
      :class="randomBgClass"
      class="relative overflow-hidden rounded-lg shadow-xl transition-colors duration-500"
      style="width: 360px; height: 640px;"
      :style="{ transform: `scale(${scaleFactor})`, transformOrigin: 'center center' }"
      id="marketing-video-container"
    >
      <!-- Persistent Text Overlay for Panels 4, 5, 6 with Fade Transition -->
      <transition name="text-fade">
        <div 
          v-if="isTextOneVisible"
          ref="textOneContainer"
          :class="['absolute bottom-0 left-0 right-0 p-4 text-center pointer-events-none z-20', { 'manual-fade-out': isTextOneFadingOut }]"
        >
          <p class="text-white text-lg sm:text-xl font-semibold leading-tight shadow-md bg-black bg-opacity-50 rounded px-2 py-1 inline">
            {{ marketingStore.content?.textOne || 'Panel 1 Text fehlt' }}
          </p>
        </div>
      </transition> 

      <transition name="fade">
        <!-- Container for Stacked Images (Panels 4, 5, 6, 7) -->
        <div v-if="currentPanel && currentPanel.id >= 4 && currentPanel.id <= 7" class="absolute inset-0">
          <template v-for="(panel) in panels.filter(p => p.id >= 4 && p.id <= 7)" :key="panel.id">
            <transition name="image-stack">
              <div 
                v-if="panel.id <= currentPanel.id" 
                :style="{ 'z-index': panel.id }" 
                :class="['absolute inset-0 p-3', { 'animate-pop-image': panel.id === 4 }]"
              >
                <img
                  :src="panel.src"
                  :alt="`Panel ${panel.id}`"
                  class="w-full h-full object-cover rounded-lg shadow-lg"
                />
              </div>
            </transition>
          </template>
        </div>

        <!-- Fall 2: Kopiertes MenuCard HTML anzeigen (Panel 7) -->
        <div v-else-if="currentPanel && currentPanel.type === 'component'" :key="currentPanel.id + '-component'" class="absolute inset-0 p-3 z-10">
          <div
            :class="{'component-active': isComponentPanelActive}" 
            class="menu-card-copy rounded-2xl antialiased bg-cover w-full flex flex-col justify-between"
            :style="{ 
              'background-image': 'url(' + currentPanel.menuCardData.imagelink + ')',
              'height': '520px' 
            }"
          >
            <!-- head -->
            <div class="w-full mt-2 p-6 flex flex-row text-right text-white">
              <div class="w-4/5 pr-4">
                <h3 class="title-element mt-1 text-base relative">{{ displayedTitle }}</h3>
              </div>
              <div class="w-1/5">
                <button class="w-16 h-16 bg-white rounded-lg cursor-not-allowed flex items-center justify-center">
                  <img :src="reloadIcon" class="reload-icon-img mx-auto" /> 
                </button>
              </div>
            </div>
            <!-- foot -->
            <div class="w-full p-6 flex flex-nowrap gap-4">
              <div class="footer-button-1 w-5/12 opacity-0">
                <div class="h-9 w-full bg-white rounded-lg flex flex-row justify-center items-center px-1 shadow-sm border border-gray-200">
                  <span class="text-xs">{{ currentPanel.menuCardData.numberOfPersons }}</span>
                  <img class="h-6 mx-1" :src="peopleIcon" alt="people" />
                  <span class="break-keep text-xs">{{ currentPanel.menuCardData.cookingTime }} min</span>
                </div>
              </div>
              <div class="footer-button-2 w-5/12 opacity-0">
                <button class="h-9 bg-white rounded-lg w-full text-xs cursor-not-allowed shadow-sm border border-gray-200 text-gray-600">
                  <span class="button-typing-text">zum Rezept</span>
                </button>
              </div>
              <div class="footer-button-3 w-2/12 mx-auto h-auto opacity-0">
                 <button class="h-9 w-9 float-right rounded-3xl transition-colors duration-150 ease-in-out focus:outline-none bg-white shadow-sm border border-gray-200">
                    <img :src="addIcon" class="mx-auto transition-all duration-150 ease-in-out h-5 w-5" alt="add"/>
                 </button>
              </div>
            </div>
            <!-- Text-Overlay für den Komponenten-Frame -->
            <div class="final-text-overlay absolute bottom-0 left-0 right-0 p-4 text-center pointer-events-none opacity-0">
              <p class="text-white text-lg sm:text-xl font-semibold leading-tight shadow-md bg-black bg-opacity-50 rounded px-2 py-1 inline">
                {{ currentPanel.text }}
              </p>
            </div>
          </div>
        </div>

        <!-- Fall 3: Logo anzeigen (Panel 8) -->
        <div v-else-if="currentPanel && currentPanel.type === 'logo'" :key="currentPanel.id + '-logo'" class="absolute inset-0 p-3 flex items-center justify-center z-10">
           <img :src="currentPanel.src" alt="Ordy Logo" class="h-28 w-auto filter-white"/>
        </div>
      </transition>
    </div>
    <!-- Lade-/Fehlerzustand (zentriert im Wrapper) -->
    <div v-else-if="isLoading || marketingStore.isLoading" class="text-center text-gray-400">
      <p>Lade Marketing-Animation...</p>
    </div>
    <div v-else-if="error || marketingStore.error" class="text-center text-red-500">
      <p>Fehler beim Laden: {{ error || marketingStore.error }}</p>
    </div>
    <div v-else class="text-center text-gray-400">
      <p>Keine Animationsdaten gefunden oder Marketing-Inhalt ist leer.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { useMarketingStore } from '@/store/marketingStore'; // Use the store

// Import static assets directly
import reloadIcon from '../assets/icons/reload.png';
import peopleIcon from '../assets/icons/people.png';
import addIcon from '../assets/icons/add.png';
import ordyLogoSrc from '../assets/ordy_logo.svg';

const marketingStore = useMarketingStore();

// Ref für den äußeren Wrapper
const wrapperRef = ref(null);

// Component-local state for animation control and content processing
const isLoading = ref(true); // Local loading state, initially true
const error = ref(null);     // Local error state
const panels = ref([]);      // Holds the structured panels for animation
const currentPanelIndex = ref(0);
const currentPanel = ref(null);
const textOneContainer = ref(null); // Ref for the text container
const isInitialLoad = ref(true);
const randomBgClass = ref('bg-gray-800');
const isComponentPanelActive = ref(false);
const displayedTitle = ref('');
const targetTitle = ref('');
const isTextOneVisible = ref(false); // State for text visibility
const isTextOneFadingOut = ref(false); // State for fade-out class

// *** NEU: Steuerbare Verzögerung für Text 1 Ausblenden (in ms ab Start) ***
const textOneFadeOutDelayMs = 3900; // Beispiel: Nach 3.5 Sekunden ausblenden

let timer = null; // Haupt-Panel-Timer
let typingInterval = null;
let typingCursorInterval = null;
/* let textOneLeaveTimer = null; */ // Alter Timer wird ersetzt
let textOneMasterFadeOutTimer = null; // NEUER Master-Timer

const bgColors = [
  'bg-slate-800', 'bg-gray-800', 'bg-zinc-800', 'bg-neutral-800', 'bg-stone-800',
  'bg-red-900', 'bg-orange-900', 'bg-amber-900', 'bg-yellow-900', 'bg-lime-900',
  'bg-green-900', 'bg-emerald-900', 'bg-teal-900', 'bg-cyan-900', 'bg-sky-900',
  'bg-blue-900', 'bg-indigo-900', 'bg-violet-900', 'bg-purple-900', 'bg-fuchsia-900',
  'bg-pink-900', 'bg-rose-900'
];

// Basis Design-Dimensionen
const designWidth = 360;
const designHeight = 640;

// Ref für den Skalierungsfaktor
const scaleFactor = ref(1);

// Berechnungsfunktion für den Skalierungsfaktor
const updateScaleFactor = () => {
  if (wrapperRef.value) {
    const availableWidth = wrapperRef.value.clientWidth;
    const availableHeight = wrapperRef.value.clientHeight;

    const scaleX = availableWidth / designWidth;
    const scaleY = availableHeight / designHeight;

    // Nutze den kleineren Faktor, um sicherzustellen, dass alles reinpasst (Letterboxing)
    // Wenn du willst, dass es überlappt (Pillarboxing/Cropping), nutze Math.max
    scaleFactor.value = Math.min(scaleX, scaleY);
    console.log(`[ScaleFactor] Wrapper: ${availableWidth}x${availableHeight}, Scale: ${scaleFactor.value.toFixed(3)}`);
  } else {
    console.log('[ScaleFactor] Wrapper ref not available yet.');
    scaleFactor.value = 1; // Fallback
  }
};

// === NEU: Preload Images Helper ===
function preloadImages(urls, timeout = 5000) {
  return new Promise((resolve) => {
    let loaded = 0;
    let hasTimedOut = false;
    const total = urls.length;
    if (total === 0) return resolve();

    const timer = setTimeout(() => {
      hasTimedOut = true;
      resolve(); // Nach Timeout trotzdem weitermachen
    }, timeout);

    urls.forEach((url) => {
      const img = new window.Image();
      img.onload = img.onerror = () => {
        loaded++;
        if (loaded === total && !hasTimedOut) {
          clearTimeout(timer);
          resolve();
        }
      };
      img.src = url;
    });
  });
}

// Function to build panels array from store data
const buildPanelsFromStore = () => {
  const content = marketingStore.content;
  console.log('[BuildPanels] Received content from store:', JSON.stringify(content)); // Log the content
  if (!content) {
    console.log('Keine Marketing-Daten im Store zum Erstellen der Panels.');
    panels.value = [];
    currentPanel.value = null;
    isLoading.value = false; 
    return;
  }

  const newPanels = [];
  const panelDuration = 1000; 
  const shortPanelDuration = panelDuration; // Define duration for image panels
  
  // --- Add Panels using specific field names (IF API provides them) --- 
  
  // Panel 1 (ID 4) - using imgOneUrl
  if (content.imgOneUrl) { 
    newPanels.push({ id: 4, type: 'image', src: content.imgOneUrl, duration: shortPanelDuration }); 
  } else {
    console.warn('[BuildPanels] content.imgOneUrl fehlt, Panel 4 wird übersprungen.');
  }

  // Panel 2 (ID 5) - using imgTwoUrl
  if (content.imgTwoUrl) { 
     newPanels.push({ id: 5, type: 'image', src: content.imgTwoUrl, duration: shortPanelDuration }); 
  } else {
    console.warn('[BuildPanels] content.imgTwoUrl fehlt, Panel 5 wird übersprungen.');
  }

  // Panel 3 (ID 6) - using imgThreeUrl
  if (content.imgThreeUrl) { 
     newPanels.push({ id: 6, type: 'image', src: content.imgThreeUrl, duration: shortPanelDuration }); 
  } else {
    console.warn('[BuildPanels] content.imgThreeUrl fehlt, Panel 6 wird übersprungen.');
  }

  // Panel 4 (ID 7) - using imgFourUrl
  if (content.imgFourUrl) { 
     console.log(`[BuildPanels] Restoring Panel 7 duration to ${shortPanelDuration}ms.`);
     newPanels.push({ id: 7, type: 'image', src: content.imgFourUrl, duration: shortPanelDuration }); // Use standard duration
  } else {
    console.warn('[BuildPanels] content.imgFourUrl fehlt, Panel 7 wird übersprungen.');
  }
  
  // Panel 5 (ID 8 - Component)
  newPanels.push({
    id: 8, type: 'component', // Updated ID
    menuCardData: {
      name: content.recipeName || 'Rezept Vorschlag',
      imagelink: content.recipeImageUrl || content.imageUrls?.[3] || '', // Use recipeImageUrl or fallback
      numberOfPersons: content.recipeNumberOfPersons || 4, 
      cookingTime: content.recipeCookingTime || 31
    },
    text: content.textTwo || 'Panel 2 Text fehlt', // Corrected field name
    duration: 6000 
  });
  // Panel 6 (ID 9 - Logo)
  newPanels.push({ id: 9, type: 'logo', src: ordyLogoSrc, duration: 1500 }); // Updated ID
  // ----------------------------------------------

  panels.value = newPanels;
  clearTimeout(textOneMasterFadeOutTimer); // Vorherigen Timer löschen, falls vorhanden

  // === ERWEITERT: Preload Images vor dem 5s Timer ===
  // Bild-URLs sammeln (inkl. Panel-Bilder, Logo und Komponenten-Bild)
  const imageUrlsToPreload = panels.value
    .filter(p => p.src && (p.type === 'image' || p.type === 'logo'))
    .map(p => p.src);

  const componentPanel = panels.value.find(p => p.type === 'component' && p.menuCardData?.imagelink);
  if (componentPanel && componentPanel.menuCardData.imagelink) {
    imageUrlsToPreload.push(componentPanel.menuCardData.imagelink);
  }

  // Eindeutige URLs sicherstellen und leere Strings filtern
  const uniqueImageUrls = [...new Set(imageUrlsToPreload.filter(url => url && typeof url === 'string' && url.trim() !== ''))];
  
  if (uniqueImageUrls.length > 0) {
    console.log('[BuildPanels] Preloading images:', uniqueImageUrls);
    preloadImages(uniqueImageUrls, 5000); // Startet Preload (max 5s Versuch), wartet nicht zwingend auf Abschluss
  } else {
    console.log('[BuildPanels] No unique image URLs to preload.');
  }

  // Start animation only if panels were created AND AFTER a delay
  if (panels.value.length > 0) {
    console.log(`[BuildPanels corrected] Panels built successfully. Starting 5s preload buffer.`);
    if (!isLoading.value) isLoading.value = true;
    clearTimeout(timer);
    // Start the 5-second preload buffer (Animation startet nach 5s, egal ob Preload fertig ist)
    setTimeout(() => {
      console.log(`[Preload Buffer] 5s elapsed. Starting animation.`);
      currentPanelIndex.value = 0;
      const initialPanel = panels.value[0];
      currentPanel.value = initialPanel;
      console.log(`[Animation Start] Initial panel set: ID=${initialPanel.id}, Type=${initialPanel.type}, Duration=${initialPanel.duration}`);
      isLoading.value = false;
      isTextOneVisible.value = initialPanel.id >= 4 && initialPanel.id <= 7;
      isTextOneFadingOut.value = false;
      nextTick(() => { isInitialLoad.value = false; });
      timer = setTimeout(showNextPanel, initialPanel.duration);
      if (textOneFadeOutDelayMs > 0) {
        console.log(`[Animation Start] Scheduling textOne fade-out timer for ${textOneFadeOutDelayMs}ms.`);
        textOneMasterFadeOutTimer = setTimeout(() => {
          console.log(`[Master Timer Fired] Triggering manual fade-out for textOne.`);
          isTextOneFadingOut.value = true;
        }, textOneFadeOutDelayMs);
      } else {
        console.log(`[Animation Start] textOneFadeOutDelayMs is 0 or less, not scheduling fade-out timer.`);
      }
    }, 5000); // 5-second delay
  } else {
    console.error('Konnte keine Panels aus den Store-Daten erstellen (nach Korrektur).');
    error.value = 'Animationsdaten konnten nicht verarbeitet werden.';
    isLoading.value = false;
  }
};

// Watch for changes in the store's content or error state
watch(() => marketingStore.content, (newContent, oldContent) => {
  if (newContent && !marketingStore.isLoading && !marketingStore.error) {
    buildPanelsFromStore();
  }
}, { immediate: false }); // Don't run immediately, wait for fetch

watch(() => marketingStore.error, (newError) => {
  if (newError) {
    error.value = newError; // Update local error state
    isLoading.value = false;
    panels.value = [];
    currentPanel.value = null;
  }
});

watch(() => marketingStore.isLoading, (newIsLoading) => {
  // Keep local isLoading true while store is loading
  if (newIsLoading) {
    isLoading.value = true; 
  }
  // Do not set isLoading false here, wait for buildPanelsFromStore
});

// Animation logic for the component panel (typing effect etc.)
watch(currentPanel, (newPanel, oldPanel) => {
  isComponentPanelActive.value = newPanel?.id === 8 && newPanel?.type === 'component'; // Check for updated ID (8)
  clearInterval(typingInterval);
  clearInterval(typingCursorInterval);
  displayedTitle.value = '';
  document.documentElement.style.removeProperty('--typing-cursor-opacity');

  // Basic visibility based on current panel
  isTextOneVisible.value = newPanel?.id >= 4 && newPanel?.id <= 7;

  if (isComponentPanelActive.value && newPanel.menuCardData?.name) {
    targetTitle.value = newPanel.menuCardData.name;
    let charIndex = 0;
    typingInterval = setInterval(() => {
      if (charIndex < targetTitle.value.length) {
        displayedTitle.value += targetTitle.value.charAt(charIndex);
        charIndex++;
      } else {
        clearInterval(typingInterval);
        // Start cursor blinking after typing finishes
        let cursorVisible = true;
        typingCursorInterval = setInterval(() => {
          cursorVisible = !cursorVisible;
          document.documentElement.style.setProperty('--typing-cursor-opacity', cursorVisible ? '1' : '0');
        }, 500);
      }
    }, 50); // Typing speed
  }
});

// Watcher for isTextOneVisible (Reset fade state)
watch(isTextOneVisible, async (newValue) => {
  await nextTick();
  if (newValue) { 
    // Reset fade-out state when text becomes visible
    isTextOneFadingOut.value = false; 
    if (textOneContainer.value) {
      textOneContainer.value.classList.add('animate-firework');
      textOneContainer.value.classList.remove('force-hidden'); 
    }
  } else if (!newValue && textOneContainer.value) {
    // Remove animation class when text should be hidden
    textOneContainer.value.classList.remove('animate-firework');
    // isTextOneFadingOut is handled separately by its timer or end of sequence
  }
});

// Function to advance to the next panel (Re-add Timer logic, fix clearing)
const showNextPanel = () => {
  clearTimeout(timer); // Clear main panel timer
  
  const nextIndex = currentPanelIndex.value + 1;
  console.log(`[ShowNextPanel] Attempting to show panel at index: ${nextIndex}`);

  if (nextIndex < panels.value.length) {
    currentPanelIndex.value = nextIndex;
    const panel = panels.value[currentPanelIndex.value];
    console.log(`[ShowNextPanel] Setting currentPanel to: ID=${panel.id}, Type=${panel.type}, Duration=${panel.duration}`);
    if ((panel.type === 'image' || panel.type === 'logo') && !panel.src) {
      console.error(`Panel ${panel.id} (${panel.type}) hat keine gültige src.`);
      showNextPanel();
      return;
    }
    if (panel.type === 'component' && (!panel.menuCardData || !panel.menuCardData.imagelink)) {
      console.error(`Panel ${panel.id} (Component) hat keine gültigen menuCardData.`);
      showNextPanel();
      return;
    }
    currentPanel.value = panel;

    timer = setTimeout(showNextPanel, currentPanel.value.duration);
  } else {
    console.log("[ShowNextPanel] Animations-Sequenz beendet.");
    isTextOneVisible.value = false; 
    isTextOneFadingOut.value = false; 
    clearTimeout(textOneMasterFadeOutTimer); // NEUEN Timer löschen
  }
};

onMounted(() => {
  console.log('MarketingVideo component mounted.');
  // ... (random background logic removed as it's now on the inner div) ...
  
  // Initial scale calculation
  nextTick(() => { // Ensure wrapperRef is available
    updateScaleFactor();
  });

  // Add resize listener
  window.addEventListener('resize', updateScaleFactor);

  // Fetch data logic ...
  if (!marketingStore.content && !marketingStore.isLoading) {
      marketingStore.fetchLatestContent();
  } else if (marketingStore.content) {
      console.log('[Mounted] Content already in store. Building panels...');
      buildPanelsFromStore();
  } else {
    console.log('[Mounted] Store is loading. Waiting for watcher...');
    isLoading.value = true; 
  }
});

onUnmounted(() => {
  clearTimeout(timer);
  clearInterval(typingInterval);
  clearInterval(typingCursorInterval);
  clearTimeout(textOneMasterFadeOutTimer);
  window.removeEventListener('resize', updateScaleFactor); // Remove resize listener
  document.documentElement.style.removeProperty('--typing-cursor-opacity');
});
</script>

<style scoped>
/* Fade-Transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Fade-Transition for the persistent text - ONLY ENTER opacity */
.text-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}
/* REMOVED .text-fade-leave-active */
.text-fade-enter-from{ 
  opacity: 0;
}
/* REMOVED .text-fade-leave-to */

/* Class to manually trigger the animation */
.animate-firework {
  animation: fireworkText 0.6s ease-out forwards;
  opacity: 1 !important; 
}

/* ADD BACK Class to manually trigger fade-out */
.manual-fade-out {
    opacity: 0 !important; /* Force opacity 0 */
    transition: opacity 0.3s ease-in; /* Apply fade-out transition */
}

/* ADD BACK Optional: Class to hide instantly if needed */
.force-hidden {
    opacity: 0 !important;
    transition: none !important;
}

/* Filter, um das schwarze SVG weiß zu machen */
.filter-white {
  filter: brightness(0) invert(1);
}

/* Text-Shadow für bessere Lesbarkeit */
p {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

/* --- Animationen aus dem Beispiel --- */
@keyframes pop-background {
  0% { transform: scale(0.95); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes pop-text {
  0% { transform: scale(0.8) translateY(5px); opacity: 0; }
  60% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

@keyframes jumpAnimation {
  0% { transform: translateY(10px) scale(0.9); opacity: 0; }
  70% { transform: translateY(-5px) scale(1.05); opacity: 1; }
  100% { transform: translateY(0) scale(1); opacity: 1; }
}
@keyframes spinFast {
  from { transform: rotate(0deg); }
  to { transform: rotate(720deg); }
}
@keyframes fireworkText {
  0% { opacity: 0; transform: scale(0.2) translateY(30px); }
  60% { opacity: 1; transform: scale(1.25) translateY(-10px); }
  80% { transform: scale(0.95) translateY(2px); }
  100% { opacity: 1; transform: scale(1) translateY(0); }
}
@keyframes revealButtonText {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes slide-in-bottom { /* For first image */
  0% {
    transform: translateY(50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce-in { /* For first text */
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

/* Tipp-Cursor */
.title-element::after {
  content: '|';
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  opacity: var(--typing-cursor-opacity, 0);
  /* Blinking handled by JS interval setting opacity */
}

/* Komponenten-Animationen anwenden */
/* Apply pop animation to the component background when it becomes active */
.component-active .menu-card-copy {
  animation: pop-background 0.4s ease-out forwards;
}

.component-active .footer-button-1 { animation: jumpAnimation 0.6s ease-out forwards; animation-delay: 0.5s; opacity: 0; }
.component-active .footer-button-2 { animation: jumpAnimation 0.6s ease-out forwards; animation-delay: 0.8s; opacity: 0; }
.component-active .footer-button-3 { animation: jumpAnimation 0.6s ease-out forwards; animation-delay: 1.1s; opacity: 0; }
.component-active .reload-icon-img { animation: spinFast 1.4s linear forwards; animation-delay: 0.3s; }
.component-active .final-text-overlay { 
  animation: fireworkText 0.6s ease-out forwards; 
  animation-delay: 1s; /* Added delay */
}
.persistent-text-overlay { animation: fireworkText 0.6s ease-out forwards; animation-delay: 0.2s; } /* This rule seems unused now, maybe remove later */
.button-typing-text { display: inline-block; overflow: hidden; white-space: nowrap; width: 0; vertical-align: top; }
.component-active .footer-button-2 .button-typing-text { animation: revealButtonText 0.1s steps(10, end) forwards; }

/* Zusätzliche Basis-Styles für kopierte Menükarte */
.menu-card-copy {
  /* Stellen Sie sicher, dass diese Klasse angewendet wird, falls Basis-Styles benötigt werden */
}

/* Transition for stacking images - Let animations handle opacity/transform */
.image-stack-enter-active {
  /* Only define transition timing, not properties */
  transition: all 0.5s ease-out; /* Match slide-in duration */
  /* transition: opacity 0.4s ease-out; */ /* Removed opacity transition */
  /* transition-delay: 0.05s; */ /* Remove delay if animation handles entry */
}
.image-stack-leave-active { 
  /* We don't want images to leave, but define for completeness if needed later */
  transition: opacity 0.1s ease-in;
}
.image-stack-enter-from,
.image-stack-leave-to { /* leave-to might not be used */
  opacity: 0; /* Still set initial/final state */
}

/* Apply slide-in animation to the first image (Panel 4) when it enters */
/* This rule now defines the complete entry animation for panel 4 */
.animate-pop-image.image-stack-enter-active {
  /* Note: Applying to the container, the img inside will inherit sizing */
  /* animation: pop-background 0.4s ease-out forwards; */ /* Replaced */
  animation: slide-in-bottom 0.5s ease-out forwards; /* Use slide-in-bottom */
  opacity: 1; /* Ensure final opacity is set by animation */
}

/* Ensure non-animated images still fade in using transition */
.image-stack-enter-active:not(.animate-pop-image) {
    transition: opacity 0.4s ease-out 0.05s; /* Re-apply standard fade for others */
}

@keyframes shine {
  0% { transform: translateX(-150%) skewX(-25deg); }
  100% { transform: translateX(150%) skewX(-25deg); }
}

/* Tipp-Cursor */
.animate-firework p,
.component-active .final-text-overlay p /* Apply base styles also to Text 2 */
{
  position: relative; /* Needed for pseudo-element positioning */
  overflow: hidden;   /* Hide overflow of pseudo-element */
  /* Apply base styles needed if text isn't block/inline-block */
  display: inline-block; 
}

.animate-firework p::after,
.component-active .final-text-overlay p::after /* Apply shine also to Text 2 */
{
  content: '';
  position: absolute;
  top: 0;
  left: 0; /* Start offset slightly to the left */
  width: 50%; /* Width of the shine */
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-150%) skewX(-25deg); /* Start off-screen left */
  animation: shine 1.2s ease-in-out forwards; /* Use 'forwards' if it shouldn't repeat */
  /* animation-delay: 0.5s; */ /* Delay shine until firework has mostly finished */
}

/* Specific delays for shine animations */
.animate-firework p::after { 
  animation-delay: 0.5s; /* Delay for Text 1 */
}
.component-active .final-text-overlay p::after {
  animation-delay: 1.4s; /* Delay for Text 2 (relative to component activation) */
}
</style> 