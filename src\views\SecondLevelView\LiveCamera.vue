<template>
    <div v-if="tempweekplanStore.videoNotAllowed || tempweekplanStore.noCameraFound" class="w-full h-96 bg-gray-200 rounded-lg">
        <p class="pt-36 h-12 w-1/2 mx-auto text-center text-white"><PERSON><PERSON> auf die Kamera hat es Probleme gegeben</p>
        <div class="w-3/4 mx-auto mt-4 flex flex-row h-auto pt-12 text-center align-middle justify-center">
            <click-shadow-button @click.prevent="reloadCamera()"  :element="{'buttondescription': 'Erneut versuchen', 'active': 'false', 'iconneeded': false}" :index="1"  />
        </div>
    </div>
    <div v-if="!tempweekplanStore.videoNotAllowed && !tempweekplanStore.noCameraFound" class="w-full h-96 rounded-lg">
        <video ref="video" autoplay playsinline webkit-playsinline muted hidden></video>
        <canvas v-if="!tempweekplanStore.tempImage" ref="canvas" class="w-full h-96 bg-black rounded-lg object-cover"></canvas>
        <div v-if="tempweekplanStore.tempImage" class="w-full h-96 bg-black rounded-lg object-cover">
            <img :src="tempweekplanStore.tempImage" class="w-full " />
        </div>
        <div class="w-full h-auto -mt-16 mx-auto flex justify-center items-center">
            <button @click="tempweekplanStore.clickSetImage()" class="w-12 h-12 bg-ordypink-100 rounded-[3rem] animate-custom-ping border-4 border-white"></button>
        </div>
    </div>
</template>
<script setup>
    import { onMounted, onBeforeUnmount, onUpdated, reactive, ref, onUnmounted, useTemplateRef } from 'vue';
    import axios from 'axios';
    import menuCard from '../../components/menuCard.vue'
    import preferenceCard from '../../components/preferenceCard.vue'
    import dayCard from '../../components/dayCard.vue'
    import useNotification from '../../../modules/notificationInformation';
    import { useMenuStore } from '@/store/menuStore';
    import { useUserStore } from '@/store/userStore'
    import { useWeekplanStore } from '@/store/weekplanStore'
    import { useTempWeekplanStore } from '@/store/weekplanStore'
    import clickShadowButton from '../../components/clickShadowButton.vue'
    import { useAboStore } from '../../store/aboStore';
    import { useHelperStore } from '../../../utils/helper';

    const userStore = useUserStore();
    const aboStore = useAboStore()
    const weekplanStore = useWeekplanStore();
    const tempweekplanStore = useTempWeekplanStore();
    const { setNotification } = useNotification();
    const helper = useHelperStore();

    let animationFrameId = null;
    let intervalId = null;

    //setup video and canvas
    const video = ref(null);
    const canvas = ref(null);

    onMounted(async () => {
        // Set references in store
        tempweekplanStore.video = video.value;
        tempweekplanStore.canvas = canvas.value;

        helper.devConsole('LiveCamera mounted, video:', !!video.value, 'canvas:', !!canvas.value);

        if (video.value && canvas.value) {
            const ctx = canvas.value.getContext("2d");
            if (!video.value.srcObject) {  // Nur wenn noch kein Stream vorhanden
                await checkCameraAccess(ctx); // Überprüfen Sie den Zugriff einmal beim Laden
            }

            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'visible' && video.value && !video.value.srcObject) {
                    checkCameraAccess(ctx);
                }
            });
        } else {
            helper.devConsole('LiveCamera: Video or Canvas ref not available');
        }
    });

    async function checkCameraAccess(ctx) {
        try {
            const constraints = {
                video: {
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    facingMode: { exact: "environment" }
                }
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            SetStream(stream, ctx);

            tempweekplanStore.videoNotAllowed = false;
            tempweekplanStore.noCameraFound = false;

            // Logge die tatsächliche Größe des Streams
            helper.devConsole(`Video Width: ${video.value?.videoWidth}`);
            helper.devConsole(`Video Height: ${video.value?.videoHeight}`);
        } catch (err) {
            console.error(err);
            if (err.name === 'NotAllowedError') {
                tempweekplanStore.videoNotAllowed = true;
            } else if (err.name === 'NotFoundError') {
                tempweekplanStore.noCameraFound = true;
            } else {
                tempweekplanStore.noCameraFound = true;
            }
        }
    }

    function SetStream(stream, ctx) {
        if (video.value) {
            video.value.srcObject = stream;

            video.value.onloadedmetadata = () => {
                helper.devConsole('Video metadata loaded:', {
                    videoWidth: video.value.videoWidth,
                    videoHeight: video.value.videoHeight
                });

                video.value.play().then(() => {
                    helper.devConsole('Video started playing');

                    // Warte einen Moment, bis das Video wirklich läuft
                    setTimeout(() => {
                        // Setze die Canvas-Größe auf die Dimensionen des Videostreams
                        if (canvas.value && video.value.videoWidth > 0 && video.value.videoHeight > 0) {
                            canvas.value.width = video.value.videoWidth;
                            canvas.value.height = video.value.videoHeight;

                            helper.devConsole('Canvas size set:', {
                                width: canvas.value.width,
                                height: canvas.value.height
                            });

                            Draw(ctx);
                        } else {
                            helper.devConsole('Video dimensions not ready, using fallback');
                            // Fallback-Größe
                            canvas.value.width = 640;
                            canvas.value.height = 480;
                            Draw(ctx);
                        }
                    }, 100);
                }).catch(error => {
                    helper.devConsole('Error playing video:', error);
                });
            };

            // Update store references
            tempweekplanStore.video = video.value;
            tempweekplanStore.canvas = canvas.value;
        }
    }

    function Draw(ctx) {
        if (video.value && canvas.value && video.value.readyState >= 2) {
            // Überprüfe, ob Video-Dimensionen verfügbar sind
            const videoWidth = video.value.videoWidth || canvas.value.width;
            const videoHeight = video.value.videoHeight || canvas.value.height;

            // Lösche Canvas vor dem Zeichnen
            ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);

            try {
                ctx.drawImage(
                    video.value,
                    0,
                    0,
                    videoWidth,
                    videoHeight
                );
            } catch (error) {
                helper.devConsole('Error drawing video to canvas:', error);
            }

            animationFrameId = requestAnimationFrame(() => Draw(ctx));
        } else if (video.value && canvas.value) {
            // Video noch nicht bereit, versuche es in 50ms erneut
            setTimeout(() => {
                if (video.value && canvas.value) {
                    Draw(ctx);
                }
            }, 50);
        }
    }

    async function reloadCamera() {
        helper.devConsole("Reloading camera..."); // Debug-Ausgabe
        // Zuerst den derzeitigen Kamerazugriff stoppen
        const stream = video.value?.srcObject;
        if (stream) {
            const tracks = stream.getTracks();
            tracks.forEach(track => track.stop());
            video.value.srcObject = null;
        }

        // Setzen Sie den Status zur Erkennung von Kameraproblemen zurück
        tempweekplanStore.videoNotAllowed = false;
        tempweekplanStore.noCameraFound = false;

        // Erneut auf die Kamera zugreifen
        if (video.value && canvas.value) {
            const ctx = canvas.value.getContext("2d");
            await checkCameraAccess(ctx); // Versuch, erneut auf die Kamera zuzugreifen
        }
    }


    onBeforeUnmount(() => {
        helper.devConsole("onBeforeUnmount on LiveCamera.vue");

        // Stoppen des Kamerastreams und Freigeben der Ressourcen
        const stream = video.value?.srcObject;
        if (stream) {
            const tracks = stream.getTracks();
            tracks.forEach(track => track.stop()); // Alle Tracks stoppen
            video.value.srcObject = null; // srcObject freigeben
        }

        // Entfernen der Referenzen auf Video und Canvas
        tempweekplanStore.video = null;
        tempweekplanStore.canvas = null;

        // AnimationFrame stoppen, falls einer existiert
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }

        // Remove the visibility change event listener
        document.removeEventListener('visibilitychange', checkCameraAccess);
    });

</script>
<style>
@keyframes custom-ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-custom-ping {
  animation: custom-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>