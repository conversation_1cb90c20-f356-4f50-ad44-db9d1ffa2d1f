<template>
  <div class="admin-dashboard">
    <!-- Welcome Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-black">
        Willkommen im Admin-Dashboard
      </h1>
      <p class="mt-2 text-gray-600">
        Verwalten Sie Ihre Ordy-Plattform und Marketing-Tools
      </p>
    </div>

    <!-- Pinterest Marketing Content Management -->
    <div class="pinterest-section">
      <h2 class="section-title">Pinterest Marketing Content</h2>

      <!-- Pinterest OAuth Status -->
      <div class="oauth-status-card">
        <div class="status-header">
          <h3>📌 Pinterest OAuth Status</h3>
          <div class="status-badge" :class="pinterestConnected ? 'connected' : 'disconnected'">
            {{ pinterestConnected ? 'Verbunden' : 'Nicht verbunden' }}
          </div>
        </div>

        <div v-if="!pinterestConnected" class="oauth-setup">
          <p>Pinterest OAuth ist nicht konfiguriert. Klicken Sie hier um die Verbindung herzustellen:</p>
          <button @click="startPinterestAuth" class="auth-button">
            Pinterest verbinden
          </button>
        </div>

        <div v-else class="oauth-connected">
          <p>✅ Pinterest ist erfolgreich verbunden</p>

          <!-- Scope-Warnung falls erforderliche Permissions fehlen -->
          <div v-if="pinterestScopeIssues" class="scope-warning">
            <p>⚠️ <strong>Fehlende Berechtigungen:</strong></p>
            <p>Ihr Pinterest Token hat nicht alle erforderlichen Scopes. Bitte verbinden Sie sich erneut:</p>
            <ul>
              <li v-for="scope in pinterestScopeIssues.missing" :key="scope">{{ scope }}</li>
            </ul>
            <button @click="startPinterestAuth" class="auth-button">
              Erneut verbinden (mit korrekten Scopes)
            </button>
          </div>

          <button @click="testPinterestConnection" class="test-button">
            Verbindung testen
          </button>
          <button @click="disconnectPinterest" class="disconnect-button">
            Verbindung trennen
          </button>
        </div>
      </div>

      <!-- Marketing Content Publishing -->
      <div class="publishing-section">
        <h3>🚀 Content Publishing</h3>

        <!-- Platform Status Overview -->
        <div class="platform-status-grid">
          <div class="platform-card pinterest">
            <div class="platform-header">
              <span class="platform-icon">📌</span>
              <h4>Pinterest</h4>
              <span class="status-indicator" :class="{ 'connected': pinterestConnected, 'disconnected': !pinterestConnected }">
                {{ pinterestConnected ? 'Verbunden' : 'Nicht verbunden' }}
              </span>
            </div>
            <div class="platform-actions">
              <button @click="publishToPinterest" class="platform-publish-btn" :disabled="!pinterestConnected">
                Zu Pinterest publizieren
              </button>
              <button @click="connectPinterest" v-if="!pinterestConnected" class="platform-connect-btn">
                Pinterest verbinden
              </button>
            </div>
          </div>

          <div class="platform-card tiktok">
            <div class="platform-header">
              <span class="platform-icon">🎵</span>
              <h4>TikTok</h4>
              <span class="status-indicator" :class="{ 'connected': tiktokConnected, 'disconnected': !tiktokConnected }">
                {{ tiktokConnected ? 'Verbunden' : 'Nicht verbunden' }}
              </span>
            </div>
            <div class="platform-actions">
              <button @click="publishToTikTok" class="platform-publish-btn" :disabled="!tiktokConnected">
                Zu TikTok publizieren
              </button>
              <button @click="connectTikTok" v-if="!tiktokConnected" class="platform-connect-btn">
                TikTok verbinden
              </button>
            </div>
          </div>

          <div class="platform-card instagram">
            <div class="platform-header">
              <span class="platform-icon">📸</span>
              <h4>Instagram</h4>
              <span class="status-indicator" :class="{ 'connected': instagramConnected, 'disconnected': !instagramConnected }">
                {{ instagramConnected ? 'Verbunden' : 'Nicht verbunden' }}
              </span>
            </div>
            <div class="platform-actions">
              <button @click="publishToInstagram" class="platform-publish-btn" :disabled="!instagramConnected">
                Zu Instagram publizieren
              </button>
              <button @click="connectInstagram" v-if="!instagramConnected" class="platform-connect-btn">
                Instagram verbinden
              </button>
            </div>
          </div>
        </div>

        <!-- Content Status -->
        <div class="content-status" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <span style="font-weight: 600; color: #374151;">Content Status</span>
            <div style="display: flex; gap: 0.5rem;">
              <button @click="checkContentStatus" style="color: #3b82f6; background: none; border: none; cursor: pointer; font-size: 0.875rem;">
                Aktualisieren
              </button>
              <button @click="debugContentStatus" style="color: #dc2626; background: none; border: none; cursor: pointer; font-size: 0.875rem;">
                Debug
              </button>
            </div>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.875rem;">
            <div>
              <span style="color: #6b7280;">Mit Video:</span>
              <span style="font-weight: 600; margin-left: 0.25rem;">{{ contentStatus.withVideo || 0 }}</span>
            </div>
            <div>
              <span style="color: #6b7280;">Ohne Video:</span>
              <span style="font-weight: 600; margin-left: 0.25rem;">{{ contentStatus.withoutVideo || 0 }}</span>
            </div>
          </div>
          <div v-if="contentStatus.withVideo === 0" style="margin-top: 0.75rem;">
            <button
              @click="createTestContent"
              style="width: 100%; background: #d97706; color: white; padding: 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; transition: background-color 0.2s; margin-bottom: 0.5rem;"
              onmouseover="this.style.background='#b45309'"
              onmouseout="this.style.background='#d97706'"
            >
              🎬 Test-Content mit Video erstellen
            </button>
            <div style="font-size: 0.75rem; color: #6b7280; text-align: center; margin-top: 0.25rem;">
              Erstellt Test-Content für TikTok/Instagram Publishing
            </div>
          </div>
          <div v-if="contentStatus.withoutVideo > 0" style="margin-top: 0.75rem;">
            <button
              @click="fixMissingVideos"
              style="width: 100%; background: #059669; color: white; padding: 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; transition: background-color 0.2s;"
              onmouseover="this.style.background='#047857'"
              onmouseout="this.style.background='#059669'"
            >
              Alle fehlenden Videos reparieren ({{ contentStatus.withoutVideo }})
            </button>
          </div>
        </div>

        <!-- Global Publishing Controls -->
        <div class="publishing-controls">
          <button
            @click="publishToAllPlatforms"
            class="publish-all-button"
            :disabled="!anyPlatformConnected || contentStatus.withVideo === 0"
            :style="{ opacity: (!anyPlatformConnected || contentStatus.withVideo === 0) ? '0.5' : '1' }"
          >
            Auf alle Plattformen publizieren
          </button>

          <button @click="schedulePublishing" class="schedule-button" :disabled="!anyPlatformConnected">
            Auto-Publishing aktivieren
          </button>

          <button @click="viewPublishingHistory" class="history-button">
            Publishing-Verlauf
          </button>
        </div>

        <div class="publishing-status">
          <p><strong>Letztes Publishing:</strong> {{ lastPublishTime || 'Noch nie' }}</p>
          <p><strong>Nächstes geplantes Publishing:</strong> {{ nextScheduledPublish || 'Nicht geplant' }}</p>
          <p><strong>Auto-Publishing:</strong> {{ autoPublishingEnabled ? 'Aktiv' : 'Inaktiv' }}</p>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="bg-white rounded-lg shadow p-4 mb-8">
      <h2 class="text-xl font-semibold text-black mb-4">Schnellübersicht</h2>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ totalUsers }}</div>
          <div class="text-sm text-gray-500">Benutzer</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ totalContent }}</div>
          <div class="text-sm text-gray-500">Marketing Content</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ publishedToday }}</div>
          <div class="text-sm text-gray-500">Heute publiziert</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600">{{ systemStatus }}</div>
          <div class="text-sm text-gray-500">System Status</div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-xl font-semibold text-black mb-4">Letzte Aktivitäten</h2>
      <div class="space-y-3">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
        >
          <div class="flex items-center">
            <div
              class="w-2 h-2 rounded-full mr-3"
              :class="{
                'bg-green-500': activity.type === 'success',
                'bg-yellow-500': activity.type === 'warning',
                'bg-red-500': activity.type === 'error',
                'bg-blue-500': activity.type === 'info'
              }"
            ></div>
            <span class="text-sm text-gray-900">{{ activity.message }}</span>
          </div>
          <span class="text-xs text-gray-500">{{ formatTime(activity.timestamp) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useAdminPermissions } from '../../composables/useAdminPermissions';
import { useUserStore } from '../../store/userStore';

const userStore = useUserStore();

const {
  canManagePinterest,
  canManageMarketing,
  canViewAnalytics,
  canManageUsers
} = useAdminPermissions();

// Debug (kann später entfernt werden)
console.log('AdminDashboard loaded for user:', userStore.user?.firstName);

// Platform Connection Status
const pinterestConnected = ref(false);
const tiktokConnected = ref(false);
const instagramConnected = ref(false);
const pinterestScopeIssues = ref(null);
const lastPublishTime = ref(null);
const nextScheduledPublish = ref(null);
const autoPublishingEnabled = ref(false);

// Computed property for any platform connected
const anyPlatformConnected = computed(() => {
  return pinterestConnected.value || tiktokConnected.value || instagramConnected.value;
});

// Dashboard Data
const totalUsers = ref(0);
const totalContent = ref(0);
const publishedToday = ref(0);
const systemStatus = ref('Online');

// Content Status
const contentStatus = ref({
  withVideo: 0,
  withoutVideo: 0,
  total: 0
});

const recentActivities = ref([
  {
    id: 1,
    type: 'info',
    message: 'Admin-Dashboard wurde geladen',
    timestamp: new Date()
  }
]);

// Methods
const formatTime = (timestamp) => {
  return new Intl.RelativeTimeFormat('de', { numeric: 'auto' }).format(
    Math.round((timestamp - new Date()) / (1000 * 60)),
    'minute'
  );
};

// Pinterest Methods
const startPinterestAuth = async () => {
  try {
    console.log('Starting Pinterest OAuth...');

    // API-Aufruf für Pinterest OAuth URL (korrekte Route)
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/oauth/pinterest/auth-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Pinterest OAuth response:', data);

      if (data.data && data.data.authUrl) {
        // Öffne Pinterest OAuth in neuem Fenster
        window.open(data.data.authUrl, 'pinterest-auth', 'width=600,height=700');
        console.log('Pinterest OAuth window opened');
      } else if (data.authUrl) {
        // Fallback für andere Response-Struktur
        window.open(data.authUrl, 'pinterest-auth', 'width=600,height=700');
        console.log('Pinterest OAuth window opened (fallback)');
      } else {
        console.error('Response structure:', data);
        throw new Error('Keine Auth-URL in der Antwort gefunden');
      }
    } else {
      const errorData = await response.text();
      console.error('Pinterest OAuth error response:', errorData);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Fehler beim Pinterest OAuth:', error);
    alert(`Fehler beim Starten der Pinterest-Authentifizierung: ${error.message}`);
  }
};

const testPinterestConnection = async () => {
  try {
    console.log('Testing Pinterest connection...');

    // API-Aufruf für Pinterest Token Status (korrekte Route)
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/oauth/pinterest/token-status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Pinterest token status response:', data);

      if (data.data && data.data.hasToken) {
        pinterestConnected.value = true;
        alert('Pinterest Verbindung erfolgreich! ✅');
      } else {
        pinterestConnected.value = false;
        alert('Pinterest nicht verbunden. Bitte authentifizieren Sie sich zuerst.');
      }
    } else {
      const errorData = await response.text();
      console.error('Pinterest token status error:', errorData);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Fehler beim Pinterest Test:', error);
    alert(`Fehler beim Testen der Pinterest-Verbindung: ${error.message}`);
  }
};

const disconnectPinterest = async () => {
  try {
    console.log('Disconnecting Pinterest...');
    pinterestConnected.value = false;
    alert('Pinterest Verbindung getrennt');
  } catch (error) {
    console.error('Fehler beim Pinterest Disconnect:', error);
  }
};

// Platform-specific publishing functions
const publishToPinterest = async () => {
  await publishToPlatform('pinterest', 'auto-publish-pinterest', 'Pinterest');
};

const publishToTikTok = async () => {
  await publishToPlatform('tiktok', 'auto-publish-tiktok', 'TikTok');
};

const publishToInstagram = async () => {
  await publishToPlatform('instagram', 'auto-publish-instagram', 'Instagram');
};

const publishToPlatform = async (platform, endpoint, displayName) => {
  try {
    console.log(`Publishing to ${displayName}...`);

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      alert(`${displayName} Publishing erfolgreich gestartet! ${data.message || 'Content wird verarbeitet...'}`);

      // Update UI
      publishedToday.value += 1;
      lastPublishTime.value = new Date();

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'success',
        message: `Content erfolgreich zu ${displayName} publiziert`,
        timestamp: new Date()
      });
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`Fehler beim ${displayName} Publishing:`, error);
    alert(`Fehler beim ${displayName} Publishing: ${error.message}`);

    // Add error activity
    recentActivities.value.unshift({
      id: Date.now(),
      type: 'error',
      message: `${displayName} Publishing fehlgeschlagen: ${error.message}`,
      timestamp: new Date()
    });
  }
};

const publishToAllPlatforms = async () => {
  try {
    console.log('Publishing to all platforms...');

    const platforms = [];
    if (pinterestConnected.value) platforms.push({ name: 'Pinterest', endpoint: 'auto-publish-pinterest' });
    if (tiktokConnected.value) platforms.push({ name: 'TikTok', endpoint: 'auto-publish-tiktok' });
    if (instagramConnected.value) platforms.push({ name: 'Instagram', endpoint: 'auto-publish-instagram' });

    if (platforms.length === 0) {
      alert('Keine Plattformen verbunden!');
      return;
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Publish to all connected platforms sequentially with error handling
    for (const platform of platforms) {
      try {
        console.log(`Publishing to ${platform.name}...`);

        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/${platform.endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('session_token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          results.push({ platform: platform.name, success: true, message: data.message });
          successCount++;

          // Add success activity
          recentActivities.value.unshift({
            id: Date.now() + Math.random(),
            type: 'success',
            message: `${platform.name} Publishing erfolgreich`,
            timestamp: new Date()
          });
        } else {
          const errorData = await response.json();
          results.push({ platform: platform.name, success: false, error: errorData.message });
          errorCount++;

          // Add error activity
          recentActivities.value.unshift({
            id: Date.now() + Math.random(),
            type: 'error',
            message: `${platform.name} Publishing fehlgeschlagen: ${errorData.message}`,
            timestamp: new Date()
          });
        }

        // Wait 3 seconds between platforms to avoid rate limiting
        if (platform !== platforms[platforms.length - 1]) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

      } catch (error) {
        console.error(`Error publishing to ${platform.name}:`, error);
        results.push({ platform: platform.name, success: false, error: error.message });
        errorCount++;

        // Add error activity
        recentActivities.value.unshift({
          id: Date.now() + Math.random(),
          type: 'error',
          message: `${platform.name} Publishing Fehler: ${error.message}`,
          timestamp: new Date()
        });
      }
    }

    // Update UI
    publishedToday.value += successCount;
    if (successCount > 0) {
      lastPublishTime.value = new Date();
    }

    // Show summary
    const summary = `Multi-Platform Publishing abgeschlossen!\n\n` +
      `✅ Erfolgreich: ${successCount}\n` +
      `❌ Fehlgeschlagen: ${errorCount}\n\n` +
      results.map(r => `${r.success ? '✅' : '❌'} ${r.platform}: ${r.success ? 'Erfolgreich' : r.error}`).join('\n');

    alert(summary);

  } catch (error) {
    console.error('Fehler beim Multi-Platform Publishing:', error);
    alert(`Fehler beim Multi-Platform Publishing: ${error.message}`);
  }
};

const schedulePublishing = async () => {
  try {
    console.log('Scheduling publishing...');
    autoPublishingEnabled.value = !autoPublishingEnabled.value;
    alert(`Auto-Publishing ${autoPublishingEnabled.value ? 'aktiviert' : 'deaktiviert'}`);
  } catch (error) {
    console.error('Fehler beim Scheduling:', error);
  }
};

const viewPublishingHistory = () => {
  console.log('Viewing publishing history...');
  alert('Publishing-Verlauf wird angezeigt... (TODO: Implementierung)');
};

// Content Status Functions
const checkContentStatus = async () => {
  try {
    console.log('Checking content status...');

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/content-status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Content status response:', data);

      contentStatus.value = {
        withVideo: data.data.contentWithVideo || 0,
        withoutVideo: data.data.contentWithoutVideo || 0,
        total: data.data.totalContent || 0
      };

      // Update total content in dashboard
      totalContent.value = contentStatus.value.total;

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'info',
        message: `Content Status aktualisiert: ${contentStatus.value.withVideo} mit Video, ${contentStatus.value.withoutVideo} ohne Video`,
        timestamp: new Date()
      });
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Fehler beim Content Status Check:', error);
    alert(`Fehler beim Content Status Check: ${error.message}`);
  }
};

const debugContentStatus = async () => {
  try {
    console.log('🔍 Debug: Checking detailed content status...');

    // Get all marketing content for debugging
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/content/all`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('🔍 Debug: All marketing content:', data);

      const content = data.data || [];
      const withVideo = content.filter(item => item.videoS3Url && item.videoS3Url.trim() !== '');
      const withoutVideo = content.filter(item => !item.videoS3Url || item.videoS3Url.trim() === '');

      const debugInfo = `
📊 MARKETING CONTENT DEBUG INFO:

📈 Gesamt: ${content.length} Einträge
✅ Mit Video: ${withVideo.length}
❌ Ohne Video: ${withoutVideo.length}

🎬 EINTRÄGE MIT VIDEO:
${withVideo.map(item => `- ${item.recipeName} (${item.videoS3Url?.substring(0, 50)}...)`).join('\n') || 'Keine'}

📝 EINTRÄGE OHNE VIDEO:
${withoutVideo.map(item => `- ${item.recipeName} (ID: ${item._id})`).join('\n') || 'Keine'}

🔗 LETZTE VIDEO-URL:
${withVideo.length > 0 ? withVideo[0].videoS3Url : 'Keine verfügbar'}
      `;

      alert(debugInfo);

      // Also log to console for detailed inspection
      console.log('🔍 Debug: Content with videos:', withVideo);
      console.log('🔍 Debug: Content without videos:', withoutVideo);

    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('🔍 Debug: Error getting content details:', error);
    alert(`Debug Fehler: ${error.message}`);
  }
};

const createTestContent = async () => {
  try {
    console.log('Creating test content...');

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/create-test-content`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Test content created:', data);

      alert(`Test-Content erfolgreich erstellt!\n\nRezept: ${data.data.recipeName}\nVideo URL: ${data.data.videoUrl ? 'Verfügbar' : 'Nicht verfügbar'}`);

      // Refresh content status
      await checkContentStatus();

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'success',
        message: `Test-Content "${data.data.recipeName}" erstellt`,
        timestamp: new Date()
      });
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Fehler beim Test-Content erstellen:', error);
    alert(`Fehler beim Test-Content erstellen: ${error.message}`);
  }
};

const fixMissingVideos = async () => {
  try {
    console.log('Fixing missing videos...');

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/fix-missing-videos`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('session_token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Missing videos fixed:', data);

      alert(`Videos erfolgreich repariert!\n\nAktualisiert: ${data.data.updatedCount} Einträge\nGesamt Content: ${data.data.totalContent}\nVideo URL: ${data.data.videoUrl ? 'Verfügbar' : 'Nicht verfügbar'}`);

      // Refresh content status
      await checkContentStatus();

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'success',
        message: `${data.data.updatedCount} Marketing Content Einträge mit Videos aktualisiert`,
        timestamp: new Date()
      });
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Fehler beim Reparieren der Videos:', error);
    alert(`Fehler beim Reparieren der Videos: ${error.message}`);
  }
};

// Platform connection functions
const connectTikTok = async () => {
  try {
    console.log('Connecting TikTok...');
    // For now, just simulate connection since TikTok OAuth is complex
    // In production, this would open TikTok OAuth flow
    const confirmed = confirm('TikTok Verbindung simulieren? (In Produktion würde hier der OAuth-Flow starten)');
    if (confirmed) {
      tiktokConnected.value = true;
      alert('TikTok erfolgreich verbunden! (Simulation)');

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'success',
        message: 'TikTok Account verbunden',
        timestamp: new Date()
      });
    }
  } catch (error) {
    console.error('Fehler beim TikTok Connect:', error);
    alert(`Fehler beim TikTok Connect: ${error.message}`);
  }
};

const connectInstagram = async () => {
  try {
    console.log('Connecting Instagram...');
    // For now, just simulate connection since Instagram OAuth is complex
    // In production, this would open Instagram OAuth flow
    const confirmed = confirm('Instagram Verbindung simulieren? (In Produktion würde hier der OAuth-Flow starten)');
    if (confirmed) {
      instagramConnected.value = true;
      alert('Instagram erfolgreich verbunden! (Simulation)');

      // Add activity
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'success',
        message: 'Instagram Account verbunden',
        timestamp: new Date()
      });
    }
  } catch (error) {
    console.error('Fehler beim Instagram Connect:', error);
    alert(`Fehler beim Instagram Connect: ${error.message}`);
  }
};

const loadDashboardData = async () => {
  try {
    console.log('🔍 [AdminDashboard] Loading dashboard data...');

    // Pinterest Status prüfen (korrekte Route)
    try {
      console.log('🔍 [AdminDashboard] Checking Pinterest status...');
      const pinterestResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/oauth/pinterest/token-status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      });

      console.log('🔍 [AdminDashboard] Pinterest response status:', pinterestResponse.status);

      if (pinterestResponse.ok) {
        const pinterestData = await pinterestResponse.json();
        console.log('🔍 [AdminDashboard] Pinterest response data:', pinterestData);

        const hasToken = (pinterestData.data && pinterestData.data.hasToken) || false;
        console.log('🔍 [AdminDashboard] Pinterest hasToken:', hasToken);

        // KRITISCH: Scope-Validation prüfen
        const scopeValidation = pinterestData.data?.scopeValidation;
        if (scopeValidation && !scopeValidation.valid) {
          console.warn('🔍 [AdminDashboard] Pinterest token has missing scopes:', scopeValidation.missing);
          pinterestScopeIssues.value = scopeValidation;
        } else {
          pinterestScopeIssues.value = null;
        }

        pinterestConnected.value = hasToken;
        console.log('🔍 [AdminDashboard] Pinterest connected set to:', pinterestConnected.value);
        console.log('🔍 [AdminDashboard] Pinterest scope issues:', pinterestScopeIssues.value);
      } else {
        console.warn('🔍 [AdminDashboard] Pinterest response not ok:', pinterestResponse.status);
        pinterestConnected.value = false;
      }
    } catch (error) {
      console.warn('🔍 [AdminDashboard] Pinterest status check failed:', error);
      pinterestConnected.value = false;
    }

    // Check TikTok and Instagram connection status
    try {
      console.log('🔍 [AdminDashboard] Checking TikTok and Instagram status...');

      // For now, assume connected if credentials are configured
      // In production, this would check actual OAuth token validity
      tiktokConnected.value = true; // Assume connected if credentials are in config.env
      instagramConnected.value = true; // Assume connected if credentials are in config.env

      console.log('Platform connection status:', {
        pinterest: pinterestConnected.value,
        tiktok: tiktokConnected.value,
        instagram: instagramConnected.value
      });
    } catch (error) {
      console.warn('Error checking platform connections:', error);
      tiktokConnected.value = false;
      instagramConnected.value = false;
    }

    // Marketing Content laden
    try {
      const contentResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/admin/marketing/content/all`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session_token')}`
        }
      });

      if (contentResponse.ok) {
        const contentData = await contentResponse.json();
        totalContent.value = contentData.total || 0;
        publishedToday.value = contentData.publishedToday || 0;
      }
    } catch (error) {
      console.warn('Content data loading failed:', error);
      // Fallback-Werte
      totalContent.value = 15;
      publishedToday.value = 3;
    }

    // Placeholder für User-Daten (falls API verfügbar)
    totalUsers.value = 42;

    // Load content status
    await checkContentStatus();

  } catch (error) {
    console.error('Fehler beim Laden der Dashboard-Daten:', error);
  }
};

onMounted(() => {
  console.log('🔍 [AdminDashboard] Component mounted, loading dashboard data...');
  loadDashboardData();
});
</script>

<style scoped>
.admin-dashboard {
  padding: 1rem;
}

.permission-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .permission-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.permission-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
}

.permission-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: #f3f4f6;
}

.pinterest-icon {
  background: #fee2e2;
}

.marketing-icon {
  background: #dbeafe;
}

.analytics-icon {
  background: #dcfce7;
}

.users-icon {
  background: #f3e8ff;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.card-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.card-button {
  width: 100%;
  padding: 0.5rem 1rem;
  background: #f9fafb;
  color: #374151;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.card-button:hover {
  background: #f3f4f6;
}

/* Pinterest Section Styles */
.pinterest-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.oauth-status-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.connected {
  background: #dcfce7;
  color: #166534;
}

.status-badge.disconnected {
  background: #fee2e2;
  color: #dc2626;
}

.oauth-setup, .oauth-connected {
  margin-top: 1rem;
}

.scope-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #856404;
}

.scope-warning ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.scope-warning li {
  font-family: monospace;
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  margin: 0.2rem 0;
  border-radius: 4px;
}

.auth-button, .test-button, .disconnect-button, .publish-button, .schedule-button, .history-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-right: 0.5rem;
  margin-top: 0.5rem;
}

.auth-button, .publish-button {
  background: #7c3aed;
  color: white;
}

.auth-button:hover, .publish-button:hover {
  background: #6d28d9;
}

.test-button, .schedule-button {
  background: #3b82f6;
  color: white;
}

.test-button:hover, .schedule-button:hover {
  background: #2563eb;
}

.disconnect-button {
  background: #ef4444;
  color: white;
}

.disconnect-button:hover {
  background: #dc2626;
}

.history-button {
  background: #6b7280;
  color: white;
}

.history-button:hover {
  background: #4b5563;
}

.publishing-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.publishing-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.publishing-controls {
  margin-bottom: 1rem;
}

.publishing-status {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.publishing-status p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #374151;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  background: inherit !important;
}

/* Platform Status Grid */
.platform-status-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .platform-status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .platform-status-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.platform-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.platform-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.platform-card.pinterest {
  border-left: 4px solid #e60023;
}

.platform-card.tiktok {
  border-left: 4px solid #ff0050;
}

.platform-card.instagram {
  border-left: 4px solid #e4405f;
}

.platform-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.platform-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.platform-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator.connected {
  background: #dcfce7;
  color: #166534;
}

.status-indicator.disconnected {
  background: #fee2e2;
  color: #dc2626;
}

.platform-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.platform-publish-btn {
  padding: 0.5rem 1rem;
  background: #7c3aed;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.platform-publish-btn:hover:not(:disabled) {
  background: #6d28d9;
}

.platform-connect-btn {
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.platform-connect-btn:hover {
  background: #2563eb;
}

.publish-all-button {
  padding: 0.75rem 1.5rem;
  background: #059669;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.publish-all-button:hover:not(:disabled) {
  background: #047857;
}
</style>
