const catchAsync = require('../../utils/catchAsync');
var helper = require('../../utils/helper');
const Jimp = require("jimp");
const fs = require('fs')
const axios = require('axios')
const cheerio = require('cheerio');  // new addition
// const Peax = require('../../models/peaxModel')
const Grocery = require('../../models/groceryModel');
const { validateHeaderValue } = require('http');
const qs = require('qs');
const { Buffer } = require('buffer');
const { error } = require('console');


//@POST /peax/arrivingdocument
exports.arrivingDocument = catchAsync(async (req, res, next) => {
  helper.devConsole("Peax arriving document functionality is currently disabled due to connection2 removal.");
  res.status(200).json({ success: true, message: "Functionality disabled." });
});

//@POST /peax/createorchangeconfig
exports.createOrChangeConfig = catchAsync(async (req, res, next) => {
  helper.devConsole("Peax create/change config functionality is currently disabled.");
  res.status(200).json({ success: true, message: "Functionality disabled." });
});

//@POST /peax/deleteconfig
exports.deleteconfig = catchAsync(async (req, res, next) => {
  helper.devConsole("Peax delete config functionality is currently disabled.");
  res.status(200).json({ success: true, message: "Functionality disabled." });
});


//@GET /peax/getconfig
exports.getconfig = catchAsync(async (req, res, next) => {
  helper.devConsole("Peax get config functionality is currently disabled.");
  res.status(200).json({ success: true, data: null, message: "Functionality disabled." });
});

//@POST /peax/getmsdata
exports.getmsdata = catchAsync(async (req, res, next) => {

  helper.devConsole("-------------- getmsdata ---------------")
  helper.devConsole(req.body)
  helper.devConsole("-------------- getmsdata ---------------")
  
  // auth at ms client
  
  const requestData = {
    requesturl: req.body.requesturl,
    tenantid: req.body.teantid,

  }

  helper.devConsole(requestData.tenantid)
  
  const authString = `https://login.microsoftonline.com/${req.body.tenantid}/oauth2/v2.0/token`

  const requestBody = {
    client_id: process.env.MS_PEAX_APPID,
    scope: 'https://graph.microsoft.com/.default',
    grant_type: 'client_credentials',
    client_secret: process.env.MS_PEAX_CLIENTSECRET
  };

  helper.devConsole(requestBody)

  const tokenResponse = await axios.post(authString, qs.stringify(requestBody), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  const { id_token, access_token } = tokenResponse.data;
  //console.log(tokenResponse.data)


  const response2 = await axios.get(requestData.requesturl, {
    headers: {
      'Authorization': `Bearer ${access_token}`,
      'Content-Type': 'application/json'
    },
  });


  res.status(201).json({
    success: true,
    data: response2.data
  });
});

exports.createPeaxTransaction = catchAsync(async (req, res, next) => {
  req.body.answerobject = { message: "Peax functionality is currently disabled." };
  next();
});

exports.getPeaxTransactionsByUserId = catchAsync(async (req, res, next) => {
  req.body.answerobject = [];
  next();
});

/*

// auth at ms client
  const tokenResponse = await axios.post('https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/token', {
    client_id: process.env.MICROSOFT_CLIENT_ID,
    scope: 'openid profile User.Read',
    code: code,
    redirect_uri: process.env.MICROSOFT_REDIRECT_URI,
    grant_type: 'authorization_code',
    client_secret: process.env.MICROSOFT_CLIENT_SECRET
  });

  const { id_token, access_token } = tokenResponse.data;
*/