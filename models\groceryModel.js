const mongoose = require('mongoose')
const { connection1 } = require('./../db.js');
const helper = require('../utils/helper')
const Unit = require('../models/unitModel')

const GrocerySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    category: {
        type: String,
        enum: [
            'Gemüse & Früchte',
            'Brotwaren & Backwaren',
            'Milchprodukte & Molkereiprodukte',
            'Fleisch, Wurst & Fisch',
            'Tiefkühlprodukte',
            'Grundnahrungsmittel',
            'Frühstück & Cerealien',
            'Süsswaren & Snacks',
            'Getränke',
            'Non-Food & Haushaltsartikel',
            'Sonstiges'
        ],
        default: 'Sonstiges'
    },
    createdAt: {
        type: String,
        default: Date.now()
    }
})


module.exports = connection1.model('Grocery', GrocerySchema)