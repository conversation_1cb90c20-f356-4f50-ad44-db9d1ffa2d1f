const mongoose = require('mongoose');
const { connection1 } = require('../db'); // Import the specific connection

const shoppingListItemSchema = new mongoose.Schema({
  shopping_list_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ShoppingList',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  quantity: {
    // Using Mixed type to allow both Numbers and Strings ("1 Packung")
    // Validation for this should happen in controller or pre-save hook if needed.
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  unit: {
    type: String,
    trim: true,
    default: null // Explicitly allow null
  },
  is_purchased: {
    type: Boolean,
    default: false,
    required: true
  },
  is_custom: {
    type: Boolean,
    default: false,
    required: true
  },
  added_by_user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null // Null if not custom or user unknown
  },
  recipeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Menu', // Refers to the Menu model (which acts as the recipe source)
    required: false, // Not required for custom items
    default: null,
    index: true // Index for faster lookup when checking active lists
  },
  category: {
    type: String,
    enum: [
      'Gemüse & Früchte',
      'Brotwaren & Backwaren',
      'Milchprodukte & Molkereiprodukte',
      'Fleisch, Wurst & Fisch',
      'Tiefkühlprodukte',
      'Grundnahrungsmittel',
      'Frühstück & Cerealien',
      'Süsswaren & Snacks',
      'Getränke',
      'Non-Food & Haushaltsartikel',
      'Sonstiges'
    ],
    default: 'Sonstiges'
  },
  // Mongoose adds createdAt and updatedAt automatically with timestamps: true
}, {
  timestamps: true
});

// Compound index for faster lookup/aggregation logic (Task 3)
// Ensures faster queries when searching for item by name+unit within a list
shoppingListItemSchema.index({ shopping_list_id: 1, name: 1, unit: 1, is_custom: 1 });

// Virtual property to populate contributions (optional, can also be done in controller)
// shoppingListItemSchema.virtual('contributions', {
//   ref: 'ShoppingListItemContribution',
//   localField: '_id',
//   foreignField: 'shopping_list_item_id'
// });

// Use the imported connection to create the model
const ShoppingListItem = connection1.model('ShoppingListItem', shoppingListItemSchema);

module.exports = ShoppingListItem;