<template>
  <!-- Change root to flex-col md:flex-row -->
  <div class="px-6 flex flex-col md:flex-row min-h-screen">

    <!-- Main Content Area (Left Column on Desktop) -->
    <!-- Add container md:w-3/4 -->
    <div class="w-full md:w-3/4 md:pr-8 lg:pr-12 flex-1">
        <!-- Header Info (Optional, kann auch in den scrollbaren Bereich) -->
         <div class="mb-8 mt-10">
            <h1 class="text-4xl md:text-3xl text-gray-900">Einkaufszettel</h1>
            <p class="text-sm text-gray-600 mt-1">Verwalte deinen Einkaufszettel - Viel Spass beim Einkaufen!</p>
            <span v-if="formattedCreatedAt" class="created-at-info mt-2 text-xs text-gray-400 block">
                 Erstellt: {{ formattedCreatedAt }}
            </span>
            <!-- An<PERSON>ige des Listenstatus -->
            <span v-if="activeListStore.listId && activeListStore.isCompleted"
                  class="list-status mt-2 text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-md inline-block">
                 🔒 Diese Einkaufsliste ist geschlossen - Nur zum Anschauen
            </span>
            <span v-else-if="activeListStore.listId && !activeListStore.isActive"
                  class="list-status mt-2 text-xs font-medium text-amber-600 bg-amber-50 px-2 py-1 rounded-md inline-block">
                 ⏸ Diese Einkaufsliste ist inaktiv
            </span>
            <div v-else-if="activeListStore.listId && activeListStore.isActive" class="mt-2">
                <span class="list-status text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-md inline-block">
                     ✓ Diese Einkaufsliste ist aktiv
                     <span v-if="activeListStore.listName" class="ml-2 font-bold">
                         "{{ activeListStore.listName }}"
                     </span>
                </span>



                <!-- Name bearbeiten und Abschließen-Buttons für AKTIVE Listen -->
                <div class="flex flex-wrap items-center gap-2 mt-2">
                    <!-- Name bearbeiten -->
                    <div v-if="editingListName" class="flex items-center gap-2">
                        <input
                            ref="nameInput"
                            v-model="editingName"
                            @keyup.enter="saveListName"
                            @keyup.escape="cancelEditName"
                            class="text-sm bg-white border border-green-300 rounded px-2 py-1 focus:outline-none focus:border-green-500"
                            placeholder="Listenname..."
                        />
                        <button
                            @mousedown.prevent
                            @click="saveListName"
                            class="text-green-600 hover:text-green-700 p-1 text-xs"
                        >
                            ✓
                        </button>
                        <button
                            @click="cancelEditName"
                            class="text-gray-500 hover:text-gray-700 p-1 text-xs"
                        >
                            ✕
                        </button>
                    </div>

                    <!-- Buttons nur für aktive, nicht abgeschlossene Listen -->
                    <div v-else-if="!activeListStore.isCompleted" class="flex items-center gap-2 md:gap-3 mt-3">
                        <!-- Name bearbeiten Button (Sekundär) -->
                        <button
                            @click="startEditName"
                            class="group relative flex items-center gap-2 px-3 py-2 md:px-4 md:py-2.5
                                   rounded-xl md:rounded-2xl bg-white border-2 border-gray-300
                                   text-xs md:text-sm font-YesevaOne text-gray-700
                                   shadow-sm hover:shadow-md hover:border-gray-400 hover:bg-gray-50
                                   transition-all duration-200 ease-out
                                   active:scale-95 active:shadow-sm"
                        >
                            <svg class="w-3.5 h-3.5 md:w-4 md:h-4 text-gray-600 group-hover:text-gray-800 transition-colors"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            <span class="hidden sm:inline">Name bearbeiten</span>
                            <span class="sm:hidden">Name</span>
                        </button>

                        <!-- Liste abschließen Button (Primär) -->
                        <button
                            @click="completeList"
                            class="group relative flex items-center gap-2 px-3 py-2 md:px-4 md:py-2.5
                                   rounded-xl md:rounded-2xl text-white text-xs md:text-sm font-YesevaOne
                                   bg-gradient-to-br from-ordypurple-100 to-ordypurple-200
                                   shadow-md hover:shadow-lg hover:from-ordypurple-200 hover:to-ordypurple-100
                                   transition-all duration-200 ease-out
                                   active:scale-95 active:shadow-md
                                   border-0 focus:outline-none focus:ring-2 focus:ring-ordypurple-100 focus:ring-opacity-50"
                        >
                            <svg class="w-3.5 h-3.5 md:w-4 md:h-4 text-white transition-transform group-hover:scale-110"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            <span class="hidden sm:inline">Liste abschliessen</span>
                            <span class="sm:hidden">Abschliessen</span>
                        </button>
                    </div>


                </div>
            </div>

            <!-- Read-Only Status für abgeschlossene Listen -->
            <div v-else-if="activeListStore.listId && activeListStore.isCompleted" class="mt-2">
                <span class="list-status text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-md inline-block">
                     🔒 Diese Einkaufsliste ist abgeschlossen (nur lesbar)
                     <span v-if="activeListStore.listName" class="ml-2 font-bold">
                         "{{ activeListStore.listName }}"
                     </span>
                </span>
            </div>
        </div>

        <!-- Input Area -->
        <!-- Add v-if condition to the container -->
         <div
            v-if="userStore.user.defaultKitchentable && !activeListStore.isLoading && !activeListStore.error"
            class="mb-2
                   fixed bottom-16 left-0 right-0 z-40 bg-white p-2 px-4 rounded-t-2xl
                   md:relative md:bottom-auto md:left-auto md:right-auto md:z-auto md:bg-transparent md:p-0 md:px-0 md:shadow-none md:rounded-none md:mb-6"
         >
           <div
              class="add-item-form flex gap-4 mx-auto md:bg-white md:p-4 md:shadow md:rounded-xl"
              v-if="activeListStore.listId && activeListStore.isActive && !isListCompleted"
            >
                <input
                  type="text"
                  v-model="newItemInput"
                  placeholder="z.B. 2 Gurken, Mehl 500g..."
                  @keyup.enter="handleAddManualItem"
                  class="single-item-input flex-grow p-3 bg-gray-25 rounded-xl transition-all duration-150 text-sm mr-5 border-0 focus:outline-none focus:ring-0 text-gray-800"
                  style="font-size:14px;"
                />
                <button
                  @click="handleAddManualItem"
                  class="px-3 py-1.5 mb-1 rounded-xl text-white text-xs font-YesevaOne transition-all duration-150
                         bg-ordypurple-100 shadow-custom shadow-[#E0ADFF]
                         hover:bg-ordypurple-200 border-0 focus:outline-none focus:ring-0"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Hinzufügen
                </button>
            </div>
            <!-- Anzeige wenn Liste geschlossen ist -->
            <div
              v-else-if="activeListStore.listId && isListCompleted"
              class="text-center text-sm text-red-600 py-2 md:bg-white md:p-4 md:shadow md:rounded-xl border border-red-200 bg-red-50"
            >
                <p class="font-medium">🔒 Diese Einkaufsliste ist geschlossen.</p>
                <p class="text-xs mt-1">Geschlossene Listen können nicht mehr bearbeitet werden. Sie dienen nur zum Anschauen vergangener Einkäufe. Wähle eine andere Liste aus oder erstelle eine neue.</p>
                <button
                  @click="createNewList"
                  class="mt-3 px-5 py-2 rounded-2xl text-white text-sm transition-all duration-150
                         bg-red-500 shadow-custom shadow-red-200
                         hover:bg-red-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Neue Liste erstellen
                </button>
            </div>
            <!-- Anzeige wenn Liste existiert aber nicht aktiv ist -->
            <div
              v-else-if="activeListStore.listId && !activeListStore.isActive"
              class="text-center text-sm text-amber-600 py-2 md:bg-white md:p-4 md:shadow md:rounded-xl"
            >
                <p>Diese Einkaufsliste ist nicht aktiv. Eine neue Liste wird automatisch erstellt, wenn du das nächste Mal ein Item hinzufügst.</p>
                <button
                  @click="createNewList"
                  class="mt-2 px-5 py-2 rounded-2xl text-white text-sm transition-all duration-150
                         bg-amber-500 shadow-custom shadow-amber-200
                         hover:bg-amber-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Neue Liste erstellen
                </button>
            </div>
            <div
               v-else
               class="text-center text-sm text-gray-400 py-2 bg-white p-4 shadow rounded-xl"
             >
                <!-- Einkaufszettel wird geladen... -->
            </div>
        </div>

        <!-- === ADDED: Check for Kitchentable ID FIRST === -->
        <div v-if="!userStore.user.defaultKitchentable"
             class="mt-10 p-6 bg-yellow-50 border border-yellow-200 rounded-lg text-center text-yellow-800">
           <h3 class="font-semibold mb-2">Kein Küchentisch ausgewählt</h3>
           <p class="text-sm mb-4">Bitte wähle zuerst einen Küchentisch aus, um den dazugehörigen Einkaufszettel anzuzeigen.</p>
           <router-link
               to="/kuechentisch"
               class="inline-block bg-yellow-400 text-yellow-900 font-semibold py-2 px-4 rounded-lg hover:bg-yellow-500 transition-colors text-sm"
           >
               Zum Küchentisch
           </router-link>
        </div>

        <!-- === ELSE: Show Loading/Error/List only if Kitchentable ID exists === -->
        <div v-else>
            <!-- Loading / Error States -->
            <div v-if="activeListStore.isLoading" class="text-center py-10">...</div>
            <div v-else-if="activeListStore.error" class="error-message p-4 bg-red-100 border border-red-300 rounded text-red-800">
               <p>Fehler: {{ activeListStore.error }}</p>
            </div>

            <!-- Main List Area (Grid Layout) -->
            <div v-else>
                <!-- View Filter -->
                <div class="flex items-center justify-end mb-4 mt-2">
                  <span class="text-sm text-gray-600 mr-2 hidden sm:inline">Ansicht:</span>
                  <div class="view-mode-switch">
                    <div class="switch-container">
                      <div
                        class="switch-track"
                        @click="viewMode = viewMode === 'category' ? 'source' : 'category'"
                      >
                        <div class="switch-labels">
                          <span
                            :class="['switch-label', { 'switch-label-active': viewMode === 'category' }]"
                            @click.stop="viewMode = 'category'"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            <span class="hidden sm:inline">Kategorie</span>
                          </span>
                          <span
                            :class="['switch-label', { 'switch-label-active': viewMode === 'source' }]"
                            @click.stop="viewMode = 'source'"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                            </svg>
                            <span class="hidden sm:inline">Herkunft</span>
                          </span>
                        </div>
                        <div
                          class="switch-thumb"
                          :class="{ 'translate-x-full': viewMode === 'source' }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Kategorisierte Einkaufsartikel (Kategorie-Ansicht) -->
                <div v-if="viewMode === 'category'" v-for="(items, category) in categorizedItems" :key="category" class="mb-6">
                    <div class="flex justify-between items-center mb-3 cursor-pointer" @click="toggleCategoryCollapse(category)">
                      <h2 class="text-base font-medium text-gray-800">{{ category }}</h2>
                      <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 transition-transform duration-200"
                             :class="{ 'rotate-180': !isCategoryCollapsed(category) }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                    </div>
                    <transition name="list-section">
                        <transition-group v-if="!isCategoryCollapsed(category)" name="list-items" tag="div" class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3">
                           <!-- ShoppingListItemCard loop -->
                           <ShoppingListItemCard
                                v-for="item in items"
                                :key="item._id"
                                :item="item"
                                :isReadOnly="activeListStore.isCompleted"
                                @toggle-purchase="toggleItemPurchase(item._id, !item.is_purchased)"
                            >
                            </ShoppingListItemCard>
                        </transition-group>
                    </transition>
                </div>

                <!-- Herkunftsbasierte Einkaufsartikel (Herkunfts-Ansicht) -->
                <div v-if="viewMode === 'source'">
                    <!-- Manuelle Artikel -->
                    <div v-if="pendingManualItems.length > 0" class="mb-6">
                        <div class="flex justify-between items-center mb-3 cursor-pointer" @click="isManualCollapsed = !isManualCollapsed">
                          <h2 class="text-base font-medium text-gray-800">Manuell</h2>
                          <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 transition-transform duration-200"
                                 :class="{ 'rotate-180': !isManualCollapsed }"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        </div>
                        <transition name="list-section">
                            <transition-group v-if="!isManualCollapsed" name="list-items" tag="div" class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3">
                               <!-- ShoppingListItemCard loop -->
                               <ShoppingListItemCard
                                    v-for="item in pendingManualItems"
                                    :key="item._id"
                                    :item="item"
                                    :isReadOnly="activeListStore.isCompleted"
                                    @toggle-purchase="toggleItemPurchase(item._id, !item.is_purchased)"
                                >
                                </ShoppingListItemCard>
                            </transition-group>
                        </transition>
                    </div>

                    <!-- Artikel aus Rezepten -->
                    <div v-if="pendingRecipeItems.length > 0 && !isListCompleted" class="mb-6">
                        <div class="flex justify-between items-center mb-3 cursor-pointer" @click="isRecipeCollapsed = !isRecipeCollapsed">
                          <h2 class="text-base font-medium text-gray-800">Aus Rezepten</h2>
                          <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 transition-transform duration-200"
                                 :class="{ 'rotate-180': !isRecipeCollapsed }"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        </div>
                        <transition name="list-section">
                            <transition-group v-if="!isRecipeCollapsed" name="list-items" tag="div" class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3">
                               <!-- ShoppingListItemCard loop -->
                               <ShoppingListItemCard
                                    v-for="item in pendingRecipeItems"
                                    :key="item._id"
                                    :item="item"
                                    :isReadOnly="activeListStore.isCompleted"
                                    @toggle-purchase="toggleItemPurchase(item._id, !item.is_purchased)"
                                >
                                </ShoppingListItemCard>
                            </transition-group>
                        </transition>
                    </div>
                </div>

                <!-- Erledigt -->
                <div v-if="completedItems.length > 0" class="mb-6">
                     <div class="flex justify-between items-center mb-3 cursor-pointer" @click="isCompletedCollapsed = !isCompletedCollapsed">
                       <h2 class="text-base font-medium text-gray-800">Erledigt</h2>
                       <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 transition-transform duration-200"
                              :class="{ 'rotate-180': !isCompletedCollapsed }"
                              fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                         </svg>
                       </button>
                     </div>
                     <transition name="list-section">
                         <transition-group v-if="!isCompletedCollapsed" name="list-items-done" tag="div" class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3">
                             <!-- ShoppingListItemCard loop -->
                              <ShoppingListItemCard
                                v-for="item in completedItems"
                                :key="item._id"
                                :item="item"
                                :isReadOnly="activeListStore.isCompleted"
                                @toggle-purchase="toggleItemPurchase(item._id, false)"
                                class="opacity-60"
                             >
                             </ShoppingListItemCard>
                         </transition-group>
                     </transition>
                </div>

                 <!-- Empty State (Only shown if kitchentable selected AND list is empty) -->
                <div v-if="(viewMode === 'category' && Object.keys(categorizedItems).length === 0 && completedItems.length === 0) ||
                          (viewMode === 'source' && pendingManualItems.length === 0 && pendingRecipeItems.length === 0 && completedItems.length === 0)"
                     class="info-box text-center py-10 text-gray-500">
                    <p v-if="activeListStore.listId && !activeListStore.isActive">
                       Diese Einkaufsliste ist abgeschlossen. Füge ein neues Item hinzu, um eine neue Liste zu erstellen.
                    </p>
                    <p v-else>
                       Der Einkaufszettel ist leer.
                    </p>
                </div>
            </div>
        </div>
        <!-- === End ELSE for Kitchentable Check === -->

    </div> <!-- End Main Content Area (Left Column) -->


    <!-- Right Sidebar for History (Desktop) -->
    <div class="hidden md:block md:w-1/4 p-6 rounded-lg mt-10 flex-shrink-0 history-sidebar">
      <ShoppingListHistory />
    </div>

    <!-- Mobile History Button and Modal -->
    <div class="md:hidden fixed bottom-40 right-4 z-50">
      <button
        @click="showHistoryModal = true; toggleBodyScroll(true);"
        class="bg-ordypurple-100 text-white rounded-full p-3 shadow-custom shadow-[#E0ADFF] hover:bg-ordypurple-200 transition-colors duration-150"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </button>
    </div>

    <!-- Mobile History Modal -->
    <transition name="fade">
      <div
        v-if="showHistoryModal"
        class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-[100] flex items-end sm:items-center justify-center"
        @click.self="showHistoryModal = false; toggleBodyScroll(false);"
      >
        <transition name="slide-up">
          <div v-if="showHistoryModal" class="bg-white rounded-t-xl sm:rounded-xl w-full max-w-md h-[70vh] sm:max-h-[80vh] flex flex-col overflow-hidden">
        <div class="flex justify-between items-center p-4 border-b sticky top-0 bg-white z-10 rounded-t-xl">
          <h2 class="text-lg font-semibold">Einkaufszettel</h2>
          <button @click="showHistoryModal = false; toggleBodyScroll(false);"
                  class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150 text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-4 overflow-y-auto flex-1">
          <ShoppingListHistory @list-activated="showHistoryModal = false; toggleBodyScroll(false);" />
        </div>
      </div>
        </transition>
      </div>
    </transition>
  </div>
  <!-- End Main Container -->
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useActiveShoppingListStore } from '../store/activeShoppingListStore';
import dayjs from 'dayjs'; // Import dayjs for formatting
import relativeTime from 'dayjs/plugin/relativeTime'; // Optional: for relative time
import 'dayjs/locale/de'; // Optional: for German locale
import { useHelperStore } from '../../utils/helper'; // Import helper store
import ShoppingListItemCard from '../components/ShoppingListItemCard.vue'; // Import the new component
import ShoppingListHistory from '../components/ShoppingListHistory.vue'; // Import the history component
import { useUserStore } from '../store/userStore'; // Import UserStore
import { useConfirmationStore } from '../../utils/helper'; // Import ConfirmationStore
import { categorizeItem } from '../utils/categoryHelper'; // Import categorizeItem function
import { useNetworkMonitor } from '../services/networkMonitor'; // Import NetworkMonitor
import { ingredientService } from '../services/ingredientService'; // Import intelligent ingredient service
import axios from 'axios'; // Import axios

dayjs.extend(relativeTime);
dayjs.locale('de');

// Placeholder für Daten
// const shoppingList = ref(null); // Replaced by store
// const isLoading = ref(false); // Replaced by store
// const error = ref(null); // Replaced by store
const activeListStore = useActiveShoppingListStore();
const helper = useHelperStore(); // Instantiate helper store
const userStore = useUserStore(); // Instantiate UserStore
const confirmationStore = useConfirmationStore(); // Instantiate ConfirmationStore
let cleanupFunction = null; // Für die Bereinigung der Event-Listener

// State for the NEW single add item input field
const newItemInput = ref('');

// State for name editing
const editingListName = ref(false);
const editingName = ref('');
const nameInput = ref(null);

// State for collapsing sections - Add one for completed
const isManualCollapsed = ref(false);
const isRecipeCollapsed = ref(false);
const isCompletedCollapsed = ref(false);
const showHistoryModal = ref(false); // State für das History-Modal in der mobilen Ansicht

// Funktion zum Sperren/Entsperren des Body-Scrollings
const toggleBodyScroll = (lock) => {
  if (lock) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
};
const viewMode = ref('category'); // State für den Ansichtsmodus (category oder source)

// Load collapse state from localStorage on mount
onMounted(async () => {
  console.log('ZettelView mounted');
  isManualCollapsed.value = localStorage.getItem('zettelManualCollapsed') === 'true';
  isRecipeCollapsed.value = localStorage.getItem('zettelRecipeCollapsed') === 'true';
  isCompletedCollapsed.value = localStorage.getItem('zettelCompletedCollapsed') === 'true';

  // Lade den Ansichtsmodus
  const savedViewMode = localStorage.getItem('zettelViewMode');
  if (savedViewMode === 'category' || savedViewMode === 'source') {
    viewMode.value = savedViewMode;
  }

  // Lade den Collapse-Status für alle Kategorien
  categoryOrder.forEach(category => {
    collapsedCategories.value[category] = localStorage.getItem(`zettelCategory_${category}`) === 'true';
  });

  // Sicherstellen, dass der Store geladen wird - aber nur wenn keine Liste bereits geladen ist
  helper.devConsole(`[ZettelView] Mount - Store status: listId=${activeListStore.listId}, isActive=${activeListStore.isActive}, isCompleted=${activeListStore.isCompleted}`);
  if (userStore.user.defaultKitchentable && !activeListStore.listId) {
    helper.devConsole('[ZettelView] No list in store, loading active list on mount');
    await activeListStore.fetchActiveList();
    helper.devConsole(`[ZettelView] After fetch - Store status: listId=${activeListStore.listId}, isActive=${activeListStore.isActive}, isCompleted=${activeListStore.isCompleted}`);
  } else if (activeListStore.listId) {
    helper.devConsole(`[ZettelView] List already in store (${activeListStore.listId}), keeping current list`);
  }
});

// Watch for changes and save to localStorage
watch(isManualCollapsed, (newValue) => {
  localStorage.setItem('zettelManualCollapsed', String(newValue));
});
watch(isRecipeCollapsed, (newValue) => {
  localStorage.setItem('zettelRecipeCollapsed', String(newValue));
});
watch(isCompletedCollapsed, (newValue) => {
  localStorage.setItem('zettelCompletedCollapsed', String(newValue));
});

// Speichere den Ansichtsmodus im localStorage
watch(viewMode, (newValue) => {
  localStorage.setItem('zettelViewMode', newValue);
});

// Watch für Store-Änderungen (Debug)
watch(() => activeListStore.isCompleted, (newValue, oldValue) => {
  helper.devConsole(`[ZettelView] isCompleted changed: ${oldValue} -> ${newValue}`);
});

watch(() => activeListStore.isActive, (newValue, oldValue) => {
  helper.devConsole(`[ZettelView] isActive changed: ${oldValue} -> ${newValue}`);
});

watch(() => activeListStore.listId, (newValue, oldValue) => {
  helper.devConsole(`[ZettelView] listId changed: ${oldValue} -> ${newValue}`);
});

// Computed property to format the creation date
const formattedCreatedAt = computed(() => {
    if (activeListStore.listCreatedAt) {
        // Example format: '16. April 2025, 10:30 Uhr' or relative 'vor 2 Stunden'
        // return dayjs(activeListStore.listCreatedAt).format('D. MMMM YYYY, HH:mm [Uhr]');
        return dayjs(activeListStore.listCreatedAt).fromNow(); // Relative time
    }
    return null;
});

// Computed property to check if current list is completed
const isListCompleted = computed(() => {
    // Check if the current list is marked as completed
    // This should be set when the list is finished via the confirmation dialog
    const completed = activeListStore.isCompleted;
    helper.devConsole(`[ZettelView] isListCompleted computed: ${completed}, listId: ${activeListStore.listId}`);
    return completed || false;
});

// --- NEW Computed properties for item groups ---
const pendingManualItems = computed(() => {
  return activeListStore.items.filter(item => item.is_custom && !item.is_purchased);
});

const pendingRecipeItems = computed(() => {
  return activeListStore.items.filter(item => !item.is_custom && !item.is_purchased);
});

const completedItems = computed(() => {
  return activeListStore.items.filter(item => item.is_purchased);
});

// Kategorie-Reihenfolge
const categoryOrder = [
  'Gemüse & Früchte',
  'Brotwaren & Backwaren',
  'Milchprodukte & Molkereiprodukte',
  'Fleisch, Wurst & Fisch',
  'Tiefkühlprodukte',
  'Grundnahrungsmittel',
  'Frühstück & Cerealien',
  'Süsswaren & Snacks',
  'Getränke',
  'Non-Food & Haushaltsartikel',
  'Sonstiges'
];

// Kategorisierte Artikel
const categorizedItems = computed(() => {
  // Erstelle ein Objekt mit allen Kategorien als leere Arrays
  const result = {};
  categoryOrder.forEach(category => {
    result[category] = [];
  });

  // Füge alle nicht erledigten Artikel in die entsprechenden Kategorien ein
  const pendingItems = activeListStore.items.filter(item => !item.is_purchased);
  pendingItems.forEach(item => {
    const category = item.category || 'Sonstiges';
    if (result[category]) {
      result[category].push(item);
    } else {
      // Falls eine Kategorie nicht in der vordefinierten Liste ist
      result['Sonstiges'].push(item);
    }
  });

  // Entferne leere Kategorien
  const filteredResult = {};
  categoryOrder.forEach(category => {
    if (result[category].length > 0) {
      filteredResult[category] = result[category];
    }
  });

  return filteredResult;
});

// State für das Ein-/Ausklappen von Kategorien
const collapsedCategories = ref({});

// Funktion zum Umschalten des Collapse-Status einer Kategorie
const toggleCategoryCollapse = (category) => {
  collapsedCategories.value[category] = !collapsedCategories.value[category];
  localStorage.setItem(`zettelCategory_${category}`, collapsedCategories.value[category]);
};

// Funktion zum Prüfen, ob eine Kategorie eingeklappt ist
const isCategoryCollapsed = (category) => {
  return collapsedCategories.value[category] === true;
};

// --- NEW Parsing Function ---
const parseItemInput = (input) => {
  let text = input.trim().toLowerCase();
  let name = '';
  let quantity = 1;
  let unit = 'Stk'; // Default unit

  const numberWords = {
    'ein': 1, 'eine': 1, 'eins': 1,
    'zwei': 2, 'drei': 3, 'vier': 4, 'fünf': 5, 'sechs': 6,
    'sieben': 7, 'acht': 8, 'neun': 9, 'zehn': 10,
    'elf': 11, 'zwölf': 12
    // Add more if needed
  };

  const knownUnits = ['g', 'kg', 'ml', 'l', 'stk', 'stück', 'prise', 'el', 'tl', 'pck', 'packung', 'bund'];

  // Try to find quantity (digits or words) and optional unit
  // Regex to find number (digits or words) optionally followed by a known unit
  const regex = new RegExp(`(^|\s)(\d+|${Object.keys(numberWords).join('|')})(?:\s*(${knownUnits.join('|')}))?(?:\s|$)`, 'i');
  const match = text.match(regex);

  if (match) {
    const numberStr = match[2];
    quantity = numberWords[numberStr] !== undefined ? numberWords[numberStr] : parseInt(numberStr, 10);
    if (match[3]) { // Unit found
      unit = match[3];
      // Normalize common units
      if (unit === 'stück') unit = 'Stk';
      if (unit === 'packung') unit = 'Pck';
    }
    // Remove matched part (number and optional unit) from text to get the name
    text = text.replace(match[0], ' ').trim();
  }

  // What remains is the name
  name = text;

  // Basic capitalization for the name
  if (name) {
    name = name.charAt(0).toUpperCase() + name.slice(1);
  }

  // Fallback if name is empty after parsing but input wasn't empty
  if (!name && input.trim()) {
      name = input.trim().charAt(0).toUpperCase() + input.trim().slice(1);
      quantity = 1; // Reset quantity if only name was given
      unit = 'Stk';
  }

  return {
    name: name || 'Unbenannt', // Provide a fallback name
    quantity: isNaN(quantity) || quantity <= 0 ? 1 : quantity, // Ensure quantity is valid
    unit: unit || 'Stk' // Ensure unit is not empty
  };
};
// --- End Parsing Function ---

// Method to handle adding a manual item (ENHANCED WITH AI)
async function handleAddManualItem() {
    if (!newItemInput.value.trim()) {
        // Optional: show a warning if input is empty
        // setNotification('Bitte Eingabe tätigen.', 'warning');
        return;
    }

    // Prüfen, ob die Liste abgeschlossen ist
    if (isListCompleted.value) {
        helper.devConsole('Cannot add items to completed list');
        if (window.showToast) {
            window.showToast('Abgeschlossene Listen können nicht bearbeitet werden', 'error');
        }
        return;
    }

    // Prüfen, ob aktive Liste existiert
    if (activeListStore.listId && !activeListStore.isActive) {
        helper.devConsole('Trying to add item to inactive list - should create new list automatically');
    }

    const userInput = newItemInput.value.trim();
    helper.devConsole('Processing ingredient input:', userInput);

    try {
        // Verwende den intelligenten Ingredient Service
        const processedIngredient = await ingredientService.processIngredient(userInput);

        helper.devConsole('Processed ingredient result:', processedIngredient);
        helper.devConsole(`Source: ${processedIngredient.source}`);

        // Zeige dem User kurz an, welche Quelle verwendet wurde (optional)
        if (processedIngredient.source === 'ai' && window.showToast) {
            window.showToast(`KI-Kategorisierung: ${processedIngredient.category}`, 'info', 2000);
        }

        activeListStore.addManualItem({
            name: processedIngredient.name,
            quantity: String(processedIngredient.quantity), // Ensure quantity is string for API
            unit: processedIngredient.unit,
            category: processedIngredient.category
        });

        // Clear form field after submission
        newItemInput.value = '';

    } catch (error) {
        helper.devConsole('Error processing ingredient:', error);

        // Fallback zur alten Methode bei Fehlern
        helper.devConsole('Falling back to local parsing and categorization');

        const parsedItem = parseItemInput(userInput);
        const category = categorizeItem(parsedItem.name);

        helper.devConsole('Fallback - Parsed Item:', parsedItem);
        helper.devConsole('Fallback - Categorized as:', category);

        activeListStore.addManualItem({
            name: parsedItem.name,
            quantity: String(parsedItem.quantity),
            unit: parsedItem.unit,
            category: category
        });

        // Clear form field after submission
        newItemInput.value = '';

        if (window.showToast) {
            window.showToast('Zutat hinzugefügt (lokale Kategorisierung)', 'warning', 3000);
        }
    }
}

// Neue Funktion, um manuell eine neue Liste zu erstellen
function createNewList() {
    helper.devConsole('Creating new shopping list by reloading page');
    // 🔧 KRITISCH: Einfacher Seiten-Reload um neue Liste zu erstellen
    // Das Backend erstellt automatisch eine neue aktive Liste wenn keine vorhanden ist
    window.location.reload();
}

// Name editing functions
const startEditName = () => {
    editingListName.value = true;
    editingName.value = activeListStore.listName || '';
    // Focus input in next tick
    nextTick(() => {
        if (nameInput.value) {
            nameInput.value.focus();
            nameInput.value.select();
        }
    });
};

const cancelEditName = () => {
    editingListName.value = false;
    editingName.value = '';
};

const saveListName = async () => {
    if (!editingName.value.trim()) {
        cancelEditName();
        return;
    }

    try {
        const response = await axios.put(
            `${import.meta.env.VITE_API_BASE_URL}/api/shopping-lists/${activeListStore.listId}/name`,
            { name: editingName.value.trim() }
        );

        if (response.data.success) {
            // Update store
            activeListStore.listName = editingName.value.trim();

            helper.devConsole(`Updated list name: ${editingName.value}`);

            if (window.showToast) {
                window.showToast('Listenname gespeichert', 'success');
            }
        } else {
            if (window.showToast) {
                window.showToast('Fehler beim Speichern des Namens', 'error');
            }
        }
    } catch (err) {
        helper.devConsole('Error updating list name:', err);
        if (window.showToast) {
            window.showToast('Fehler beim Speichern des Namens', 'error');
        }
    } finally {
        cancelEditName();
    }
};

// Complete list function
const completeList = async () => {
    if (!activeListStore.listId) return;

    // Show confirmation dialog
    const confirmed = await confirmationStore.showConfirmation(
        'Liste abschließen',
        'Möchten Sie diese Einkaufsliste wirklich abschließen? Abgeschlossene Listen können nicht mehr bearbeitet werden.',
        'Ja, abschließen',
        'Abbrechen'
    );

    if (!confirmed) return;

    try {
        const response = await axios.put(
            `${import.meta.env.VITE_API_BASE_URL}/api/shopping-lists/${activeListStore.listId}/finish`
        );

        if (response.data.success) {
            // Update store
            activeListStore.isCompleted = true;
            activeListStore.isActive = false;

            helper.devConsole(`Completed list ${activeListStore.listId}`);

            if (window.showToast) {
                window.showToast('Einkaufsliste wurde abgeschlossen', 'success');
            }
        } else {
            if (window.showToast) {
                window.showToast('Fehler beim Abschließen der Liste', 'error');
            }
        }
    } catch (err) {
        helper.devConsole('Error completing list:', err);
        if (window.showToast) {
            window.showToast('Fehler beim Abschließen der Liste', 'error');
        }
    }
};

// --- NEW Toggle Purchase Handler ---
const toggleItemPurchase = async (itemId, newPurchaseStatus) => {
    // Blockiere Änderungen für abgeschlossene Listen
    if (activeListStore.isCompleted) {
        helper.devConsole(`ZettelView: Cannot toggle item in completed list`);
        if (window.showToast) {
            window.showToast('Diese Liste ist abgeschlossen und kann nicht bearbeitet werden', 'info');
        }
        return;
    }

    helper.devConsole(`ZettelView: toggleItemPurchase called for item ${itemId} to ${newPurchaseStatus}`);
    try {
        await activeListStore.updateItemPurchasedStatus(itemId, newPurchaseStatus);
        // The logic for checking list completion and showing confirmation will be moved to a watcher.
    } catch (error) {
        helper.devConsole('ZettelView: Error in toggleItemPurchase:', error);
    }
};
// ---------------------------------

// NEW: Watcher for the defaultKitchentable ID
watch(() => userStore.user.defaultKitchentable, (newKitchentableId, oldKitchentableId) => {
    helper.devConsole(`ZettelView Watcher: defaultKitchentable changed from ${oldKitchentableId} to ${newKitchentableId}`);

    // Nur bei tatsächlicher Änderung reagieren, nicht beim ersten Mount
    if (oldKitchentableId !== undefined && newKitchentableId !== oldKitchentableId) {
        if (newKitchentableId) {
            // Fetch list only if kitchentable actually changed
            helper.devConsole('ZettelView Watcher: Kitchentable changed, fetching active list');
            activeListStore.fetchActiveList();
        } else {
            // Clear list if no kitchentable is selected
            helper.devConsole('ZettelView Watcher: No kitchentable selected, clearing list');
            activeListStore.resetList();
        }
    } else {
        helper.devConsole('ZettelView Watcher: No actual change or initial mount, keeping current state');
    }
});

// Verbesserte Funktion zur Behandlung von Sichtbarkeitsänderungen (App im Vordergrund/Hintergrund)
const networkMonitor = useNetworkMonitor();
const connectionStatus = ref(networkMonitor.getIsOnline());
const isReconnecting = ref(false);

// Netzwerkstatus-Listener
const handleNetworkStatusChange = (isOnline) => {
  helper.devConsole(`ZettelView: Netzwerkstatus geändert: ${isOnline ? 'Online' : 'Offline'}`);
  connectionStatus.value = isOnline;

  if (isOnline && activeListStore.listId) {
    // Wenn wir wieder online sind und eine aktive Liste haben, versuchen wir die Verbindung wiederherzustellen
    handleReconnect();
  }
};

// Funktion zur Wiederherstellung der Verbindung
const handleReconnect = async () => {
  if (isReconnecting.value) return; // Verhindere mehrfache gleichzeitige Wiederverbindungsversuche

  isReconnecting.value = true;
  helper.devConsole('ZettelView: Versuche, die Verbindung wiederherzustellen...');

  try {
    // Kurze Verzögerung, um sicherzustellen, dass das Gerät Zeit hatte, die Netzwerkverbindung wiederherzustellen
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Überprüfen und wiederherstellen der WebSocket-Verbindung
    await activeListStore.checkAndRestoreConnection();

    helper.devConsole('ZettelView: Verbindung wiederhergestellt.');
  } catch (error) {
    helper.devConsole(`ZettelView: Fehler beim Wiederherstellen der Verbindung: ${error.message}`);
    // Trotz Fehler versuchen wir, die Liste zu laden
    activeListStore.fetchActiveList();
  } finally {
    isReconnecting.value = false;
  }
};

// Verbesserte Funktion zur Behandlung von Sichtbarkeitsänderungen
const handleVisibilityChange = async () => {
  if (document.visibilityState === 'visible') {
    helper.devConsole('ZettelView: App ist jetzt sichtbar (Vordergrund).');

    // Wenn wir eine aktive Liste haben, überprüfen wir die Verbindung
    if (activeListStore.listId) {
      // Netzwerkstatus aktualisieren
      connectionStatus.value = networkMonitor.getIsOnline();

      if (connectionStatus.value) {
        // Wenn wir online sind, versuchen wir die Verbindung wiederherzustellen
        handleReconnect();
      } else {
        // Wenn wir offline sind, laden wir die Liste trotzdem neu, um lokale Änderungen anzuzeigen
        helper.devConsole('ZettelView: Gerät ist offline. Lade Liste neu, um lokale Änderungen anzuzeigen.');
        activeListStore.fetchActiveList();
      }
    }
  }
};

onMounted(() => {
  console.log('ZettelView mounted');
  isManualCollapsed.value = localStorage.getItem('zettelManualCollapsed') === 'true';
  isRecipeCollapsed.value = localStorage.getItem('zettelRecipeCollapsed') === 'true';
  isCompletedCollapsed.value = localStorage.getItem('zettelCompletedCollapsed') === 'true';

  // Lade den Ansichtsmodus
  const savedViewMode = localStorage.getItem('zettelViewMode');
  if (savedViewMode === 'category' || savedViewMode === 'source') {
    viewMode.value = savedViewMode;
  }

  // Lade den Collapse-Status für alle Kategorien
  categoryOrder.forEach(category => {
    collapsedCategories.value[category] = localStorage.getItem(`zettelCategory_${category}`) === 'true';
  });

  // Note: fetchActiveList() is already called above if needed

  // Attempt to connect WebSocket if we have a list ID
  if (activeListStore.listId && !activeListStore.isConnected) {
    activeListStore.connectWebSocket();
  }

  // Netzwerkstatus-Listener hinzufügen
  const removeNetworkListener = networkMonitor.addStatusListener(handleNetworkStatusChange);

  // Event-Listener für Sichtbarkeitsänderungen hinzufügen (App im Vordergrund/Hintergrund)
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Cleanup-Funktion für onUnmounted speichern
  cleanupFunction = () => {
    removeNetworkListener();
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
});

onUnmounted(() => {
  console.log('ZettelView unmounted');
  // WebSocket disconnect is handled by the store internally now

  // Event-Listener entfernen
  if (cleanupFunction) {
    cleanupFunction();
  }
});

</script>

<style scoped>
.error-message {
  color: red;
  /* Add other styling as needed */
}
.warning-message {
    color: orange;
}

.add-item-form {
    margin-top: 1rem;
    margin-bottom: 1rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.add-item-form input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.item-list {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-bottom: 1px solid #eee;
}

.list-item.purchased .item-info {
  text-decoration: line-through;
  color: #888;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Abstand zwischen Elementen */
}

.item-checkbox {
    cursor: pointer;
    /* Add more styling as needed */
}

.item-name {
  font-weight: bold;
}

.item-details {
  font-size: 0.9em;
  color: #555;
}

.list-item.purchased .item-details {
    color: #aaa;
}

.item-origin {
    font-size: 0.8em;
    color: #aaa;
    margin-left: auto; /* Schiebt Herkunft nach rechts */
}

.list-item.custom .item-origin {
    /* Optional: Andere Farbe für manuelle Herkunft */
    opacity: 0.9;
}

.delete-button {
    background: none;
    border: none;
    color: red;
    cursor: pointer;
    font-size: 1rem;
    margin-left: 0.5rem; /* Kleiner Abstand zum Herkunfts-Text */
    padding: 0;
    line-height: 1;
}

.delete-button:hover {
    color: darkred;
}

.info-box {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: center;
}

.item-section + .item-section {
  margin-top: 1.5rem; /* Add space between sections */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.3rem;
}

.section-title {
  font-weight: bold;
  font-size: 1.1em;
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

/* Toggle buttons now use inline Tailwind classes for modern design */

/* Gradient background for history sidebar */
.history-sidebar {
  background: linear-gradient(to bottom, #ffffff, #f5f5f5);
  border: 1px solid #f0f0f0;
}

.add-item-form {
    /* Adjust styling if needed for single input */
    gap: 0.5rem;
}

.single-item-input {
    flex-grow: 1; /* Allow input to take available space */
    padding: 0.5rem;
    border-radius: 4px;
}

/* Remove styles for old separate inputs if they exist */

/* Add component-specific styles here */

/* --- List Transition Animation --- */
.list-manual-enter-active,
.list-manual-leave-active {
  transition: all 0.5s ease; /* Transition duration and easing */
}
.list-manual-enter-from,
.list-manual-leave-to {
  opacity: 0;
  transform: translateY(-20px); /* Start slightly above */
}
/* Optional: Ensure leaving items don't disrupt layout */
.list-manual-leave-active {
  position: absolute;
  width: 100%; /* Adjust if needed */
}
/* --- End List Transition --- */

/* Minimal styles for the grid layout */
.grid {
  display: grid;
}

/* --- Item Transition Animation --- */
.list-items-enter-active,
.list-items-leave-active,
.list-items-done-enter-active,
.list-items-done-leave-active {
  transition: all 0.4s ease;
}
.list-items-enter-from,
.list-items-leave-to,
.list-items-done-enter-from,
.list-items-done-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
/* Ensure smooth layout shifts */
.list-items-leave-active {
  position: relative;
}

/* --- Section Collapse/Expand Animation --- */
.list-section-enter-active,
.list-section-leave-active {
  transition: all 0.3s ease-out;
  max-height: 1000px; /* Adjust if needed for very long lists */
  overflow: hidden;
}

.list-section-enter-from,
.list-section-leave-to {
  opacity: 0;
  max-height: 0;
  transform: scaleY(0.9);
}

/* --- Toggle Button Rotation (already done via class binding) --- */
/* Removed comments for potential linting issues */

/* --- Slide Up Animation for History Modal --- */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease-out;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100px);
  opacity: 0;
}

/* --- Fade Animation for Modal Background --- */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Fix for empty rulesets */
.list-items-leave-active {
  position: relative;
}

.grid {
  display: grid;
}

.list-item.custom .item-origin {
  display: inline-block;
}

/* --- View Mode Switch Styles --- */
.view-mode-switch {
  display: flex;
  align-items: center;
}

.switch-container {
  position: relative;
  width: auto;
  min-width: 180px;
  height: 36px;
}

.switch-track {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f3f4f6;
  border-radius: 18px;
  cursor: pointer;
  padding: 2px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.switch-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 2;
}

.switch-labels span {
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-size: 0.875rem;
  color: #4b5563; /* Dunkleres Grau für besseren Kontrast zum aktiven Weiß */
  transition: color 0.3s ease;
  white-space: nowrap;
  cursor: pointer;
}

.switch-labels .icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.switch-label {
  font-size: 0.875rem;
}

/* Aktives Label mit höherer Spezifität */
.switch-labels .switch-label.switch-label-active,
.switch-labels .switch-label.switch-label-active .icon,
.switch-labels .switch-label.switch-label-active span {
  color: white !important;
  font-weight: 600;
  font-size: 0.75rem;
  letter-spacing: 0.005em;
}

.switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 50%;
  height: calc(100% - 4px);
  background-color: #8b5cf6; /* Original Ordy-Purple-100 */
  border-radius: 16px;
  transition: transform 0.3s ease, width 0.2s ease;
  z-index: 1;
}

@media (max-width: 640px) {
  .switch-container {
    min-width: 120px;
  }

  .switch-thumb {
    width: 50%;
  }

  .switch-labels span {
    padding: 0 8px;
    justify-content: center;
    font-size: 0.75rem;
  }
}

</style>