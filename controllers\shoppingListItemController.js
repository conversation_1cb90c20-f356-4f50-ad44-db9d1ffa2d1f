const ShoppingListItem = require('../models/shoppingListItem');
const ShoppingList = require('../models/shoppingList');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');

// Hilfsfunktion zur Kategorisierung von Artikeln
const categorizeItem = (name) => {
  name = name.toLowerCase();
  
  // Gemüse & Früchte
  if (/apfel|birne|banane|orange|zitrone|tomate|gurke|salat|karotte|zwiebel|knoblauch|paprika|broccoli|spinat|kohl|pilz/i.test(name)) {
    return 'Gemüse & Früchte';
  }
  
  // Brotwaren & Backwaren
  if (/brot|brötchen|gipfeli|kuchen|sandwich|toast|croissant|bagel|wecken|zopf/i.test(name)) {
    return 'Brotwaren & Backwaren';
  }
  
  // Milchprodukte & Molkereiprodukte
  if (/milch|joghurt|butter|rahm|käse|quark|sahne|schmand|frischkäse|mozzarella|parmesan/i.test(name)) {
    return 'Milchprodukte & Molkereiprodukte';
  }
  
  // Fleisch, Wurst & Fisch
  if (/fleisch|wurst|schinken|speck|fisch|lachs|thunfisch|hähnchen|rind|schwein|lamm|salami/i.test(name)) {
    return 'Fleisch, Wurst & Fisch';
  }
  
  // Tiefkühlprodukte
  if (/tiefkühl|tiefgekühlt|glacé|eis|pizza|tk/i.test(name)) {
    return 'Tiefkühlprodukte';
  }
  
  // Grundnahrungsmittel
  if (/mehl|zucker|salz|öl|essig|reis|pasta|nudel|spaghetti|konserve|dose|tomatenmark|gewürz/i.test(name)) {
    return 'Grundnahrungsmittel';
  }
  
  // Frühstück & Cerealien
  if (/müsli|cornflakes|flocken|marmelade|honig|nutella|aufstrich|sirup/i.test(name)) {
    return 'Frühstück & Cerealien';
  }
  
  // Süsswaren & Snacks
  if (/schokolade|bonbon|chips|nüsse|riegel|keks|süssigkeit|snack|popcorn/i.test(name)) {
    return 'Süsswaren & Snacks';
  }
  
  // Getränke
  if (/wasser|saft|bier|wein|getränk|cola|fanta|sprite|kaffee|tee|milch/i.test(name)) {
    return 'Getränke';
  }
  
  // Non-Food & Haushaltsartikel
  if (/putzmittel|waschmittel|seife|shampoo|toilettenpapier|küchenpapier|serviette|zahnpasta/i.test(name)) {
    return 'Non-Food & Haushaltsartikel';
  }
  
  // Fallback
  return 'Sonstiges';
};

// Bestehende Controller-Funktionen erweitern

// Artikel erstellen mit automatischer Kategorisierung
exports.createItem = catchAsync(async (req, res, next) => {
  const { name, quantity, unit, is_custom, recipe } = req.body;
  const { listId } = req.params;
  
  // Prüfe, ob Liste existiert
  const list = await ShoppingList.findById(listId);
  if (!list) {
    return next(new AppError('Einkaufsliste nicht gefunden', 404));
  }
  
  // Automatische Kategorisierung
  const category = categorizeItem(name);
  
  // Erstelle Artikel
  const item = await ShoppingListItem.create({
    name,
    quantity: quantity || '1',
    unit: unit || 'Stk',
    is_custom: is_custom !== undefined ? is_custom : true,
    list: listId,
    recipe: recipe || null,
    category
  });
  
  res.status(201).json({
    status: 'success',
    data: item
  });
});

// Bestehende Artikel kategorisieren (für Migration)
exports.categorizeExistingItems = catchAsync(async (req, res, next) => {
  const items = await ShoppingListItem.find({ category: { $exists: false } });
  
  let updated = 0;
  for (const item of items) {
    item.category = categorizeItem(item.name);
    await item.save();
    updated++;
  }
  
  res.status(200).json({
    status: 'success',
    message: `${updated} Artikel wurden kategorisiert`
  });
});