const MarketingContent = require('../models/marketingContentModel');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const helper = require('../utils/helper');
const cron = require('node-cron');
const axios = require('axios'); // Import axios

// --- TikTok Service Implementierung ---
const tiktokService = {
    publishVideo: async (videoUrl, title, description) => {
        helper.devConsole('[tiktokService.publishVideo] Attempting to publish to TikTok:', { videoUrl, title, description });

        // Check for Sandbox mode using simple flag
        const isSandboxMode = process.env.IS_SANDBOX_MODE_TIKTOK === 'true';

        helper.devConsole('[tiktokService.publishVideo] Mode check:', {
            isSandboxMode,
            sandboxFlag: process.env.IS_SANDBOX_MODE_TIKTOK,
            hasAccessToken: !!process.env.TIKTOK_ACCESS_TOKEN,
            hasOpenId: !!process.env.TIKTOK_OPEN_ID
        });

        if (isSandboxMode) {
            // Sandbox mode - simulate publishing
            helper.devConsole('[tiktokService.publishVideo] SANDBOX MODE: Simulating TikTok publish');

            // Simulate a successful publish for sandbox
            return {
                success: true,
                platformPostId: `sandbox_tiktok_${Date.now()}`,
                status: 'SANDBOX_PUBLISHED',
                message: 'Successfully published to TikTok Sandbox (simulated)',
                sandbox: true,
                mode: 'sandbox'
            };
        }

        // Production mode
        const accessToken = process.env.TIKTOK_ACCESS_TOKEN;
        // Die Open ID wird hier nicht direkt für den Post benötigt, aber gut sie parat zu haben oder für andere API Calls
        // const openId = process.env.TIKTOK_OPEN_ID;

        if (!accessToken) {
            helper.devConsole('[tiktokService.publishVideo] ERROR: TIKTOK_ACCESS_TOKEN is not defined in .env');
            return { success: false, error: 'TikTok Access Token not configured for production mode.' };
        }
        if (!videoUrl) {
            helper.devConsole('[tiktokService.publishVideo] ERROR: videoUrl is missing.');
            return { success: false, error: 'Video URL is missing for TikTok publish.' };
        }

        try {
            // Schritt 1: Creator Info abfragen (Optional, aber gute Praxis laut Doku)
            // Dies dient auch als Test, ob der Access Token valide ist.
            helper.devConsole('[tiktokService.publishVideo] Querying creator info...');
            const creatorInfoResponse = await axios.post(
                'https://open.tiktokapis.com/v2/post/publish/creator_info/query/',
                {},
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json; charset=UTF-8'
                    }
                }
            );

            if (creatorInfoResponse.data.error && creatorInfoResponse.data.error.code !== 'ok') {
                helper.devConsole('[tiktokService.publishVideo] ERROR querying creator info:', creatorInfoResponse.data.error);
                return { success: false, error: `TikTok API Error (Creator Info): ${creatorInfoResponse.data.error.message} (Code: ${creatorInfoResponse.data.error.code})` };
            }
            helper.devConsole('[tiktokService.publishVideo] Creator info queried successfully:', creatorInfoResponse.data.data);
            const maxDurationSec = creatorInfoResponse.data.data?.max_video_post_duration_sec || 300;
            // Hier könnte man die Videolänge prüfen, falls bekannt, ist aber für Pull-From-URL eher nachgelagert.

            // Schritt 2: Video-Post initiieren (PULL_FROM_URL)
            helper.devConsole('[tiktokService.publishVideo] Initializing video post with PULL_FROM_URL...');
            const postData = {
                post_info: {
                    title: title || 'Neues Ordy Rezeptvideo!', // Fallback-Titel
                    // description: description || '', // Beschreibung ist laut Doku optional im post_info, oft im Titel mit Hashtags
                    privacy_level: 'PUBLIC_TO_EVERYONE', // Oder eine andere Option aus creatorInfoResponse.data.data.privacy_level_options
                    disable_comment: false,
                    disable_duet: false,
                    disable_stitch: false,
                    // video_cover_timestamp_ms: 1000, // Optional: Zeitstempel in ms für das Video-Coverbild
                },
                source_info: {
                    source: 'PULL_FROM_URL',
                    video_url: videoUrl, // Stelle sicher, dass diese URL von einer bei TikTok verifizierten Domain kommt!
                }
            };

            const videoInitResponse = await axios.post(
                'https://open.tiktokapis.com/v2/post/publish/video/init/',
                postData,
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json; charset=UTF-8'
                    }
                }
            );

            if (videoInitResponse.data.error && videoInitResponse.data.error.code !== 'ok') {
                helper.devConsole('[tiktokService.publishVideo] ERROR initializing video post:', videoInitResponse.data.error);
                return { success: false, error: `TikTok API Error (Video Init): ${videoInitResponse.data.error.message} (Code: ${videoInitResponse.data.error.code})` };
            }

            const publishId = videoInitResponse.data.data.publish_id;
            if (!publishId) {
                helper.devConsole('[tiktokService.publishVideo] ERROR: No publish_id received from TikTok.', videoInitResponse.data);
                return { success: false, error: 'No publish_id received from TikTok after video init.' };
            }
            helper.devConsole('[tiktokService.publishVideo] Video post initiated successfully. Publish ID:', publishId);

            // Schritt 3: Post-Status prüfen (optional, aber empfohlen für PULL_FROM_URL, da asynchron)
            // Wir warten hier ein paar Sekunden, bevor wir den Status abfragen, um TikTok Zeit zur Verarbeitung zu geben.
            await new Promise(resolve => setTimeout(resolve, 15000)); // 15 Sekunden warten

            helper.devConsole(`[tiktokService.publishVideo] Fetching post status for publish_id: ${publishId}...`);
            const statusResponse = await axios.post(
                'https://open.tiktokapis.com/v2/post/publish/status/fetch/',
                { publish_id: publishId },
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json; charset=UTF-8'
                    }
                }
            );

            if (statusResponse.data.error && statusResponse.data.error.code !== 'ok') {
                helper.devConsole('[tiktokService.publishVideo] WARN: Could not fetch post status or error in status:', statusResponse.data.error);
                // Trotzdem als potenziell erfolgreich werten, da der Init erfolgreich war.
                // Die App sollte ggf. einen Mechanismus haben, fehlgeschlagene Posts später zu prüfen.
                return { success: true, platformPostId: publishId, warning: `Could not confirm final post status: ${statusResponse.data.error.message}` };
            }

            const postStatus = statusResponse.data.data.status;
            helper.devConsole('[tiktokService.publishVideo] Post status fetched:', postStatus);

            if (postStatus === 'PUBLISH_COMPLETE') {
                return { success: true, platformPostId: publishId };
            } else if (postStatus === 'FAILED' || postStatus === 'AUDIT_FAILED') {
                return { success: false, error: `TikTok post failed or audit failed. Status: ${postStatus}`, platformPostId: publishId };
            } else {
                // PROCESSING, AWAITING_UPLOAD, AWAITING_COVER_UPLOAD, AUDITING, etc.
                // Für PULL_FROM_URL sollte es relativ schnell zu PUBLISH_COMPLETE oder FAILED gehen.
                return { success: true, platformPostId: publishId, warning: `TikTok post is still processing. Status: ${postStatus}` };
            }

        } catch (error) {
            helper.devConsole('[tiktokService.publishVideo] EXCEPTION during TikTok API call:', error.isAxiosError ? error.response?.data || error.message : error.message);
            return {
                success: false,
                error: `Exception during TikTok publish: ${error.isAxiosError ? error.response?.data?.error?.message || error.message : error.message}`
            };
        }
    }
};

const facebookService = {
    publishVideo: async (videoUrl, title, description) => {
        helper.devConsole('[facebookService.publishVideo] Attempting to publish to Facebook:', { videoUrl, title, description });

        const accessToken = process.env.FACEBOOK_ACCESS_TOKEN || process.env.INSTAGRAM_TOKEN; // Fallback to Instagram token
        const pageId = process.env.FACEBOOK_PAGE_ID;
        const apiVersion = 'v22.0';

        // Debug-Logs für Token und Page-ID
        helper.devConsole('[DEBUG] Used FACEBOOK_ACCESS_TOKEN:', accessToken ? 'present' : 'missing');
        helper.devConsole('[DEBUG] Used FACEBOOK_PAGE_ID:', pageId);

        if (!accessToken || !pageId) {
            helper.devConsole('[facebookService.publishVideo] ERROR: FACEBOOK_ACCESS_TOKEN or FACEBOOK_PAGE_ID not defined in .env');
            return { success: false, error: 'Facebook credentials not configured. Please set FACEBOOK_ACCESS_TOKEN and FACEBOOK_PAGE_ID.' };
        }
        if (!videoUrl) {
            helper.devConsole('[facebookService.publishVideo] ERROR: videoUrl is missing.');
            return { success: false, error: 'Video URL is missing for Facebook publish.' };
        }

        try {
            // Facebook Graph API - Video Upload
            // Schritt 1: Video Upload initialisieren
            const initUploadUrl = `https://graph.facebook.com/${apiVersion}/${pageId}/videos`;
            const initPayload = {
                upload_phase: 'start',
                access_token: accessToken
            };

            helper.devConsole('[facebookService.publishVideo] Initializing video upload...');
            const initResponse = await axios.post(initUploadUrl, initPayload);

            if (!initResponse.data || !initResponse.data.video_id) {
                helper.devConsole('[facebookService.publishVideo] ERROR: Failed to initialize video upload:', initResponse.data);
                return { success: false, error: 'Failed to initialize Facebook video upload.' };
            }

            const videoId = initResponse.data.video_id;
            const uploadSessionId = initResponse.data.upload_session_id;
            helper.devConsole('[facebookService.publishVideo] Video upload initialized. Video ID:', videoId);

            // Schritt 2: Video-Datei von URL herunterladen und hochladen
            helper.devConsole('[facebookService.publishVideo] Downloading video from URL...');
            const videoResponse = await axios.get(videoUrl, { responseType: 'stream' });

            // Schritt 3: Video-Daten an Facebook senden
            const uploadUrl = `https://graph.facebook.com/${apiVersion}/${pageId}/videos`;
            const uploadPayload = {
                upload_phase: 'transfer',
                upload_session_id: uploadSessionId,
                video_file_chunk: videoResponse.data,
                access_token: accessToken
            };

            helper.devConsole('[facebookService.publishVideo] Uploading video data...');
            const uploadResponse = await axios.post(uploadUrl, uploadPayload, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            // Schritt 4: Upload abschließen und Post erstellen
            const finishPayload = {
                upload_phase: 'finish',
                upload_session_id: uploadSessionId,
                title: title,
                description: `${title}${description ? '\n\n' + description : ''}`,
                published: true,
                access_token: accessToken
            };

            helper.devConsole('[facebookService.publishVideo] Finishing upload and publishing...');
            const finishResponse = await axios.post(uploadUrl, finishPayload);

            if (!finishResponse.data || !finishResponse.data.id) {
                helper.devConsole('[facebookService.publishVideo] ERROR: Failed to finish video upload:', finishResponse.data);
                return { success: false, error: 'Failed to finish Facebook video upload.' };
            }

            const postId = finishResponse.data.id;
            helper.devConsole('[facebookService.publishVideo] Video published successfully. Post ID:', postId);

            return {
                success: true,
                platformPostId: postId,
                message: 'Successfully published to Facebook',
                videoId: videoId
            };

        } catch (error) {
            helper.devConsole('[facebookService.publishVideo] EXCEPTION during Facebook API call:', error.isAxiosError ? error.response?.data || error.message : error.message);

            // Detaillierte Fehlerbehandlung
            let errorMessage = 'Unknown Facebook API error';
            if (error.isAxiosError && error.response?.data) {
                errorMessage = error.response.data.error?.message || error.response.data.message || JSON.stringify(error.response.data);
            } else {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: `Facebook publish failed: ${errorMessage}`
            };
        }
    }
};

const instagramService = {
    publishVideo: async (videoUrl, title, description) => {
        helper.devConsole('[instagramService.publishVideo] Attempting to publish to Instagram:', { videoUrl, title, description });

        const accessToken = process.env.INSTAGRAM_TOKEN;
        const instagramUserId = process.env.INSTAGRAM_USER_ID;
        const apiVersion = 'v22.0';

        // NEU: Debug-Logs für Token und User-ID
        helper.devConsole('[DEBUG] Used INSTAGRAM_TOKEN:' + accessToken);
        helper.devConsole('[DEBUG] Used INSTAGRAM_USER_ID:' + instagramUserId);

        if (!accessToken || !instagramUserId) {
            helper.devConsole('[instagramService.publishVideo] ERROR: INSTAGRAM_TOKEN or INSTAGRAM_USER_ID not defined in .env');
            return { success: false, error: 'Instagram Token or User ID not configured.' };
        }
        if (!videoUrl) {
            helper.devConsole('[instagramService.publishVideo] ERROR: videoUrl is missing.');
            return { success: false, error: 'Video URL is missing for Instagram publish.' };
        }

        try {
            // Schritt 1: Media-Container erstellen
            const createContainerUrl = `https://graph.facebook.com/${apiVersion}/${instagramUserId}/media`;
            const containerPayload = {
                video_url: videoUrl,
                media_type: 'REELS',
                caption: `${title}${description ? '\n\n' + description : ''}`,
                access_token: accessToken
            };

            // NEU: Logge die Payload und die URL
            helper.devConsole('[DEBUG] Instagram createContainerUrl:', createContainerUrl);
            helper.devConsole('[DEBUG] Instagram containerPayload:', containerPayload);

            const containerResponse = await axios.post(createContainerUrl, containerPayload);

            // NEU: Logge die komplette Antwort
            helper.devConsole('[DEBUG] Instagram containerResponse:', containerResponse.data);

            if (!containerResponse.data || !containerResponse.data.id) {
                helper.devConsole('[instagramService.publishVideo] ERROR creating media container:', containerResponse.data);
                const apiError = containerResponse.data?.error?.message || 'Failed to create media container, no ID received.';
                return { success: false, error: `Instagram API Error (Create Container): ${apiError}` };
            }
            const creationId = containerResponse.data.id;
            helper.devConsole('[instagramService.publishVideo] Media container created successfully. Creation ID:', creationId);

            // Schritt 2: Container veröffentlichen
            await new Promise(resolve => setTimeout(resolve, 10000));
            const publishUrl = `https://graph.facebook.com/${apiVersion}/${instagramUserId}/media_publish`;
            const publishPayload = {
                creation_id: creationId,
                access_token: accessToken
            };

            // NEU: Logge die Publish-URL und Payload
            helper.devConsole('[DEBUG] Instagram publishUrl:', publishUrl);
            helper.devConsole('[DEBUG] Instagram publishPayload:', publishPayload);

            const publishResponse = await axios.post(publishUrl, publishPayload);

            // NEU: Logge die komplette Antwort
            helper.devConsole('[DEBUG] Instagram publishResponse:', publishResponse.data);

            if (!publishResponse.data || !publishResponse.data.id) {
                helper.devConsole('[instagramService.publishVideo] ERROR publishing media container:', publishResponse.data);
                let statusCheckError = 'Failed to publish media container, no ID received.';
                try {
                    const statusCheckUrl = `https://graph.facebook.com/${apiVersion}/${creationId}?fields=status_code,status&access_token=${accessToken}`;
                    const statusCheckResponse = await axios.get(statusCheckUrl);
                    statusCheckError = `Container status: ${statusCheckResponse.data?.status_code} - ${statusCheckResponse.data?.status}. ` + (publishResponse.data?.error?.message || '');
                    helper.devConsole('[instagramService.publishVideo] Container status check after failed publish:', statusCheckResponse.data);
                } catch (e) {
                    helper.devConsole('[instagramService.publishVideo] Error during status check of failed publish:', e.message);
                }
                return { success: false, error: `Instagram API Error (Publish Container): ${statusCheckError}` };
            }

            const platformPostId = publishResponse.data.id;
            helper.devConsole('[instagramService.publishVideo] Media container published successfully. Post ID:', platformPostId);
            return { success: true, platformPostId };

        } catch (error) {
            // Erweiterte Exception-Logs für Axios-Fehler mit JSON.stringify für vollständige Ausgabe
            if (error.isAxiosError) {
                let errorDetails = {};
                try {
                    errorDetails = {
                        message: error.message,
                        code: error.code,
                        config: error.config,
                        response: error.response ? {
                            status: error.response.status,
                            statusText: error.response.statusText,
                            headers: error.response.headers,
                            data: error.response.data
                        } : undefined
                    };
                } catch (e) {
                    errorDetails = { parseError: e.message, raw: error };
                }
                helper.devConsole(
                    '[instagramService.publishVideo] EXCEPTION during Instagram API call (AxiosError):\n' +
                    JSON.stringify(errorDetails, null, 2)
                );
            } else {
                helper.devConsole('[instagramService.publishVideo] EXCEPTION during Instagram API call (Other):\n' + JSON.stringify(error, null, 2));
            }
            const errorMessage = error.isAxiosError ?
                (error.response?.data?.error?.message || error.message) :
                error.message;
            return {
                success: false,
                error: `Exception during Instagram publish: ${errorMessage}`
            };
        }
    },

    // Instagram Stories Publishing
    publishStory: async (videoUrl, title, description) => {
        helper.devConsole('[instagramService.publishStory] Attempting to publish Instagram Story:', { videoUrl, title, description });

        const accessToken = process.env.INSTAGRAM_TOKEN;
        const instagramUserId = process.env.INSTAGRAM_USER_ID;
        const apiVersion = 'v22.0';

        if (!accessToken || !instagramUserId) {
            helper.devConsole('[instagramService.publishStory] ERROR: INSTAGRAM_TOKEN or INSTAGRAM_USER_ID not defined in .env');
            return { success: false, error: 'Instagram credentials not configured for Stories.' };
        }
        if (!videoUrl) {
            helper.devConsole('[instagramService.publishStory] ERROR: videoUrl is missing.');
            return { success: false, error: 'Video URL is missing for Instagram Story publish.' };
        }

        try {
            // Instagram Stories API - Create Media Container for Stories
            const createContainerUrl = `https://graph.facebook.com/${apiVersion}/${instagramUserId}/media`;
            const containerPayload = {
                video_url: videoUrl,
                media_type: 'STORIES', // Stories media type
                access_token: accessToken
            };

            helper.devConsole('[instagramService.publishStory] Creating Stories media container...');
            const containerResponse = await axios.post(createContainerUrl, containerPayload);

            if (!containerResponse.data || !containerResponse.data.id) {
                helper.devConsole('[instagramService.publishStory] ERROR creating Stories media container:', containerResponse.data);
                return { success: false, error: 'Failed to create Instagram Stories media container.' };
            }

            const creationId = containerResponse.data.id;
            helper.devConsole('[instagramService.publishStory] Stories media container created successfully. Creation ID:', creationId);

            // Wait for media processing (Stories usually process faster than Reels)
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Publish the Stories container
            const publishUrl = `https://graph.facebook.com/${apiVersion}/${instagramUserId}/media_publish`;
            const publishPayload = {
                creation_id: creationId,
                access_token: accessToken
            };

            helper.devConsole('[instagramService.publishStory] Publishing Stories media container...');
            const publishResponse = await axios.post(publishUrl, publishPayload);

            if (!publishResponse.data || !publishResponse.data.id) {
                helper.devConsole('[instagramService.publishStory] ERROR publishing Stories media container:', publishResponse.data);

                // Check container status for debugging
                try {
                    const statusCheckUrl = `https://graph.facebook.com/${apiVersion}/${creationId}?fields=status_code,status&access_token=${accessToken}`;
                    const statusCheckResponse = await axios.get(statusCheckUrl);
                    const statusError = `Stories container status: ${statusCheckResponse.data?.status_code} - ${statusCheckResponse.data?.status}`;
                    helper.devConsole('[instagramService.publishStory] Stories container status check:', statusCheckResponse.data);
                    return { success: false, error: `Failed to publish Stories container. ${statusError}` };
                } catch (e) {
                    return { success: false, error: 'Failed to publish Instagram Stories container.' };
                }
            }

            const storyId = publishResponse.data.id;
            helper.devConsole('[instagramService.publishStory] Instagram Story published successfully. Story ID:', storyId);

            return {
                success: true,
                platformPostId: storyId,
                message: 'Successfully published to Instagram Stories'
            };

        } catch (error) {
            helper.devConsole('[instagramService.publishStory] EXCEPTION during Instagram Stories API call:', error.isAxiosError ? error.response?.data || error.message : error.message);

            let errorMessage = 'Unknown Instagram Stories API error';
            if (error.isAxiosError && error.response?.data) {
                errorMessage = error.response.data.error?.message || error.response.data.message || JSON.stringify(error.response.data);
            } else {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: `Instagram Stories publish failed: ${errorMessage}`
            };
        }
    }
};

const pinterestService = {
    // Token refresh function
    refreshPinterestToken: async (refreshToken) => {
        try {
            const clientId = process.env.PINTEREST_CLIENT_ID || process.env.PINTEREST_APP_ID;
            const clientSecret = process.env.PINTEREST_CLIENT_SECRET || process.env.PINTEREST_APP_SECRET;

            // Pinterest API expects form-urlencoded data
            const formData = new URLSearchParams();
            formData.append('grant_type', 'refresh_token');
            formData.append('refresh_token', refreshToken);
            formData.append('client_id', clientId);
            formData.append('client_secret', clientSecret);

            const apiBaseUrl = process.env.PINTEREST_API_SANDBOX === 'true'
                ? 'https://api-sandbox.pinterest.com'
                : 'https://api.pinterest.com';

            const response = await axios.post(`${apiBaseUrl}/v5/oauth/token`, formData, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.data && response.data.access_token) {
                // Save new token to database using correct method
                const PinterestToken = require('../models/pinterestTokenModel');
                await PinterestToken.saveTokens(
                    response.data.access_token,
                    response.data.refresh_token || refreshToken,
                    response.data.scope
                );

                return {
                    success: true,
                    accessToken: response.data.access_token
                };
            } else {
                return {
                    success: false,
                    error: 'Invalid refresh token response'
                };
            }
        } catch (error) {
            helper.devConsole('[pinterestService.refreshPinterestToken] Error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.response?.data?.message || error.message
            };
        }
    },

    publishVideo: async (videoUrl, title, description) => {
        helper.devConsole('[pinterestService.publishVideo] Called', { videoUrl, title });
        helper.devConsole('[pinterestService.publishVideo] Description:', description);

        try {
            // Try to get tokens from database first, fallback to environment variables
            let accessToken = process.env.PINTEREST_ACCESS_TOKEN;
            let boardId = process.env.PINTEREST_BOARD_ID;

            // Try to load from database if available
            try {
                const PinterestToken = require('../models/pinterestTokenModel');
                let tokenRecord = await PinterestToken.getCurrentToken();

                if (tokenRecord && tokenRecord.accessToken) {
                    // Check if token is expired (if we have expiry info)
                    const now = new Date();
                    const tokenAge = (now - tokenRecord.createdAt) / 1000; // seconds
                    const isExpired = tokenRecord.expiresIn && tokenAge >= tokenRecord.expiresIn;

                    if (isExpired && tokenRecord.refreshToken) {
                        helper.devConsole('[pinterestService.publishVideo] Token expired, attempting refresh...');
                        try {
                            // Refresh the token
                            const refreshResult = await this.refreshPinterestToken(tokenRecord.refreshToken);
                            if (refreshResult.success) {
                                accessToken = refreshResult.accessToken;
                                helper.devConsole('[pinterestService.publishVideo] Token refreshed successfully');
                            } else {
                                helper.devConsole('[pinterestService.publishVideo] Token refresh failed, using expired token');
                                accessToken = tokenRecord.accessToken;
                            }
                        } catch (refreshError) {
                            helper.devConsole('[pinterestService.publishVideo] Token refresh error:', refreshError.message);
                            accessToken = tokenRecord.accessToken;
                        }
                    } else {
                        accessToken = tokenRecord.accessToken;
                        helper.devConsole('[pinterestService.publishVideo] Using valid access token from database');
                    }
                } else {
                    helper.devConsole('[pinterestService.publishVideo] No token found in database, using environment variable');

                    // If no token in database and no environment variable, provide helpful error
                    if (!accessToken) {
                        return {
                            success: false,
                            error: 'No Pinterest access token available. Please run the Pinterest OAuth setup first by calling: POST /api/v1/oauth/pinterest/setup'
                        };
                    }
                }
            } catch (dbError) {
                helper.devConsole('[pinterestService.publishVideo] Could not load token from database:', dbError.message);
                helper.devConsole('[pinterestService.publishVideo] Falling back to environment variables');
            }

            // Check if we have the required credentials
            if (!accessToken) {
                helper.devConsole('[pinterestService.publishVideo] ERROR: Missing Pinterest access token');
                return {
                    success: false,
                    error: 'Pinterest API access token not configured. Please configure via OAuth or environment variables.'
                };
            }

            if (!boardId) {
                helper.devConsole('[pinterestService.publishVideo] ERROR: Missing Pinterest board ID');
                return {
                    success: false,
                    error: 'Pinterest board ID not configured. Please set PINTEREST_BOARD_ID environment variable.'
                };
            }

            helper.devConsole('[pinterestService.publishVideo] Using credentials:', {
                accessToken: accessToken.substring(0, 10) + '...',
                boardId: boardId
            });

            // KRITISCH: Pinterest API Sandbox für Trial Apps
            const apiBaseUrl = process.env.PINTEREST_API_SANDBOX === 'true'
                ? 'https://api-sandbox.pinterest.com'
                : 'https://api.pinterest.com';

            console.log('[PINTEREST API] Using API base URL:', apiBaseUrl);

            // Step 1: Register media upload
            helper.devConsole('[pinterestService.publishVideo] Step 1: Registering media upload');
            let registerResponse;
            try {
                registerResponse = await axios.post(
                    `${apiBaseUrl}/v5/media`,
                    {
                        media_type: 'video',
                    },
                    {
                        headers: {
                            'Authorization': `Bearer ${accessToken}`,
                            'Content-Type': 'application/json',
                        },
                    }
                );
                helper.devConsole('[pinterestService.publishVideo] Register response:', registerResponse.data);
                console.log('[PINTEREST MEDIA REGISTRATION] Full response details:', JSON.stringify(registerResponse.data, null, 2));
            } catch (registerError) {
                const errorDetails = registerError.response?.data || registerError.message;
                console.log('[PINTEREST ERROR] Media registration failed:', JSON.stringify(errorDetails, null, 2));
                helper.devConsole('[pinterestService.publishVideo] ERROR during media registration:', errorDetails);

                // Check if it's an authentication error and we have a refresh token
                if (registerError.response?.data?.code === 2 || registerError.response?.data?.message?.includes('Authentication failed')) {
                    helper.devConsole('[pinterestService.publishVideo] Authentication failed, checking for refresh token...');

                    try {
                        const PinterestToken = require('../models/pinterestTokenModel');
                        const tokenRecord = await PinterestToken.getCurrentToken();

                        if (tokenRecord && tokenRecord.refreshToken) {
                            helper.devConsole('[pinterestService.publishVideo] Attempting token refresh due to auth failure...');
                            const refreshResult = await pinterestService.refreshPinterestToken(tokenRecord.refreshToken);

                            if (refreshResult.success) {
                                helper.devConsole('[pinterestService.publishVideo] Token refreshed, retrying media registration...');
                                // Retry the media registration with new token
                                accessToken = refreshResult.accessToken;

                                try {
                                    registerResponse = await axios.post(
                                        'https://api.pinterest.com/v5/media',
                                        {
                                            media_type: 'video'
                                        },
                                        {
                                            headers: {
                                                'Authorization': `Bearer ${accessToken}`,
                                                'Content-Type': 'application/json',
                                            },
                                        }
                                    );
                                    helper.devConsole('[pinterestService.publishVideo] Media registration successful after token refresh');
                                } catch (retryError) {
                                    helper.devConsole('[pinterestService.publishVideo] Media registration failed even after token refresh:', retryError.response?.data || retryError.message);
                                    return {
                                        success: false,
                                        error: `Failed to register media even after token refresh: ${retryError.response?.data?.message || retryError.message}`
                                    };
                                }
                            } else {
                                return {
                                    success: false,
                                    error: `Authentication failed and token refresh failed: ${refreshResult.error}`
                                };
                            }
                        } else {
                            return {
                                success: false,
                                error: 'Authentication failed and no refresh token available. Please re-authorize the Pinterest integration.'
                            };
                        }
                    } catch (refreshAttemptError) {
                        helper.devConsole('[pinterestService.publishVideo] Error during refresh attempt:', refreshAttemptError.message);
                        return {
                            success: false,
                            error: `Authentication failed and refresh attempt failed: ${refreshAttemptError.message}`
                        };
                    }
                } else {
                    return {
                        success: false,
                        error: `Failed to register media with Pinterest API: ${registerError.response?.data?.message || registerError.message}`
                    };
                }
            }

            if (!registerResponse.data || !registerResponse.data.media_id || !registerResponse.data.upload_url) {
                helper.devConsole('[pinterestService.publishVideo] ERROR: Invalid response from Pinterest media registration', registerResponse.data);
                return {
                    success: false,
                    error: 'Failed to register media with Pinterest API: Invalid response'
                };
            }

            const { media_id, upload_url, upload_parameters } = registerResponse.data;
            console.log('[PINTEREST MEDIA] Extracted data:', {
                media_id: media_id,
                upload_url: upload_url,
                upload_parameters: upload_parameters
            });
            helper.devConsole('[pinterestService.publishVideo] Media registered successfully', { media_id, upload_url });

            // Step 2: Download video from S3 URL
            helper.devConsole('[pinterestService.publishVideo] Step 2: Downloading video from S3:', videoUrl);
            let videoResponse;
            let videoBuffer;
            try {
                videoResponse = await axios.get(videoUrl, { responseType: 'arraybuffer' });
                videoBuffer = Buffer.from(videoResponse.data, 'binary');
                helper.devConsole('[pinterestService.publishVideo] Video downloaded successfully, size:', videoBuffer.length);
            } catch (downloadError) {
                helper.devConsole('[pinterestService.publishVideo] ERROR downloading video:', downloadError.message);
                return {
                    success: false,
                    error: `Failed to download video from S3: ${downloadError.message}`
                };
            }

            // Step 3: Upload video to Pinterest using FormData
            helper.devConsole('[pinterestService.publishVideo] Step 3: Uploading video to Pinterest');
            helper.devConsole('[pinterestService.publishVideo] Upload details:', {
                upload_url: upload_url,
                video_size: videoBuffer.length,
                video_type: 'video/mp4',
                has_upload_parameters: !!upload_parameters
            });

            try {
                let uploadResponse;

                if (upload_parameters) {
                    // Pinterest S3 Upload mit FormData und upload_parameters
                    const FormData = require('form-data');
                    const formData = new FormData();

                    // Füge alle upload_parameters hinzu
                    Object.keys(upload_parameters).forEach(key => {
                        formData.append(key, upload_parameters[key]);
                    });

                    // Füge die Video-Datei hinzu
                    formData.append('file', videoBuffer, {
                        filename: 'video.mp4',
                        contentType: 'video/mp4'
                    });

                    console.log('[PINTEREST UPLOAD] Using FormData with parameters:', Object.keys(upload_parameters));

                    uploadResponse = await axios.post(upload_url, formData, {
                        headers: {
                            ...formData.getHeaders()
                        },
                        maxContentLength: Infinity,
                        maxBodyLength: Infinity,
                        timeout: 60000
                    });
                } else {
                    // Fallback: Versuche verschiedene Upload-Methoden
                    console.log('[PINTEREST UPLOAD] No upload_parameters, trying alternative methods...');

                    try {
                        // Methode 1: POST mit FormData (ohne Parameter)
                        const FormData = require('form-data');
                        const formData = new FormData();
                        formData.append('file', videoBuffer, {
                            filename: 'video.mp4',
                            contentType: 'video/mp4'
                        });

                        console.log('[PINTEREST UPLOAD] Trying POST with FormData...');
                        uploadResponse = await axios.post(upload_url, formData, {
                            headers: {
                                ...formData.getHeaders()
                            },
                            maxContentLength: Infinity,
                            maxBodyLength: Infinity,
                            timeout: 60000
                        });
                    } catch (postError) {
                        console.log('[PINTEREST UPLOAD] POST failed, trying PUT...');

                        // Methode 2: PUT Upload
                        uploadResponse = await axios.put(upload_url, videoBuffer, {
                            headers: {
                                'Content-Type': 'video/mp4',
                                'Content-Length': videoBuffer.length.toString()
                            },
                            maxContentLength: Infinity,
                            maxBodyLength: Infinity,
                            timeout: 60000
                        });
                    }
                }

                helper.devConsole('[pinterestService.publishVideo] Video uploaded successfully to Pinterest');
                helper.devConsole('[pinterestService.publishVideo] Upload response status:', uploadResponse.status);

            // Step 3.5: Warte bis Media verarbeitet wurde
            let mediaReady = false;
            let attempts = 0;
            const maxAttempts = 10;

            while (!mediaReady && attempts < maxAttempts) {
                try {
                    console.log(`[PINTEREST MEDIA STATUS] Checking media status (attempt ${attempts + 1}/${maxAttempts})...`);
                    const statusResponse = await axios.get(
                        `${apiBaseUrl}/v5/media/${media_id}`,
                        {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`,
                                'Content-Type': 'application/json',
                            },
                        }
                    );

                    console.log('[PINTEREST MEDIA STATUS] Media status:', statusResponse.data);

                    if (statusResponse.data.status === 'ready') {
                        mediaReady = true;
                        console.log('[PINTEREST MEDIA STATUS] Media is ready for pin creation!');
                    } else if (statusResponse.data.status === 'failed') {
                        console.error('[PINTEREST MEDIA STATUS] Media processing failed:', statusResponse.data);
                        return {
                            success: false,
                            error: `Media processing failed: ${statusResponse.data.failure_reason || 'Unknown error'}`
                        };
                    } else {
                        console.log(`[PINTEREST MEDIA STATUS] Media status: ${statusResponse.data.status}, waiting 3 seconds...`);
                        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 Sekunden warten
                    }
                } catch (statusError) {
                    console.log('[PINTEREST MEDIA STATUS] Could not check status:', statusError.response?.data || statusError.message);
                    break; // Bei Fehler trotzdem versuchen Pin zu erstellen
                }
                attempts++;
            }

            if (!mediaReady && attempts >= maxAttempts) {
                console.warn('[PINTEREST MEDIA STATUS] Media not ready after maximum attempts, proceeding anyway...');
            }

        } catch (uploadError) {
                console.error('[PINTEREST UPLOAD ERROR] Full error details:', {
                    status: uploadError.response?.status,
                    statusText: uploadError.response?.statusText,
                    headers: uploadError.response?.headers,
                    data: uploadError.response?.data,
                    message: uploadError.message,
                    upload_url: upload_url,
                    video_size: videoBuffer.length
                });

                helper.devConsole('[pinterestService.publishVideo] ERROR uploading video to Pinterest:',
                    uploadError.response?.data || uploadError.message);
                return {
                    success: false,
                    error: `Failed to upload video to Pinterest: ${uploadError.response?.status} ${uploadError.response?.statusText} - ${uploadError.response?.data?.message || uploadError.message}`
                };
            }

            // Step 4: Create a Pin with the uploaded video
            helper.devConsole('[pinterestService.publishVideo] Step 4: Creating Pin with uploaded video');

            // KRITISCH: Pinterest API v5 Video Pin Format
            const pinPayload = {
                board_id: boardId,
                media_source: {
                    source_type: "video_id",
                    content_type: "video/mp4",
                    data: media_id,
                    url: videoUrl  // Original video URL als Fallback
                },
                title: title,
                description: description,
                alt_text: title
            };

            console.log('[PINTEREST PIN CREATION] Using video pin payload format:', JSON.stringify(pinPayload, null, 2));
            helper.devConsole('[pinterestService.publishVideo] Pin creation payload:', pinPayload);

            let createPinResponse;
            let pinCreationAttempts = [
                // Format 1: media_source mit video_id
                {
                    board_id: boardId,
                    media_source: {
                        source_type: "video_id",
                        content_type: "video/mp4",
                        data: media_id
                    },
                    title: title,
                    description: description,
                    alt_text: title
                },
                // Format 2: Nur media_id (einfachstes Format)
                {
                    board_id: boardId,
                    media_id: media_id,
                    title: title,
                    description: description,
                    alt_text: title
                },
                // Format 3: media object (ursprüngliches Format)
                {
                    board_id: boardId,
                    media: {
                        media_id: media_id
                    },
                    title: title,
                    description: description,
                    alt_text: title
                }
            ];

            let lastError = null;
            for (let i = 0; i < pinCreationAttempts.length; i++) {
                const attempt = pinCreationAttempts[i];
                console.log(`[PINTEREST PIN CREATION] Attempt ${i + 1}:`, JSON.stringify(attempt, null, 2));

                try {
                    createPinResponse = await axios.post(
                        `${apiBaseUrl}/v5/pins`,
                        attempt,
                        {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`,
                                'Content-Type': 'application/json',
                            },
                        }
                    );

                    console.log(`[PINTEREST PIN CREATION] Attempt ${i + 1} successful!`);
                    helper.devConsole('[pinterestService.publishVideo] Pin creation response:', createPinResponse.data);
                    break; // Erfolg - verlasse die Schleife

                } catch (pinError) {
                    lastError = pinError;
                    console.log(`[PINTEREST PIN CREATION] Attempt ${i + 1} failed:`, pinError.response?.data || pinError.message);

                    // Wenn das der letzte Versuch war, gib den Fehler zurück
                    if (i === pinCreationAttempts.length - 1) {
                        helper.devConsole('[pinterestService.publishVideo] ERROR creating Pin (all attempts failed):',
                            pinError.response?.data || pinError.message);
                        return {
                            success: false,
                            error: `Failed to create Pin on Pinterest: ${pinError.response?.data?.message || pinError.message}`
                        };
                    }
                }
            }

            if (!createPinResponse.data || !createPinResponse.data.id) {
                helper.devConsole('[pinterestService.publishVideo] ERROR: Failed to create Pin, invalid response', createPinResponse.data);
                return {
                    success: false,
                    error: 'Failed to create Pin on Pinterest: No pin ID returned'
                };
            }

            helper.devConsole('[pinterestService.publishVideo] Pin created successfully', { pin_id: createPinResponse.data.id });
            return {
                success: true,
                platformPostId: createPinResponse.data.id
            };

        } catch (error) {
            helper.devConsole('[pinterestService.publishVideo] EXCEPTION during Pinterest API call:',
                error.isAxiosError ? error.response?.data || error.message : error.message);
            return {
                success: false,
                error: `Exception during Pinterest publish: ${error.isAxiosError ? error.response?.data?.message || error.message : error.message}`
            };
        }
    }
};

// --- Haupt-Publishing-Funktion ---
exports.publishNow = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.publishNow] Triggered');

    // 1. Suche nach Content mit published_pinterest: 0 (Priorität)
    let contentToPublish = await MarketingContent.findOne({
        $and: [
            { videoS3Url: { $ne: null, $ne: '' } }, // Stelle sicher, dass ein Video vorhanden ist
            { $or: [{ published_pinterest: 0 }, { published_pinterest: { $exists: false } }] }
        ]
    }).sort({ generationDate: 1 }); // Älteste zuerst

    helper.devConsole('[publishController.publishNow] Search for published_pinterest: 0 result:', !!contentToPublish);

    // 2. Falls kein Content mit published_pinterest: 0 gefunden wurde,
    //    suche nach Content mit published_pinterest: 1 und setze auf 0 zurück
    if (!contentToPublish) {
        helper.devConsole('[publishController.publishNow] No content with published_pinterest: 0 found, searching for published_pinterest: 1...');

        contentToPublish = await MarketingContent.findOne({
            $and: [
                { videoS3Url: { $ne: null, $ne: '' } },
                { published_pinterest: 1 }
            ]
        }).sort({ generationDate: 1 }); // Älteste zuerst

        if (contentToPublish) {
            helper.devConsole('[publishController.publishNow] Found content with published_pinterest: 1, resetting to 0 for republishing...');
            await MarketingContent.findByIdAndUpdate(contentToPublish._id, {
                $set: { published_pinterest: 0 }
            });
            contentToPublish.published_pinterest = 0; // Update local object
        }
    }

    // 3. Falls immer noch kein Content gefunden wurde, prüfe andere Plattformen
    if (!contentToPublish) {
        helper.devConsole('[publishController.publishNow] No Pinterest content found, checking other platforms...');
        contentToPublish = await MarketingContent.findOne({
            $and: [
                { videoS3Url: { $ne: null, $ne: '' } }, // Stelle sicher, dass ein Video vorhanden ist
                {
                    $or: [ // Mindestens eine andere Plattform muss 0 Posts haben
                        { $or: [{ published_tiktok: 0 }, { published_tiktok: { $exists: false } }] },
                        { $or: [{ published_fb: 0 }, { published_fb: { $exists: false } }] },
                        { $or: [{ published_insta: 0 }, { published_insta: { $exists: false } }] }
                    ]
                }
            ]
        }).sort({ generationDate: 1 }); // Älteste zuerst
    }

    if (!contentToPublish) {
        helper.devConsole('[publishController.publishNow] No content found that needs publishing.');

        // Check total count of marketing content for debugging
        const totalCount = await MarketingContent.countDocuments({});
        const withVideoCount = await MarketingContent.countDocuments({ videoS3Url: { $ne: null, $ne: '' } });

        helper.devConsole('[publishController.publishNow] Debug info:', {
            totalMarketingContent: totalCount,
            contentWithVideo: withVideoCount
        });

        return res.status(200).json({
            status: 'success',
            message: 'No content found that needs publishing at the moment.',
            debug: {
                totalMarketingContent: totalCount,
                contentWithVideo: withVideoCount
            }
        });
    }

    helper.devConsole('[publishController.publishNow] Found content to publish:', contentToPublish._id);

    const contentDetails = {
        id: contentToPublish._id,
        recipeName: contentToPublish.recipeName,
        videoS3Url: contentToPublish.videoS3Url,
        published_tiktok: contentToPublish.published_tiktok,
        published_fb: contentToPublish.published_fb,
        published_insta: contentToPublish.published_insta,
        published_pinterest: contentToPublish.published_pinterest
    };

    console.log('[CONTENT DEBUG] Content details:', JSON.stringify(contentDetails, null, 2));
    helper.devConsole('[publishController.publishNow] Content details:', contentDetails);

    let updates = {};
    let errors = [];
    let successes = [];

    // 2. Versuche, auf jeder Plattform zu veröffentlichen, auf der noch nicht veröffentlicht wurde

    // TikTok
    if (contentToPublish.published_tiktok === 0 && contentToPublish.videoS3Url) {
        helper.devConsole('[publishController.publishNow] Attempting to publish to TikTok...');
        const result = await tiktokService.publishVideo(
            contentToPublish.videoS3Url,
            contentToPublish.recipeName, // Titel könnte auch socialMediaDescription sein
            contentToPublish.socialMediaDescription || contentToPublish.textOne // Fallback für Beschreibung
        );
        if (result.success) {
            updates.published_tiktok = (contentToPublish.published_tiktok || 0) + 1;
            successes.push({ platform: 'TikTok', id: result.platformPostId });
            helper.devConsole('[publishController.publishNow] Successfully published to TikTok.');
        } else {
            errors.push({ platform: 'TikTok', error: result.error });
            helper.devConsole('[publishController.publishNow] Failed to publish to TikTok:', result.error);
        }
    }

    // Facebook (Platzhalter)
    if (contentToPublish.published_fb === 0 && contentToPublish.videoS3Url) {
        helper.devConsole('[publishController.publishNow] Attempting to publish to Facebook...');
        const result = await facebookService.publishVideo(
            contentToPublish.videoS3Url,
            contentToPublish.recipeName,
            contentToPublish.socialMediaDescription || contentToPublish.textOne
        );
        if (result.success) {
            updates.published_fb = (contentToPublish.published_fb || 0) + 1;
            successes.push({ platform: 'Facebook', id: result.platformPostId });
        } else {
            errors.push({ platform: 'Facebook', error: result.error });
        }
    }

    // Instagram (Platzhalter)
    if (contentToPublish.published_insta === 0 && contentToPublish.videoS3Url) {
        helper.devConsole('[publishController.publishNow] Attempting to publish to Instagram...');
        const result = await instagramService.publishVideo(
            contentToPublish.videoS3Url,
            contentToPublish.recipeName,
            contentToPublish.socialMediaDescription || contentToPublish.textOne
        );
        if (result.success) {
            updates.published_insta = (contentToPublish.published_insta || 0) + 1;
            successes.push({ platform: 'Instagram', id: result.platformPostId });
        } else {
            errors.push({ platform: 'Instagram', error: result.error });
        }
    }

    console.log('=== PINTEREST SECTION REACHED ===');

    // Pinterest
    const pinterestDebugInfo = {
        published_pinterest: contentToPublish.published_pinterest,
        published_pinterest_type: typeof contentToPublish.published_pinterest,
        published_pinterest_equals_zero: contentToPublish.published_pinterest === 0,
        published_pinterest_is_undefined: contentToPublish.published_pinterest === undefined,
        videoS3Url_exists: !!contentToPublish.videoS3Url,
        videoS3Url: contentToPublish.videoS3Url
    };

    console.log('[PINTEREST DEBUG] Condition check:', JSON.stringify(pinterestDebugInfo, null, 2));
    helper.devConsole('[publishController.publishNow] Pinterest condition check:', pinterestDebugInfo);

    if ((contentToPublish.published_pinterest === 0 || contentToPublish.published_pinterest === undefined) && contentToPublish.videoS3Url) {
        helper.devConsole('[publishController.publishNow] Attempting to publish to Pinterest...');
        helper.devConsole('[publishController.publishNow] Pinterest credentials check: ACCESS_TOKEN exists: ' +
            (!!process.env.PINTEREST_ACCESS_TOKEN) + ', BOARD_ID exists: ' + (!!process.env.PINTEREST_BOARD_ID));
        helper.devConsole('[publishController.publishNow] Pinterest BOARD_ID value: ' + process.env.PINTEREST_BOARD_ID);

        try {
            const result = await pinterestService.publishVideo(
                contentToPublish.videoS3Url,
                contentToPublish.recipeName,
                contentToPublish.socialMediaDescription || contentToPublish.textOne
            );

            helper.devConsole('[publishController.publishNow] Pinterest publish result:', result);

            if (result.success) {
                updates.published_pinterest = (contentToPublish.published_pinterest || 0) + 1;
                successes.push({ platform: 'Pinterest', id: result.platformPostId });
                helper.devConsole('[publishController.publishNow] Successfully published to Pinterest with ID: ' + result.platformPostId);
            } else {
                errors.push({ platform: 'Pinterest', error: result.error });
                helper.devConsole('[publishController.publishNow] Failed to publish to Pinterest: ' + result.error);
            }
        } catch (error) {
            const errorMessage = `Unexpected error during Pinterest publish: ${error.message}`;
            helper.devConsole('[publishController.publishNow] EXCEPTION: ' + errorMessage);
            errors.push({ platform: 'Pinterest', error: errorMessage });
        }
    } else {
        const skipReason = {
            published_pinterest: contentToPublish.published_pinterest,
            published_pinterest_type: typeof contentToPublish.published_pinterest,
            videoS3Url_exists: !!contentToPublish.videoS3Url,
            condition_met: (contentToPublish.published_pinterest === 0 || contentToPublish.published_pinterest === undefined) && contentToPublish.videoS3Url
        };

        console.log('[PINTEREST DEBUG] Skipping Pinterest publish. Reason:', JSON.stringify(skipReason, null, 2));
        helper.devConsole('[publishController.publishNow] Skipping Pinterest publish. Reason:', skipReason);
    }

    // 3. Aktualisiere den MarketingContent in der Datenbank
    if (Object.keys(updates).length > 0) {
        await MarketingContent.findByIdAndUpdate(contentToPublish._id, { $set: updates });
        helper.devConsole('[publishController.publishNow] Updated MarketingContent in DB with counts:', updates);
    }

    res.status(200).json({
        status: 'success',
        message: 'Publishing process finished.',
        publishedContentId: contentToPublish._id,
        successes,
        errors
    });
});

// --- Cronjob für tägliches Publishing ---
// Format: Sekunde (0-59) Minute (0-59) Stunde (0-23) Tag des Monats (1-31) Monat (1-12) Wochentag (0-7, 0 oder 7 ist Sonntag)
// Beispiel: Jeden Tag um 08:00 Uhr morgens
cron.schedule('0 8 * * *', async () => {
    helper.devConsole('[Cronjob] Daily publishing triggered.');
    try {
        // Hier könnten wir die publishNow-Funktion direkt aufrufen oder einen internen API-Call machen.
        // Für Einfachheit und direkten Zugriff auf Abhängigkeiten rufen wir die Logik nach.
        // Wichtig: Dies ist eine vereinfachte Darstellung. In Produktion ggf. Fehlerbehandlung, Retry-Mechanismen etc. verbessern.

        const contentToPublish = await MarketingContent.findOne({
            $and: [
                { videoS3Url: { $ne: null, $ne: '' } }, // Stelle sicher, dass ein Video vorhanden ist
                {
                    $or: [ // Mindestens eine Plattform muss entweder 0 Posts haben ODER das Feld noch nicht besitzen
                        { $or: [{ published_tiktok: 0 }, { published_tiktok: { $exists: false } }] },
                        { $or: [{ published_fb: 0 }, { published_fb: { $exists: false } }] },
                        { $or: [{ published_insta: 0 }, { published_insta: { $exists: false } }] },
                        { $or: [{ published_pinterest: 0 }, { published_pinterest: { $exists: false } }] }
                    ]
                }
            ]
        }).sort({ generationDate: 1 }); // Älteste zuerst

        if (!contentToPublish) {
            helper.devConsole('[Cronjob] No content found for daily publishing.');
            return;
        }

        helper.devConsole('[Cronjob] Found content to publish via cron:', contentToPublish._id);
        let cronUpdates = {};
        // TikTok
        if (contentToPublish.published_tiktok === 0 && contentToPublish.videoS3Url) {
            const result = await tiktokService.publishVideo(contentToPublish.videoS3Url, contentToPublish.recipeName, contentToPublish.socialMediaDescription || contentToPublish.textOne);
            if (result.success) cronUpdates.published_tiktok = (contentToPublish.published_tiktok || 0) + 1;
        }
        // Facebook
        if (contentToPublish.published_fb === 0 && contentToPublish.videoS3Url) {
            const result = await facebookService.publishVideo(contentToPublish.videoS3Url, contentToPublish.recipeName, contentToPublish.socialMediaDescription || contentToPublish.textOne);
            if (result.success) cronUpdates.published_fb = (contentToPublish.published_fb || 0) + 1;
        }
        // Pinterest
        if (contentToPublish.published_pinterest === 0 && contentToPublish.videoS3Url) {
            const result = await pinterestService.publishVideo(contentToPublish.videoS3Url, contentToPublish.recipeName, contentToPublish.socialMediaDescription || contentToPublish.textOne);
            if (result.success) cronUpdates.published_pinterest = (contentToPublish.published_pinterest || 0) + 1;
        }

        // TikTok
        if (contentToPublish.published_tiktok === 0 && contentToPublish.videoS3Url) {
            const result = await tiktokService.publishVideo(contentToPublish.videoS3Url, contentToPublish.recipeName, contentToPublish.socialMediaDescription || contentToPublish.textOne);
            if (result.success) cronUpdates.published_tiktok = (contentToPublish.published_tiktok || 0) + 1;
        }

        // Instagram
        if (contentToPublish.published_insta === 0 && contentToPublish.videoS3Url) {
            const result = await instagramService.publishVideo(contentToPublish.videoS3Url, contentToPublish.recipeName, contentToPublish.socialMediaDescription || contentToPublish.textOne);
            if (result.success) cronUpdates.published_insta = (contentToPublish.published_insta || 0) + 1;
        }

        if (Object.keys(cronUpdates).length > 0) {
            await MarketingContent.findByIdAndUpdate(contentToPublish._id, { $set: cronUpdates });
            helper.devConsole('[Cronjob] Updated MarketingContent from cron job:', cronUpdates);
        }
        helper.devConsole('[Cronjob] Daily publishing finished.');

    } catch (error) {
        helper.devConsole('[Cronjob] Error during daily publishing:', error);
    }
}, {
    scheduled: process.env.NODE_ENV === 'production', // Cronjob nur in Produktion starten
    timezone: "Europe/Zurich"
});

// @desc    Setup Pinterest OAuth integration
// @route   POST /api/v1/marketing/publish/setup-pinterest
// @access  Protected
exports.setupPinterestOAuth = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.setupPinterestOAuth] Setting up Pinterest OAuth...');

    try {
        // Check if we already have a valid token
        const PinterestToken = require('../models/pinterestTokenModel');
        const existingToken = await PinterestToken.getCurrentToken();

        if (existingToken && existingToken.accessToken) {
            helper.devConsole('[publishController.setupPinterestOAuth] Token already exists, testing validity...');

            // Test if existing token works
            try {
                const testResponse = await axios.get('https://api.pinterest.com/v5/user_account', {
                    headers: {
                        'Authorization': `Bearer ${existingToken.accessToken}`
                    }
                });

                if (testResponse.status === 200) {
                    return res.status(200).json({
                        status: 'success',
                        message: 'Pinterest OAuth is already configured and working',
                        data: {
                            tokenExists: true,
                            tokenValid: true,
                            userInfo: testResponse.data
                        }
                    });
                }
            } catch (testError) {
                helper.devConsole('[publishController.setupPinterestOAuth] Existing token invalid, proceeding with new setup...');
            }
        }

        // Generate authorization URL
        const clientId = process.env.PINTEREST_CLIENT_ID || process.env.PINTEREST_APP_ID;
        const redirectUri = process.env.PINTEREST_REDIRECT_URI || process.env.PINTEREST_CALLBACK_URL || 'http://localhost:8080/api/v1/oauth/pinterest/callback';

        if (!clientId) {
            return res.status(400).json({
                status: 'error',
                message: 'Pinterest Client ID not configured. Please set PINTEREST_CLIENT_ID or PINTEREST_APP_ID environment variable.'
            });
        }

        const authUrl = `https://www.pinterest.com/oauth/?` +
            `client_id=${clientId}&` +
            `redirect_uri=${encodeURIComponent(redirectUri)}&` +
            `response_type=code&` +
            `scope=boards:read,pins:write,user_accounts:read&` +
            `state=pinterest_oauth_setup`;

        helper.devConsole('[publishController.setupPinterestOAuth] Generated auth URL with:', {
            clientId: clientId ? 'present' : 'missing',
            redirectUri: redirectUri
        });

        res.status(200).json({
            status: 'success',
            message: 'Pinterest OAuth setup required',
            data: {
                authUrl: authUrl,
                instructions: [
                    '1. Visit the authorization URL',
                    '2. Authorize the application',
                    '3. Copy the authorization code from the callback URL',
                    '4. Call POST /api/v1/oauth/pinterest/token with the code'
                ],
                nextStep: 'POST /api/v1/oauth/pinterest/token'
            }
        });

    } catch (error) {
        helper.devConsole('[publishController.setupPinterestOAuth] Error:', error.message);
        res.status(500).json({
            status: 'error',
            message: 'Failed to setup Pinterest OAuth',
            error: error.message
        });
    }
});

// @desc    Reset Pinterest publish counts to 0 for all marketing content
// @route   POST /api/v1/marketing/publish/reset-pinterest
// @access  Protected
exports.resetPinterestCounts = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.resetPinterestCounts] Resetting all Pinterest publish counts...');

    try {
        const result = await MarketingContent.updateMany(
            {}, // Alle Dokumente
            { $set: { published_pinterest: 0 } }
        );

        helper.devConsole('[publishController.resetPinterestCounts] Reset completed:', {
            matchedCount: result.matchedCount,
            modifiedCount: result.modifiedCount
        });

        res.status(200).json({
            status: 'success',
            message: 'Pinterest publish counts reset successfully',
            data: {
                totalDocuments: result.matchedCount,
                modifiedDocuments: result.modifiedCount
            }
        });
    } catch (error) {
        helper.devConsole('[publishController.resetPinterestCounts] Error:', error.message);
        res.status(500).json({
            status: 'error',
            message: 'Failed to reset Pinterest publish counts',
            error: error.message
        });
    }
});

// @desc    Test Pinterest publishing with latest content
// @route   POST /api/v1/marketing/publish/test-pinterest
// @access  Protected
exports.testPinterestPublish = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.testPinterestPublish] Testing Pinterest publishing...');

    // 1. Finde den neuesten MarketingContent mit Video
    const latestContent = await MarketingContent.findOne({
        videoS3Url: { $ne: null, $ne: '' }
    }).sort({ generationDate: -1 });

    if (!latestContent) {
        return res.status(404).json({
            status: 'error',
            message: 'No marketing content with video found for testing.'
        });
    }

    helper.devConsole('[publishController.testPinterestPublish] Found content for testing:', {
        id: latestContent._id,
        recipeName: latestContent.recipeName,
        videoUrl: latestContent.videoS3Url
    });

    // 2. Teste Pinterest-Veröffentlichung
    try {
        const result = await pinterestService.publishVideo(
            latestContent.videoS3Url,
            latestContent.recipeName,
            latestContent.socialMediaDescription || latestContent.textOne
        );

        helper.devConsole('[publishController.testPinterestPublish] Pinterest test result:', result);

        if (result.success) {
            // Optional: Aktualisiere den Content in der DB
            await MarketingContent.findByIdAndUpdate(latestContent._id, {
                $inc: { published_pinterest: 1 }
            });

            res.status(200).json({
                status: 'success',
                message: 'Pinterest publishing test successful!',
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    pinterestPostId: result.platformPostId,
                    videoUrl: latestContent.videoS3Url
                }
            });
        } else {
            res.status(400).json({
                status: 'error',
                message: 'Pinterest publishing test failed',
                error: result.error,
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    videoUrl: latestContent.videoS3Url
                }
            });
        }
    } catch (error) {
        helper.devConsole('[publishController.testPinterestPublish] Exception during test:', error);
        res.status(500).json({
            status: 'error',
            message: 'Exception during Pinterest publishing test',
            error: error.message,
            data: {
                contentId: latestContent._id,
                recipeName: latestContent.recipeName
            }
        });
    }
});

// @desc    Test TikTok publishing with latest content
// @route   POST /api/v1/marketing/publish/test-tiktok
// @access  Protected
exports.testTikTokPublish = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.testTikTokPublish] Testing TikTok publishing...');

    // 1. Finde den neuesten MarketingContent mit Video
    const latestContent = await MarketingContent.findOne({
        videoS3Url: { $ne: null, $ne: '' }
    }).sort({ generationDate: -1 });

    if (!latestContent) {
        return res.status(404).json({
            status: 'error',
            message: 'No marketing content with video found for testing.'
        });
    }

    helper.devConsole('[publishController.testTikTokPublish] Found content for testing:', {
        id: latestContent._id,
        recipeName: latestContent.recipeName,
        videoUrl: latestContent.videoS3Url
    });

    // 2. Teste TikTok-Veröffentlichung
    try {
        const result = await tiktokService.publishVideo(
            latestContent.videoS3Url,
            latestContent.recipeName,
            latestContent.socialMediaDescription || latestContent.textOne
        );

        helper.devConsole('[publishController.testTikTokPublish] TikTok test result:', result);

        if (result.success) {
            // Optional: Aktualisiere den Content in der DB
            await MarketingContent.findByIdAndUpdate(latestContent._id, {
                $inc: { published_tiktok: 1 }
            });

            res.status(200).json({
                status: 'success',
                message: 'TikTok publishing test successful!',
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    tiktokPostId: result.platformPostId,
                    videoUrl: latestContent.videoS3Url,
                    status: result.status
                }
            });
        } else {
            res.status(400).json({
                status: 'error',
                message: 'TikTok publishing test failed',
                error: result.error,
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    videoUrl: latestContent.videoS3Url
                }
            });
        }
    } catch (error) {
        helper.devConsole('[publishController.testTikTokPublish] Error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Internal server error during TikTok test',
            error: error.message
        });
    }
});

// @desc    Test Instagram publishing with latest content
// @route   POST /api/v1/marketing/publish/test-instagram
// @access  Protected
exports.testInstagramPublish = catchAsync(async (req, res, next) => {
    helper.devConsole('[publishController.testInstagramPublish] Testing Instagram publishing...');

    // 1. Finde den neuesten MarketingContent mit Video
    const latestContent = await MarketingContent.findOne({
        videoS3Url: { $ne: null, $ne: '' }
    }).sort({ generationDate: -1 });

    if (!latestContent) {
        return res.status(404).json({
            status: 'error',
            message: 'No marketing content with video found for testing.'
        });
    }

    helper.devConsole('[publishController.testInstagramPublish] Found content for testing:', {
        id: latestContent._id,
        recipeName: latestContent.recipeName,
        videoUrl: latestContent.videoS3Url
    });

    // 2. Teste Instagram-Veröffentlichung
    try {
        const result = await instagramService.publishVideo(
            latestContent.videoS3Url,
            latestContent.recipeName,
            latestContent.socialMediaDescription || latestContent.textOne
        );

        helper.devConsole('[publishController.testInstagramPublish] Instagram test result:', result);

        if (result.success) {
            // Optional: Aktualisiere den Content in der DB
            await MarketingContent.findByIdAndUpdate(latestContent._id, {
                $inc: { published_insta: 1 }
            });

            res.status(200).json({
                status: 'success',
                message: 'Instagram publishing test successful!',
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    instagramPostId: result.platformPostId,
                    videoUrl: latestContent.videoS3Url
                }
            });
        } else {
            res.status(400).json({
                status: 'error',
                message: 'Instagram publishing test failed',
                error: result.error,
                data: {
                    contentId: latestContent._id,
                    recipeName: latestContent.recipeName,
                    videoUrl: latestContent.videoS3Url
                }
            });
        }
    } catch (error) {
        helper.devConsole('[publishController.testInstagramPublish] Error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Internal server error during Instagram test',
            error: error.message
        });
    }
});

helper.devConsole('[publishController.js] Loaded. Cronjob for daily publishing is scheduled if in production.');



// Export services for use in other controllers
exports.tiktokService = tiktokService;
exports.instagramService = instagramService;
exports.facebookService = facebookService;
exports.pinterestService = pinterestService;