# BACKEND_TASKS.md

## Übersicht

Aktuelle Entwicklungsaufgaben für das `ordy-api`-Backend.

| ID   | Beschreibung                                                                 | Status | Zugehörige Anforderung ID | Erstellt am  | Erledigt am |
| :--- | :--------------------------------------------------------------------------- | :----- | :------------------------ | :----------- | :---------- |
| B-1  | Setup Basis-Express-Server                                                   | done   | -                         | 2023-01-01   | 2023-01-02  |
| B-2  | Implementiere JWT-Login                                                      | done   | Auth-2                    | 2023-10-26   | 2024-07-30  |
| B-3  | Implementiere Google OAuth                                                   | done   | Auth-4                    | 2024-07-26   | 2024-07-30  |
| B-4  | Implementiere Stytch Auth                                                    | done   | Auth-5                    | 2024-07-26   | 2024-07-30  |
| B-5  | CRUD Endpunkte für Menüs                                                     | done   | Menu-X                    | 2024-07-26   | 2024-07-30  |
| B-6  | CRUD Endpunkte für Einkaufslisten                                            | done   | Shopping-Y                | 2024-07-26   | 2024-07-30  |
| B-7  | CRUD Endpunkte für Küchentische                                              | done   | KitchenTable-Z            | 2024-07-26   | 2024-07-30  |
| B-8  | CRUD Endpunkte für Wochenpläne                                               | done   | Weekplan-A                | 2024-07-26   | 2024-07-30  |
| B-9  | Implementiere GPT Integration                                                | done   | GPT-B                     | 2024-07-26   | 2024-07-30  |
| B-10 | Implementiere Stripe Integration                                             | done   | Payment-C                 | 2024-07-26   | 2024-07-30  |
| B-11 | Implementiere S3 File Uploads                                                | done   | File-D                    | 2024-07-26   | 2024-07-30  |
| B-12 | Implementiere 16-Tage Sliding Window Session (Stytch)                        | done   | Auth-5                    | 2024-07-29   | 2024-07-29  |
| B-13  | Analyse: Assistant Funktion Performance & Übertragung (Backend-Teil)         | todo   | -                         | 2024-07-30   |             |
| B-14  | Implementierung: Intelligente Sortierlogik für Zutaten (/zettel)             | todo   | -                         | 2024-07-30   |             |
| B-15  | Implementierung: Fixes für Assistant Funktion Performance (Backend-Teil)     | todo   | F-9, B-13                 | 2024-07-30   |             |
| B-16 | Aktuelle Backend-Dokumentation fertigstellen                                 | todo   | -                         | 2024-07-30   |             |
| B-12 | API: Endpunkt für Einkaufszettel-Historie mit Sortierung                     | todo   | REQ-101                   | 2025-05-22   |             |
| B-13 | Datenmodell: Erweiterung um Verknüpfung zwischen Einkaufszetteln und Menüs   | todo   | REQ-101                   | 2025-05-22   |             |
| B-14 | Datenmodell: Kategorie-Feld für Einkaufsartikel                              | todo   | REQ-102                   | 2025-05-22   |
| B-15 | API: Automatische Kategorisierung von Einkaufsartikeln                       | todo   | REQ-102                   | 2025-05-22   |             |
| B-16 | Datenbank: Migration bestehender Artikel zu kategorisierten Artikeln         | todo   | REQ-102                   | 2025-05-22   |             |

## PAYWALL ENHANCEMENT TASKS - DEZEMBER 2024

### Anforderung 1: Realtime API Nutzung tracken
| B-17 | User Model um nrRealtimeApiCalls erweitern                                  | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |
| B-18 | Realtime API Usage Tracking in Assistant implementieren                     | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |
| B-19 | API Endpoint für Realtime API Nutzung abrufen                               | done   | REQ-PAY-001               | 2024-12-20   | 2024-12-20  |

### Anforderung 2: Abo-Management Fehler beheben
| B-20 | 401 Error in paymentController.js analysieren und beheben                   | done   | REQ-PAY-003               | 2024-12-20   | 2024-12-20  |
| B-21 | Error Handling in Stripe Integration verbessern                             | todo   | REQ-PAY-003               | 2024-12-20   |             |
| B-22 | Session Management bei Abo-Änderungen korrigieren                           | todo   | REQ-PAY-003               | 2024-12-20   |             |

### Anforderung 3: Profilbild-Management Backend
| B-23 | API Endpoint für Profilbild Upload implementieren                           | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |
| B-24 | KI-Profilbild-Generator API implementieren                                  | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |
| B-25 | S3 Integration für Profilbilder erweitern                                   | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |
| B-26 | User Model um Profilbild-Metadaten erweitern                                | done   | REQ-PAY-005               | 2024-12-20   | 2024-12-20  |

### Anforderung 4: Automatische Profilbild-Generierung
| B-27 | Automatische Profilbild-Generierung bei User-Erstellung                     | done   | REQ-PAY-006               | 2024-12-20   | 2024-12-20  |
| B-28 | Background Job für Profilbild-Generierung implementieren                    | done   | REQ-PAY-006               | 2024-12-20   | 2024-12-20  |

### Datenmodell Erweiterungen
| B-29 | User Schema um Realtime API Felder erweitern                                | todo   | REQ-PAY-001               | 2024-12-20   |             |
| B-30 | Migration für neue Nutzungsfelder erstellen                                 | todo   | REQ-PAY-001               | 2024-12-20   |             |
| B-31 | Stripe Webhook für neue Nutzungstypen erweitern                             | todo   | REQ-PAY-001               | 2024-12-20   |             |
