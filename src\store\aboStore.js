import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useNotification from '../../modules/notificationInformation';
import axios from 'axios';
import { useUserStore } from './userStore'
import { useHelperStore } from '../../utils/helper'

export const useAboStore = defineStore('abonnement', () => {

    const { setNotification } = useNotification();
    const router = useRouter();
    const userStore = useUserStore();
    const helper = useHelperStore()

    const switchSettingsComponentButton = ref(false)

    const uiUsageData = ref([{
        name: "menucreation",
        displayName: "KI-Menüerstellung",
        value: 0,
        used: 0,
        total: 0,
        available: 0
    },{
        name: "cookeasy",
        displayName: "KI-Kochassistent",
        value: 0,
        used: 0,
        total: 0,
        available: 0
    },{
        name: "uploads",
        displayName: "Rezept-Uploads",
        value: 0,
        used: 0,
        total: 0,
        available: 0
    },{
        name: "realtimeapi",
        displayName: "Sprach-Assistent",
        value: 0,
        used: 0,
        total: 0,
        available: 0
    }])

    ////////////////// SETTINGS ////////////////////////
    // Definiere die Preis-IDs für jede Umgebung
    const priceIds = {
        development: {
        free: 'price_1QL0gU8OMOfBdtQmikjmBJeO',
        basic: 'price_1QKNrn8OMOfBdtQmF3QgR18i',
        advan: 'price_1QKirr8OMOfBdtQmFOZbTu7T',
        },
        preview: {
        free: 'price_1QMbJkG1QDxmQSlXkxMFG12I',
        basic: 'price_1QMbDLG1QDxmQSlXmgxCYnQD',
        advan: 'price_1QMbEuG1QDxmQSlXcnlmkUYv',
        },
        production: {
        free: 'price_1QMbMZG1QDxmQSlXMuutrnrI',
        basic: 'price_1QMbOCG1QDxmQSlXr2J8LZgN',
        advan: 'price_1QMbOsG1QDxmQSlXwRuf0Lz3',
        },
    };

    const stripeURLs = {
        development: {
            link: 'https://billing.stripe.com/p/login/test_bIY5lR9hR0fugOk8ww',
        },
        preview: {
            link: 'https://billing.stripe.com/p/login/test_9AQfZ15uKb0Kbi89AA',
        },
        production: {
            link: 'https://billing.stripe.com/p/login/5kA6oH0Um72l7oQ144',
        },
    };
    ////////////////// SETTINGS ////////////////////////

    // Bestimme die aktuelle Umgebung
    const currentEnvironment = import.meta.env.VITE_ENV; // VITE_ENV aus Umgebungsvariablen

    // Preis-IDs für die aktuelle Umgebung abrufen
    const currentPriceIds = priceIds[currentEnvironment] || {};
    const currentLink = stripeURLs[currentEnvironment].link || {};

    // Definiere die verfügbaren Abonnements mit den dynamischen Preis-IDs
    const availableContingentAbos = ref([
    {
        name: "free",
        priceId: currentPriceIds.free,
        aboId: 0,
        contingent: {
        nrMenucreationCalls: 5,
        nrCookeasyCalls: 8,
        nrMenuuploadCalls: 3,
        nrRealtimeApiCalls: 10,
        },
    },
    {
        name: "basic",
        priceId: currentPriceIds.basic,
        aboId: 1,
        contingent: {
        nrMenucreationCalls: 23,
        nrCookeasyCalls: 23,
        nrMenuuploadCalls: 10,
        nrRealtimeApiCalls: 25,
        },
    },
    {
        name: "advan",
        priceId: currentPriceIds.advan,
        aboId: 2,
        contingent: {
        nrMenucreationCalls: 100,
        nrCookeasyCalls: 100,
        nrMenuuploadCalls: 30,
        nrRealtimeApiCalls: 100,
        },
    },
    ]);

    // STORE
    const items = ref([
    {
        name: "Free",
        active: true,
        litext: `
        <li>5 KI-Menüerstellungen <b>(gesamt)</b></li>
        <li>8 KI-Kochassistent <b>(gesamt)</b></li>
        <li>3 Rezept-Uploads <b>(gesamt)</b></li>
        <li>10 Sprach-Assistent <b>(gesamt)</b></li>
        `,
        description: "In der Free Version kannst du keinem Küchentisch beitreten. Sprich du kannst keine Rezepte mit deiner Familie, Freunden etc teilen.",
        value: 0,
        priceId: currentPriceIds.free,
        type: 0,
    },
    {
        name: "Basic",
        active: false,
        litext: `
        <li>23 KI-Menüerstellungen <b>(monatlich)</b></li>
        <li>23 KI-Kochassistent <b>(monatlich)</b></li>
        <li>12 Rezept-Uploads <b>(monatlich)</b></li>
        <li>25 Sprach-Assistent <b>(monatlich)</b></li>
        `,
        description: "In der Basic Version kannst du einem Küchentisch beitreten und deine Rezepte mit deiner Familie, Freunden, etc teilen. Zusätzlich hast du mehr Kontingent für alle Creations.",
        value: 6,
        priceId: currentPriceIds.basic,
        type: 1,
    },
    {
        name: "Advan",
        active: false,
        litext: `
        <li>100 KI-Menüerstellungen <b>(monatlich)</b></li>
        <li>100 KI-Kochassistent <b>(monatlich)</b></li>
        <li>100 Rezept-Uploads <b>(monatlich)</b></li>
        <li>100 Sprach-Assistent <b>(monatlich)</b></li>
        `,
        description: "In der Advan Version bekommst du eine schier unbegrenzte Möglichkeit für alle Creations.",
        value: 17,
        priceId: currentPriceIds.advan,
        type: 2,
    },
    ]);

    // STORE

    // FUNCTIONS
    const setAndCalcAboUsage = () => {
        helper.devConsole("inside calcAboUsage")

        // Ensure user has all required usage fields with fallbacks
        if (!userStore.user.bookedAboUsage.nrRealtimeApiCalls) {
            userStore.user.bookedAboUsage.nrRealtimeApiCalls = 0;
        }

        //availableContingentAbos.value
        //userStore.user.bookedAbo.type
        // filter active abo and set limits
        const activeAbo = availableContingentAbos.value.map(item => {
            if (item.aboId === Number(userStore.user.bookedAbo.type)) {
                // Menucreation
                const menuUsed = userStore.user.bookedAboUsage.nrMenucreationCalls;
                const menuTotal = item.contingent.nrMenucreationCalls;
                uiUsageData.value[0].value = 100 / menuTotal * menuUsed;
                uiUsageData.value[0].used = menuUsed;
                uiUsageData.value[0].total = menuTotal;
                uiUsageData.value[0].available = menuTotal - menuUsed;

                // Cookeasy
                const cookeasyUsed = userStore.user.bookedAboUsage.nrCookeasyCalls;
                const cookeasyTotal = item.contingent.nrCookeasyCalls;
                uiUsageData.value[1].value = 100 / cookeasyTotal * cookeasyUsed;
                uiUsageData.value[1].used = cookeasyUsed;
                uiUsageData.value[1].total = cookeasyTotal;
                uiUsageData.value[1].available = cookeasyTotal - cookeasyUsed;

                // Uploads
                const uploadsUsed = userStore.user.bookedAboUsage.nrMenuuploadCalls;
                const uploadsTotal = item.contingent.nrMenuuploadCalls;
                uiUsageData.value[2].value = 100 / uploadsTotal * uploadsUsed;
                uiUsageData.value[2].used = uploadsUsed;
                uiUsageData.value[2].total = uploadsTotal;
                uiUsageData.value[2].available = uploadsTotal - uploadsUsed;

                // Realtime API
                const realtimeUsed = userStore.user.bookedAboUsage.nrRealtimeApiCalls || 0;
                const realtimeTotal = item.contingent.nrRealtimeApiCalls;
                uiUsageData.value[3].value = 100 / realtimeTotal * realtimeUsed;
                uiUsageData.value[3].used = realtimeUsed;
                uiUsageData.value[3].total = realtimeTotal;
                uiUsageData.value[3].available = realtimeTotal - realtimeUsed;
            }
        });
    }

    const goToCalcWindow = () => {
            window.open(currentLink, "_blank");
            helper.devConsole(currentLink)
    }
    const setCorrectAbo = () => {
        const updatedItems = items.value.map(item => {
            if (item.type === Number(userStore.user.bookedAbo.type)) {
                return { ...item, active: true }; // Setze active auf true für das gefundene Objekt
            } else {
                return { ...item, active: false }; // Setze active auf false für alle anderen
            }
          });
          //console.log(updatedItems)
          items.value = updatedItems
    }

    const checkout = async (amount) => {
        helper.devConsole("checkout at aboStore")
        try{
            const res = await axios.get(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/payment/checkout/' + amount + '/' + userStore.user.id);
            //console.log(import.meta.env.VITE_API_BASE_URL + '/api/v1/auth/payment/checkout/' + amount)
            console.log('res from checkout', res);
            if (res.data.success === true) {
                //if new customer and or abo was created, new page has to open. otherwise not
                helper.devConsole(res.data.data.reload)
                if(res.data.success && res.data.data.reload){
                    window.open(res.data.data.url, "_blank");
                }
            }

        } catch (error){
            helper.devConsole(error)
            // IMPROVED: Better error handling for subscription changes
            if (error.response) {
                const status = error.response.status;
                const message = error.response.data?.message || 'Unbekannter Fehler';

                if (status === 500) {
                    setNotification('Fehler beim Verarbeiten der Abo-Änderung. Bitte versuchen Sie es erneut.', 'alert');
                } else if (status === 400) {
                    setNotification('Ungültige Abo-Anfrage. Bitte überprüfen Sie Ihre Auswahl.', 'alert');
                } else {
                    setNotification(`Abo-Fehler: ${message}`, 'alert');
                }
            } else {
                setNotification('Netzwerkfehler beim Abo-Wechsel. Bitte überprüfen Sie Ihre Internetverbindung.', 'alert');
            }
        }
    }

    // Refresh usage data when component mounts or when needed
    const refreshUsageData = async () => {
        try {
            helper.devConsole("Refreshing usage data...");

            // Fetch fresh user data from API
            const response = await axios.post(
                `${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/userbyid`,
                {},
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data && response.data.user) {
                // Update user store with fresh data
                userStore.user.bookedAboUsage = response.data.user.bookedAboUsage;

                // Recalculate usage data
                setAndCalcAboUsage();

                helper.devConsole("Usage data refreshed successfully");
            }
        } catch (error) {
            helper.devConsole("Error refreshing usage data:", error);
        }
    };

    // Watch for changes in user usage data and auto-refresh UI
    watch(
        () => userStore.user.bookedAboUsage,
        (newUsage, oldUsage) => {
            if (newUsage && oldUsage) {
                // Check if any usage values have changed
                const hasChanged =
                    newUsage.nrMenucreationCalls !== oldUsage.nrMenucreationCalls ||
                    newUsage.nrCookeasyCalls !== oldUsage.nrCookeasyCalls ||
                    newUsage.nrMenuuploadCalls !== oldUsage.nrMenuuploadCalls ||
                    newUsage.nrRealtimeApiCalls !== oldUsage.nrRealtimeApiCalls;

                if (hasChanged) {
                    helper.devConsole("Usage data changed, recalculating UI...");
                    setAndCalcAboUsage();
                }
            }
        },
        { deep: true }
    );

    return {
        // EXPORTET VALUES
        switchSettingsComponentButton,
        uiUsageData,
        items,
        availableContingentAbos,
        // EXPORTED FUNCTIONS
        checkout,
        setCorrectAbo,
        goToCalcWindow,
        setAndCalcAboUsage,
        refreshUsageData
    };

});