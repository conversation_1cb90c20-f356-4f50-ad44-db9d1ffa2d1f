/* // <PERSON><PERSON><PERSON> ausko<PERSON>, da connection2 entfernt wurde
const { connection2 } = require('./../db.js');
const mongoose = require('mongoose');

const peaxSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    transactionId: {
        type: String,
        required: true,
        unique: true
    },
    date: {
        type: Date,
        required: true
    },
    description: {
        type: String
    },
    amount: {
        type: Number,
        required: true
    },
    category: {
        type: String
    },
    subCategory: {
        type: String
    },
    paymentMethod: {
        type: String
    },
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed'],
        default: 'pending'
    },
    createdAt: {
        type: Date,
        default: Date.now()
    },
    updatedAt: {
        type: Date,
        default: Date.now()
    }
});

module.exports = connection2.model('Peax', peaxSchema)
*/