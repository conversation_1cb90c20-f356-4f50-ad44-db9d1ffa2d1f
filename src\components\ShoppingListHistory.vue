<template>
  <div class="shopping-list-history">
    <!-- Desktop Layout: Tite<PERSON> und Button untereinander -->
    <div class="hidden md:block mb-3">
      <h2 class="text-lg mb-2">Einkaufslisten</h2>
      <button
        @click="showCompletedLists = !showCompletedLists"
        class="text-xs px-3 py-1 rounded-full transition-colors"
        :class="showCompletedLists
          ? 'bg-red-100 text-red-700 hover:bg-red-200'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
      >
        {{ showCompletedLists ? '🔒 Ausblenden' : '🔒 Geschlossene' }}
      </button>
    </div>

    <!-- Mobile Layout: Titel und Button nebeneinander -->
    <div class="flex justify-between items-center mb-3 md:hidden">
      <h2 class="text-lg">Einkaufslisten</h2>
      <button
        @click="showCompletedLists = !showCompletedLists"
        class="text-xs px-3 py-1 rounded-full transition-colors"
        :class="showCompletedLists
          ? 'bg-red-100 text-red-700 hover:bg-red-200'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
      >
        {{ showCompletedLists ? '🔒 Ausblenden' : '🔒 Geschlossene' }}
      </button>
    </div>

    <div v-if="isLoading" class="text-center py-4">
      <div class="animate-pulse flex space-x-4">
        <div class="flex-1 space-y-4 py-1">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="error" class="text-red-500 text-sm py-2">
      {{ error }}
    </div>

    <div v-else-if="historyLists.length === 0" class="text-gray-500 text-sm py-2">
      Keine Einkaufszettel vorhanden.
    </div>

    <div v-else class="space-y-3">
      <div
        v-for="list in paginatedLists"
        :key="list._id"
        :class="[
          'p-4 rounded-lg shadow-sm border transition-all duration-200 bg-white cursor-pointer hover:shadow-md',
          list.is_completed
            ? 'border-red-300 opacity-80'
            : list.is_active
            ? 'border-green-300 hover:border-green-400'
            : 'border-amber-300 hover:border-amber-400'
        ]"
        @click="handleListClick(list)"
      >
        <div class="flex flex-col space-y-2">
          <!-- Header mit Name -->
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <!-- Anzeige Name (nicht editierbar) -->
              <div class="flex items-center gap-2">
                <span
                  :class="[
                    'text-sm font-medium',
                    list.is_completed
                      ? 'text-red-700 line-through'
                      : list.is_active
                      ? 'text-green-800'
                      : 'text-amber-700'
                  ]"
                >
                  {{ list.name || 'Unbenannte Liste' }}
                </span>
              </div>
            </div>

            <!-- Status Badge -->
            <div class="flex items-center">
              <span
                v-if="list.is_completed"
                class="text-xs px-3 py-1 rounded-full bg-red-500 text-white font-medium whitespace-nowrap shadow-sm"
              >
                🔒 Geschlossen
              </span>
              <span
                v-else-if="list.is_active"
                class="text-xs px-3 py-1 rounded-full bg-green-500 text-white font-medium whitespace-nowrap shadow-sm"
              >
                ✓ Aktiv
              </span>
              <span
                v-else
                class="text-xs px-3 py-1 rounded-full bg-amber-500 text-white font-medium whitespace-nowrap shadow-sm"
              >
                ⏸ Inaktiv
              </span>
            </div>
          </div>

          <!-- Datum und Details -->
          <div
            :class="[
              'flex justify-between items-center text-xs',
              list.is_completed ? 'text-red-600' : 'text-gray-500'
            ]"
          >
            <span>{{ formatDate(list.createdAt) }}</span>
            <span>
              {{ list.item_count || 0 }} Artikel, {{ list.completed_item_count || 0 }} erledigt
              <span v-if="list.is_completed" class="ml-1">✓</span>
            </span>
          </div>


        </div>
      </div>

      <!-- Mehr laden Button -->
      <div v-if="hasMoreItems" class="text-center mt-4">
        <button
          @click="loadMoreItems"
          class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Mehr laden ({{ sortedHistoryLists.length - paginatedLists.length }} weitere)
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useUserStore } from '../store/userStore';
import { useHelperStore } from '../../utils/helper';
import { useActiveShoppingListStore } from '../store/activeShoppingListStore';
import axios from 'axios';
import dayjs from 'dayjs';
import 'dayjs/locale/de';

// Stores
const userStore = useUserStore();
const helper = useHelperStore();
const activeListStore = useActiveShoppingListStore();

// State
const historyLists = ref([]);
const isLoading = ref(false);
const error = ref(null);

// Pagination state
const itemsPerPage = 10;
const currentPage = ref(1);

// Toggle state for completed lists
const showCompletedLists = ref(false);



// Computed: Sortierte Listen - Aktive+Inaktive chronologisch, dann abgeschlossene (optional)
const sortedHistoryLists = computed(() => {
  const activeAndInactiveLists = historyLists.value.filter(list => !list.is_completed);
  const completedLists = historyLists.value.filter(list => list.is_completed);

  // Sortiere beide Gruppen nach Datum (neueste zuerst)
  const sortByDate = (a, b) => new Date(b.createdAt) - new Date(a.createdAt);

  // Nur abgeschlossene Listen hinzufügen, wenn Toggle aktiviert ist
  const listsToShow = [
    ...activeAndInactiveLists.sort(sortByDate),
    ...(showCompletedLists.value ? completedLists.sort(sortByDate) : [])
  ];

  return listsToShow;
});

// Computed: Paginierte Listen (nur die ersten X anzeigen)
const paginatedLists = computed(() => {
  const maxItems = currentPage.value * itemsPerPage;
  return sortedHistoryLists.value.slice(0, maxItems);
});

// Computed: Prüfen ob mehr Items verfügbar sind
const hasMoreItems = computed(() => {
  return sortedHistoryLists.value.length > currentPage.value * itemsPerPage;
});

// Funktion: Mehr Items laden
const loadMoreItems = () => {
  currentPage.value += 1;
};

// Fetch shopping list history
const fetchShoppingListHistory = async () => {
  const kitchentableId = userStore.user.defaultKitchentable;
  if (!kitchentableId) {
    error.value = 'Kein Küchentisch ausgewählt.';
    return;
  }

  isLoading.value = true;
  error.value = null;

  // Reset pagination when fetching new data
  currentPage.value = 1;

  try {
    // Hole alle Einkaufslisten (aktive und historische)
    const historyResponse = await axios.get(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${kitchentableId}/shopping-lists/history`
    );

    if (historyResponse.data.success) {
      // Alle Listen (Backend gibt jetzt alle Listen zurück)
      historyLists.value = historyResponse.data.data;

      helper.devConsole(`Loaded ${historyLists.value.length} shopping lists`);
      helper.devConsole('Lists with completion status:', historyLists.value.map(l => ({
        id: l._id,
        name: l.name,
        is_active: l.is_active,
        is_completed: l.is_completed
      })));
    } else {
      error.value = 'Fehler beim Laden der Einkaufszettel-Historie.';
    }
  } catch (err) {
    helper.devConsole('Error fetching shopping list history:', err);
    error.value = 'Fehler beim Laden der Einkaufszettel-Historie.';
  } finally {
    isLoading.value = false;
  }
};

// Define emits
const emit = defineEmits(['list-activated']);

// Edit functions
const startEdit = (listId, currentName) => {
  editingListId.value = listId;
  editingName.value = currentName || '';
  // Focus input in next tick
  setTimeout(() => {
    if (nameInput.value) {
      nameInput.value.focus();
      nameInput.value.select();
    }
  }, 50);
};

const cancelEdit = () => {
  editingListId.value = null;
  editingName.value = '';
};

const saveListName = async (listId) => {
  if (!editingName.value.trim()) {
    cancelEdit();
    return;
  }

  try {
    const response = await axios.put(
      `${import.meta.env.VITE_API_BASE_URL}/api/shopping-lists/${listId}/name`,
      { name: editingName.value.trim() }
    );

    if (response.data.success) {
      // Update local list
      const list = historyLists.value.find(l => l._id === listId);
      if (list) {
        list.name = editingName.value.trim();
      }

      helper.devConsole(`Updated list name: ${editingName.value}`);

      if (window.showToast) {
        window.showToast('Listenname gespeichert', 'success');
      }
    } else {
      if (window.showToast) {
        window.showToast('Fehler beim Speichern des Namens', 'error');
      }
    }
  } catch (err) {
    helper.devConsole('Error updating list name:', err);
    if (window.showToast) {
      window.showToast('Fehler beim Speichern des Namens', 'error');
    }
  } finally {
    cancelEdit();
  }
};

// Handle list click - different behavior for completed vs active lists
const handleListClick = (list) => {
  if (list.is_completed) {
    // Für abgeschlossene Listen: Nur anzeigen, nicht aktivieren
    helper.devConsole(`Viewing completed list ${list._id}`);
    if (window.showToast) {
      window.showToast('Diese Liste ist abgeschlossen und kann nur angesehen werden', 'info');
    }
    // Hier könnten wir später eine "View-Only" Funktion implementieren
    // Für jetzt: Liste trotzdem laden, aber als read-only
    viewCompletedList(list._id);
  } else {
    // Für aktive/inaktive Listen: Normal aktivieren
    activateList(list._id);
  }
};

// View a completed list (read-only)
const viewCompletedList = async (listId) => {
  helper.devConsole(`Loading completed list ${listId} for viewing`);

  try {
    // Finde die Liste in der lokalen historyLists (bereits geladen)
    const listData = historyLists.value.find(list => list._id === listId);

    if (!listData) {
      helper.devConsole(`List ${listId} not found in local history`);
      if (window.showToast) {
        window.showToast('Liste nicht gefunden', 'error');
      }
      return;
    }

    // Lade die Items für diese spezifische Liste über die History-API mit includeItems Parameter
    const kitchentableId = userStore.user.defaultKitchentable;
    const response = await axios.get(
      `${import.meta.env.VITE_API_BASE_URL}/api/v1/kitchentable/${kitchentableId}/shopping-lists/history?includeItems=true`
    );

    if (response.data.success) {
      // Finde die spezifische Liste mit Items in der Response
      const fullListData = response.data.data.find(list => list._id === listId);

      if (fullListData) {
        // Manuell die Store-Werte setzen für die Anzeige
        activeListStore.items = fullListData.items || [];
        activeListStore.listId = fullListData._id;
        activeListStore.listName = fullListData.name || '';
        activeListStore.listCreatedAt = fullListData.createdAt;
        activeListStore.isActive = false; // Nicht aktiv
        activeListStore.isCompleted = true; // Abgeschlossen

        helper.devConsole(`Loaded completed list for viewing: ${fullListData.name}`);

        // Emittiere Event, dass eine Liste angezeigt wird
        emit('list-activated');

        if (window.showToast) {
          window.showToast('Abgeschlossene Liste wird angezeigt (nur lesbar)', 'info');
        }
      } else {
        if (window.showToast) {
          window.showToast('Liste nicht in der Historie gefunden', 'error');
        }
      }
    } else {
      if (window.showToast) {
        window.showToast('Fehler beim Laden der Liste', 'error');
      }
    }
  } catch (err) {
    helper.devConsole('Error loading completed list:', err);
    if (window.showToast) {
      window.showToast('Fehler beim Laden der Liste', 'error');
    }
  }
};

// Activate a historical shopping list
const activateList = async (listId) => {
  const list = historyLists.value.find(l => l._id === listId);

  // Prüfe ob die Liste abgeschlossen ist (kann nicht reaktiviert werden)
  if (list && list.is_completed) {
    helper.devConsole(`List ${listId} is completed and cannot be reactivated`);
    if (window.showToast) {
      window.showToast('Abgeschlossene Listen können nicht reaktiviert werden', 'info');
    }
    return;
  }

  // Wenn die Liste bereits aktiv ist UND bereits im Store angezeigt wird, nichts tun
  if (list && list.is_active && activeListStore.listId === listId && !activeListStore.isCompleted) {
    helper.devConsole(`List ${listId} is already active and displayed`);
    return;
  }

  // Wenn die Liste aktiv ist, aber eine abgeschlossene Liste angezeigt wird, lade die aktive Liste
  if (list && list.is_active) {
    helper.devConsole(`Loading active list ${listId} (switching from completed list view)`);
    // Lade die aktive Liste direkt über den Store
    await activeListStore.fetchActiveList();

    // Emittiere Event, dass eine Liste aktiviert wurde
    emit('list-activated');

    if (window.showToast) {
      window.showToast('Aktive Liste wird angezeigt', 'success');
    }
    return;
  }

  isLoading.value = true;

  try {
    const response = await axios.put(
      `${import.meta.env.VITE_API_BASE_URL}/api/shopping-lists/${listId}/activate`
    );

    if (response.data.success) {
      helper.devConsole(`Activated shopping list ${listId}`);

      // Aktualisiere die lokale Liste, um die UI sofort zu aktualisieren
      historyLists.value.forEach(list => {
        list.is_active = list._id === listId;
      });

      // Zeige eine Erfolgsmeldung an
      if (window.showToast) {
        window.showToast('Einkaufszettel wurde aktiviert', 'success');
      }

      // Emittiere Event, dass eine Liste aktiviert wurde
      emit('list-activated');

      // Aktualisiere die Seite ohne vollständigen Reload
      setTimeout(() => {
        // Aktualisiere die aktive Liste
        activeListStore.fetchActiveList();
      }, 500);
    } else {
      error.value = 'Fehler beim Aktivieren der Einkaufsliste.';
      if (window.showToast) {
        window.showToast('Fehler beim Aktivieren der Einkaufsliste', 'error');
      }
    }
  } catch (err) {
    helper.devConsole('Error activating shopping list:', err);

    // Spezifische Fehlerbehandlung
    if (err.response && err.response.status === 401) {
      // Authentifizierungsfehler - nicht ausloggen, sondern Fehlermeldung anzeigen
      error.value = 'Bitte melden Sie sich erneut an, um diese Aktion durchzuführen.';
      if (window.showToast) {
        window.showToast('Bitte melden Sie sich erneut an', 'error');
      }
    } else {
      // Allgemeiner Fehler
      error.value = 'Fehler beim Aktivieren der Einkaufsliste.';
      if (window.showToast) {
        window.showToast('Fehler beim Aktivieren der Einkaufsliste', 'error');
      }
    }
  } finally {
    isLoading.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  dayjs.locale('de');
  return dayjs(dateString).format('DD.MM.YYYY');
};

// Watch for changes in the kitchentable ID
watch(() => userStore.user.defaultKitchentable, (newVal) => {
  if (newVal) {
    fetchShoppingListHistory();
  } else {
    historyLists.value = [];
  }
}, { immediate: true });

// Watch for changes in the active shopping list items
watch(() => activeListStore.items, (newItems) => {
  // Wenn keine aktive Liste vorhanden ist oder keine Listen in der Historie sind, nichts tun
  if (!activeListStore.listId || historyLists.value.length === 0) return;

  // Finde die aktive Liste in der Historie
  const activeListIndex = historyLists.value.findIndex(list => list.is_active);
  if (activeListIndex === -1) return;

  // Aktualisiere die Anzahl der Artikel und erledigten Artikel
  const totalItems = newItems.length;
  const completedItems = newItems.filter(item => item.is_purchased).length;

  // Aktualisiere die Liste in der Historie
  historyLists.value[activeListIndex].item_count = totalItems;
  historyLists.value[activeListIndex].completed_item_count = completedItems;
}, { deep: true });

// Watch for list name changes
watch(() => activeListStore.listName, (newName) => {
  if (!activeListStore.listId || historyLists.value.length === 0) return;

  // Finde die aktive Liste in der Historie und aktualisiere den Namen
  const activeList = historyLists.value.find(list => list._id === activeListStore.listId);
  if (activeList) {
    activeList.name = newName;
    helper.devConsole(`Updated list name in history: ${newName}`);
  }
});

// Watch for list completion status changes
watch(() => activeListStore.isCompleted, (isCompleted) => {
  if (!activeListStore.listId || historyLists.value.length === 0) return;

  // Finde die Liste in der Historie und aktualisiere den Status
  const list = historyLists.value.find(list => list._id === activeListStore.listId);
  if (list) {
    list.is_completed = isCompleted;
    list.is_active = !isCompleted; // Wenn abgeschlossen, dann nicht mehr aktiv
    helper.devConsole(`Updated list completion status in history: ${isCompleted}`);
  }
});

// Fetch data on component mount
onMounted(() => {
  if (userStore.user.defaultKitchentable) {
    fetchShoppingListHistory();
  }
});
</script>
