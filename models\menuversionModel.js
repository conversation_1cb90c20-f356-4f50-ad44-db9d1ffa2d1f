const mongoose = require('mongoose');
const { connection1 } = require('./../db.js');
//const MenuesRelations = require('../models/menuRelationModel')
const Unit = require('./unitModel')
const Grocery = require('./groceryModel')

const menuversionSchema = new mongoose.Schema({
    parentId: {
        type: String
    },
    seatCount: {
        type: Number
    },
    cookingTime: {
        type: String
    },
    ingridients: [
        {
            amount: Number,
            unit: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Unit'
            },
            name: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Grocery'
            }
        }
    ],
    preperation: {
        type: Array,
    },
    nutritions: {
        type: Array,
    },
    versions: {
        type: Array,
    },
    createdAt: {
        type: Date,
        default: Date.now()
    }
})

module.exports = connection1.model('MenuVersion', menuversionSchema)