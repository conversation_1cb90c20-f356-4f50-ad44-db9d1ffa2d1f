/**
 * Categorization Routes
 * API-Endpunkte für intelligente Zutat-Kategorisierung
 */

const express = require('express');
const authController = require('../controllers/authController');
const categorizationController = require('../controllers/categorizationController');

const router = express.Router();

// Protect all routes after this middleware
router.use(authController.verify);

// Single ingredient categorization
router.route('/single')
    .post(categorizationController.categorizeSingleIngredient);

// Batch ingredient categorization
router.route('/batch')
    .post(categorizationController.categorizeBatchIngredients);

// Get available categories
router.route('/categories')
    .get(categorizationController.getAvailableCategories);

// Update grocery category
router.route('/grocery/:groceryId')
    .put(categorizationController.updateGroceryCategory);

// Migrate existing groceries
router.route('/migrate-existing')
    .post(categorizationController.migrateExistingGroceries);

// Get categorization statistics
router.route('/stats')
    .get(categorizationController.getCategorizationStats);

module.exports = router;
